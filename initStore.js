// 确保 store 在应用启动时就被初始化
// import './packages/common/src/models/system';
import Loading from "@xlb/common/src/components/RootView/Loading";
global.__XLB_COMPONENT__ = {};
global.__XLB_STORES__ = {
  system: null,
};
global.__XLB__ = {
  event: (() => {
    class Emitter {
      constructor() {
        this.events = {};
        this.watchs = [];
      }
      add(eventName, callback, count) {
        if (!eventName || typeof callback !== 'function') return;
        if (!this.events[eventName]) {
          this.events[eventName] = [];
          this.events[eventName].push({callback, count});
        } else {
          const hasExist = this.events[eventName].some(
            item => item.callback === callback && item.count === count,
          );
          !hasExist && this.events[eventName].push({callback, count});
        }
      }
      emit(...args) {
        const [eventName, ...restArgs] = args;
        const callbacks = this.events[eventName] || [];
        if (eventName && this.watchs.length > 0) {
          this.watchs.forEach(item => {
            item.apply(this, [eventName, ...restArgs]);
          });
        }
        if (eventName && callbacks.length > 0) {
          callbacks.forEach(({callback, count}) => {
            callback.apply(this, [eventName, ...restArgs]);
            count && this.off(eventName, callback);
          });
        }
      }
      on(eventName, callback) {
        this.add(eventName, callback, 0);
      }
      once(eventName, callback) {
        this.add(eventName, callback, 1);
      }
      off(eventName, callback) {
        const callbacks = this.events[eventName] || [];
        if (callbacks.legnth <= 0) return;
        if (!callback) this.events[eventName] = [];
        callbacks.forEach((item, index) => {
          if (item.callback === callback) {
            callbacks.splice(index, 1);
          }
        });
      }
      watch(callback) {
        if (typeof fn !== 'function') return;
        this.watchs.push(callback);
      }
    }
    return new Emitter();
  })(),
};
global.xlbNative = {}
global.xlbConstant = {}
global.Loading = {
  show: ()=> Loading.show(),
  hide: () => Loading.hide()
}
