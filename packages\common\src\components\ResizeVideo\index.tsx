import React, { useRef, useState } from 'react'
import { Pressable, StyleSheet, View, Text, Linking, Platform, NativeModules } from 'react-native'

// import RNVideo, { VideoProperties } from 'react-native-video'
import XIcon from '../others/XIcon'
import { ResizeImage, ResizeImageProps } from '../ResizeImage'
import ProModal from '../ProModal'
import { Button, Flex } from '..'
import WebView from 'react-native-webview'

import Toast from 'react-native-root-toast'

// import { VLCPlayer } from 'react-native-vlc-media-player'

import { cloneDeep } from 'lodash'

// export interface ResizeVideoProps extends Omit<VideoProperties, 'onLoad'> {
//   /** 定宽不定高, 宽高相同优先定高 */
//   width?: number
//   /** 定高不定宽 */
//   height?: number
//   /** 自定义匹配盒子宽高来适配， 不超过盒子高度或者宽度 */
//   fitable?: boolean
// }

export const ResizeVideo: React.FC<ResizeImageProps & { poster: string }> = ({ source, poster, ...rest }) => {
  const [visible, setVisible] = useState(false)
  const [isVisible, setIsVisible] = useState(true)
  // const [imageSize, setImageSize] = useState({
  //   width: width ?? 10,
  //   height: height ?? 10,
  // })
  // const [isPaused, setIsPaused] = useState(true)

  // const videoRef = useRef<RNVideo>(null)

  // const computeSize = (imageWidth: number, imageHeight: number) => {
  //   const ratio = imageWidth === 0 ? 0 : imageHeight / imageWidth
  //   const computedHeight = ratio * (width || 0)
  //   const computedWidth = ratio === 0 ? 0 : (height || 0) / ratio
  //   if (!fitable) {
  //     if (width) {
  //       setImageSize({
  //         width,
  //         height: computedHeight,
  //       })
  //     } else if (height) {
  //       setImageSize({
  //         width: computedWidth,
  //         height,
  //       })
  //     }
  //   } else {
  //     if (imageWidth >= imageHeight && width) {
  //       setImageSize({
  //         width,
  //         height: computedHeight,
  //       })
  //     } else if (imageWidth < imageHeight && height) {
  //       setImageSize({
  //         width: computedWidth,
  //         height,
  //       })
  //     }
  //   }
  // }

  // if (!source || (source && ((Array.isArray(source) && source.length === 0) || (typeof source === 'object' && !source?.uri)))) return null
  // console.log(source, 'source')

  const handlePreview = () => {
    if (Platform.OS === 'ios') {
      try {
        NativeModules?.XLBAVPlayerBridge?.xlbAVPlayerEvent?.('play', source?.uri, (error: any, events: any) => {})
      } catch (error) {
        setVisible(true)
        setIsVisible(true)
        // console.log('读取文件出错:', error.message);
      }
    } else {
      setVisible(true)
      setIsVisible(true)
    }
  }

  return (
    <>
      <View style={{ ...styles.container }}>
        <ResizeImage {...rest} source={{ uri: poster }} />
        {/* <RNVideo
        {...rest}
        ref={videoRef}
        source={source}
        repeat
        // rate={Number(!isPaused)}
        paused={false}
        resizeMode="contain"
        // allowsExternalPlayback
        //@ts-ignore
        style={StyleSheet.flatten([rest.style, { width: imageSize.width, height: imageSize.height }])}
        onLoad={(e) => {
          const { width: imageWidth, height: imageHeight } = e.naturalSize

          // console.log(imageWidth, imageHeight, e, 'eeee')

          computeSize(imageWidth, imageHeight)
        }}
      /> */}

        <Pressable style={styles.playIcon} onPress={handlePreview}>
          <XIcon color="#fff" size={30} name={'iconPlay'} onPress={handlePreview} />
        </Pressable>
      </View>

      <ProModal statusBarTranslucent visible={visible} onRequestClose={() => setVisible(false)} animationType="slide">
        <Flex>
          <WebView source={source as any} />
          {isVisible ? (
            <View
              style={{
                position: 'absolute',
                bottom: 65,
                height: 40,
                width: '80%',
                backgroundColor: '#fff',
                left: '10%',
                borderRadius: 8,
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 12,
                justifyContent: 'space-between',
              }}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <XIcon
                  name={'close'}
                  size={16}
                  right={4}
                  onPress={() => {
                    setIsVisible(false)
                  }}
                />
                <Text style={{ fontSize: 14, color: '#1F2126' }}>无法播放？试试本地播放</Text>
              </View>

              <Pressable
                style={{ height: 26, paddingHorizontal: 8, justifyContent: 'center', borderColor: '#1C6AFF', borderWidth: 1, borderRadius: 6 }}
                onPress={() => {
                  if (source && source.uri) Linking.openURL(source.uri)
                }}
              >
                <Text style={{ fontSize: 14, fontWeight: '500', color: '#1C6AFF' }}>本地播放</Text>
              </Pressable>
            </View>
          ) : null}

          <Button title="关闭" onPress={() => setVisible(false)} />
        </Flex>
      </ProModal>
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    zIndex: 1,
  },
  playIcon: {
    position: 'absolute',

    width: '100%',
    height: '100%',

    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 30,
  },
})
