import axios, { AxiosError } from 'axios'
import { Platform } from 'react-native'
import { enhance } from 'foca-axios'
import dayjs from 'dayjs'
import { getToken, saveToken, signOut } from '../auth'
import XLBStorage from '../../utils/storage'
import { USER_ACCOUNT, USER_LICENSE, USER_PASSWORD } from '../../config/constant'
import XLBBaseApi from '@xlb/business-base/src/api'
// import { Toast } from 'native-base';
import { XLBHttp } from './http'
import { authModel } from '@xlb/business-base/src/models/auth'
import Config from 'react-native-config'
import { Cell, Toast } from '@fruits-chain/react-native-xiaoshu'
import { navigationRef } from '../../navigation/NavigationService'
import { msgMap } from '../msg'

import Const from '@xlb/common/src/utils/const'
import * as Sentry from '@sentry/react-native'

const green = '\u001b[32m'
const yellow = '\u001b[33m'
const magenta = '\u001b[35m'
const cyan = '\u001b[36m'
const white = '\u001b[37m'
const reset = '\u001b[0m'
const timestampToDatetime = (timestamp: string | number | Date) => {
  const dateObj = new Date(timestamp)

  const year = dateObj.getFullYear()
  const month = dateObj.getMonth() + 1
  const day = dateObj.getDate()
  const hours = dateObj.getHours()
  const minutes = dateObj.getMinutes()
  const seconds = dateObj.getSeconds()

  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
}

const instance = axios.create()

instance.interceptors.request.use(
  // 动态获取baseUrl 便于切换环境
  async (config) => {
    // const accessToken = config.url === '/login/login' ? null : await refreshToken();
    // console.info(cyan + '| ----------------- fetcher loging ----------------- ');
    // console.info(cyan + '| fetcher      | ' + magenta + 'requestFetcher');
    // console.info(cyan + '| endpoint     | ' + yellow + `${config.url}`);
    // console.info(cyan + '| body         | ' + white, config.data);
    // console.info(cyan + '| Bearer token | ' + accessToken);
    // console.info(cyan + '| -------------------------------------------------- ' + reset);

    if (authModel?.state?.userInfo) {
      config.data._u_id = authModel?.state?.userInfo.id
      config.data.operator = authModel?.state?.userInfo.id
      config.data.company_id = authModel?.state?.userInfo.company_id || 1000
    }

    const urlSplit = config.url?.split('/')?.filter((item) => !!item) || []

    config.data.xlb_request_start_time = new Date().getTime()
    // config.baseURL = await XLBGlobalStorage.getItem(BASE_URL);
    // config.baseURL = Config.BMS_URL;
    const prefixList: any = {
      4754: 'https://api.xlbsoft.com',
      66666: 'https://ws-api.xlbsoft.com',
    }
    const userInfos: any = authModel.state.userInfos
    const url = prefixList[userInfos.company_id]
    if (url) {
      config.baseURL = 'https://hxl-api.xlbsoft.com'
    } else {
      // config.baseURL = Config.ENV === 'DEVELOP' && urlSplit && urlSplit.length ? Const.NewApiService?.[urlSplit[0]] || Config.ERP_URL : Config.ERP_URL
      const urlEnvName = ['ENV', 'DEVELOP', 'TEST'].includes(Config.ENV || '') ? 'test' : Config.BRANCH_NAME;
      config.baseURL = `https://react-web.react-web.ali-${urlEnvName}.xlbsoft.com/`;
      console.log('bmshttp.js -> config.baseURL', config.baseURL);
    }
    config.headers = {
      'Content-type': 'application/json',
      Authorization: `Bearer ${authModel?.state?.userInfos?.old_access_token}`,
      XLB: true,
      'Company-Id': authModel?.state?.userInfos?.company_id || 1000,
      'User-Id': authModel?.state?.userInfos?.id || '',
      'Org-Ids': authModel?.state.userInfos?.org_ids ? authModel?.state.userInfos?.org_ids.join(',') : '',
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

instance.interceptors.response.use(
  (response) => {
    if (response.data.code !== 0) {
      Toast.fail(response.data.msg)
    }
    return response
  },
  (err: AxiosError) => {
    if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
      // if (Config?.ENV == 'PROD') {
      //   axios({
      //     method: 'post',
      //     url: 'https://open.feishu.cn/open-apis/bot/v2/hook/4a355bd9-89b0-41f2-b001-6c3b9d4d39c0',
      //     data: {
      //       msg_type: 'text',
      //       content: {
      //         text: `${err.config?.baseURL}${err.config?.url} 接口超时;\n用户：${authModel?.state?.userInfos?.company_id} - ${
      //           authModel?.state?.userInfos?.name
      //         } - ${authModel?.state?.userInfos?.tel}\n平台：${Platform?.OS};\n参数：\n ${err.config?.data};\n错误码：\n ${err?.code || ''};\n错误信息：\n ${
      //           err?.message || ''
      //         }\n开始时间：\n ${timestampToDatetime(JSON.parse(err.config?.data)?.xlb_request_start_time)};\n结束时间：\n ${timestampToDatetime(
      //           new Date().getTime()
      //         )};\n用时(ms)：\n ${new Date().getTime() - (JSON.parse(err.config?.data)?.xlb_request_start_time || 0)}`,
      //       },
      //     },
      //   })
      // }
      if (Config?.ENV == 'DEVELOP') {
        axios({
          method: 'post',
          url: 'https://open.feishu.cn/open-apis/bot/v2/hook/0629b5cc-8daf-402d-9cac-aceac3a201d0',
          data: {
            msg_type: 'text',
            content: {
              text: `${err.config?.baseURL}${err.config?.url} 接口超时;\n用户：${authModel?.state?.userInfos?.company_id} - ${
                authModel?.state?.userInfos?.name
              } - ${authModel?.state?.userInfos?.tel}\n平台：${Platform?.OS};\n参数：\n ${err.config?.data};\n错误码：\n ${err?.code || ''};\n错误信息：\n ${
                err?.message || ''
              }\n开始时间：\n ${timestampToDatetime(JSON.parse(err.config?.data)?.xlb_request_start_time)};\n结束时间：\n ${timestampToDatetime(
                new Date().getTime()
              )};\n用时(ms)：\n ${new Date().getTime() - JSON.parse(err.config?.data)?.xlb_request_start_time}`,
            },
          },
        })
      }
      // Toast.fail('请求超时，请重试')
    } else if (err?.response?.status == 401) {
      Toast.fail('用户暂无该接口权限\n请联系信息部处理')
    } else if (err.message === 'Network Error') {
      const currentRoute = navigationRef?.current?.getCurrentRoute()
      if (currentRoute.name !== 'Home') {
        debugger
                    navigationRef.current.navigate('NoNetWork')
      }
    } else {
      if (err?.response?.status) {
        Sentry.captureException(`服务告警${err?.response?.status}---------${err?.config?.baseURL}${err?.config?.url};\n用户：${authModel?.state?.userInfos?.name} - ${authModel?.state?.userInfos?.tel}`)
        Toast.fail(msgMap[err?.response?.status])
      }
    }
    return Promise.reject(err)
  }
)

export const BmsHttp = enhance(instance, {
  cache: {
    enable: false,
  },
  throttle: {
    enable: true,
  },
  retry: {
    enable: true,
  },
})

async function refreshToken() {
  const token = await getToken()
  let accessToken = token.accessToken
  if (!token || !token?.accessToken) {
    await signOut()
    return ''
  }
  // 判断当前日期是否晚于tokenExpireTime，如果是表示token已经过期，根据持久化的数据去调用登录接口返回token
  if (dayjs().isAfter(dayjs.unix(<number>token.time))) {
    const license = await XLBStorage.getItem(USER_LICENSE)
    const account = await XLBStorage.getItem(USER_ACCOUNT)
    const password = await XLBStorage.getItem(USER_PASSWORD)
    const response: VerifyPasswordSuccessPayload = await XLBHttp.post('/login/login', {
      license: license,
      account: account,
      pwd: password,
      from: 'app',
    })
    //登录成功，将token放置缓存中便于查询
    const tokenInfo: Token = {}
    tokenInfo.accessToken = response.access_token
    tokenInfo.userId = response.data._u_id
    tokenInfo.time = response.data._time
    tokenInfo.xlbToken = response.data._token
    tokenInfo.username = response.data.name
    saveToken(tokenInfo)
    return XLBBaseApi.Base.getUserInfo()
  }
  return accessToken
}
