/**
 * 公共 零售调价门店和成分商品调价门店 model
 */
import { defineModel } from 'foca';
import { CommonSelectApi } from '../../api/select.api';
import { removeEmpty } from '../../utils/object';
import {ErpHttp} from "../../services/lib/erphttp";

export const SelectNewStoreModel = defineModel('SelectNewStoreModel', {
  initialState: {
    page: 1,
    total: 0,
    list: [],
    keywords: ''
  },
  reducers: {
    setPage(state, page: number) {
      state.page = page;
    },
    setList(state, list: any) {
      state.list = list;
    },
    setTotal(state, total: number) {
      state.total = total;
    },
    setKeywords(state, keywords) {
      state.keywords = keywords;
    },
    resetKeywords(state) {
      state.keywords = this.initialState.keywords;
    }
  },
  methods: {
    async queryList(page?: number, queryParams?: any, url?: string, returnKey?: string) {
      const postUrl = url ? url:'/erp-mdm/hxl.erp.store.short.page'/*  */
      const { list, keywords } = this.state;
      const _page = page || 1;
      const res = await ErpHttp.post<CommonResponse>(postUrl,{/*  */
        // page_number: _page - 1,
        ...removeEmpty({
          keyword: keywords,
          store_name: url === '/erp-mdm/hxl.erp.deliverycenterstore.page' ? keywords : '',
          ...queryParams,
        }),
      });
      const l = returnKey?res.data:res.data.content
      let _list = [...list] || [];
      _list =
        _page > 1 && l.length ? _list.concat(l) : l;
      this.setList(_list);
      _page == 1 && this.setTotal(returnKey?res.data.length:res.data.total_elements);
      this.setPage(_page);
    },
  },
});
