import { safeMath } from '../utils'

const PAGE = 'page'

const DEPART = 'depart'

const SAVE = 'save'

const HALFMINUTE = 30000
const PAGE_TIMEOUT = HALFMINUTE
// FIXME 之前只有5秒， 现在给他换成15秒
const OTHER_TIMEOUT = safeMath.divide(HALFMINUTE, 2)
// 特殊处理的超时
const SPECIAL_TIMEOUT = safeMath.divide(HALFMINUTE, 3)

const specialHandling = ['/erp-mdm/hxl.erp.requestorder.app.item.page', '/erp-mdm/hxl.erp.requestorder.page']

/* 后期支持多种类型的请求时间扩展 */
const TIMEOUT: any = {
  [PAGE]: PAGE_TIMEOUT,
  [DEPART]: PAGE_TIMEOUT * 4,
  [SAVE]: PAGE_TIMEOUT,
}
/*
 * 根据接口结尾单词判断接口类型,返回处理超时时间
 * */
export const configTime = (url: string) => {
  const data = url?.split('.')
  const type = data.pop() as string

  if (specialHandling.includes(url)) return SPECIAL_TIMEOUT

  return TIMEOUT[type] ?? OTHER_TIMEOUT
}
