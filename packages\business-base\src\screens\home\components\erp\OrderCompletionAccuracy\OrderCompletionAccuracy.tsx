import { Pressable, StyleSheet, View, ViewStyle } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import { colors } from '@xlb/common/src/config/theme'
import { useNavigation } from '@react-navigation/native'
import { Col, Row, Toast } from '@fruits-chain/react-native-xiaoshu'

import BaseCard from '../components/BaseCard'
import DataItemView, { Themes } from '../components/DataItemView'

import store from './store'
import { server } from './server'
import { completionList, accuracyList } from './model'
import { items } from '../‌AbnormalityAlarmCard/model'
import { cloneDeep } from 'lodash'
import dayjs from 'dayjs'
import useHasAuth from '@xlb/common/src/hooks/useHasAuth'

export interface DataItemViewProps {
  title?: string
  container?: ViewStyle
}

const index = (props: DataItemViewProps) => {
  const { container } = props
  const navigation = useNavigation<any>()

  const time_data = store((state: any) => state.time_data)
  const setTime_data = store((state: any) => state.setTime_data)

  const complete_data = store((state: any) => state.complete_data)
  const setComplete_data = store((state: any) => state.setComplete_data)
  const endDate = useRef('')
  const getTime = async () => {
    const firstDayOfLastMonth = dayjs().subtract(1, 'day').startOf('month').format('YYYY-MM-DD')
    endDate.current = firstDayOfLastMonth + ' 至 ' + dayjs().subtract(1, 'day').endOf('day').format('YYYY-MM-DD')
  }

  const getData = async () => {
    let create_date = [`${dayjs().subtract(1, 'day').startOf('month').format('YYYY-MM-DD')}`, `${dayjs().subtract(1, 'day').endOf('day').format('YYYY-MM-DD')}`]
    const res = await server.getOntimeData({ summary_types: ['SUPPLIER', 'STORE'], appoint_date: create_date })
    const res1 = await server.getCompleteData({ summary_types: ['SUPPLIER', 'STORE'], unit_type: 'PURCHASE', create_date })
    getTime()
    let accuracyList_Tmp = accuracyList.map((item: any, index: any) => {
      return { title: item?.name, lable: res?.data?.[item.key] ?? '0', unit: '' }
    })
    setTime_data(cloneDeep(accuracyList_Tmp))
    let completionList_Tmp = completionList.map((item: any, index: any) => {
      return {
        title: item?.name,
        lable: res1?.data?.[item.key] ?? '0',
        unit: '',
      }
    })
    setComplete_data(completionList_Tmp)
  }

  useEffect(() => {
    getData()
  }, [])

  return (
    <View style={[{ ...container }]}>
      <BaseCard title="采购关注" description={'以下指标仅统计当月数据（截止昨日）'} time={`统计时间 ${endDate.current}`}>
        <View style={{ paddingHorizontal: 12, paddingBottom: 12 }}>
          <Row gap={12}>
            <Col span={12} style={{ marginBottom: 12 }}>
              <Pressable
                onPress={() => {
                  // if (useHasAuth(['商品销售分析', '查询'])) {
                  navigation.navigate(ErpRoutes.punctualityRateH5)
                  // } else {
                  // Toast('无对应报表权限，请联系管理员')
                  // }
                }}
              >
                <DataItemView title="订单准点率" theme={Themes.ORANGE} data={time_data} titleStyle={{ width: 55 }}></DataItemView>
              </Pressable>
            </Col>
            <Col span={12} style={{ marginBottom: 12 }}>
              <Pressable
                onPress={() => {
                  if (useHasAuth(['订单完成率', '查询'])) {
                    navigation.navigate(ErpRoutes.completionRateH5)
                  } else {
                    Toast('无对应报表权限，请联系管理员')
                  }
                }}
              >
                <DataItemView title="订单完成率" theme={Themes.ORANGE} data={complete_data} titleStyle={{ width: 55 }}></DataItemView>
              </Pressable>
            </Col>
          </Row>
        </View>
      </BaseCard>
    </View>
  )
}
const style = StyleSheet.create({
  container: {
    paddingHorizontal: 8,
  },
  cardBox: {
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingLeft: 14,
  },
  headerBox: {
    paddingVertical: 12,
    borderBottomColor: colors.lineGrey,
    borderBottomWidth: 0.5,
    height: 50,
    alignItems: 'center',
  },
})

export default index
