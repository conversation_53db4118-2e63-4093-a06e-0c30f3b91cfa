// /*
//  * @Author: 杜云涛 <EMAIL>
//  * @Date: 2025-03-11 17:02:42
//  * @LastEditors: 杜云涛 <EMAIL>
//  * @LastEditTime: 2025-06-21 17:13:51
//  * @FilePath: /xlb_app/packages/business-im/src/api/index.ts
//  * @Description:
//  *
//  */
import axios, {AxiosInstance, AxiosRequestConfig} from 'axios';
import Config from 'react-native-config';
export const createAxiosByinterceptors = (
  config?: AxiosRequestConfig,
): AxiosInstance => {
  const baseURL = `https://im.im.ali-${Config.ENV}.xlbsoft.com`;
  const instance = axios.create({
    baseURL: baseURL,
    timeout: 100000, //超时配置
    withCredentials: true, //跨域携带cookie
    ...config, // 自定义配置覆盖基本配置
  });

  // 添加请求拦截器
  instance.interceptors.request.use(
    function (config: any) {
      // 在发送请求之前做些什么
      // console.log("config:", config);
      config.headers.Authorization = `Bearer ${authModel?.state?.userInfos?.access_token}`;
      config.headers['Access-Token'] =
        authModel?.state?.userInfos?.access_token;
      return config;
    },
    function (error) {
      // 对请求错误做些什么
      return Promise.reject(error);
    },
  );

  // 添加响应拦截器
  instance.interceptors.response.use(
    function (response) {
      // 对响应数据做点什么
      // console.log("response:", response);
      const {code, data, message} = response.data;
      if (code === 200 || code === 0) return data;
      else if (code === 401) {
      } else {
        return Promise.reject(response.data);
      }
    },
    function (error) {
      // 对响应错误做点什么
      // console.log("error-response:", error.response);
      // console.log("error-config:", error.config);
      // console.log("error-request:", error.request);
      if (error.response) {
        if (error.response.status === 401) {
        }
      }
      return Promise.reject(error);
    },
  );
  return instance;
};
