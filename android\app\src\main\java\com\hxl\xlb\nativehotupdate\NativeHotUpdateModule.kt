package com.hxl.xlb.nativehotupdate

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.core.content.FileProvider
import com.facebook.react.bridge.*
import com.facebook.react.module.annotations.ReactModule
import com.jakewharton.processphoenix.ProcessPhoenix
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.net.HttpURLConnection
import java.net.URL

@ReactModule(name = NativeHotUpdateModule.NAME)
class NativeHotUpdateModule(private val reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String = NAME

    @Volatile
    private var isDownloading = false

    private fun resetApp() {
        ProcessPhoenix.triggerRebirth(reactApplicationContext)
    }

    private fun getBundleCacheDir(): File {
        return File(reactApplicationContext.cacheDir, "RNHotUpdateBundle")
    }

    private fun downloadFile(downloadUrl: String, destFile: File): Boolean {
        return try {
            val url = URL(downloadUrl)
            val conn = url.openConnection() as HttpURLConnection
            conn.connectTimeout = 10000
            conn.readTimeout = 10000
            conn.setRequestProperty("User-Agent", "NativeHotUpdateModule/1.0")

            val responseCode = conn.responseCode
            if (responseCode != HttpURLConnection.HTTP_OK) {
                println("下载失败，HTTP响应码: $responseCode")
                return false
            }

            conn.inputStream.use { input ->
                destFile.parentFile?.also { parent ->
                    if (!parent.exists()) {
                        parent.mkdirs()
                    }
                }
                FileOutputStream(destFile).use { output ->
                    input.copyTo(output)
                }
            }
            true
        } catch (e: Exception) {
            e.printStackTrace()
            println("下载失败：$e")
            false
        }
    }


    @ReactMethod
    fun hasUpdate(remoteVersion: String?, promise: Promise) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 添加空值检查
                if (remoteVersion == null) {
                    promise.resolve(false)
                    return@launch
                }

                val sharedPref = reactApplicationContext.getSharedPreferences("hot_update_version", ReactApplicationContext.MODE_PRIVATE)
                val currentVersion = sharedPref.getString("current_version", null)
                val bundleFile = File(getBundleCacheDir(), "index.bundle")

                // 更健壮的检查逻辑
                val hasUpdate = when {
                    currentVersion == null -> true // 没有当前版本，需要更新
                    remoteVersion != currentVersion -> true // 版本不同，需要更新
                    !bundleFile.exists() -> true // 文件不存在，需要更新
                    bundleFile.length() <= 0 -> true // 文件为空，需要更新
                    else -> false
                }

                promise.resolve(hasUpdate)
            } catch (e: Exception) {
                e.printStackTrace()
                promise.reject("HOT_UPDATE_CHECK_FAILED", "检查更新失败: ${e.message}", e)
            }
        }
    }


    @ReactMethod
    fun runDownload(remoteVersion: String, downloadUrl: String) {
        if (isDownloading) {
            Handler(Looper.getMainLooper()).post {
                Toast.makeText(reactApplicationContext, "正在下载中...", Toast.LENGTH_SHORT).show()
            }
            return
        }
        isDownloading = true
        Thread {
            try {
                val sharedPref = reactApplicationContext.getSharedPreferences("hot_update_version", ReactApplicationContext.MODE_PRIVATE)
                val currentVersion = sharedPref.getString("current_version", null)
                val cacheDir = getBundleCacheDir()
                val tmpFile = File(cacheDir, "index.bundle.tmp")
                val bundleFile = File(cacheDir, "index.bundle")

                // 添加目录检查
                if (!cacheDir.exists()) {
                    if (!cacheDir.mkdirs()) {
                        Handler(Looper.getMainLooper()).post {
                            Toast.makeText(reactApplicationContext, "无法创建缓存目录", Toast.LENGTH_LONG).show()
                        }
                        return@Thread
                    }
                }

                if (currentVersion == remoteVersion && bundleFile.exists() && bundleFile.length() > 0) {
                    Handler(Looper.getMainLooper()).post {
                        Toast.makeText(reactApplicationContext, "已是最新版本", Toast.LENGTH_SHORT).show()
                    }
                    return@Thread
                }

                Handler(Looper.getMainLooper()).post {
                    Toast.makeText(reactApplicationContext, "新版本下载中...", Toast.LENGTH_LONG).show()
                }

                val success = downloadFile(downloadUrl, tmpFile)
                if (success) {
                    bundleFile.delete()
                    // 检查 renameTo 是否成功
                    if (tmpFile.renameTo(bundleFile)) {
                        val isEdit = sharedPref.edit().putString("current_version", remoteVersion).commit()
                        Handler(Looper.getMainLooper()).post {
                            if (isEdit) {
                                Toast.makeText(reactApplicationContext, "新版本下载成功，正在重启...", Toast.LENGTH_SHORT).show()
                                resetApp()
                            } else {
                                Toast.makeText(reactApplicationContext, "写入版本号失败", Toast.LENGTH_SHORT).show()
                                // 清理文件
                                bundleFile.delete()
                            }
                        }
                    } else {
                        Handler(Looper.getMainLooper()).post {
                            Toast.makeText(reactApplicationContext, "文件移动失败", Toast.LENGTH_SHORT).show()
                        }
                        tmpFile.delete()
                    }
                } else {
                    Handler(Looper.getMainLooper()).post {
                        Toast.makeText(reactApplicationContext, "新版本下载失败", Toast.LENGTH_LONG).show()
                    }
                    tmpFile.delete()
                }
            } catch (e: Exception) {
                e.printStackTrace()
                Handler(Looper.getMainLooper()).post {
                    Toast.makeText(reactApplicationContext, "更新过程异常: ${e.message}", Toast.LENGTH_LONG).show()
                }
            } finally {
                isDownloading = false
            }
        }.start()
    }

    @ReactMethod
    fun installApk(filePath: String, promise: Promise) {
        try {
            val file = File(filePath)
            val intent = Intent(Intent.ACTION_VIEW)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                val apkUri = FileProvider.getUriForFile(
                    reactApplicationContext,
                    "${reactApplicationContext.packageName}.fileprovider",
                    file
                )
                intent.setDataAndType(apkUri, "application/vnd.android.package-archive")
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            } else {
                intent.setDataAndType(Uri.fromFile(file), "application/vnd.android.package-archive")
            }

            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            reactApplicationContext.startActivity(intent)
            promise.resolve(true)
        } catch (e: Exception) {
            e.printStackTrace()
            promise.reject("INSTALL_ERROR", e.message)
        }
    }

    @ReactMethod
    fun getCurrentVersion(promise: Promise) {
        try {
            val sharedPref = reactApplicationContext.getSharedPreferences("hot_update_version", ReactApplicationContext.MODE_PRIVATE)
            val currentVersion = sharedPref.getString("current_version", null)
            promise.resolve(currentVersion)
        } catch (e: Exception) {
            e.printStackTrace()
            return promise.reject("GET_VERSION_ERROR", "获取当前版本失败: ${e.message}", e)
        }
    }

    companion object {
        const val NAME = "NativeHotUpdate"
    }
}
