package com.hxl.xlb.map;

import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.InputFilter;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hxl.xlb.R;
import com.hxl.xlb.bean.FileBean;
import com.hxl.xlb.bean.eventbus.AddLandlordEvent;
import com.hxl.xlb.bean.eventbus.MessageEvent;
import com.hxl.xlb.contract.MapAddLandlordContract;
import com.hxl.xlb.responsebody.ItemChangeReponse;
import com.hxl.xlb.utils.FilePickerUtils;
import com.hxl.xlb.utils.MySPTool;
import com.hxl.xlb.utils.UploadUtil;
import com.hxl.xlb.widget.BottomSheetDialogHelper;
import com.hxl.xlb.widget.DialogMapPromptView;
import com.hxl.xlb.widget.DialogTemplateView;
import com.xlb.mvplibrary.mvp.MvpActivity;
import com.xlb.mvplibrary.network.BaseResponse;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MapAddLandlordActivity extends MvpActivity<MapAddLandlordContract.MapAddLandlordPresenter> implements MapAddLandlordContract.IAddLandlordView, View.OnClickListener {


    private int companyId;//公司id

    private int point_id;//点位id

    private boolean isEdit;//是否编辑进来还是新增进来

    private int someIndex;//编辑的是第几个房东

    private boolean isFromDetail;//是否从点位详情进来

    private ScrollView activityRootView;//根布局scrollview

    private HashMap<String, Object> dataDic = new HashMap<>();//编辑时用到的外层输入的数据

    private HashMap<String, Boolean> requiredMap = new HashMap<>();//房东必填项与否


    private static final int DELETE_BUTTON_ID = 10001;

    private static final int SAVE_BUTTON_ID = 10002; // 动态生成删除按钮的唯一 ID

    private FilePickerUtils filePickerUtils;//文件选择器


    private EditText nameET;//房东姓名

    private EditText phoneET;//房东电话

    private HashMap<String, Object> landlordCFDic = new HashMap<>();//房东身份证人像

    private HashMap<String, Object> landlordCBDic = new HashMap<>();//房东身份证国徽像

    private HashMap<String, String> landlordNatureDic = new HashMap<>();//房东性质

    private HashMap<String, String> landlordIdentityDic = new HashMap<>();//房东身份

    Boolean isExclusive;//是否排他协议

    Boolean isRentDivide;//是否分租

    private EditText rentET;//租金

    private EditText transfer_feeET;//转让费

    private List<HashMap<String, Object>> lease_contract_list = new ArrayList<>();//租房合同

    private List<HashMap<String, Object>> title_certificate_list = new ArrayList<>();//产权证

    private HashMap<String, Object> store_headDic = new HashMap<>();//门头照

    private EditText memoET;//备注

    private EditText rentPeopleET;//租户姓名

    private EditText rentPhoneET;//租户联系方式

    private TextView rent_deadLineTV;//出租截止日期

    private EditText bankAccountET;//当前租户收款账户

    private EditText bankAccountNameET;//当前租户收款账户户名

    private HashMap<String, Object> licenseDic = new HashMap<>();//当前租户涉及营业执照

    private HashMap<String, Object> rentCFDic = new HashMap<>();//租户身份证人像像
    private HashMap<String, Object> rentCBDic = new HashMap<>();//租户身份证国徽像

    Boolean isIdentical;//执照法人与收款账户户名同一人

    private EditText legalpersonNameET;//法人姓名

    private EditText legalpersonPhoneET;//法人电话

    private HashMap<String, Object> legalpersonCFDic = new HashMap<>();//法人身份证人像像
    private HashMap<String, Object> legalpersonCBDic = new HashMap<>();//法人身份证国徽像

    private List<HashMap<String, Object>> rent_url_list = new ArrayList<>();//当前租户与房东的租房合同

    Boolean isIdenticalUser;//租房合同签约是否与收款账户户名同一人

    private HashMap<String, String> relationshipProofDic = new HashMap<>();//收款户名与合同签署人关系证明

    private List<HashMap<String, Object>> relationship_list = new ArrayList<>();//关系证明附件

    Boolean isAdditional;//转让费是否包含其他费用

    private EditText additionalMemoET;//转让费备注

    private List<HashMap<String, Object>> transferAgreement_list = new ArrayList<>();//转让协议

    @Override
    public void initData(Bundle savedInstanceState) {

        companyId = MySPTool.getInt(this, "app_companyId");
        point_id = getIntent().getIntExtra("point_id", 0);
        isEdit = getIntent().getBooleanExtra("isEdit", false);
        isFromDetail = getIntent().getBooleanExtra("isFromDetail", false);
        someIndex = getIntent().getIntExtra("someIndex", -1);


        HashMap<String, Object> resultDic = (HashMap<String, Object>) getIntent().getSerializableExtra("dataDic");
        if (resultDic != null) {
            dataDic = resultDic;
            Log.d("TAG", "传入的参数为=====" + dataDic);
        }


        findViewById(R.id.back_ll).setOnClickListener(this);


        // 监听布局变化，适配软键盘的显示和隐藏
        activityRootView = findViewById(R.id.mainscrollview);

        // 注册 ActivityResultLauncher
        ActivityResultLauncher<Intent> activityResultLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> filePickerUtils.handleActivityResult(result)
        );

        // 初始化 FilePickerUtils
        filePickerUtils = new FilePickerUtils(this, this, activityResultLauncher);

//        底部按钮页面
        initBottomView();
//        loding
        showLoadingDialog();
        //初始化变动项接口
        initChangeItem();
    }


    //    动态调整页面必填与否
    private void initReqiredView() {

        TextView landlordNameIndicator = findViewById(R.id.landlord_indicator);
        nameET = findViewById(R.id.landlord_name);
        if (!requiredMap.get("landlord_name")) {//房东姓名
            landlordNameIndicator.setText("");
        }

        TextView landlordPhoneIndicator = findViewById(R.id.landlordphone_indicator);
        phoneET = findViewById(R.id.landlord_phone);
        if (!requiredMap.get("landlord_phone")) {//房东电话
            landlordPhoneIndicator.setText("");
        }

        TextView landlordCardIndicator = findViewById(R.id.landlordcard_indicator);
        if (!requiredMap.get("owner_id_number_front_url")) {//房东证件照
            landlordCardIndicator.setText("");
        }

//        房东身份证证件  正面
        FrameLayout frameLayoutf = findViewById(R.id.landlord_cardff);
        ImageView cardf = findViewById(R.id.landlord_cardf);
        ImageView cardfCenter = findViewById(R.id.landlord_cardf_center);
        ImageView cardfTop = findViewById(R.id.landlord_cardf_top);
        frameLayoutf.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击事件逻辑
                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");
                showActionChooseImageDialog("/kms/hxl.kms.storeplanhouseinfo.file.upload", map, landlordCFDic, frameLayoutf, cardf, cardfCenter, cardfTop);
            }
        });

        cardfTop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 点击事件逻辑
                cardf.setImageDrawable(getResources().getDrawable(R.drawable.landlord_card_front));
                v.setVisibility(View.GONE);
                cardfCenter.setVisibility(View.VISIBLE);
                landlordCFDic.clear();

                frameLayoutf.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {

                        HashMap<String, Object> map = new HashMap<>();
                        map.put("id", point_id);
                        map.put("fileType", "LandlordLog");
                        showActionChooseImageDialog("/kms/hxl.kms.storeplanhouseinfo.file.upload", map, landlordCFDic, frameLayoutf, cardf, cardfCenter, cardfTop);
                    }
                });
            }
        });


//        反面
        FrameLayout frameLayoutb = findViewById(R.id.landlord_cardbf);
        ImageView cardb = findViewById(R.id.landlord_cardb);
        ImageView cardbCenter = findViewById(R.id.landlord_cardb_center);
        ImageView cardbTop = findViewById(R.id.landlord_cardb_top);
        frameLayoutb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");
                showActionChooseImageDialog("/kms/hxl.kms.storeplanhouseinfo.file.upload", map, landlordCBDic, frameLayoutb, cardb, cardbCenter, cardbTop);
            }
        });

        cardbTop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 点击事件逻辑
                cardb.setImageDrawable(getResources().getDrawable(R.drawable.landlord_card_back));
                v.setVisibility(View.GONE);
                cardbCenter.setVisibility(View.VISIBLE);

                landlordCBDic.clear();
                frameLayoutb.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("id", point_id);
                        map.put("fileType", "LandlordLog");
                        showActionChooseImageDialog("/kms/hxl.kms.storeplanhouseinfo.file.upload", map, landlordCBDic, frameLayoutb, cardb, cardbCenter, cardbTop);
                    }
                });
            }
        });

//        房东性质
        TextView landlordTypeIndicator = findViewById(R.id.landlordtype_indicator);
        if (!requiredMap.get("landlord_type")) {
            landlordTypeIndicator.setText("");
        }

        TextView landlordTypeText = findViewById(R.id.landlordtype_text);
        LinearLayout landlordTypeLayout = findViewById(R.id.landlordtypef);
        landlordTypeLayout.setOnClickListener(v -> {
            List<Map<String, String>> items = new ArrayList<>();
            items.add(createItem("NATURAL_PERSON", "自然人"));
            items.add(createItem("STATE_OWNED_ENTERPRISE", "国企"));
            items.add(createItem("PRIVATE_ENTERPRISE", "民企"));
            items.add(createItem("GOVERNMENT_AGENCY", "政府机构"));
            items.add(createItem("PUBLIC_INSTITUTION", "事业单位"));
            items.add(createItem("OTHER_ORGANIZATION", "其他机构"));
            BottomSheetDialogHelper.showSingleSelectBottomSheetDialog(
                    this,
                    "请选择房东性质",
                    items,
                    landlordNatureDic, // 可以为 null
                    (id, name) -> {
                        landlordTypeText.setText(name);
                        landlordTypeText.setTextColor(getResources().getColor(R.color.color_map_template_cb_text_normal));

                        landlordNatureDic.clear();
                        landlordNatureDic.put("id", id);
                        landlordNatureDic.put("name", name);
                    }
            );

        });

//        房东身分
        TextView landidentity_indicator = findViewById(R.id.landidentity_indicator);
        if (!requiredMap.get("landlord_identity")) {
            landidentity_indicator.setText("");
        }

        TextView landlordidentity_text = findViewById(R.id.landlordidentity_text);
        LinearLayout landlordidentityf = findViewById(R.id.landlordidentityf);
        landlordidentityf.setOnClickListener(v -> {
            List<Map<String, String>> items = new ArrayList<>();
            items.add(createItem("PROPERTY_OWNER", "产权人"));
            items.add(createItem("SECONDARY_TENANT", "二房东"));
            items.add(createItem("THIRD_PARTY_TENANT", "三房东"));
            items.add(createItem("OTHER", "其他"));
            BottomSheetDialogHelper.showSingleSelectBottomSheetDialog(
                    this,
                    "请选择房东身份",
                    items,
                    landlordIdentityDic, // 可以为 null
                    (id, name) -> {
                        landlordidentity_text.setText(name);
                        landlordidentity_text.setTextColor(getResources().getColor(R.color.color_map_template_cb_text_normal));

                        landlordIdentityDic.clear();
                        landlordIdentityDic.put("id", id);
                        landlordIdentityDic.put("name", name);
                    }
            );

        });


//        排他协议
        TextView landlordExclusiveIndicator = findViewById(R.id.landlordexclusive_indicator);
        if (!requiredMap.get("is_exclusive_agreement")) {
            landlordExclusiveIndicator.setText("");
        }

        RadioGroup exclusiveRG = findViewById(R.id.landlordexclusive_rg);
        exclusiveRG.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                // 获取被选中的RadioButton的实例
                RadioButton selectedRadioButton = findViewById(checkedId);
                // 获取被选中的RadioButton的文本（即值）
                String title = selectedRadioButton.getText().toString();
                if (title.equals("是")) {
                    isExclusive = true;
                } else {
                    isExclusive = false;
                }

            }
        });

//        是否分租
        TextView landlordrent_dividedIndicator = findViewById(R.id.landlordrent_divided);
        if (!requiredMap.get("is_rent_divided")) {
            landlordrent_dividedIndicator.setText("");
        }
        LinearLayout rentDividedLayout = findViewById(R.id.rent_divided);
        rentDividedLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击事件逻辑
                showPromptDialog();
            }
        });

        RadioGroup rentDividedRG = findViewById(R.id.rent_divided_rg);
        rentDividedRG.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                // 获取被选中的RadioButton的实例
                RadioButton selectedRadioButton = findViewById(checkedId);
                // 获取被选中的RadioButton的文本（即值）
                String title = selectedRadioButton.getText().toString();
                if (title.equals("是")) {
                    isRentDivide = true;
                } else {
                    isRentDivide = false;
                }

            }
        });

//        租金
        TextView rent_indicator = findViewById(R.id.rent_indicator);
        if (!requiredMap.get("rent_money")) {
            rent_indicator.setText("");
        }

        LinearLayout rent_ll = findViewById(R.id.rent_ll);
        rent_ll.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击事件逻辑
                showPromptRentDialog();
            }
        });
        rentET = findViewById(R.id.rent_et);
        TextView rent_UNMoney = findViewById(R.id.rent_UNMoney);

        // 设置输入过滤器，限制输入为整数或小数
        rentET.setFilters(new InputFilter[]{new DecimalInputFilter(9, 2)});
        rentET.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // 在文本改变之前执行的操作
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // 在文本改变时执行的操作
                // s 是当前输入框的内容
                Log.d("EditText", "当前内容：" + s.toString());

                // 将输入的数字转换为中文大写
                String input = s.toString();
                if (!input.isEmpty()) {
                    try {
                        BigDecimal number = new BigDecimal(input);
                        String chineseCapital = convertToChineseCapital(number);
                        SpannableStringBuilder coloredText = applyColorToChineseCapital(chineseCapital);
                        rent_UNMoney.setText(coloredText);
                    } catch (NumberFormatException e) {
                        rent_UNMoney.setText("");
                    }
                } else {
                    rent_UNMoney.setText("");
                }

            }

            @Override
            public void afterTextChanged(Editable editable) {

            }

        });

//        转让费
        TextView transfer_fee_indicator = findViewById(R.id.transfer_fee_indicator);
        if (!requiredMap.get("transfer_fee")) {
            transfer_fee_indicator.setText("");
        }
        transfer_feeET = findViewById(R.id.transfer_fee_et);
        TextView transfer_fee_UNMoney = findViewById(R.id.transfer_fee_UNMoney);
        LinearLayout bottomView = findViewById(R.id.botton_view);//下半部分界面

        // 设置输入过滤器，限制输入为整数或小数
        transfer_feeET.setFilters(new InputFilter[]{new DecimalInputFilter(9, 2)});
        transfer_feeET.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // 在文本改变之前执行的操作
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

                // 将输入的数字转换为中文大写
                String input = s.toString();
                if (!input.isEmpty()) {
                    try {
                        BigDecimal number = new BigDecimal(input);
                        String chineseCapital = convertToChineseCapital(number);
                        SpannableStringBuilder coloredText = applyColorToChineseCapital(chineseCapital);
                        transfer_fee_UNMoney.setText(coloredText);
                    } catch (NumberFormatException e) {
                        transfer_fee_UNMoney.setText("");
                    }

                    double value = Double.parseDouble(input);
                    if (value > 0) {
                        bottomView.setVisibility(View.VISIBLE);
                    } else {
                        bottomView.setVisibility(View.GONE);
                    }

                } else {
                    transfer_fee_UNMoney.setText("");
                    bottomView.setVisibility(View.GONE);
                }

            }

            @Override
            public void afterTextChanged(Editable editable) {

            }

        });

//        租房合同
        TextView lease_contract_indicator = findViewById(R.id.lease_contract_indicator);
        if (!requiredMap.get("lease_contract")) {
            lease_contract_indicator.setText("");
        }
        TextView lease_contract_upload = findViewById(R.id.lease_contract_upload);

//        内容区域
        LinearLayout lease_contract_content = findViewById(R.id.lease_contract_content);
        lease_contract_upload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");
                showActionChooseFileDialog("/kms/hxl.kms.storeplanhouseinfo.file.upload", map, lease_contract_list, lease_contract_content);
            }
        });

//        产权证
        TextView title_certificate_indicator = findViewById(R.id.title_certificate_indicator);
        if (!requiredMap.get("title_certificate")) {
            title_certificate_indicator.setText("");
        }
        TextView title_certificate_upload = findViewById(R.id.title_certificate_upload);

//        内容区域
        LinearLayout title_certificate_content = findViewById(R.id.title_certificate_content);
        title_certificate_upload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");
                showActionChooseFileDialog("/kms/hxl.kms.storeplanhouseinfo.file.upload", map, title_certificate_list, title_certificate_content);
            }
        });

//        门头照
        TextView store_head_indicator = findViewById(R.id.store_head_indicator);
        if (!requiredMap.get("store_head")) {
            store_head_indicator.setText("");
        }
        FrameLayout store_head_frame = findViewById(R.id.store_head_frame);
        ImageView store_head_IM = findViewById(R.id.store_head_IM);
        ImageView store_headCenter = findViewById(R.id.store_head_center);
        ImageView store_headTop = findViewById(R.id.store_head_top);
        store_head_frame.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击事件逻辑

                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");
                showActionChooseImageDialog("/kms/hxl.kms.storeplanhouseinfo.file.upload", map, store_headDic, store_head_frame, store_head_IM, store_headCenter, store_headTop);

            }
        });

        store_headTop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 点击事件逻辑
                store_head_IM.setImageDrawable(getResources().getDrawable(R.drawable.upload_store_head));
                v.setVisibility(View.GONE);
                store_headCenter.setVisibility(View.VISIBLE);
                store_headDic.clear();


                store_head_frame.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("id", point_id);
                        map.put("fileType", "LandlordLog");
                        showActionChooseImageDialog("/kms/hxl.kms.storeplanhouseinfo.file.upload", map, store_headDic, store_head_frame, store_head_IM, store_headCenter, store_headTop);
                    }
                });
            }
        });

//        备注
        TextView memo_indicator = findViewById(R.id.memo_indicator);
        memoET = findViewById(R.id.memo_text);
        if (!requiredMap.get("memo")) {
            memo_indicator.setText("");
        }

//        租户姓名
        TextView rentpeople_indicator = findViewById(R.id.rentpeople_indicator);
        rentPeopleET = findViewById(R.id.rentpeople_name);
        if (!requiredMap.get("rent_name")) {
            rentpeople_indicator.setText("");
        }

//        租户联系方式
        TextView rentpeoplephone_indicator = findViewById(R.id.rentpeoplephone_indicator);
        rentPhoneET = findViewById(R.id.rentpeoplephone);
        if (!requiredMap.get("rent_phone")) {
            rentpeoplephone_indicator.setText("");
        }

//        出租截止日期
        TextView rent_deadline_indicator = findViewById(R.id.rent_deadline_indicator);
        if (!requiredMap.get("rent_deadline_date")) {
            rent_deadline_indicator.setText("");
        }

        rent_deadLineTV = findViewById(R.id.rent_deadline_text);
        LinearLayout rent_deadlineLayout = findViewById(R.id.rent_deadline);
        rent_deadlineLayout.setOnClickListener(v -> {

            BottomSheetDialogHelper.showBottomSheetDatePicker(
                    this,
                    BottomSheetDialogHelper.DatePickerMode.YEAR_MONTH_DAY,
                    (year, month, day) -> {
                        String date = year + "-" + month + "-" + day;
                        rent_deadLineTV.setText(date);
                        rent_deadLineTV.setTextColor(getResources().getColor(R.color.color_map_template_cb_text_normal));
                    }
            );

        });


//        当前租户收款账户
        TextView bank_account_indicator = findViewById(R.id.bank_account_indicator);
        bankAccountET = findViewById(R.id.bank_account);
        bankAccountET.setFilters(new InputFilter[]{
                (source, start, end, dest, dstart, dend) -> {
                    // 只允许数字字符
                    if (source.toString().matches("[0-9]*")) {
                        return null; // 合法字符，允许输入
                    }
                    return ""; // 非法字符，拒绝输入
                }
        });
        if (!requiredMap.get("bank_account")) {
            bank_account_indicator.setText("");
        }

//        当前租户收款账户户名
        TextView bank_account_name_indicator = findViewById(R.id.bank_account_name_indicator);
        bankAccountNameET = findViewById(R.id.bank_account_name);
        if (!requiredMap.get("bank_account_name")) {
            bank_account_name_indicator.setText("");
        }


//  当前租户营业执照
        TextView license_url_indicator = findViewById(R.id.license_url_indicator);
        if (!requiredMap.get("license_url")) {
            license_url_indicator.setText("");
        }
        FrameLayout license_url_frame = findViewById(R.id.license_url_frame);
        ImageView license_url_IM = findViewById(R.id.license_url_IM);
        ImageView license_urlCenter = findViewById(R.id.license_url_center);
        ImageView license_urlTop = findViewById(R.id.license_url_top);
        license_url_frame.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击事件逻辑
                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");
                showActionChooseImageDialog("kms/hxl.kms.storeplanhouseinfo.file.upload", map, licenseDic, license_url_frame, license_url_IM, license_urlCenter, license_urlTop);
            }
        });

        license_urlTop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 点击事件逻辑
                license_url_IM.setImageDrawable(getResources().getDrawable(R.drawable.landlord_license));
                v.setVisibility(View.GONE);
                license_urlCenter.setVisibility(View.VISIBLE);
                licenseDic.clear();

                license_url_frame.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {

                        HashMap<String, Object> map = new HashMap<>();
                        map.put("id", point_id);
                        map.put("fileType", "LandlordLog");
                        showActionChooseImageDialog("kms/hxl.kms.storeplanhouseinfo.file.upload", map, licenseDic, license_url_frame, license_url_IM, license_urlCenter, license_urlTop);
                    }
                });
            }
        });


//租户身份证
        TextView tenant_numberCardIndicator = findViewById(R.id.tenant_number_indicator);
        if (!requiredMap.get("tenant_number_front_url")) {
            tenant_numberCardIndicator.setText("");
        }
        FrameLayout tenant_numberLayoutf = findViewById(R.id.tenant_number_cardff);
        ImageView tenant_numbercardf = findViewById(R.id.tenant_number_cardf);
        ImageView tenant_numbercardfCenter = findViewById(R.id.tenant_number_cardf_center);
        ImageView tenant_numbercardfTop = findViewById(R.id.tenant_number_cardf_top);
        tenant_numberLayoutf.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击事件逻辑
                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");
                showActionChooseImageDialog("kms/hxl.kms.storeplanhouseinfo.file.upload", map, rentCFDic, tenant_numberLayoutf, tenant_numbercardf, tenant_numbercardfCenter, tenant_numbercardfTop);
            }
        });

        tenant_numbercardfTop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 点击事件逻辑
                tenant_numbercardf.setImageDrawable(getResources().getDrawable(R.drawable.landlord_card_front));
                v.setVisibility(View.GONE);
                tenant_numbercardfCenter.setVisibility(View.VISIBLE);

                rentCFDic.clear();


                tenant_numberLayoutf.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        // 点击事件逻辑
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("id", point_id);
                        map.put("fileType", "LandlordLog");
                        showActionChooseImageDialog("kms/hxl.kms.storeplanhouseinfo.file.upload", map, rentCFDic, tenant_numberLayoutf, tenant_numbercardf, tenant_numbercardfCenter, tenant_numbercardfTop);
                    }
                });


            }
        });


        FrameLayout tenant_numberLayoutb = findViewById(R.id.tenant_number_cardbf);
        ImageView tenant_numbercardb = findViewById(R.id.tenant_number_cardb);
        ImageView tenant_numbercardbCenter = findViewById(R.id.tenant_number_cardb_center);
        ImageView tenant_numbercardbTop = findViewById(R.id.tenant_number_cardb_top);
        tenant_numberLayoutb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击事件逻辑
                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");
                showActionChooseImageDialog("kms/hxl.kms.storeplanhouseinfo.file.upload", map, rentCBDic, tenant_numberLayoutb, tenant_numbercardb, tenant_numbercardbCenter, tenant_numbercardbTop);
            }
        });

        tenant_numbercardbTop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 点击事件逻辑
                tenant_numbercardb.setImageDrawable(getResources().getDrawable(R.drawable.landlord_card_back));
                v.setVisibility(View.GONE);
                tenant_numbercardbCenter.setVisibility(View.VISIBLE);

                rentCBDic.clear();

                tenant_numberLayoutb.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        // 点击事件逻辑
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("id", point_id);
                        map.put("fileType", "LandlordLog");
                        showActionChooseImageDialog("kms/hxl.kms.storeplanhouseinfo.file.upload", map, rentCBDic, tenant_numberLayoutb, tenant_numbercardb, tenant_numbercardbCenter, tenant_numbercardbTop);
                    }
                });


            }
        });

//        执照法人和租户是否同一人
        TextView identicalIndicator = findViewById(R.id.identical_indicator);
        RadioGroup identicalRG = findViewById(R.id.identical_rg);
        LinearLayout legalpersonname_layout = findViewById(R.id.legalpersonname_layout);
        LinearLayout legalpersonphone_layout = findViewById(R.id.legalpersonphone_layout);
        LinearLayout id_number_layout = findViewById(R.id.id_number_layout);
        if (!requiredMap.get("identical")) {
            identicalIndicator.setText("");
        }

        identicalRG.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                // 获取被选中的RadioButton的实例
                RadioButton selectedRadioButton = findViewById(checkedId);
                // 获取被选中的RadioButton的文本（即值）
                String title = selectedRadioButton.getText().toString();
                if (title.equals("是")) {
                    isIdentical = true;
                    legalpersonname_layout.setVisibility(View.GONE);
                    legalpersonphone_layout.setVisibility(View.GONE);
                    id_number_layout.setVisibility(View.GONE);
                } else {
                    isIdentical = false;
                    legalpersonname_layout.setVisibility(View.VISIBLE);
                    legalpersonphone_layout.setVisibility(View.VISIBLE);
                    id_number_layout.setVisibility(View.VISIBLE);
                }

            }
        });

//        法人姓名
        TextView legalperson_indicator = findViewById(R.id.legalperson_indicator);
        legalpersonNameET = findViewById(R.id.legalperson_name);
        if (!requiredMap.get("legal_person")) {
            legalperson_indicator.setText("");
        }

//        法人电话
        TextView legalpersonphone_indicator = findViewById(R.id.legalpersonphone_indicator);
        legalpersonPhoneET = findViewById(R.id.legalpersonphone);
        if (!requiredMap.get("legal_person_phone")) {
            legalpersonphone_indicator.setText("");
        }
        legalpersonPhoneET.setFilters(new InputFilter[]{
                (source, start, end, dest, dstart, dend) -> {
                    // 只允许数字字符
                    if (source.toString().matches("[0-9]*")) {
                        return null; // 合法字符，允许输入
                    }
                    return ""; // 非法字符，拒绝输入
                }
        });

//        法人身份证
        TextView id_number_indicator = findViewById(R.id.id_number_indicator);
        if (!requiredMap.get("id_number_front_url")) {
            id_number_indicator.setText("");
        }
        FrameLayout id_numberLayoutf = findViewById(R.id.id_number_cardff);
        ImageView id_numbercardf = findViewById(R.id.id_number_cardf);
        ImageView id_numbercardfCenter = findViewById(R.id.id_number_cardf_center);
        ImageView id_numbercardfTop = findViewById(R.id.id_number_cardf_top);
        id_numberLayoutf.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击事件逻辑
                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");
                showActionChooseImageDialog("kms/hxl.kms.storeplanhouseinfo.file.upload", map, legalpersonCFDic, id_numberLayoutf, id_numbercardf, id_numbercardfCenter, id_numbercardfTop);
            }
        });

        id_numbercardfTop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 点击事件逻辑
                id_numbercardf.setImageDrawable(getResources().getDrawable(R.drawable.landlord_card_front));
                v.setVisibility(View.GONE);
                id_numbercardfCenter.setVisibility(View.VISIBLE);
                legalpersonCFDic.clear();

                id_numberLayoutf.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        // 点击事件逻辑
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("id", point_id);
                        map.put("fileType", "LandlordLog");
                        showActionChooseImageDialog("kms/hxl.kms.storeplanhouseinfo.file.upload", map, legalpersonCFDic, id_numberLayoutf, id_numbercardf, id_numbercardfCenter, id_numbercardfTop);
                    }
                });
            }
        });


        FrameLayout id_numberLayoutb = findViewById(R.id.id_number_cardbf);
        ImageView id_numbercardb = findViewById(R.id.id_number_cardb);
        ImageView id_numbercardbCenter = findViewById(R.id.id_number_cardb_center);
        ImageView id_numbercardbTop = findViewById(R.id.id_number_cardb_top);
        id_numberLayoutb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击事件逻辑

                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");

                showActionChooseImageDialog("kms/hxl.kms.storeplanhouseinfo.file.upload", map, legalpersonCBDic, id_numberLayoutb, id_numbercardb, id_numbercardbCenter, id_numbercardbTop);
            }
        });

        id_numbercardbTop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 点击事件逻辑
                id_numbercardb.setImageDrawable(getResources().getDrawable(R.drawable.landlord_card_back));
                v.setVisibility(View.GONE);
                id_numbercardbCenter.setVisibility(View.VISIBLE);
                legalpersonCBDic.clear();

                id_numberLayoutb.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        // 点击事件逻辑

                        HashMap<String, Object> map = new HashMap<>();
                        map.put("id", point_id);
                        map.put("fileType", "LandlordLog");

                        showActionChooseImageDialog("kms/hxl.kms.storeplanhouseinfo.file.upload", map, legalpersonCBDic, id_numberLayoutb, id_numbercardb, id_numbercardbCenter, id_numbercardbTop);
                    }
                });
            }
        });

//        当前租户与房东的租房合同
        TextView rental_contract_indicator = findViewById(R.id.rental_contract_indicator);
        if (!requiredMap.get("rental_contract")) {
            rental_contract_indicator.setText("");
        }
        TextView rental_contract_upload = findViewById(R.id.rental_contract_upload);

        LinearLayout rental_contract_content = findViewById(R.id.rental_contract_content);
        rental_contract_upload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");
                showActionChooseFileDialog("/kms/hxl.kms.storeplanhouseinfo.file.upload", map, rent_url_list, rental_contract_content);
            }
        });

//租房合同签约是否与收款账户户名同一人
        TextView identical_user_indicator = findViewById(R.id.identical_user_indicator);
        LinearLayout relationship_proof_layout = findViewById(R.id.relationship_proof_layout);
        LinearLayout relationship_proof_urls_layout = findViewById(R.id.relationship_proof_urls_layout);
        RadioGroup identical_userRG = findViewById(R.id.identical_user_rg);
        if (!requiredMap.get("identical_user")) {
            identical_user_indicator.setText("");
        }

        identical_userRG.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                // 获取被选中的RadioButton的实例
                RadioButton selectedRadioButton = findViewById(checkedId);
                // 获取被选中的RadioButton的文本（即值）
                String title = selectedRadioButton.getText().toString();
                if (title.equals("是")) {
                    isIdenticalUser = true;
                    relationship_proof_layout.setVisibility(View.GONE);
                    relationship_proof_urls_layout.setVisibility(View.GONE);
                } else {
                    isIdenticalUser = false;
                    relationship_proof_layout.setVisibility(View.VISIBLE);
                    relationship_proof_urls_layout.setVisibility(View.VISIBLE);
                }

            }
        });


//        收款户名与合同签署人关系证明
        TextView relationship_proofIndicator = findViewById(R.id.relationship_proof_indicator);
        if (!requiredMap.get("relationship_proof")) {
            relationship_proofIndicator.setText("");
        }

        TextView relationship_proofText = findViewById(R.id.relationship_proof_text);
        LinearLayout relationship_proofLayout = findViewById(R.id.relationship_prooff);
        relationship_proofLayout.setOnClickListener(v -> {
            List<Map<String, String>> items = new ArrayList<>();
            items.add(createItem("MARRIAGE_CERTIFICATE", "夫妻结婚证"));
            items.add(createItem("PARTNERSHIP_PROO", "合伙关系证明"));
            items.add(createItem("HOUSEHOLD_REGISTER", "户口本"));
            BottomSheetDialogHelper.showSingleSelectBottomSheetDialog(
                    this,
                    "请选择关系",
                    items,
                    null, // 可以为 null
                    (id, name) -> {
                        relationship_proofText.setText(name);
                        relationship_proofText.setTextColor(getResources().getColor(R.color.color_map_template_cb_text_normal));

                        relationshipProofDic.clear();
                        relationshipProofDic.put("id", id);
                        relationshipProofDic.put("name", name);
                    }
            );

        });


//        关系证明附件
        TextView relationship_proof_urls_indicator = findViewById(R.id.relationship_proof_urls_indicator);
        if (!requiredMap.get("relationship_proof_urls")) {
            relationship_proof_urls_indicator.setText("");
        }
        TextView relationship_proof_urls_upload = findViewById(R.id.relationship_proof_urls_upload);
//        内容区域
        LinearLayout relationship_proof_urls_content = findViewById(R.id.relationship_proof_urls_content);
        relationship_proof_urls_upload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");
                showActionChooseFileDialog("/kms/hxl.kms.storeplanhouseinfo.file.upload", map, relationship_list, relationship_proof_urls_content);

            }
        });

//        转让费是否包含其他费用
        TextView additional_indicator = findViewById(R.id.additional_indicator);
        LinearLayout additional_memo_layout = findViewById(R.id.additional_memo_layout);
        RadioGroup additionalRG = findViewById(R.id.additional_rg);
        if (!requiredMap.get("additional")) {
            additional_indicator.setText("");
        }

        additionalRG.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                // 获取被选中的RadioButton的实例
                RadioButton selectedRadioButton = findViewById(checkedId);
                // 获取被选中的RadioButton的文本（即值）
                String title = selectedRadioButton.getText().toString();
                if (title.equals("是")) {
                    isAdditional = true;
                    additional_memo_layout.setVisibility(View.VISIBLE);

                } else {
                    isAdditional = false;
                    additional_memo_layout.setVisibility(View.GONE);

                }

            }
        });

//        转让费备注
        TextView additional_memo_indicator = findViewById(R.id.additional_memo_indicator);
        additionalMemoET = findViewById(R.id.additional_memo_text);
        if (!requiredMap.get("additional_memo")) {
            additional_memo_indicator.setText("");
        }

//        转让协议
        TextView agreement_urls_indicator = findViewById(R.id.agreement_urls_indicator);
        if (!requiredMap.get("agreement_urls")) {
            agreement_urls_indicator.setText("");
        }

        TextView agreement_urls_upload = findViewById(R.id.agreement_urls_upload);
//        内容区域
        LinearLayout agreement_urls_content = findViewById(R.id.agreement_urls_content);
        agreement_urls_upload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("id", point_id);
                map.put("fileType", "LandlordLog");
                showActionChooseFileDialog("/kms/hxl.kms.storeplanhouseinfo.file.upload", map, transferAgreement_list, agreement_urls_content);

            }
        });
    }


    private void initEditView() {

        //            房东姓名
        String landlordName = (String) dataDic.get("name");
        if (!TextUtils.isEmpty(landlordName)) {
            nameET.setText(landlordName);
        }
//            房东电话
        String landlordPhone = (String) dataDic.get("phone");
        if (!TextUtils.isEmpty(landlordPhone)) {
            phoneET.setText(landlordPhone);
        }

//            房东证件照正面
        FrameLayout frameLayoutf = findViewById(R.id.landlord_cardff);
        ImageView cardf = findViewById(R.id.landlord_cardf);
        ImageView cardfCenter = findViewById(R.id.landlord_cardf_center);
        ImageView cardfTop = findViewById(R.id.landlord_cardf_top);
        String landlordCardfUrl = (String) dataDic.get("owner_id_number_front_url");
        if (!TextUtils.isEmpty(landlordCardfUrl)) {
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("url", landlordCardfUrl);
            landlordCFDic = hashMap;

            Glide.with(this) // 上下文
                    .load(landlordCardfUrl)
                    .into(cardf);
            cardfCenter.setVisibility(View.GONE);
            cardfTop.setVisibility(View.VISIBLE);

            // 设置点击事件
            frameLayoutf.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // 点击事件逻辑
                    ArrayList<String> images = new ArrayList<>();
                    images.add(landlordCardfUrl);
                    previewImage(images);
                }
            });

        }
//            房东证件照反面
        FrameLayout frameLayoutb = findViewById(R.id.landlord_cardbf);
        ImageView cardb = findViewById(R.id.landlord_cardb);
        ImageView cardbCenter = findViewById(R.id.landlord_cardb_center);
        ImageView cardbTop = findViewById(R.id.landlord_cardb_top);
        String landlordCardbUrl = (String) dataDic.get("owner_id_number_back_url");
        if (!TextUtils.isEmpty(landlordCardbUrl)) {

            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("url", landlordCardbUrl);
            landlordCBDic = hashMap;

            Glide.with(this) // 上下文
                    .load(landlordCardbUrl)
                    .into(cardb);
            cardbCenter.setVisibility(View.GONE);
            cardbTop.setVisibility(View.VISIBLE);

            // 设置点击事件
            frameLayoutb.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // 点击事件逻辑
                    ArrayList<String> images = new ArrayList<>();
                    images.add(landlordCardbUrl);
                    previewImage(images);
                }
            });

        }


//            房东性质
        TextView landlordTypeText = findViewById(R.id.landlordtype_text);
        String landlordType = (String) dataDic.get("landlord_type");
        if (!TextUtils.isEmpty(landlordType)) {
            landlordTypeText.setTextColor(getResources().getColor(R.color.color_map_template_cb_text_normal));
            if (landlordType.equals("NATURAL_PERSON")) {
                landlordTypeText.setText("自然人");
            } else if (landlordType.equals("STATE_OWNED_ENTERPRISE")) {
                landlordTypeText.setText("国企");
            } else if (landlordType.equals("PRIVATE_ENTERPRISE")) {
                landlordTypeText.setText("民企");
            } else if (landlordType.equals("GOVERNMENT_AGENCY")) {
                landlordTypeText.setText("政府机构");
            } else if (landlordType.equals("PUBLIC_INSTITUTION")) {
                landlordTypeText.setText("事业单位");
            } else {
                landlordTypeText.setText("其他机构");
            }

            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("name", landlordTypeText.getText().toString().trim());
            hashMap.put("id", landlordType);
            landlordNatureDic = hashMap;
        }

//        房东身份
        TextView landlordidentity_text = findViewById(R.id.landlordidentity_text);
        String landlord_identity = (String) dataDic.get("landlord_identity");
        if (!TextUtils.isEmpty(landlord_identity)) {
            landlordidentity_text.setTextColor(getResources().getColor(R.color.color_map_template_cb_text_normal));

            if (landlord_identity.equals("PROPERTY_OWNER")) {
                landlordidentity_text.setText("产权人");
            } else  if (landlord_identity.equals("SECONDARY_TENANT")) {

                landlordidentity_text.setText("二房东");
            } else  if (landlord_identity.equals("THIRD_PARTY_TENANT")) {

                landlordidentity_text.setText("三房东");
            } else  {

                landlordidentity_text.setText("其他");
            }

            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("name", landlordidentity_text.getText().toString().trim());
            hashMap.put("id", landlord_identity);
            landlordIdentityDic= hashMap;

        }

//            排他协议
        RadioGroup exclusiveRG = findViewById(R.id.landlordexclusive_rg);
        Boolean is_exclusive = (Boolean) dataDic.get("is_exclusive_agreement");
        if (Boolean.TRUE.equals(is_exclusive)) {
            isExclusive = true;
            exclusiveRG.check(R.id.landlordexclusive_true);
        } else if (Boolean.FALSE.equals(is_exclusive)) {
            isExclusive = false;
            exclusiveRG.check(R.id.landlordexclusive_false);
        }

//           是否分租
        RadioGroup rentDividedRG = findViewById(R.id.rent_divided_rg);
        Boolean is_rent_divided = (Boolean) dataDic.get("is_rent_divided");
        if (Boolean.TRUE.equals(is_rent_divided)) {
            isRentDivide = true;
            rentDividedRG.check(R.id.rent_divided_true);
        } else if (Boolean.FALSE.equals(is_rent_divided)) {
            isRentDivide = false;
            rentDividedRG.check(R.id.rent_divided_false);
        }

//        租金
        TextView rent_UNMoney = findViewById(R.id.rent_UNMoney);

        if (dataDic.get("rent_money") != null) {
            String input = dataDic.get("rent_money").toString();
            if (!input.isEmpty()) {
                rentET.setText(input);
                try {
                    BigDecimal number = new BigDecimal(input);
                    String chineseCapital = convertToChineseCapital(number);
                    SpannableStringBuilder coloredText = applyColorToChineseCapital(chineseCapital);
                    rent_UNMoney.setText(coloredText);
                } catch (NumberFormatException e) {
                    rent_UNMoney.setText("");
                }
            } else {
                rentET.setText("");
                rent_UNMoney.setText("");
            }
        }

//        转让费
        TextView transfer_fee_UNMoney = findViewById(R.id.transfer_fee_UNMoney);
        if (dataDic.get("transfer_fee") != null) {
            String inputTransfer_fee = dataDic.get("transfer_fee").toString();
            if (!inputTransfer_fee.isEmpty()) {
                transfer_feeET.setText(inputTransfer_fee);
                try {
                    BigDecimal number = new BigDecimal(inputTransfer_fee);
                    String chineseCapital = convertToChineseCapital(number);
                    SpannableStringBuilder coloredText = applyColorToChineseCapital(chineseCapital);
                    transfer_fee_UNMoney.setText(coloredText);
                } catch (NumberFormatException e) {
                    transfer_fee_UNMoney.setText("");
                }
            } else {
                transfer_feeET.setText("");
                rent_UNMoney.setText("");
            }
        }

        //        租房合同
        LinearLayout lease_contract_content = findViewById(R.id.lease_contract_content);
        ArrayList<HashMap<String, Object>> arrayList = (ArrayList<HashMap<String, Object>>) dataDic.get("lease_contract_files");

        if (arrayList != null && !arrayList.isEmpty()) {
            lease_contract_list = arrayList;

            for (int i = 0; i < lease_contract_list.size(); i++) {
                HashMap<String, Object> map = lease_contract_list.get(i);

                String fileName = (String) map.get("name");
                String fileType = (String) map.get("suffix_type");
                String fileUrl = (String) map.get("url");

//                                    添加布局
                LinearLayout horizontalLayout = new LinearLayout(MapAddLandlordActivity.this);
                horizontalLayout.setOrientation(LinearLayout.HORIZONTAL);

                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        dpToPx(44)
                );
                layoutParams.setMargins(0, 0, 0, dpToPx(8));
                horizontalLayout.setLayoutParams(layoutParams);
                horizontalLayout.setPadding(dpToPx(16), 0, dpToPx(16), 0);
                horizontalLayout.setBackground(getResources().getDrawable(R.drawable.upload_file_border));
                lease_contract_content.addView(horizontalLayout);

                horizontalLayout.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        // 点击事件逻辑
                        if (fileType.contains("png") || fileType.contains("PNG") || fileType.contains("JPG") || fileType.contains("jpg") || fileType.contains("jpeg") || fileType.contains("JPEG")) {//图片
                            ArrayList<String> images = new ArrayList<>();
                            images.add(fileUrl);
                            previewImage(images);

                        } else {
                            if (!TextUtils.isEmpty(fileUrl)) {
                                Uri uri = Uri.parse(fileUrl);
                                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                try {
                                    context.startActivity(intent);
                                } catch (ActivityNotFoundException e) {
                                    Toast.makeText(context, "没有可用的浏览器应用", Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                Toast.makeText(context, "无效的链接", Toast.LENGTH_SHORT).show();
                            }

                        }
                    }
                });

                // 创建图标 (ImageView)
                ImageView icon = new ImageView(MapAddLandlordActivity.this);
                LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(24), dpToPx(24));
                iconParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                icon.setLayoutParams(iconParams);

                if (fileType.contains("png") || fileType.contains("PNG") || fileType.contains("JPG") || fileType.contains("jpg") || fileType.contains("jpeg") || fileType.contains("JPEG")) {//图片
                    icon.setImageResource(R.drawable.upload_filetype_image);
                } else if (fileType.contains("pdf")) {//pdf
                    icon.setImageResource(R.drawable.upload_filetype_pdf);
                } else if (fileType.contains("doc") || fileType.contains("docx")) {//word
                    icon.setImageResource(R.drawable.upload_filetype_word);
                } else if (fileType.contains("xls") || fileType.contains("xlsx")) {//excel
                    icon.setImageResource(R.drawable.upload_filetype_excel);
                } else if (fileType.contains("ppt") || fileType.contains("pptx")) {//ppt
                    icon.setImageResource(R.drawable.upload_filetype_ppt);
                } else {
                    icon.setImageResource(R.drawable.upload_filetype_wenhao);
                }
                horizontalLayout.addView(icon);

//                            文件名
                TextView textView = new TextView(MapAddLandlordActivity.this);
                textView.setText(fileName);
                textView.setTextSize(14);
                textView.setSingleLine();
                textView.setEllipsize(TextUtils.TruncateAt.MIDDLE); // 中间省略
                textView.setGravity(Gravity.CENTER_VERTICAL);
                textView.setTextColor(Color.parseColor("#1F2126")); // 设置为橙色
                LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                        0,
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        1.0f // 权重为1，占据剩余空间
                );
                textParams.gravity = Gravity.CENTER_VERTICAL;
                textParams.leftMargin = dpToPx(8); // 设置左边距为 8dp
                textParams.rightMargin = dpToPx(8);
                textView.setLayoutParams(textParams);
                horizontalLayout.addView(textView);

//                            右边删除图标
                ImageView deleteIM = new ImageView(context);
                deleteIM.setImageResource(R.drawable.upload_filetype_delete);
                int size = dpToPx(16);
                LinearLayout.LayoutParams deleteParams = new LinearLayout.LayoutParams(size, size);
                deleteParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                deleteIM.setLayoutParams(deleteParams);
                horizontalLayout.addView(deleteIM);
                int finalI = i;
                deleteIM.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        lease_contract_list.remove(finalI);
                        lease_contract_content.removeView(horizontalLayout);//移除当前view
                    }
                });


            }


        }


        //        产权证
        LinearLayout title_certificate_content = findViewById(R.id.title_certificate_content);
        ArrayList<HashMap<String, Object>> certificatearrayList = (ArrayList<HashMap<String, Object>>) dataDic.get("title_certificate_files");

        if (certificatearrayList != null && !certificatearrayList.isEmpty()) {
            title_certificate_list = certificatearrayList;

            for (int i = 0; i < title_certificate_list.size(); i++) {

                HashMap<String, Object> map = title_certificate_list.get(i);
                String fileName = (String) map.get("name");
                String fileType = (String) map.get("suffix_type");
                String fileUrl = (String) map.get("url");

//                                    添加布局
                LinearLayout horizontalLayout = new LinearLayout(MapAddLandlordActivity.this);
                horizontalLayout.setOrientation(LinearLayout.HORIZONTAL);

                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        dpToPx(44)
                );
                layoutParams.setMargins(0, 0, 0, dpToPx(8));
                horizontalLayout.setLayoutParams(layoutParams);
                horizontalLayout.setPadding(dpToPx(16), 0, dpToPx(16), 0);
                horizontalLayout.setBackground(getResources().getDrawable(R.drawable.upload_file_border));
                title_certificate_content.addView(horizontalLayout);

                horizontalLayout.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        // 点击事件逻辑
                        if (fileType.contains("png") || fileType.contains("PNG") || fileType.contains("JPG") || fileType.contains("jpg") || fileType.contains("jpeg") || fileType.contains("JPEG")) {//图片
                            ArrayList<String> images = new ArrayList<>();
                            images.add(fileUrl);
                            previewImage(images);

                        } else {
                            if (!TextUtils.isEmpty(fileUrl)) {
                                Uri uri = Uri.parse(fileUrl);
                                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                try {
                                    context.startActivity(intent);
                                } catch (ActivityNotFoundException e) {
                                    Toast.makeText(context, "没有可用的浏览器应用", Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                Toast.makeText(context, "无效的链接", Toast.LENGTH_SHORT).show();
                            }

                        }
                    }
                });

                // 创建图标 (ImageView)
                ImageView icon = new ImageView(MapAddLandlordActivity.this);
                LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(24), dpToPx(24));
                iconParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                icon.setLayoutParams(iconParams);

                if (fileType.contains("png") || fileType.contains("PNG") || fileType.contains("JPG") || fileType.contains("jpg") || fileType.contains("jpeg") || fileType.contains("JPEG")) {//图片
                    icon.setImageResource(R.drawable.upload_filetype_image);
                } else if (fileType.contains("pdf")) {//pdf
                    icon.setImageResource(R.drawable.upload_filetype_pdf);
                } else if (fileType.contains("doc") || fileType.contains("docx")) {//word
                    icon.setImageResource(R.drawable.upload_filetype_word);
                } else if (fileType.contains("xls") || fileType.contains("xlsx")) {//excel
                    icon.setImageResource(R.drawable.upload_filetype_excel);
                } else if (fileType.contains("ppt") || fileType.contains("pptx")) {//ppt
                    icon.setImageResource(R.drawable.upload_filetype_ppt);
                } else {
                    icon.setImageResource(R.drawable.upload_filetype_wenhao);
                }
                horizontalLayout.addView(icon);

//                            文件名
                TextView textView = new TextView(MapAddLandlordActivity.this);
                textView.setText(fileName);
                textView.setTextSize(14);
                textView.setSingleLine();
                textView.setEllipsize(TextUtils.TruncateAt.MIDDLE); // 中间省略
                textView.setGravity(Gravity.CENTER_VERTICAL);
                textView.setTextColor(Color.parseColor("#1F2126")); // 设置为橙色
                LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                        0,
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        1.0f // 权重为1，占据剩余空间
                );
                textParams.gravity = Gravity.CENTER_VERTICAL;
                textParams.leftMargin = dpToPx(8); // 设置左边距为 8dp
                textParams.rightMargin = dpToPx(8);
                textView.setLayoutParams(textParams);
                horizontalLayout.addView(textView);

//                            右边删除图标
                ImageView deleteIM = new ImageView(context);
                deleteIM.setImageResource(R.drawable.upload_filetype_delete);
                int size = dpToPx(16);
                LinearLayout.LayoutParams deleteParams = new LinearLayout.LayoutParams(size, size);
                deleteParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                deleteIM.setLayoutParams(deleteParams);
                horizontalLayout.addView(deleteIM);
                int finalI = i;
                deleteIM.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        title_certificate_list.remove(finalI);
                        title_certificate_content.removeView(horizontalLayout);//移除当前view
                    }
                });


            }


        }


//        门头照
        FrameLayout store_head_frame = findViewById(R.id.store_head_frame);
        ImageView store_head_IM = findViewById(R.id.store_head_IM);
        ImageView store_headCenter = findViewById(R.id.store_head_center);
        ImageView store_headTop = findViewById(R.id.store_head_top);
        List store_headUrls = (List) dataDic.get("urls");
        if (store_headUrls != null && store_headUrls.size() > 0) {

            String store_headUrl = (String) store_headUrls.get(0);
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("url", store_headUrl);
            store_headDic = hashMap;


            Glide.with(this) // 上下文
                    .load(store_headUrl)
                    .into(store_head_IM);
            store_headCenter.setVisibility(View.GONE);
            store_headTop.setVisibility(View.VISIBLE);

            // 设置点击事件
            store_head_frame.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // 点击事件逻辑
                    ArrayList<String> images = new ArrayList<>();
                    images.add(store_headUrl);
                    previewImage(images);
                }
            });

        }

//        备注
        String memo = (String) dataDic.get("memo");
        if (!TextUtils.isEmpty(memo)) {
            memoET.setText(memo);
        }

//        当前租户姓名
        String rentName = (String) dataDic.get("rent_name");
        if (!TextUtils.isEmpty(rentName)) {
            rentPeopleET.setText(rentName);
        }

//        当前租户联系方式
        String rentPhone = (String) dataDic.get("rent_phone");
        if (!TextUtils.isEmpty(rentPhone)) {
            rentPhoneET.setText(rentPhone);
        }

//        出租截止日期
        String rent_deadline_date = (String) dataDic.get("rent_deadline_date");
        if (!TextUtils.isEmpty(rent_deadline_date)) {
            rent_deadLineTV.setText(rent_deadline_date);
            rent_deadLineTV.setTextColor(getResources().getColor(R.color.color_map_template_cb_text_normal));
        }

//        当前收款账户
        String bank_account = (String) dataDic.get("bank_account");
        if (!TextUtils.isEmpty(bank_account)) {
            bankAccountET.setText(bank_account);
        }

//        收款账户户名
        String bank_account_name = (String) dataDic.get("bank_account_name");
        if (!TextUtils.isEmpty(bank_account_name)) {
            bankAccountNameET.setText(bank_account_name);
        }

//        当前租户营业执照
        FrameLayout license_url_frame = findViewById(R.id.license_url_frame);
        ImageView license_url_IM = findViewById(R.id.license_url_IM);
        ImageView license_urlCenter = findViewById(R.id.license_url_center);
        ImageView license_urlTop = findViewById(R.id.license_url_top);
        String license_url = (String) dataDic.get("license_url");
        if (!TextUtils.isEmpty(license_url)) {

            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("url", license_url);
            licenseDic = hashMap;


            Glide.with(this) // 上下文
                    .load(license_url)
                    .into(license_url_IM);
            license_urlCenter.setVisibility(View.GONE);
            license_urlTop.setVisibility(View.VISIBLE);

            // 设置点击事件
            license_url_frame.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // 点击事件逻辑
                    ArrayList<String> images = new ArrayList<>();
                    images.add(license_url);
                    previewImage(images);
                }
            });

        }

//        租户身份证正面
        FrameLayout tenant_numberLayoutf = findViewById(R.id.tenant_number_cardff);
        ImageView tenant_numbercardf = findViewById(R.id.tenant_number_cardf);
        ImageView tenant_numbercardfCenter = findViewById(R.id.tenant_number_cardf_center);
        ImageView tenant_numbercardfTop = findViewById(R.id.tenant_number_cardf_top);
        String tenant_numberCardfUrl = (String) dataDic.get("tenant_number_front_url");
        if (!TextUtils.isEmpty(tenant_numberCardfUrl)) {

            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("url", tenant_numberCardfUrl);
            rentCFDic = hashMap;

            Glide.with(this) // 上下文
                    .load(tenant_numberCardfUrl)
                    .into(tenant_numbercardf);
            tenant_numbercardfCenter.setVisibility(View.GONE);
            tenant_numbercardfTop.setVisibility(View.VISIBLE);

            // 设置点击事件
            tenant_numberLayoutf.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // 点击事件逻辑
                    ArrayList<String> images = new ArrayList<>();
                    images.add(tenant_numberCardfUrl);
                    previewImage(images);
                }
            });

        }
//            租户身份证反面
        FrameLayout tenant_numberLayoutb = findViewById(R.id.tenant_number_cardbf);
        ImageView tenant_numbercardb = findViewById(R.id.tenant_number_cardb);
        ImageView tenant_numbercardbCenter = findViewById(R.id.tenant_number_cardb_center);
        ImageView tenant_numbercardbTop = findViewById(R.id.tenant_number_cardb_top);
        String tenant_numberCardbUrl = (String) dataDic.get("owner_id_number_back_url");
        if (!TextUtils.isEmpty(tenant_numberCardbUrl)) {

            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("url", tenant_numberCardbUrl);
            rentCBDic = hashMap;

            Glide.with(this) // 上下文
                    .load(tenant_numberCardfUrl)
                    .into(tenant_numbercardb);
            tenant_numbercardbCenter.setVisibility(View.GONE);
            tenant_numbercardbTop.setVisibility(View.VISIBLE);

            // 设置点击事件
            tenant_numberLayoutb.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // 点击事件逻辑
                    ArrayList<String> images = new ArrayList<>();
                    images.add(tenant_numberCardbUrl);
                    previewImage(images);
                }
            });

        }

//        执照法人与收款账户户名同一人
        RadioGroup identicalRG = findViewById(R.id.identical_rg);
        LinearLayout legalpersonname_layout = findViewById(R.id.legalpersonname_layout);
        LinearLayout legalpersonphone_layout = findViewById(R.id.legalpersonphone_layout);
        LinearLayout id_number_layout = findViewById(R.id.id_number_layout);

        Boolean is_identical = (Boolean) dataDic.get("identical");
        if (Boolean.TRUE.equals(is_identical)) {
            isIdentical = true;
            identicalRG.check(R.id.identical_true);
            legalpersonname_layout.setVisibility(View.GONE);
            legalpersonphone_layout.setVisibility(View.GONE);
            id_number_layout.setVisibility(View.GONE);

        } else if (Boolean.FALSE.equals(is_identical)) {

            identicalRG.check(R.id.identical_false);
            isIdentical = false;
            legalpersonname_layout.setVisibility(View.VISIBLE);
            legalpersonphone_layout.setVisibility(View.VISIBLE);
            id_number_layout.setVisibility(View.VISIBLE);
        }

//        法人姓名
        String legal_person = (String) dataDic.get("legal_person");
        if (!TextUtils.isEmpty(legal_person)) {
            legalpersonNameET.setText(legal_person);
        }

//        法人联系方式
        String legal_person_phone = (String) dataDic.get("legal_person_phone");
        if (!TextUtils.isEmpty(legal_person_phone)) {
            legalpersonPhoneET.setText(legal_person_phone);
        }

//        法人身份证
        FrameLayout id_numberLayoutf = findViewById(R.id.id_number_cardff);
        ImageView id_numbercardf = findViewById(R.id.id_number_cardf);
        ImageView id_numbercardfCenter = findViewById(R.id.id_number_cardf_center);
        ImageView id_numbercardfTop = findViewById(R.id.id_number_cardf_top);
        String id_numberCardfUrl = (String) dataDic.get("id_number_front_url");
        if (!TextUtils.isEmpty(id_numberCardfUrl)) {

            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("url", id_numberCardfUrl);
            legalpersonCFDic = hashMap;

            Glide.with(this) // 上下文
                    .load(id_numberCardfUrl)
                    .into(id_numbercardf);
            id_numbercardfCenter.setVisibility(View.GONE);
            id_numbercardfTop.setVisibility(View.VISIBLE);

            // 设置点击事件
            id_numberLayoutf.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // 点击事件逻辑
                    ArrayList<String> images = new ArrayList<>();
                    images.add(id_numberCardfUrl);
                    previewImage(images);
                }
            });

        }
//         法人身份证反面
        FrameLayout id_numberLayoutb = findViewById(R.id.id_number_cardbf);
        ImageView id_numbercardb = findViewById(R.id.id_number_cardb);
        ImageView id_numbercardbCenter = findViewById(R.id.id_number_cardb_center);
        ImageView id_numbercardbTop = findViewById(R.id.id_number_cardb_top);
        String id_numberCardbUrl = (String) dataDic.get("id_number_back_url");
        if (!TextUtils.isEmpty(id_numberCardbUrl)) {


            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("url", id_numberCardbUrl);
            legalpersonCBDic = hashMap;

            Glide.with(this) // 上下文
                    .load(id_numberCardbUrl)
                    .into(id_numbercardb);
            id_numbercardbCenter.setVisibility(View.GONE);
            id_numbercardbTop.setVisibility(View.VISIBLE);

            // 设置点击事件
            id_numberLayoutb.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // 点击事件逻辑
                    ArrayList<String> images = new ArrayList<>();
                    images.add(id_numberCardbUrl);
                    previewImage(images);
                }
            });

        }

//        当前租户与房东的租房合同

        LinearLayout rental_contract_content = findViewById(R.id.rental_contract_content);
        ArrayList<HashMap<String, Object>> rental_contractList = (ArrayList<HashMap<String, Object>>) dataDic.get("rental_contract_files");

        if (rental_contractList != null && !rental_contractList.isEmpty()) {
            rent_url_list = rental_contractList;

            for (int i = 0; i < rent_url_list.size(); i++) {

                HashMap<String, Object> map = rent_url_list.get(i);
                String fileName = (String) map.get("name");
                String fileType = (String) map.get("suffix_type");
                String fileUrl = (String) map.get("url");

//                                    添加布局
                LinearLayout horizontalLayout = new LinearLayout(MapAddLandlordActivity.this);
                horizontalLayout.setOrientation(LinearLayout.HORIZONTAL);

                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        dpToPx(44)
                );
                layoutParams.setMargins(0, 0, 0, dpToPx(8));
                horizontalLayout.setLayoutParams(layoutParams);
                horizontalLayout.setPadding(dpToPx(16), 0, dpToPx(16), 0);
                horizontalLayout.setBackground(getResources().getDrawable(R.drawable.upload_file_border));
                rental_contract_content.addView(horizontalLayout);

                horizontalLayout.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        // 点击事件逻辑
                        if (fileType.contains("png") || fileType.contains("PNG") || fileType.contains("JPG") || fileType.contains("jpg") || fileType.contains("jpeg") || fileType.contains("JPEG")) {//图片
                            ArrayList<String> images = new ArrayList<>();
                            images.add(fileUrl);
                            previewImage(images);

                        } else {
                            if (!TextUtils.isEmpty(fileUrl)) {
                                Uri uri = Uri.parse(fileUrl);
                                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                try {
                                    context.startActivity(intent);
                                } catch (ActivityNotFoundException e) {
                                    Toast.makeText(context, "没有可用的浏览器应用", Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                Toast.makeText(context, "无效的链接", Toast.LENGTH_SHORT).show();
                            }

                        }
                    }
                });

                // 创建图标 (ImageView)
                ImageView icon = new ImageView(MapAddLandlordActivity.this);
                LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(24), dpToPx(24));
                iconParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                icon.setLayoutParams(iconParams);

                if (fileType.contains("png") || fileType.contains("PNG") || fileType.contains("JPG") || fileType.contains("jpg") || fileType.contains("jpeg") || fileType.contains("JPEG")) {//图片
                    icon.setImageResource(R.drawable.upload_filetype_image);
                } else if (fileType.contains("pdf")) {//pdf
                    icon.setImageResource(R.drawable.upload_filetype_pdf);
                } else if (fileType.contains("doc") || fileType.contains("docx")) {//word
                    icon.setImageResource(R.drawable.upload_filetype_word);
                } else if (fileType.contains("xls") || fileType.contains("xlsx")) {//excel
                    icon.setImageResource(R.drawable.upload_filetype_excel);
                } else if (fileType.contains("ppt") || fileType.contains("pptx")) {//ppt
                    icon.setImageResource(R.drawable.upload_filetype_ppt);
                } else {
                    icon.setImageResource(R.drawable.upload_filetype_wenhao);
                }
                horizontalLayout.addView(icon);

//                            文件名
                TextView textView = new TextView(MapAddLandlordActivity.this);
                textView.setText(fileName);
                textView.setTextSize(14);
                textView.setSingleLine();
                textView.setEllipsize(TextUtils.TruncateAt.MIDDLE); // 中间省略
                textView.setGravity(Gravity.CENTER_VERTICAL);
                textView.setTextColor(Color.parseColor("#1F2126")); // 设置为橙色
                LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                        0,
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        1.0f // 权重为1，占据剩余空间
                );
                textParams.gravity = Gravity.CENTER_VERTICAL;
                textParams.leftMargin = dpToPx(8); // 设置左边距为 8dp
                textParams.rightMargin = dpToPx(8);
                textView.setLayoutParams(textParams);
                horizontalLayout.addView(textView);

//                            右边删除图标
                ImageView deleteIM = new ImageView(context);
                deleteIM.setImageResource(R.drawable.upload_filetype_delete);
                int size = dpToPx(16);
                LinearLayout.LayoutParams deleteParams = new LinearLayout.LayoutParams(size, size);
                deleteParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                deleteIM.setLayoutParams(deleteParams);
                horizontalLayout.addView(deleteIM);
                int finalI = i;
                deleteIM.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        rent_url_list.remove(finalI);
                        rental_contract_content.removeView(horizontalLayout);//移除当前view
                    }
                });


            }


        }

//   租房合同签约是否与收款账户户名同一人
        RadioGroup identical_userRG = findViewById(R.id.identical_user_rg);
        LinearLayout relationship_proof_layout = findViewById(R.id.relationship_proof_layout);
        LinearLayout relationship_proof_urls_layout = findViewById(R.id.relationship_proof_urls_layout);
        Boolean is_identical_user = (Boolean) dataDic.get("identical_user");
        if (Boolean.TRUE.equals(is_identical_user)) {
            isIdenticalUser = true;
            identical_userRG.check(R.id.identical_user_true);
            relationship_proof_layout.setVisibility(View.GONE);
            relationship_proof_urls_layout.setVisibility(View.GONE);
        } else if (Boolean.FALSE.equals(is_identical_user)) {
            identical_userRG.check(R.id.identical_user_false);
            isIdenticalUser = false;
            relationship_proof_layout.setVisibility(View.VISIBLE);
            relationship_proof_urls_layout.setVisibility(View.VISIBLE);
        }

//     收款户名与合同签署人关系证明
        TextView relationship_proof_text = findViewById(R.id.relationship_proof_text);
        String relationship_proof = (String) dataDic.get("relationship_proof");
        if (!TextUtils.isEmpty(relationship_proof)) {

            relationship_proof_text.setTextColor(getResources().getColor(R.color.color_map_template_cb_text_normal));
            if (landlordType.equals("HOUSEHOLD_REGISTER")) {
                relationship_proof_text.setText("户口本");
            } else if (landlordType.equals("MARRIAGE_CERTIFICATE")) {
                relationship_proof_text.setText("夫妻结婚证");
            } else {
                relationship_proof_text.setText("合伙关系证明");
            }

            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("name", landlordTypeText.getText().toString().trim());
            hashMap.put("id", landlordType);
            relationshipProofDic = hashMap;

        }

//        关系证明附件
        LinearLayout relationship_proof_urls_content = findViewById(R.id.relationship_proof_urls_content);
        ArrayList<HashMap<String, Object>> relationship_proof_files = (ArrayList<HashMap<String, Object>>) dataDic.get("relationship_proof_files");

        if (relationship_proof_files != null && !relationship_proof_files.isEmpty()) {
            relationship_list = relationship_proof_files;

            for (int i = 0; i < relationship_list.size(); i++) {

                HashMap<String, Object> map = relationship_list.get(i);
                String fileName = (String) map.get("name");
                String fileType = (String) map.get("suffix_type");
                String fileUrl = (String) map.get("url");

//                                    添加布局
                LinearLayout horizontalLayout = new LinearLayout(MapAddLandlordActivity.this);
                horizontalLayout.setOrientation(LinearLayout.HORIZONTAL);

                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        dpToPx(44)
                );
                layoutParams.setMargins(0, 0, 0, dpToPx(8));
                horizontalLayout.setLayoutParams(layoutParams);
                horizontalLayout.setPadding(dpToPx(16), 0, dpToPx(16), 0);
                horizontalLayout.setBackground(getResources().getDrawable(R.drawable.upload_file_border));
                relationship_proof_urls_content.addView(horizontalLayout);

                horizontalLayout.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        // 点击事件逻辑
                        if (fileType.contains("png") || fileType.contains("PNG") || fileType.contains("JPG") || fileType.contains("jpg") || fileType.contains("jpeg") || fileType.contains("JPEG")) {//图片
                            ArrayList<String> images = new ArrayList<>();
                            images.add(fileUrl);
                            previewImage(images);

                        } else {
                            if (!TextUtils.isEmpty(fileUrl)) {
                                Uri uri = Uri.parse(fileUrl);
                                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                try {
                                    context.startActivity(intent);
                                } catch (ActivityNotFoundException e) {
                                    Toast.makeText(context, "没有可用的浏览器应用", Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                Toast.makeText(context, "无效的链接", Toast.LENGTH_SHORT).show();
                            }

                        }
                    }
                });

                // 创建图标 (ImageView)
                ImageView icon = new ImageView(MapAddLandlordActivity.this);
                LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(24), dpToPx(24));
                iconParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                icon.setLayoutParams(iconParams);

                if (fileType.contains("png") || fileType.contains("PNG") || fileType.contains("JPG") || fileType.contains("jpg") || fileType.contains("jpeg") || fileType.contains("JPEG")) {//图片
                    icon.setImageResource(R.drawable.upload_filetype_image);
                } else if (fileType.contains("pdf")) {//pdf
                    icon.setImageResource(R.drawable.upload_filetype_pdf);
                } else if (fileType.contains("doc") || fileType.contains("docx")) {//word
                    icon.setImageResource(R.drawable.upload_filetype_word);
                } else if (fileType.contains("xls") || fileType.contains("xlsx")) {//excel
                    icon.setImageResource(R.drawable.upload_filetype_excel);
                } else if (fileType.contains("ppt") || fileType.contains("pptx")) {//ppt
                    icon.setImageResource(R.drawable.upload_filetype_ppt);
                } else {
                    icon.setImageResource(R.drawable.upload_filetype_wenhao);
                }
                horizontalLayout.addView(icon);

//                            文件名
                TextView textView = new TextView(MapAddLandlordActivity.this);
                textView.setText(fileName);
                textView.setTextSize(14);
                textView.setSingleLine();
                textView.setEllipsize(TextUtils.TruncateAt.MIDDLE); // 中间省略
                textView.setGravity(Gravity.CENTER_VERTICAL);
                textView.setTextColor(Color.parseColor("#1F2126")); // 设置为橙色
                LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                        0,
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        1.0f // 权重为1，占据剩余空间
                );
                textParams.gravity = Gravity.CENTER_VERTICAL;
                textParams.leftMargin = dpToPx(8); // 设置左边距为 8dp
                textParams.rightMargin = dpToPx(8);
                textView.setLayoutParams(textParams);
                horizontalLayout.addView(textView);

//                            右边删除图标
                ImageView deleteIM = new ImageView(context);
                deleteIM.setImageResource(R.drawable.upload_filetype_delete);
                int size = dpToPx(16);
                LinearLayout.LayoutParams deleteParams = new LinearLayout.LayoutParams(size, size);
                deleteParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                deleteIM.setLayoutParams(deleteParams);
                horizontalLayout.addView(deleteIM);
                int finalI = i;
                deleteIM.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        relationship_list.remove(finalI);
                        relationship_proof_urls_content.removeView(horizontalLayout);//移除当前view
                    }
                });


            }


        }

//        转让费是否包含其他费用
        RadioGroup additionalRG = findViewById(R.id.additional_rg);
        LinearLayout additional_memo_layout = findViewById(R.id.additional_memo_layout);
        Boolean is_additional = (Boolean) dataDic.get("additional");
        if (Boolean.TRUE.equals(is_additional)) {
            isAdditional = true;
            additionalRG.check(R.id.additional_true);
            additional_memo_layout.setVisibility(View.VISIBLE);
        } else if (Boolean.FALSE.equals(is_additional)) {
            additionalRG.check(R.id.additional_false);
            isAdditional = false;
            additional_memo_layout.setVisibility(View.VISIBLE);

        }

//        其他费用备注
        String additional_memo = (String) dataDic.get("additional_memo");
        if (!TextUtils.isEmpty(additional_memo)) {
            additionalMemoET.setText(additional_memo);
        }


//        转让协议
        LinearLayout agreement_urls_content = findViewById(R.id.agreement_urls_content);
        ArrayList<HashMap<String, Object>> agreement_files = (ArrayList<HashMap<String, Object>>) dataDic.get("agreement_files");

        if (agreement_files != null && !agreement_files.isEmpty()) {
            transferAgreement_list = agreement_files;

            for (int i = 0; i < transferAgreement_list.size(); i++) {

                HashMap<String, Object> map = transferAgreement_list.get(i);
                String fileName = (String) map.get("name");
                String fileType = (String) map.get("suffix_type");
                String fileUrl = (String) map.get("url");

//                                    添加布局
                LinearLayout horizontalLayout = new LinearLayout(MapAddLandlordActivity.this);
                horizontalLayout.setOrientation(LinearLayout.HORIZONTAL);

                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        dpToPx(44)
                );
                layoutParams.setMargins(0, 0, 0, dpToPx(8));
                horizontalLayout.setLayoutParams(layoutParams);
                horizontalLayout.setPadding(dpToPx(16), 0, dpToPx(16), 0);
                horizontalLayout.setBackground(getResources().getDrawable(R.drawable.upload_file_border));
                agreement_urls_content.addView(horizontalLayout);

                horizontalLayout.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        // 点击事件逻辑
                        if (fileType.contains("png") || fileType.contains("PNG") || fileType.contains("JPG") || fileType.contains("jpg") || fileType.contains("jpeg") || fileType.contains("JPEG")) {//图片
                            ArrayList<String> images = new ArrayList<>();
                            images.add(fileUrl);
                            previewImage(images);

                        } else {
                            if (!TextUtils.isEmpty(fileUrl)) {
                                Uri uri = Uri.parse(fileUrl);
                                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                try {
                                    context.startActivity(intent);
                                } catch (ActivityNotFoundException e) {
                                    Toast.makeText(context, "没有可用的浏览器应用", Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                Toast.makeText(context, "无效的链接", Toast.LENGTH_SHORT).show();
                            }

                        }
                    }
                });

                // 创建图标 (ImageView)
                ImageView icon = new ImageView(MapAddLandlordActivity.this);
                LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(24), dpToPx(24));
                iconParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                icon.setLayoutParams(iconParams);

                if (fileType.contains("png") || fileType.contains("PNG") || fileType.contains("JPG") || fileType.contains("jpg") || fileType.contains("jpeg") || fileType.contains("JPEG")) {//图片
                    icon.setImageResource(R.drawable.upload_filetype_image);
                } else if (fileType.contains("pdf")) {//pdf
                    icon.setImageResource(R.drawable.upload_filetype_pdf);
                } else if (fileType.contains("doc") || fileType.contains("docx")) {//word
                    icon.setImageResource(R.drawable.upload_filetype_word);
                } else if (fileType.contains("xls") || fileType.contains("xlsx")) {//excel
                    icon.setImageResource(R.drawable.upload_filetype_excel);
                } else if (fileType.contains("ppt") || fileType.contains("pptx")) {//ppt
                    icon.setImageResource(R.drawable.upload_filetype_ppt);
                } else {
                    icon.setImageResource(R.drawable.upload_filetype_wenhao);
                }
                horizontalLayout.addView(icon);

//                            文件名
                TextView textView = new TextView(MapAddLandlordActivity.this);
                textView.setText(fileName);
                textView.setTextSize(14);
                textView.setSingleLine();
                textView.setEllipsize(TextUtils.TruncateAt.MIDDLE); // 中间省略
                textView.setGravity(Gravity.CENTER_VERTICAL);
                textView.setTextColor(Color.parseColor("#1F2126")); // 设置为橙色
                LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                        0,
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        1.0f // 权重为1，占据剩余空间
                );
                textParams.gravity = Gravity.CENTER_VERTICAL;
                textParams.leftMargin = dpToPx(8); // 设置左边距为 8dp
                textParams.rightMargin = dpToPx(8);
                textView.setLayoutParams(textParams);
                horizontalLayout.addView(textView);

//                            右边删除图标
                ImageView deleteIM = new ImageView(context);
                deleteIM.setImageResource(R.drawable.upload_filetype_delete);
                int size = dpToPx(16);
                LinearLayout.LayoutParams deleteParams = new LinearLayout.LayoutParams(size, size);
                deleteParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                deleteIM.setLayoutParams(deleteParams);
                horizontalLayout.addView(deleteIM);
                int finalI = i;
                deleteIM.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        transferAgreement_list.remove(finalI);
                        agreement_urls_content.removeView(horizontalLayout);//移除当前view
                    }
                });


            }


        }


    }

    private Map<String, String> createItem(String id, String name) {
        Map<String, String> item = new HashMap<>();
        item.put("id", id);
        item.put("name", name);
        return item;
    }


    //    预览图片
    private void previewImage(ArrayList<String> imageUrlList) {

        if (null != imageUrlList && !imageUrlList.isEmpty()) {
            Intent intent = new Intent(this, ImageShowActivity.class);
            intent.putExtra("position", 0);
            intent.putStringArrayListExtra("imgUrls", (ArrayList<String>) imageUrlList);
            this.startActivity(intent);
        }
    }

    private void initChangeItem() {//获取变动项
        HashMap<String, Object> body = new HashMap<>();
        body.put("companyId", companyId);
        body.put("type", "STORE");
        body.put("store_apply", 0);
        String org_id = getIntent().getStringExtra("org_id");
        if (org_id != null) {
            body.put("org_id", org_id);
        }


        if (point_id > 0) {
            body.put("store_plan_id", point_id);
        }
        Log.d("TAG", "变动项传参" + body);
        mPresenter.addPointChangeItemPOST(body);
    }

    @Override
    public void addPointChangeItemPOST(ItemChangeReponse reponse) {

        hideLoadingDialog();
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0 && reponse.data != null) {

//            转换返回的JSON对象
            Gson gson = new Gson();
            String json = gson.toJson(reponse.data);
            Type typeToken = new TypeToken<List<Map<String, Object>>>() {
            }.getType();
            List<Map<String, Object>> list = gson.fromJson(json, typeToken);

            List<Map<String, Object>> details = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                Map<String, Object> item = list.get(i);

                String itemName = String.valueOf(item.get("name"));

                //只查询房东信息
                if (itemName.equals("房东信息")) {
                    details = (List<Map<String, Object>>) item.get("details");
                    break;

                }
            }

            Log.d("TAG", "点位详情=====" + details);
            if (details.size() > 0) {

                //    apiname为key，require为值
                for (Map<String, Object> map : details) {
                    if (map.get("init_required") != null) {
                        String api_name = (String) map.get("api_name");
                        boolean init_required = (boolean) map.get("init_required");
                        requiredMap.put(api_name, init_required);
                    }

                }

                initReqiredView();
                //        如果是编辑进来还需要赋值

                if (isEdit && dataDic != null) {
                    initEditView();
                }
            }


        }
    }


    @Override
    public void addPointChangeItemPOSTFailed(Throwable e) {
        hideLoadingDialog();
        showCenterToast("获取变动项失败");
    }

    private void initAddLandlord(HashMap<String, Object> map) {


        map.put("companyId", companyId);
        mPresenter.addLandlordPOST(map);

    }

    @Override
    public void addLandlord(BaseResponse reponse) {

        hideLoadingDialog();
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0 && reponse.data != null) {

//            转换返回的JSON对象
            showMsg("添加房东成功");
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {

                    EventBus.getDefault().post(new MessageEvent("StorePlanDetailActivity"));
                    finish();

                }
            }, 1000); // 延迟时间，单位毫秒

        }
    }

    @Override
    public void addLandlordFailed(Throwable e) {
        showMsg("添加房东失败");
    }


    private void updateAddLandlord(HashMap<String, Object> map) {

        map.put("id",dataDic.get("id"));
        map.put("companyId", companyId);

        mPresenter.updateLandlordPOST(map);;

    }

    @Override
    public void updateLandlord(BaseResponse reponse) {

        hideLoadingDialog();
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0 && reponse.data != null) {

//            转换返回的JSON对象
            showMsg("编辑房东成功");
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {

                    EventBus.getDefault().post(new MessageEvent("StorePlanDetailActivity"));
                    finish();

                }
            }, 1000); // 延迟时间，单位毫秒

        }

    }

    @Override
    public void updateLandlordFailed(Throwable e) {
        showMsg("编辑房东失败");
    }


    private void deleteAddLandlord() {
        HashMap<String,Object> map = new HashMap<>();
        map.put("id",dataDic.get("id"));
        Log.d("TAG","更新传参====" + map);
        mPresenter.deleteLandlordPOST(map);

    }

    @Override
    public void deleteLandlord(BaseResponse reponse) {
        hideLoadingDialog();
        if (reponse == null) {
            showMsg("删除房东失败");
            return;
        }
        int code = reponse.code;
        if (code == 0) {

//            转换返回的JSON对象
            showMsg("删除房东成功");
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {

                    EventBus.getDefault().post(new MessageEvent("StorePlanDetailActivity"));
                    finish();

                }
            }, 1000); // 延迟时间，单位毫秒

        } else {
            showMsg("删除房东失败");
        }
    }

    @Override
    public void deleteLandlordFailed(Throwable e) {
        showMsg("删除房东失败");
    }

    @Override
    public void onClick(View view) {

        if (view.getId() == R.id.back_ll) {
            // 点击左上角返回按钮
            finish();
        } else if (view.getId() == DELETE_BUTTON_ID) {
            showDeleteDialog();
        } else if (view.getId() == SAVE_BUTTON_ID) {
            submitCheck();
        }
    }


    private void scrollToTargerView(View view) {

        int[] screenLocation = new int[2];
        view.getLocationOnScreen(screenLocation);
        int[] scrollOrigin = new int[2];
        activityRootView.getLocationOnScreen(scrollOrigin);
        int relativeY = screenLocation[1] - scrollOrigin[1];
        int currentScrollY = activityRootView.getScrollY();
        int targetScrollY = relativeY + currentScrollY - dpToPx(94);
        activityRootView.smoothScrollTo(0, targetScrollY);
    }

    private void submitCheck() {

        if (TextUtils.isEmpty(nameET.getText()) && requiredMap.get("landlord_name")) {
            showCenterToast("房东姓名为必填项");
            activityRootView.scrollTo(0, 0);
            return;
        }

        if (TextUtils.isEmpty(phoneET.getText()) && requiredMap.get("landlord_phone")) {
            showCenterToast("房东联系方式为必填项");
            activityRootView.smoothScrollTo(0, 0);
            return;
        }

        if (landlordCFDic.isEmpty() && requiredMap.get("owner_id_number_front_url")) {
            showCenterToast("房东身份证人像面为必传项");
            activityRootView.smoothScrollTo(0, 0);
            return;
        }

        if (landlordCBDic.isEmpty() && requiredMap.get("owner_id_number_front_url")) {
            showCenterToast("房东身份证国徽面为必传项");
            activityRootView.smoothScrollTo(0, 0);
            return;
        }

        if (landlordNatureDic.isEmpty() && requiredMap.get("landlord_type")) {
            showCenterToast("房东性质为必选项");
            activityRootView.smoothScrollTo(0, 0);
            return;
        }

        if (landlordIdentityDic.isEmpty() && requiredMap.get("landlord_identity")) {
            showCenterToast("房东身份为必选项");
            activityRootView.smoothScrollTo(0, 0);
            return;
        }

        if (isExclusive == null && requiredMap.get("is_exclusive_agreement")) {
            showCenterToast("是否有排他协议为必选项");
            activityRootView.smoothScrollTo(0, 0);
            return;
        }

        if (isRentDivide == null && requiredMap.get("is_rent_divided")) {
            showCenterToast("是否分租为必选项");
            activityRootView.smoothScrollTo(0, 0);
            return;
        }

        if (TextUtils.isEmpty(rentET.getText()) && requiredMap.get("rent_money")) {
            showCenterToast("租金为必填项");
            activityRootView.smoothScrollTo(0, 0);
            return;
        }

        if (TextUtils.isEmpty(transfer_feeET.getText()) && requiredMap.get("transfer_fee")) {
            showCenterToast("转让费为必填项");
            activityRootView.smoothScrollTo(0, 0);
            return;
        }

        if (lease_contract_list.isEmpty() && requiredMap.get("lease_contract")) {
            showCenterToast("租房合同为必传项");
            activityRootView.smoothScrollTo(0, 0);
            return;
        }

        if (title_certificate_list.isEmpty() && requiredMap.get("title_certificate")) {
            showCenterToast("产权证为必传项");

            scrollToTargerView(findViewById(R.id.title_certificate_indicator));
            return;
        }

        if (store_headDic.isEmpty() && requiredMap.get("store_head")) {
            showCenterToast("门头照为必传项");
            scrollToTargerView(findViewById(R.id.store_head_frame));
            return;
        }

        if (TextUtils.isEmpty(memoET.getText()) && requiredMap.get("memo")) {
            showCenterToast("备注为必填项");

            scrollToTargerView(findViewById(R.id.memo_text));
            return;
        }

//        判断下半部分
        String input = transfer_feeET.getText().toString().trim();
        double transfer_fee = 0.0;
        if (!input.isEmpty()) {
            try {
                transfer_fee = Double.parseDouble(input);
            } catch (NumberFormatException e) {
                transfer_fee = 0.0;

            }
        }
        if (!input.isEmpty() && transfer_fee > 0) {//只有转让费大于0

            if (TextUtils.isEmpty(rentPeopleET.getText()) && requiredMap.get("rent_name")) {
                showCenterToast("当前租户姓名为必填项");
                scrollToTargerView(rentPeopleET);
                return;
            }

            if (TextUtils.isEmpty(rentPhoneET.getText()) && requiredMap.get("rent_phone")) {
                showCenterToast("当前租户联系方式为必填项");
                scrollToTargerView(rentPhoneET);
                return;
            }

            if (TextUtils.isEmpty(rent_deadLineTV.getText()) && requiredMap.get("rent_deadline_date")) {
                showCenterToast("出租截止日期为必选项");
                scrollToTargerView(rent_deadLineTV);
                return;
            }

            if (TextUtils.isEmpty(bankAccountET.getText()) && requiredMap.get("bank_account")) {
                showCenterToast("当前租户收款银行账户为必填项");

                scrollToTargerView(bankAccountET);
                return;
            }

            if (TextUtils.isEmpty(bankAccountNameET.getText()) && requiredMap.get("bank_account_name")) {
                showCenterToast("当前租户收款银行账户为必填项");
                scrollToTargerView(bankAccountNameET);
                return;
            }

            if (licenseDic.isEmpty() && requiredMap.get("license_url")) {
                showCenterToast("当前租户涉及营业执照为必传项");

                scrollToTargerView(findViewById(R.id.license_url_frame));
                return;
            }

            if (rentCFDic.isEmpty() && requiredMap.get("tenant_number_front_url")) {
                showCenterToast("当前租户身份证人像面为必传项");

                scrollToTargerView(findViewById(R.id.tenant_number_cardb));
                return;
            }


            if (rentCBDic.isEmpty() && requiredMap.get("tenant_number_front_url")) {
                showCenterToast("当前租户身份证国徽面为必传项");
                scrollToTargerView(findViewById(R.id.tenant_number_cardb));
                return;
            }

            if (isIdentical == null && requiredMap.get("identical")) {
                showCenterToast("执照法人是否与租户收款银行账户户名同一人为必选项");

                scrollToTargerView(findViewById(R.id.identical_indicator));
                return;
            }

            if (Boolean.FALSE.equals(isIdentical)) {

                if (TextUtils.isEmpty(legalpersonNameET.getText()) && requiredMap.get("legal_person")) {
                    showCenterToast("法人姓名为必填项");

                    scrollToTargerView(legalpersonNameET);
                    return;
                }

                if (TextUtils.isEmpty(legalpersonPhoneET.getText()) && requiredMap.get("legal_person_phone")) {
                    showCenterToast("法人联系方式为必填项");
                    scrollToTargerView(legalpersonPhoneET);
                    return;
                }

                if (legalpersonCFDic.isEmpty() && requiredMap.get("id_number_front_url")) {
                    showCenterToast("法人身份证人像面为必传项");


                    scrollToTargerView(findViewById(R.id.id_number_cardf));
                    return;
                }

                if (legalpersonCBDic.isEmpty() && requiredMap.get("id_number_front_url")) {
                    showCenterToast("法人身份证国徽面为必传项");
                    scrollToTargerView(findViewById(R.id.id_number_cardf));
                    return;
                }

            }


            if (rent_url_list.isEmpty() && requiredMap.get("rental_contract")) {
                showCenterToast("当前租户与房东的租房合同为必传项");

                scrollToTargerView(findViewById(R.id.rental_contract_upload));
                return;
            }

            if (isIdenticalUser == null && requiredMap.get("identical_user")) {
                showCenterToast("租房合同签约是否与收款账户户名同一人为必选项");

                scrollToTargerView(findViewById(R.id.identical_user_indicator));
                return;
            }

            if (Boolean.FALSE.equals(isIdenticalUser)) {

                if (relationshipProofDic.isEmpty() && requiredMap.get("relationship_proof")) {
                    showCenterToast("收款户名与合同签署人关系证明为必选项");

                    scrollToTargerView(findViewById(R.id.relationship_proof_indicator));
                    return;
                }


                if (relationship_list.isEmpty() && requiredMap.get("relationship_proof_urls")) {
                    showCenterToast("关系证明附件为必传项");

                    scrollToTargerView(findViewById(R.id.relationship_proof_urls_indicator));
                    return;
                }


            }


            if (isAdditional == null && requiredMap.get("additional")) {
                showCenterToast("转让费是否包含其他费用为必选项");

                scrollToTargerView(findViewById(R.id.additional_indicator));

                return;
            }

            if (Boolean.TRUE.equals(isAdditional)) {
                if (TextUtils.isEmpty(additionalMemoET.getText()) && requiredMap.get("additional_memo")) {
                    showCenterToast("其他费用备注为必填项");

                    scrollToTargerView(findViewById(R.id.additional_memo_indicator));
                    return;
                }

            }

            if (transferAgreement_list.isEmpty() && requiredMap.get("agreement_urls")) {
                showCenterToast("转让协议为必传项");

                scrollToTargerView(findViewById(R.id.agreement_urls_indicator));
                return;
            }

        }


//        校验通过,拼接传参
        HashMap<String, Object> submitMap = new HashMap<>();
        if (point_id > 0) {
            submitMap.put("store_plan_id", point_id);
        }

        if (!TextUtils.isEmpty(nameET.getText())) {
            submitMap.put("name", nameET.getText().toString().trim());
        }

        if (!TextUtils.isEmpty(phoneET.getText())) {
            submitMap.put("phone", phoneET.getText().toString().trim());
        }

        if (!landlordCFDic.isEmpty()) {
            submitMap.put("owner_id_number_front_url", landlordCFDic.get("url"));
        }

        if (!landlordCBDic.isEmpty()) {
            submitMap.put("owner_id_number_back_url", landlordCBDic.get("url"));
        }

        if (!landlordNatureDic.isEmpty()) {
            submitMap.put("landlord_type", landlordNatureDic.get("id"));
        }

        if (!landlordIdentityDic.isEmpty()) {
            submitMap.put("landlord_identity", landlordIdentityDic.get("id"));
        }

        if (isExclusive != null) {
            submitMap.put("is_exclusive_agreement", isExclusive);

        }

        if (isRentDivide != null) {
            submitMap.put("is_rent_divided", isRentDivide);
        }

        if (!TextUtils.isEmpty(rentET.getText())) {
            submitMap.put("rent_money", rentET.getText().toString().trim());
        }

        if (!TextUtils.isEmpty(transfer_feeET.getText())) {
            submitMap.put("transfer_fee", transfer_feeET.getText().toString().trim());
        }

        if (!lease_contract_list.isEmpty()) {
            submitMap.put("lease_contract_files", lease_contract_list);

            ArrayList<String> arrayList = new ArrayList<>();
            for (Map<String, Object> map : lease_contract_list) {
                String url = (String) map.get("url");
                arrayList.add(url);
            }
            submitMap.put("lease_contract", arrayList);
        }

        if (!title_certificate_list.isEmpty()) {
            submitMap.put("title_certificate_files", title_certificate_list);

            ArrayList<String> arrayList = new ArrayList<>();
            for (Map<String, Object> map : title_certificate_list) {
                String url = (String) map.get("url");
                arrayList.add(url);
            }
            submitMap.put("title_certificate", arrayList);
        }

        if (!store_headDic.isEmpty()) {
            ArrayList<String> arrayList = new ArrayList<>();
            arrayList.add((String) store_headDic.get("url"));
            submitMap.put("urls", arrayList);
        }

        if (!TextUtils.isEmpty(memoET.getText())) {
            submitMap.put("memo", memoET.getText().toString().trim());
        }

        if (!input.isEmpty() && transfer_fee > 0) {

            if (!TextUtils.isEmpty(rentPeopleET.getText())) {
                submitMap.put("rent_name", rentPeopleET.getText().toString().trim());
            }

            if (!TextUtils.isEmpty(rentPhoneET.getText())) {
                submitMap.put("rent_phone", rentPhoneET.getText().toString().trim());
            }

            if (!TextUtils.isEmpty(rent_deadLineTV.getText())) {
                submitMap.put("rent_deadline_date", rent_deadLineTV.getText().toString().trim());
            }

            if (!TextUtils.isEmpty(bankAccountET.getText())) {
                submitMap.put("bank_account", bankAccountET.getText().toString().trim());
            }

            if (!TextUtils.isEmpty(bankAccountNameET.getText())) {
                submitMap.put("bank_account_name", bankAccountNameET.getText().toString().trim());
            }

            if (!licenseDic.isEmpty()) {
                submitMap.put("license_url", licenseDic.get("url"));
            }

            if (!rentCFDic.isEmpty()) {
                submitMap.put("tenant_number_front_url", rentCFDic.get("url"));
            }

            if (!rentCBDic.isEmpty()) {
                submitMap.put("tenant_number_back_url", rentCBDic.get("url"));
            }

            if (isIdentical != null) {
                submitMap.put("identical", isIdentical);
            }

            if (Boolean.FALSE.equals(isIdentical)) {
                if (!TextUtils.isEmpty(legalpersonNameET.getText())) {
                    submitMap.put("legal_person", legalpersonNameET.getText().toString().trim());
                }

                if (!TextUtils.isEmpty(legalpersonPhoneET.getText())) {
                    submitMap.put("legal_person_phone", legalpersonPhoneET.getText().toString().trim());
                }

                if (!legalpersonCFDic.isEmpty()) {
                    submitMap.put("id_number_front_url", legalpersonCFDic.get("url"));
                }

                if (!legalpersonCBDic.isEmpty()) {
                    submitMap.put("id_number_back_url", legalpersonCBDic.get("url"));
                }

            }

            if (!rent_url_list.isEmpty()) {

                submitMap.put("rental_contract_files", rent_url_list);

                ArrayList<String> arrayList = new ArrayList<>();
                for (Map<String, Object> map : rent_url_list) {
                    String url = (String) map.get("url");
                    arrayList.add(url);
                }
                submitMap.put("rental_contract", arrayList);
            }

            if (isIdenticalUser != null) {
                submitMap.put("identical_user", isIdenticalUser);
            }

            if (Boolean.FALSE.equals(isIdenticalUser)) {

                if (!relationshipProofDic.isEmpty()) {
                    submitMap.put("relationship_proof", relationshipProofDic.get("id"));
                }

                if (!relationship_list.isEmpty()) {
                    submitMap.put("relationship_proof_files", relationship_list);

                    ArrayList<String> arrayList = new ArrayList<>();
                    for (Map<String, Object> map : relationship_list) {
                        String url = (String) map.get("url");
                        arrayList.add(url);
                    }
                    submitMap.put("relationship_proof_urls", arrayList);
                }

            }

            if (isAdditional != null) {
                submitMap.put("additional", isAdditional);
            }

            if (Boolean.TRUE.equals(isAdditional)) {
                if (!TextUtils.isEmpty(additionalMemoET.getText())) {
                    submitMap.put("additional_memo", additionalMemoET.getText().toString().trim());
                }
            }

            if (!transferAgreement_list.isEmpty()) {
                submitMap.put("agreement_files", transferAgreement_list);

                ArrayList<String> arrayList = new ArrayList<>();
                for (Map<String, Object> map : transferAgreement_list) {
                    String url = (String) map.get("url");
                    arrayList.add(url);
                }
                submitMap.put("agreement_urls", arrayList);
            }

        }


//        所有的校验完成
        Log.d("TAG", "当前提交参数=====" + submitMap);


        if (isFromDetail) {//从详情进来

            if (isEdit) {

                updateAddLandlord(submitMap);
            } else {//新增

                initAddLandlord(submitMap);
            }

        } else {

            if (isEdit) {
                if (someIndex < 0) {
                    someIndex = 0;
                }
                submitMap.put("someIndex", someIndex);
                EventBus.getDefault().post(new AddLandlordEvent("EDIT_LANDLORD", submitMap));
                finish();

            } else {

                EventBus.getDefault().post(new AddLandlordEvent("ADD_LANDLORD", submitMap));
                finish();
            }

        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.activty_map_add_landlord;
    }

    @Override
    protected MapAddLandlordContract.MapAddLandlordPresenter createPresenter() {

        return new MapAddLandlordContract.MapAddLandlordPresenter();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //注销当前Subscriber
//        EventBus.getDefault().unregister(this);
    }

    private int dpToPx(int dp) {
        return (int) (dp * getResources().getDisplayMetrics().density);
    }

    // 显示自定义底部弹窗
    private void showActionChooseImageDialog(String uploadUrl, HashMap<String, Object> uploadParams, HashMap<String, Object> resultDic, FrameLayout layout, ImageView cardBackgroud, ImageView centerIM, ImageView topIM) {

        // 创建 BottomSheetDialog
        BottomSheetDialog actionSheetDialog = new BottomSheetDialog(context, R.style.BottomSingleDialog);

        // 填充自定义布局
        View actionSheetView = LayoutInflater.from(getApplicationContext())
                .inflate(R.layout.bottom_option_layout, findViewById(R.id.actionSheetContainer));

        // 设置选项
        TextView takePhotoOption = actionSheetView.findViewById(R.id.actionTakePhoto);
        takePhotoOption.setText("拍照"); // 设置为 "拍照"
        takePhotoOption.setOnClickListener(v -> {

            actionSheetDialog.dismiss(); // 关闭弹窗


            filePickerUtils.pickFile("拍照", false, new FilePickerUtils.FilePickerCallback() {
                @Override
                public void onFilePicked(List<Map<String, String>> filePaths) {

                    Map<String, String> map = filePaths.get(0);
                    // 检查文件路径是否有效
                    if (map == null || map.isEmpty()) {
                        showCenterToast("获取文件路径失败");
                        return;
                    }

                    String filePath = map.get("filePath");
                    String fileName = map.get("fileName");
                    String fileType = map.get("fileType");

                    UploadUtil.uploadFile(uploadUrl, new File(filePath), uploadParams, new UploadUtil.UploadCallback() {
                        @Override
                        public void onSuccess(FileBean result) {
                            Log.d("TAG", "上传成功");

                            Glide.with(MapAddLandlordActivity.this) // 上下文
                                    .load(result.getUrl())
                                    .into(cardBackgroud);
                            topIM.setVisibility(View.VISIBLE);
                            centerIM.setVisibility(View.GONE);
                            resultDic.clear();
                            resultDic.put("detail_num", result.getDetail_num());
                            resultDic.put("id", result.getId());
                            resultDic.put("name", result.getName());
                            resultDic.put("ref_id", result.getRef_id());
                            resultDic.put("ref_img", result.getRef_img());
                            resultDic.put("ref_sub_type", result.getRef_sub_type());
                            resultDic.put("suffix_type", result.getSuffix_type());
                            resultDic.put("transform_url", result.getTransform_url());
                            resultDic.put("url", result.getUrl());

                            layout.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View view) {
                                    // 点击事件逻辑
                                    ArrayList<String> images = new ArrayList<>();
                                    images.add(result.getUrl());
                                    previewImage(images);
                                }
                            });

                        }

                        @Override
                        public void onProgress(int progress) {

                        }

                        @Override
                        public void onError(String message) {
                            showCenterToast("上传失败，请重新上传");
                        }
                    });

                }

                @Override
                public void onError(String errorMsg) {

                }
            });
        });

        TextView selectFromGalleryOption = actionSheetView.findViewById(R.id.actionSelectFromGallery);
        selectFromGalleryOption.setText("从相册中选择"); // 设置为
        selectFromGalleryOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                actionSheetDialog.dismiss(); // 关闭弹窗

                filePickerUtils.pickFile("从相册中选择", false, new FilePickerUtils.FilePickerCallback() {
                    @Override
                    public void onFilePicked(List<Map<String, String>> filePaths) {


                        Map<String, String> map = filePaths.get(0);
                        // 检查文件路径是否有效
                        if (map == null || map.isEmpty()) {
                            showCenterToast("获取文件路径失败");
                            return;
                        }

                        // 使用 BitmapFactory 加载图片并显示在 ImageView 中
                        String filePath = map.get("filePath");
                        String fileName = map.get("fileName");
                        String fileType = map.get("fileType");

                        UploadUtil.uploadFile(uploadUrl, new File(filePath), uploadParams, new UploadUtil.UploadCallback() {
                            @Override
                            public void onSuccess(FileBean result) {
                                Log.d("TAG", "上传成功");

                                Glide.with(MapAddLandlordActivity.this) // 上下文
                                        .load(result.getUrl())
                                        .into(cardBackgroud);
                                topIM.setVisibility(View.VISIBLE);
                                centerIM.setVisibility(View.GONE);
                                resultDic.clear();
                                resultDic.put("detail_num", result.getDetail_num());
                                resultDic.put("id", result.getId());
                                resultDic.put("name", result.getName());
                                resultDic.put("ref_id", result.getRef_id());
                                resultDic.put("ref_img", result.getRef_img());
                                resultDic.put("ref_sub_type", result.getRef_sub_type());
                                resultDic.put("suffix_type", result.getSuffix_type());
                                resultDic.put("transform_url", result.getTransform_url());
                                resultDic.put("url", result.getUrl());

                                layout.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View view) {
                                        // 点击事件逻辑
                                        ArrayList<String> images = new ArrayList<>();
                                        images.add(result.getUrl());
                                        previewImage(images);
                                    }
                                });

                            }

                            @Override
                            public void onProgress(int progress) {

                            }

                            @Override
                            public void onError(String message) {
                                showCenterToast("上传失败，请重新上传");
                            }
                        });


                    }

                    @Override
                    public void onError(String errorMsg) {

                    }
                });
            }
        });

        TextView cancelOption = actionSheetView.findViewById(R.id.actionCancel);
        cancelOption.setText("取消"); // 设置为 ""
        cancelOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                actionSheetDialog.dismiss(); // 关闭弹窗
            }
        });


        // 设置自定义布局到 BottomSheetDialog
        actionSheetDialog.setContentView(actionSheetView);
        // 显示弹窗
        actionSheetDialog.show();
    }

    private void showActionChooseFileDialog(String uploadUrl, HashMap<String, Object> uploadParams, List<HashMap<String, Object>> resultList, LinearLayout layout) {

        // 创建 BottomSheetDialog
        BottomSheetDialog actionSheetDialog = new BottomSheetDialog(context, R.style.BottomSingleDialog);

        // 填充自定义布局
        View actionSheetView = LayoutInflater.from(getApplicationContext())
                .inflate(R.layout.bottom_choose_file, findViewById(R.id.actionFileContainer));

        // 设置选项
        TextView takePhotoOption = actionSheetView.findViewById(R.id.action_TakePhoto);
        takePhotoOption.setText("拍照"); // 设置为 "拍照"
        takePhotoOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                actionSheetDialog.dismiss(); // 关闭弹窗

                Log.e("filePickerUtils","拍照");

                filePickerUtils.pickFile("拍照", true, new FilePickerUtils.FilePickerCallback() {
                    @Override
                    public void onFilePicked(List<Map<String, String>> filePaths) {
                        Log.d("TAG", "文件路径为" + filePaths);


                        // 检查文件路径是否有效
                        if (filePaths == null || filePaths.isEmpty()) {
                            showCenterToast("获取文件路径失败");
                            return;
                        }

                        for (Map<String, String> map : filePaths) {

                            String filePath = map.get("filePath");
                            String fileName = map.get("fileName");
                            String fileType = map.get("fileType");

                            UploadUtil.uploadFile(uploadUrl, new File(filePath), uploadParams, new UploadUtil.UploadCallback() {
                                @Override
                                public void onSuccess(FileBean result) {
                                    Log.d("TAG", "上传成功");

                                    HashMap<String, Object> resultDic = new HashMap<>();
                                    resultDic.put("detail_num", result.getDetail_num());
                                    resultDic.put("id", result.getId());
                                    resultDic.put("name", result.getName());
                                    resultDic.put("ref_id", result.getRef_id());
                                    resultDic.put("ref_img", result.getRef_img());
                                    resultDic.put("ref_sub_type", result.getRef_sub_type());
                                    resultDic.put("suffix_type", result.getSuffix_type());
                                    resultDic.put("transform_url", result.getTransform_url());
                                    resultDic.put("url", result.getUrl());

                                    resultList.add(resultDic);

//                                    添加布局
                                    LinearLayout horizontalLayout = new LinearLayout(MapAddLandlordActivity.this);
                                    horizontalLayout.setOrientation(LinearLayout.HORIZONTAL);

                                    LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                                            LinearLayout.LayoutParams.MATCH_PARENT,
                                            dpToPx(44)
                                    );
                                    layoutParams.setMargins(0, 0, 0, dpToPx(8));
                                    horizontalLayout.setLayoutParams(layoutParams);
                                    horizontalLayout.setPadding(dpToPx(16), 0, dpToPx(16), 0);
                                    horizontalLayout.setBackground(getResources().getDrawable(R.drawable.upload_file_border));
                                    layout.addView(horizontalLayout);

                                    horizontalLayout.setOnClickListener(new View.OnClickListener() {
                                        @Override
                                        public void onClick(View view) {
                                            // 点击事件逻辑
                                            ArrayList<String> images = new ArrayList<>();
                                            images.add(result.getUrl());
                                            previewImage(images);
                                        }
                                    });

                                    // 创建图标 (ImageView)
                                    ImageView icon = new ImageView(MapAddLandlordActivity.this);
                                    LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(24), dpToPx(24));
                                    iconParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                                    icon.setLayoutParams(iconParams);

                                    if (fileType.contains("image/")) {//图片
                                        icon.setImageResource(R.drawable.upload_filetype_image);
                                    } else if (fileType.contains("application/pdf")) {//pdf
                                        icon.setImageResource(R.drawable.upload_filetype_pdf);
                                    } else if (fileType.contains("application/msword")) {//word
                                        icon.setImageResource(R.drawable.upload_filetype_word);
                                    } else if (fileType.contains("application/vnd.ms-excel")) {//excel
                                        icon.setImageResource(R.drawable.upload_filetype_excel);
                                    } else if (fileType.contains("application/vnd.ms-powerpoint") || fileType.contains("application/vnd.openxmlformats-officedocument.presentationml.presentation")) {//ppt
                                        icon.setImageResource(R.drawable.upload_filetype_ppt);
                                    } else {
                                        icon.setImageResource(R.drawable.upload_filetype_wenhao);
                                    }
                                    horizontalLayout.addView(icon);

//                            文件名
                                    TextView textView = new TextView(MapAddLandlordActivity.this);
                                    textView.setText(fileName);
                                    textView.setTextSize(14);
                                    textView.setSingleLine();
                                    textView.setEllipsize(TextUtils.TruncateAt.MIDDLE); // 中间省略
                                    textView.setGravity(Gravity.CENTER_VERTICAL);
                                    textView.setTextColor(Color.parseColor("#1F2126")); // 设置为橙色
                                    LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                                            0,
                                            LinearLayout.LayoutParams.WRAP_CONTENT,
                                            1.0f // 权重为1，占据剩余空间
                                    );
                                    textParams.gravity = Gravity.CENTER_VERTICAL;
                                    textParams.leftMargin = dpToPx(8); // 设置左边距为 8dp
                                    textParams.rightMargin = dpToPx(8);
                                    textView.setLayoutParams(textParams);
                                    horizontalLayout.addView(textView);

//                            右边删除图标
                                    ImageView deleteIM = new ImageView(context);
                                    deleteIM.setImageResource(R.drawable.upload_filetype_delete);
                                    int size = dpToPx(16);
                                    LinearLayout.LayoutParams deleteParams = new LinearLayout.LayoutParams(size, size);
                                    deleteParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                                    deleteIM.setLayoutParams(deleteParams);
                                    horizontalLayout.addView(deleteIM);
                                    deleteIM.setOnClickListener(new View.OnClickListener() {
                                        @Override
                                        public void onClick(View v) {
                                            resultList.remove(resultDic);
                                            layout.removeView(horizontalLayout);//移除当前view
                                        }
                                    });


                                }

                                @Override
                                public void onProgress(int progress) {

                                }

                                @Override
                                public void onError(String message) {
                                    showCenterToast("上传失败，请重新上传");
                                }
                            });

                        }


                    }


                    @Override
                    public void onError(String errorMsg) {

                    }
                });
            }
        });

        TextView selectFromGalleryOption = actionSheetView.findViewById(R.id.action_SelectFromGallery);
        selectFromGalleryOption.setText("从相册中选择"); // 设置为
        selectFromGalleryOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                actionSheetDialog.dismiss(); // 关闭弹窗

                filePickerUtils.pickFile("从相册中选择", true, new FilePickerUtils.FilePickerCallback() {
                    @Override
                    public void onFilePicked(List<Map<String, String>> filePaths) {
                        Log.d("TAG", "文件路径为" + filePaths);


                        // 检查文件路径是否有效
                        if (filePaths == null || filePaths.isEmpty()) {
                            showCenterToast("获取文件路径失败");
                            return;
                        }

                        for (Map<String, String> map : filePaths) {

                            String filePath = map.get("filePath");
                            String fileName = map.get("fileName");
                            String fileType = map.get("fileType");

                            UploadUtil.uploadFile(uploadUrl, new File(filePath), uploadParams, new UploadUtil.UploadCallback() {
                                @Override
                                public void onSuccess(FileBean result) {
                                    Log.d("TAG", "上传成功");

                                    HashMap<String, Object> resultDic = new HashMap<>();
                                    resultDic.put("detail_num", result.getDetail_num());
                                    resultDic.put("id", result.getId());
                                    resultDic.put("name", result.getName());
                                    resultDic.put("ref_id", result.getRef_id());
                                    resultDic.put("ref_img", result.getRef_img());
                                    resultDic.put("ref_sub_type", result.getRef_sub_type());
                                    resultDic.put("suffix_type", result.getSuffix_type());
                                    resultDic.put("transform_url", result.getTransform_url());
                                    resultDic.put("url", result.getUrl());

                                    resultList.add(resultDic);

//                                    添加布局
                                    LinearLayout horizontalLayout = new LinearLayout(MapAddLandlordActivity.this);
                                    horizontalLayout.setOrientation(LinearLayout.HORIZONTAL);

                                    LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                                            LinearLayout.LayoutParams.MATCH_PARENT,
                                            dpToPx(44)
                                    );
                                    layoutParams.setMargins(0, 0, 0, dpToPx(8));
                                    horizontalLayout.setLayoutParams(layoutParams);
                                    horizontalLayout.setPadding(dpToPx(16), 0, dpToPx(16), 0);
                                    horizontalLayout.setBackground(getResources().getDrawable(R.drawable.upload_file_border));
                                    layout.addView(horizontalLayout);

                                    horizontalLayout.setOnClickListener(new View.OnClickListener() {
                                        @Override
                                        public void onClick(View view) {
                                            // 点击事件逻辑
                                            ArrayList<String> images = new ArrayList<>();
                                            images.add(result.getUrl());
                                            previewImage(images);
                                        }
                                    });

                                    // 创建图标 (ImageView)
                                    ImageView icon = new ImageView(MapAddLandlordActivity.this);
                                    LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(24), dpToPx(24));
                                    iconParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                                    icon.setLayoutParams(iconParams);

                                    if (fileType.contains("image/")) {//图片
                                        icon.setImageResource(R.drawable.upload_filetype_image);
                                    } else if (fileType.contains("application/pdf")) {//pdf
                                        icon.setImageResource(R.drawable.upload_filetype_pdf);
                                    } else if (fileType.contains("application/msword")) {//word
                                        icon.setImageResource(R.drawable.upload_filetype_word);
                                    } else if (fileType.contains("application/vnd.ms-excel")) {//excel
                                        icon.setImageResource(R.drawable.upload_filetype_excel);
                                    } else if (fileType.contains("application/vnd.ms-powerpoint") || fileType.contains("application/vnd.openxmlformats-officedocument.presentationml.presentation")) {//ppt
                                        icon.setImageResource(R.drawable.upload_filetype_ppt);
                                    } else {
                                        icon.setImageResource(R.drawable.upload_filetype_wenhao);
                                    }
                                    horizontalLayout.addView(icon);

//                            文件名
                                    TextView textView = new TextView(MapAddLandlordActivity.this);
                                    textView.setText(fileName);
                                    textView.setTextSize(14);
                                    textView.setSingleLine();
                                    textView.setEllipsize(TextUtils.TruncateAt.MIDDLE); // 中间省略
                                    textView.setGravity(Gravity.CENTER_VERTICAL);
                                    textView.setTextColor(Color.parseColor("#1F2126")); // 设置为橙色
                                    LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                                            0,
                                            LinearLayout.LayoutParams.WRAP_CONTENT,
                                            1.0f // 权重为1，占据剩余空间
                                    );
                                    textParams.gravity = Gravity.CENTER_VERTICAL;
                                    textParams.leftMargin = dpToPx(8); // 设置左边距为 8dp
                                    textParams.rightMargin = dpToPx(8);
                                    textView.setLayoutParams(textParams);
                                    horizontalLayout.addView(textView);

//                            右边删除图标
                                    ImageView deleteIM = new ImageView(context);
                                    deleteIM.setImageResource(R.drawable.upload_filetype_delete);
                                    int size = dpToPx(16);
                                    LinearLayout.LayoutParams deleteParams = new LinearLayout.LayoutParams(size, size);
                                    deleteParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                                    deleteIM.setLayoutParams(deleteParams);
                                    horizontalLayout.addView(deleteIM);
                                    deleteIM.setOnClickListener(new View.OnClickListener() {
                                        @Override
                                        public void onClick(View v) {
                                            resultList.remove(resultDic);
                                            layout.removeView(horizontalLayout);//移除当前view
                                        }
                                    });


                                }

                                @Override
                                public void onProgress(int progress) {

                                }

                                @Override
                                public void onError(String message) {
                                    showCenterToast("上传失败，请重新上传");
                                }
                            });

                        }
                    }

                    @Override
                    public void onError(String errorMsg) {


                    }
                });
            }
        });

        TextView selectFromFileOption = actionSheetView.findViewById(R.id.actionSelectFromFile);
        selectFromFileOption.setText("从文件中选择"); // 设置为
        selectFromFileOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                actionSheetDialog.dismiss(); // 关闭弹窗

                filePickerUtils.pickFile("从文件中选择", true, new FilePickerUtils.FilePickerCallback() {
                    @Override
                    public void onFilePicked(List<Map<String, String>> filePaths) {
                        Log.d("TAG", "文件路径为" + filePaths);


                        // 检查文件路径是否有效
                        if (filePaths == null || filePaths.isEmpty()) {
                            showCenterToast("获取文件路径失败");
                            return;
                        }

                        for (Map<String, String> map : filePaths) {

                            String filePath = map.get("filePath");
                            String fileName = map.get("fileName");
                            String fileType = map.get("fileType");

                            UploadUtil.uploadFile(uploadUrl, new File(filePath), uploadParams, new UploadUtil.UploadCallback() {
                                @Override
                                public void onSuccess(FileBean result) {
                                    Log.d("TAG", "上传成功");

                                    HashMap<String, Object> resultDic = new HashMap<>();
                                    resultDic.put("detail_num", result.getDetail_num());
                                    resultDic.put("id", result.getId());
                                    resultDic.put("name", result.getName());
                                    resultDic.put("ref_id", result.getRef_id());
                                    resultDic.put("ref_img", result.getRef_img());
                                    resultDic.put("ref_sub_type", result.getRef_sub_type());
                                    resultDic.put("suffix_type", result.getSuffix_type());
                                    resultDic.put("transform_url", result.getTransform_url());
                                    resultDic.put("url", result.getUrl());

                                    resultList.add(resultDic);

//                                    添加布局
                                    LinearLayout horizontalLayout = new LinearLayout(MapAddLandlordActivity.this);
                                    horizontalLayout.setOrientation(LinearLayout.HORIZONTAL);

                                    LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                                            LinearLayout.LayoutParams.MATCH_PARENT,
                                            dpToPx(44)
                                    );
                                    layoutParams.setMargins(0, 0, 0, dpToPx(8));
                                    horizontalLayout.setLayoutParams(layoutParams);
                                    horizontalLayout.setPadding(dpToPx(16), 0, dpToPx(16), 0);
                                    horizontalLayout.setBackground(getResources().getDrawable(R.drawable.upload_file_border));
                                    layout.addView(horizontalLayout);

                                    horizontalLayout.setOnClickListener(new View.OnClickListener() {
                                        @Override
                                        public void onClick(View view) {

                                            if (!TextUtils.isEmpty(result.getUrl())) {
                                                Uri uri = Uri.parse(result.getUrl());
                                                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                                try {
                                                    context.startActivity(intent);
                                                } catch (ActivityNotFoundException e) {
                                                    Toast.makeText(context, "没有可用的浏览器应用", Toast.LENGTH_SHORT).show();
                                                }
                                            } else {
                                                Toast.makeText(context, "无效的链接", Toast.LENGTH_SHORT).show();
                                            }


                                        }
                                    });

                                    // 创建图标 (ImageView)
                                    ImageView icon = new ImageView(MapAddLandlordActivity.this);
                                    LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(24), dpToPx(24));
                                    iconParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                                    icon.setLayoutParams(iconParams);

                                    if (fileType.contains("image/")) {//图片
                                        icon.setImageResource(R.drawable.upload_filetype_image);
                                    } else if (fileType.contains("application/pdf")) {//pdf
                                        icon.setImageResource(R.drawable.upload_filetype_pdf);
                                    } else if (fileType.contains("application/msword")) {//word
                                        icon.setImageResource(R.drawable.upload_filetype_word);
                                    } else if (fileType.contains("application/vnd.ms-excel")) {//excel
                                        icon.setImageResource(R.drawable.upload_filetype_excel);
                                    } else if (fileType.contains("application/vnd.ms-powerpoint") || fileType.contains("application/vnd.openxmlformats-officedocument.presentationml.presentation")) {//ppt
                                        icon.setImageResource(R.drawable.upload_filetype_ppt);
                                    } else {
                                        icon.setImageResource(R.drawable.upload_filetype_wenhao);
                                    }
                                    horizontalLayout.addView(icon);

//                            文件名
                                    TextView textView = new TextView(MapAddLandlordActivity.this);
                                    textView.setText(fileName);
                                    textView.setTextSize(14);
                                    textView.setSingleLine();
                                    textView.setEllipsize(TextUtils.TruncateAt.MIDDLE); // 中间省略
                                    textView.setGravity(Gravity.CENTER_VERTICAL);
                                    textView.setTextColor(Color.parseColor("#1F2126")); // 设置为橙色
                                    LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                                            0,
                                            LinearLayout.LayoutParams.WRAP_CONTENT,
                                            1.0f // 权重为1，占据剩余空间
                                    );
                                    textParams.gravity = Gravity.CENTER_VERTICAL;
                                    textParams.leftMargin = dpToPx(8); // 设置左边距为 8dp
                                    textParams.rightMargin = dpToPx(8);
                                    textView.setLayoutParams(textParams);
                                    horizontalLayout.addView(textView);

//                            右边删除图标
                                    ImageView deleteIM = new ImageView(context);
                                    deleteIM.setImageResource(R.drawable.upload_filetype_delete);
                                    int size = dpToPx(16);
                                    LinearLayout.LayoutParams deleteParams = new LinearLayout.LayoutParams(size, size);
                                    deleteParams.gravity = Gravity.CENTER_VERTICAL; // 垂直居中
                                    deleteIM.setLayoutParams(deleteParams);
                                    horizontalLayout.addView(deleteIM);
                                    deleteIM.setOnClickListener(new View.OnClickListener() {
                                        @Override
                                        public void onClick(View v) {
                                            resultList.remove(resultDic);
                                            layout.removeView(horizontalLayout);//移除当前view
                                        }
                                    });


                                }

                                @Override
                                public void onProgress(int progress) {

                                }

                                @Override
                                public void onError(String message) {
                                    showCenterToast("上传失败，请重新上传");
                                }
                            });

                        }


                    }

                    @Override
                    public void onError(String errorMsg) {

                    }
                });
            }
        });

        TextView cancelOption = actionSheetView.findViewById(R.id.actionCancel);
        cancelOption.setText("取消");
        cancelOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                actionSheetDialog.dismiss(); // 关闭弹窗
            }
        });


        // 设置自定义布局到 BottomSheetDialog
        actionSheetDialog.setContentView(actionSheetView);
        // 显示弹窗
        actionSheetDialog.show();
    }

    private void initBottomView() {


        LinearLayout buttonContainer = findViewById(R.id.button_container);

        // 创建自定义按钮布局
        LinearLayout deleteButtonLayout = new LinearLayout(this);
        deleteButtonLayout.setId(DELETE_BUTTON_ID); // 设置唯一 ID
        deleteButtonLayout.setOrientation(LinearLayout.VERTICAL);
        deleteButtonLayout.setGravity(Gravity.CENTER);
        deleteButtonLayout.setBackgroundColor(getResources().getColor(R.color.white)); // 背景色白色
        deleteButtonLayout.setPadding(0, 0, 0, 0); // 内边距

// 添加删除图标
        ImageView deleteIcon = new ImageView(this);
        deleteIcon.setImageResource(R.drawable.btn_map_bottom_delete); // 替换为实际的图标资源
        LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(
                dpToPx(24), // 图标宽度
                dpToPx(24)  // 图标高度
        );
        iconParams.gravity = Gravity.CENTER;
        deleteIcon.setLayoutParams(iconParams);
        deleteButtonLayout.addView(deleteIcon);

// 添加删除文字
        TextView deleteText = new TextView(this);
        deleteText.setText("删除");
        deleteText.setTextColor(getResources().getColor(R.color.black)); // 设置文字颜色
        deleteText.setTextSize(14); // 设置文字大小
        deleteText.setGravity(Gravity.CENTER);
        deleteButtonLayout.addView(deleteText);

// 设置自定义按钮的布局参数
        LinearLayout.LayoutParams deleteParams = new LinearLayout.LayoutParams(
                dpToPx(94), // 宽度 94dp
                LinearLayout.LayoutParams.MATCH_PARENT // 高度填满
        );
        deleteParams.rightMargin = dpToPx(8); // 右边距 8dp
        deleteButtonLayout.setLayoutParams(deleteParams);

// 添加到容器中
        buttonContainer.addView(deleteButtonLayout);

        // 动态添加保存按钮
        Button saveButton = new Button(this);
        saveButton.setId(SAVE_BUTTON_ID); // 设置唯一 ID
        saveButton.setText("保存");
        saveButton.setTextSize(16);
        // 设置字体加粗
        saveButton.setTypeface(null, Typeface.BOLD);
        saveButton.setBackground(getResources().getDrawable(R.drawable.map_point_submit));
        saveButton.setTextColor(getResources().getColor(R.color.white));

        LinearLayout.LayoutParams saveParams = new LinearLayout.LayoutParams(
                0, // 宽度自适应
                LinearLayout.LayoutParams.MATCH_PARENT, // 高度填满
                1 // 权重为1
        );
        saveButton.setLayoutParams(saveParams);
        buttonContainer.addView(saveButton);


        // 获取 TextView 并设置标题
        TextView titleTextView = findViewById(R.id.title_tv);
        if (!isEdit) {
            titleTextView.setText("新增房东");

            deleteButtonLayout.setVisibility(View.GONE);

            // 修改保存按钮的宽度，使其自适应左右边距
            LinearLayout.LayoutParams newSaveParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT, // 宽度填满
                    LinearLayout.LayoutParams.MATCH_PARENT // 高度填满
            );
            saveButton.setLayoutParams(newSaveParams);

        } else {

            titleTextView.setText("编辑房东");

        }

        findViewById(deleteButtonLayout.getId()).setOnClickListener(this);
        findViewById(saveButton.getId()).setOnClickListener(this);
    }

    // 自定义过滤器类,只允许整数和小数点
    public class NumberDecimalInputFilter implements InputFilter {

        @Override
        public CharSequence filter(CharSequence charSequence, int i, int i1, Spanned spanned, int i2, int i3) {
            // 输入内容
            String input = spanned.toString() + charSequence.toString();

            // 正则表达式：匹配整数或小数
            if (input.matches("^\\d*\\.?\\d*$")) {
                return null; // 验证通过，不做过滤
            } else {
                return ""; // 验证不通过，拦截输入
            }
        }
    }

    //    只允许整数
    public class NumberInputFilter implements InputFilter {

        @Override
        public CharSequence filter(CharSequence charSequence, int i, int i1, Spanned spanned, int i2, int i3) {
            // 输入内容
            String input = spanned.toString() + charSequence.toString();

            // 正则表达式：匹配整数或小数
            if (input.matches("^\\d*$")) {
                return null; // 验证通过，不做过滤
            } else {
                return ""; // 验证不通过，拦截输入
            }
        }
    }

    // 自定义过滤器类
    public class DecimalInputFilter implements InputFilter {
        private final int maxIntegerDigits; // 最大整数位数
        private final int maxDecimalDigits; // 最大小数位数
        private final String regex;

        public DecimalInputFilter(int maxIntegerDigits, int maxDecimalDigits) {
            this.maxIntegerDigits = maxIntegerDigits;
            this.maxDecimalDigits = maxDecimalDigits;
            this.regex = "^\\d{0," + maxIntegerDigits + "}(\\.\\d{0," + maxDecimalDigits + "})?$";
        }

        @Override
        public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
            // 拼接当前内容和新输入的内容
            String input = dest.toString().substring(0, dstart) + source + dest.toString().substring(dend);

            // 匹配正则表达式
            if (input.matches(regex)) {
                return null; // 验证通过，不做过滤
            } else {
                return ""; // 验证不通过，拦截输入
            }
        }
    }

    /**
     * 将数字转换为中文大写
     */
    private String convertToChineseCapital(BigDecimal number) {
        String[] chineseDigits = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
        String[] units = {"", "拾", "佰", "仟"};
        String[] bigUnits = {"", "万", "亿", "兆"};

        StringBuilder result = new StringBuilder();

        // 判断是否为负数
        if (number.signum() < 0) {
            result.append("负");
            number = number.abs();
        }

        // 拆分整数部分和小数部分
        String[] parts = number.toPlainString().split("\\.");
        String integerPart = parts[0];
        String decimalPart = parts.length > 1 ? parts[1] : "";

        // 转换整数部分
        int bigUnitIndex = 0;
        while (integerPart.length() > 0) {
            int segmentLength = Math.min(4, integerPart.length());
            String segment = integerPart.substring(integerPart.length() - segmentLength);
            integerPart = integerPart.substring(0, integerPart.length() - segmentLength);

            StringBuilder segmentResult = new StringBuilder();
            boolean hasNonZero = false;
            for (int i = 0; i < segment.length(); i++) {
                int digit = segment.charAt(segment.length() - 1 - i) - '0';
                if (digit != 0) {
                    segmentResult.insert(0, chineseDigits[digit] + units[i]);
                    hasNonZero = true;
                } else if (hasNonZero && !segmentResult.toString().startsWith("零")) {
                    segmentResult.insert(0, "零");
                }
            }

            if (hasNonZero) {
                segmentResult.append(bigUnits[bigUnitIndex]);
            }
            result.insert(0, segmentResult);
            bigUnitIndex++;
        }


        // 去掉多余的零
//        String integerResult = result.toString().replaceAll("零+", "零").replaceAll("零$", "");

        String integerResult = result.toString();

// 替换连续多个“零”为单个“零”
        integerResult = integerResult.replaceAll("零+", "零");

// 如果字符串以“零”结尾，并且长度大于1，则去掉末尾的“零”
        if (integerResult.endsWith("零") && integerResult.length() > 1) {
            integerResult = integerResult.substring(0, integerResult.length() - 1);
        }

// 如果处理后为空字符串，说明原本就是“零”
        if (integerResult.isEmpty()) {
            integerResult = "零";
        }
        // 转换小数部分
        StringBuilder decimalResult = new StringBuilder();
        if (!decimalPart.isEmpty()) {
            for (int i = 0; i < Math.min(decimalPart.length(), 2); i++) {
                int digit = decimalPart.charAt(i) - '0';
                decimalResult.append(chineseDigits[digit]);
                if (i == 0) {
                    decimalResult.append("角");
                } else if (i == 1) {
                    decimalResult.append("分");
                }
            }
        }

        // 拼接整数部分和小数部分
        if (decimalResult.length() > 0) {
            return integerResult + "元" + decimalResult.toString();
        } else {
            return integerResult + "元整";
        }
    }

    /**
     * 给“万”和“仟”设置红色，其它文字灰色
     */
    private SpannableStringBuilder applyColorToChineseCapital(String chineseCapital) {
        SpannableStringBuilder spannable = new SpannableStringBuilder(chineseCapital);

        // 设置“万”和“仟”为红色，其它为灰色
        for (int i = 0; i < chineseCapital.length(); i++) {
            char c = chineseCapital.charAt(i);
            if (c == '万' || c == '仟') {
                spannable.setSpan(new ForegroundColorSpan(Color.parseColor("#FF6F00")), i, i + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            } else {
                spannable.setSpan(new ForegroundColorSpan(Color.parseColor("#731E2126")), i, i + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
        }

        return spannable;
    }

    private void showPromptDialog() {

        View.OnClickListener onConfimClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //点击知道了按钮处理事件
            }
        };
        showInfoDialog(onConfimClickListener, "若存在分租，请填写分组后的租金");
    }

    private void showPromptRentDialog() {

        View.OnClickListener onConfimClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //点击知道了按钮处理事件
            }
        };
        showInfoDialog(onConfimClickListener, "指实际承租金额(需去除分租租金)");
    }

    protected void showInfoDialog(View.OnClickListener confirmOnClick, String str) {
        DialogMapPromptView.Builder builder = new DialogMapPromptView.Builder(MapAddLandlordActivity.this);
        builder.setButtonConfirm("知道了", confirmOnClick);
        builder.setInfo(str);
        DialogMapPromptView dialog = builder.create();
        dialog.show();
    }

    private void showDeleteDialog() {

        View.OnClickListener onConfimClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {

               if (!isFromDetail){
                   showCenterToast("删除成功");
               }
                new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                    @Override
                    public void run() {

                        if (isFromDetail) {//从详情进来

                            deleteAddLandlord();
                        } else {

                            HashMap<String, Object> submitMap = new HashMap<>();
                            if (someIndex < 0) {
                                someIndex = 0;
                            }
                            submitMap.put("someIndex", someIndex);
                            EventBus.getDefault().post(new AddLandlordEvent("DELETE_LANDLORD", submitMap));
                            finish();
                        }

                    }
                }, 1000); // 延迟时间，单位毫秒


            }
        };
        showInfoDeleteDialog(onConfimClickListener, "确定删除该房东?");
    }

    protected void showInfoDeleteDialog(View.OnClickListener confirmOnClick, String str) {
        DialogTemplateView.Builder builder = new DialogTemplateView.Builder(this);
        builder.setButtonConfirm("确定", confirmOnClick);
        builder.setTitle(str);
        DialogTemplateView dialog = builder.create();
        dialog.show();
    }


}


