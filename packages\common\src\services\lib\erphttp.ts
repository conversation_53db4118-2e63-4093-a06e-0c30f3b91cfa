import axios, {AxiosError} from 'axios';
import {enhance} from 'foca-axios';
import {Platform} from 'react-native';
import {authModel} from '@xlb/business-base/src/models/auth';
import Config from 'react-native-config';
// import Toast from 'react-native-root-toast'
import Loading from '../../components/RootView/Loading';
import {configTime} from '../timeout';
import XlbUpdate from '../../components/features/xlbUpdate';
import dayjs from 'dayjs';
import {updateTime} from '../../utils/commonConstant';
import {DeviceEventEmitter} from 'react-native';
import {navigationRef} from '@xlb/common/src/navigation/NavigationService';
import {Cell, Toast} from '@fruits-chain/react-native-xiaoshu';
import {fetch} from '@react-native-community/netinfo';
import {msgMap} from '../msg';
import DeviceInfo from 'react-native-device-info';

import Const from '@xlb/common/src/utils/const';
import XLBStorage from '@xlb/common/src/utils/storage';
// import * as Sentry from '@sentry/react-native'

const instance = axios.create();
const timestampToDatetime = (timestamp: string | number | Date) => {
  const dateObj = new Date(timestamp);

  const year = dateObj.getFullYear();
  const month = dateObj.getMonth() + 1;
  const day = dateObj.getDate();
  const hours = dateObj.getHours();
  const minutes = dateObj.getMinutes();
  const seconds = dateObj.getSeconds();

  return (
    year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
  );
};

let isRefresh = false;
let retryList: any[] = [];

instance.interceptors.request.use(
  // 动态获取baseUrl 便于切换环境
  async config => {
    // @ts-ignore
    let loadingFlag = config.url.split('.')[config.url.split('.').length - 1];
    if (
      !config?.hideLoading &&
      (loadingFlag === 'audit' ||
        loadingFlag === 'update' ||
        loadingFlag === 'save' ||
        loadingFlag === 'reaudit' ||
        loadingFlag === 'batchdelete' ||
        loadingFlag === 'invalid' ||
        loadingFlag === 'check' ||
        loadingFlag === 'finish' ||
        loadingFlag === 'complete' ||
        loadingFlag === 'checkstorage' ||
        loadingFlag === 'receive' ||
        loadingFlag === 'revoke' ||
        loadingFlag === 'cancelreceive' ||
        loadingFlag === 'submit' ||
        loadingFlag === 'depart')
    ) {
      Loading.show();
    }
    // const accessToken = config.url === '/login/login' ? null : await refreshToken();

    // @ts-ignore
    if (updateTime.includes(dayjs().format('mm')) && XlbUpdate.updateVersion) {
      XlbUpdate.updateVersion('http').then();
    }

    // @ts-ignore
    config.timeout = config.timeout || configTime(config.url);
    config.data = config.data ? config.data : {};
    if (
      authModel?.state?.userInfos &&
      JSON.stringify(authModel?.state?.userInfos) !== '{}'
    ) {
      config.data.operator_store_id =
        authModel?.state?.userInfos?.store?.id || '';
      config.data.company_id =
        config.data.company_id || authModel?.state?.userInfos?.company_id || ''; //兼容页面传company_id
    }

    const urlSplit = config.url?.split('/')?.filter(item => !!item) || [];

    config.data.xlb_request_start_time = new Date().getTime();

    // config.data.source = 'APP'
    // config.baseURL = await XLBGlobalStorage.getItem(BASE_URL);
    // config.baseURL = Config.ERP_URL

    // config.baseURL =
    //   Config.ENV === 'DEVELOP' && urlSplit && urlSplit.length
    //     ? config.baseURL || Const.NewApiService[urlSplit[0]] || Config.ERP_URL
    //     : config.baseURL || Config.ERP_URL;
    // config.baseURL = 'http://192.168.2.226:55113']
    const urlEnvName =['ENV', 'DEVELOP', 'TEST'].includes(Config.ENV || '') ? 'test' : Config.BRANCH_NAME;
    config.baseURL = `https://react-web.react-web.ali-${urlEnvName}.xlbsoft.com/`;
    console.log('erphttp.js -> config.baseURL', config.baseURL);
    config.headers = {
      'Content-type': 'application/json',
      // 在登录页时有可能authModel没有清空就会传过期token,但是登录config.data.AccessToken是有值的，其他地方不会传config.data.AccessToken，所以改变这个传值优先级
      'Access-Token':
        config.data.AccessToken || authModel?.state?.userInfos?.access_token,
      'Api-Version': '1.5.0',
      'Company-Id':
        config?.data?.company_id ||
        authModel?.state?.userInfos?.company_id ||
        '',
      'User-Id': authModel?.state?.userInfos?.id,
      'Org-Ids': authModel?.state.userInfos?.org_ids
        ? authModel?.state.userInfos?.org_ids.join(',')
        : '',
      'App-Device-Id': await DeviceInfo.getDeviceId(),
    };
    //清空数据为空参数
    const RequestBody: any = {};
    for (const i in config.data) {
      if (
        config.data[i] !== '' &&
        config.data[i] !== null &&
        config.data[i] !== undefined
      ) {
        RequestBody[i] = config.data[i];
      }
    }
    config.data = RequestBody;
    console.log(config);

    return config;
  },
  error => {
    console.log(error, 'error');
    Loading.hide();
    return Promise.reject(error);
  },
);

instance.interceptors.response.use(
  response => {
    Loading.hide();
    if (response?.data?.code !== 0) {
      if (response.status == 401 && !response?.config?.isHiddenMsg) {
        Toast.fail('用户暂无该接口权限\n请联系信息部处理');
      } else {
        if (
          (response.data.msg === '登录已过期，请重新登录' ||
            response.data?.code === 1005) &&
          !response.config.url?.includes('usernotice.page') &&
          !response.config.url?.includes('user.temp.login')
        ) {
          DeviceEventEmitter.emit('logOut');
          // if (!isRefresh) {
          //   isRefresh = true
          //   return instance
          //     .post('/erp/hxl.erp.user.token.refresh', {
          //       refresh_token: authModel.state.userInfos.refresh_token,
          //       company_id: authModel.state.userInfos.company_id,
          //     })
          //     .then((res) => {
          //       if (res?.code === 0) {
          //         const { access_token, refresh_token } = res.data
          //         authModel.setUserInfos({ ...cloneDeep(authModel.state.userInfos), access_token, token: access_token, refresh_token })
          //         XLBStorage.setItem('loginInfo', { loginTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), refresh_token })

          //         // token 刷新后将数组的方法重新执行
          //         retryList.forEach((cb) => {
          //           cb()
          //         })
          //         retryList = [] // 重新请求完清空
          //         return instance({ ...response.config, isHiddenMsg: true, data: JSON.parse(response.config.data) }) // 第一次401进入的接口重试
          //       } else {
          //         DeviceEventEmitter.emit('logOut')
          //         retryList = []
          //       }
          //     })
          //     .catch((err) => {
          //       console.log('抱歉，您的登录状态已失效，请重新登录！')
          //       return Promise.reject(err)
          //     })
          //     .finally(() => {
          //       isRefresh = false
          //     })
          // } else {
          //   // 返回未执行 resolve 的 Promise
          //   return new Promise((resolve) => {
          //     // 用函数形式将 resolve 存入，等待刷新后再执行
          //     retryList.push(() => {
          //       resolve(instance({ ...response.config, isHiddenMsg: true, data: JSON.parse(response.config.data) }))
          //     })
          //   })
          // }
        }
        if (response.data.msg && !response?.config?.isHiddenMsg) {
          Toast.fail(response.data.msg);
        }
      }
    }
    return response;
  },
  (err: AxiosError) => {
    Loading.hide();
    if (!err?.config?.isHiddenMsg) {
      console.warn(
        'err',
        err,
        err.request._url,
        err.status,
        err.code,
        err?.response?.status,
      );
      if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
        // if (Config?.ENV == 'PROD') {
        //   axios({
        //     method: 'post',
        //     url: 'https://open.feishu.cn/open-apis/bot/v2/hook/4a355bd9-89b0-41f2-b001-6c3b9d4d39c0',
        //     data: {
        //       msg_type: 'text',
        //       content: {
        //         text: `${err.config?.baseURL}${err.config?.url} 接口超时;\n用户：${authModel?.state?.userInfos?.company_id} - ${
        //           authModel?.state?.userInfos?.name
        //         } - ${authModel?.state?.userInfos?.tel}\n平台：${Platform?.OS};\n参数：\n ${err.config?.data};\n错误码：\n ${err?.code || ''};\n错误信息：\n ${
        //           err?.message || ''
        //         }\n开始时间：\n ${timestampToDatetime(JSON.parse(err.config?.data)?.xlb_request_start_time)};\n结束时间：\n ${timestampToDatetime(
        //           new Date().getTime()
        //         )};\n用时(ms)：\n ${new Date().getTime() - (JSON.parse(err.config?.data)?.xlb_request_start_time || 0)}`,
        //       },
        //     },
        //   })
        // }
        if (Config?.ENV == 'DEVELOP') {
          axios({
            method: 'post',
            url: 'https://open.feishu.cn/open-apis/bot/v2/hook/0629b5cc-8daf-402d-9cac-aceac3a201d0',
            data: {
              msg_type: 'text',
              content: {
                text: `${err.config?.baseURL}${err.config?.url} 接口超时;\n用户：${authModel?.state?.userInfos?.company_id} - ${
                  authModel?.state?.userInfos?.name
                } - ${authModel?.state?.userInfos?.tel}\n平台：${Platform?.OS};\n参数：\n ${err.config?.data};\n错误码：\n ${err.code};\n错误信息：\n ${
                  err.message
                }\n开始时间：\n ${timestampToDatetime(JSON.parse(err.config?.data).xlb_request_start_time)};\n结束时间：\n ${timestampToDatetime(
                  new Date().getTime(),
                )};\n用时(ms)：\n ${new Date().getTime() - JSON.parse(err.config?.data).xlb_request_start_time}`,
              },
            },
          });
        }
        // Toast.fail('请求超时，请重试')
      } else if (err?.response?.status == 401) {
        Toast.fail('用户暂无该接口权限\n请联系信息部处理');
      } else if (err.message === 'Network Error') {
        const currentRoute = navigationRef?.current?.getCurrentRoute();
        if (currentRoute.name !== 'Home') {
          navigationRef.current.navigate('NoNetWork');
        }
      } else {
        if (err?.response?.status) {
          // Sentry.captureException(
          //   `服务告警${err?.response?.status}---------${err?.config?.baseURL}${err?.config?.url};\n用户：${authModel?.state?.userInfos?.name} - ${authModel?.state?.userInfos?.tel}`
          // )
          Toast.fail(msgMap[err?.response?.status]);
        }
      }
    }

    // Promise.reject(err)
    return err;
  },
);

export const ErpHttp = enhance(instance, {
  cache: {
    enable: false,
  },
  throttle: {
    enable: true,
  },
  retry: {
    enable: false,
  },
});
