#!/usr/bin/env bash
set -euo pipefail

# 提示用户选择 debug 或 release
echo "请选择要安装的 APK 类型："
echo "  [0] Debug"
echo "  [1] Release"
read -rp "输入编号（默认 0）: " SELECTED
SELECTED="${SELECTED:-0}"

if [[ "$SELECTED" == "1" ]]; then
  APK_PATH="android/app/build/outputs/apk/release/app-release.apk"
  BUILD_TYPE="Release"
else
  APK_PATH="android/app/build/outputs/apk/debug/app-debug.apk"
  BUILD_TYPE="Debug"
fi

# 检查 APK 是否存在
if [[ ! -f "$APK_PATH" ]]; then
  echo "❌ 未找到 $BUILD_TYPE APK 文件：$APK_PATH"
  echo "请先执行打包命令："
  echo "  cd android && ./gradlew assemble${BUILD_TYPE}"
  exit 1
fi

# 安装 APK
echo "🚀 正在通过 adb 安装 $BUILD_TYPE APK..."
adb install -r "$APK_PATH"
