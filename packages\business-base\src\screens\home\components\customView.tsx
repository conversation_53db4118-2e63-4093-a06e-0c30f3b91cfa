import React, {useEffect, useMemo} from 'react';
import {Component, ComponentKey} from '../../../data';
import {TouchableOpacity, View, StyleSheet, Image, Text} from 'react-native';
import {normalize, TOKEN} from '@xlb/components-rn/styles';
import useHasAuth from '@xlb/common/src/hooks/useHasAuth';
import WithCustomerData from './withCustomerData';
import {XlbCard, XlbText} from '@xlb/components-rn';
import {useNavigation} from '@react-navigation/native';
import {Skeleton} from '@fruits-chain/react-native-xiaoshu';
import useSystemStore from '@xlb/common/src/models/system';
import LazyLoadComponent from './lazyloadComponent';
import * as _reactNative from 'react-native';
// import Swiper from './global/swiper'
import QuickAccess from '../../staging/components/QuickAccess';
// import NoticeItem from './noticeItem'
// import OperateItemNew from './operateItemNew';
// import loadModuleFromCDN from '../../../../../../src/bundleLoader';
// import {getRemoteComponent} from '../../../../../../src/getRemoteComponent';
import {getRemoteComponent} from '@/getRemoteComponent';
import {ErpRouteKeys} from '@xlb/common/src/navigation/erpRoute';
import {BmsRouteKeys} from '@xlb/common/src/navigation/bmsRoute';
import MessageItem from './messageItem';
import BacklogItem from './backlogItem';
import {FsmsRouteKeys} from '@xlb/common/src/navigation/fsmsRoute';
import WebView from '../../webView';
import WebViewPage from '@xlb/common/src/components/WebViewPage';
import XlbIcon from '@xlb/common/src/assets/xlbIconFont';
import NoticeItem from './noticeItem';
import Swiper from './global/swiper';
// import {getPreloadStatus, isAppPreloaded} from '@/utils/bundlePreloadUtils';
// import BacklogItem from './backlogItem'
// import MessageItem from './messageItem'
// import CrmIntention from './crm/crmIntention'
// import CrmOfficial from './crm/crmOfficial'
// import CrmShopTop from './crm/crmShopTop'
// import CrmShopAnalysis from './crm/crmShopAnalysis'

// // 零售组件
// import MemberData from './RetailHome/memberData'
// import CampaignsData from './RetailHome/campaignsData'
// import CouponData from './RetailHome/couponData'
// import MyBalance from './bms/myBalance'
// import MerchantRefund from './bms/merchantRefund'
// import PayTransactions from './bms/payTransactions'

// // sds组件
// import { SdsBadStoreRate } from './sds/SdsBadStoreRate'
// import { SdsDevelopTarget } from './sds/SdsDevelopTarget'
// import { SdsDevelopTop3 } from './sds/SdsDevelopTop3'
// import { SdsMarketCapacity } from './sds/SdsMarketCapacity'
// import { SdsMarketRate } from './sds/SdsMarketRate'
// import { SdsRent } from './sds/SdsRent'
// import { SdsRentRate } from './sds/SdsRentRate'
// import { SdsflagMap } from './sds/sdsflagMap'

// //erp组件
// import AbnormalityAlarmCard from './erp/‌AbnormalityAlarmCard/AbnormalityAlarmCard'
// import BlackListCard from './erp/BlackListCard/BlackListCard'
// import DeliveryAnalysisCard from './erp/DeliveryAnalysisCard/DeliveryAnalysisCard'
// import DistributionCenterCard from './erp/DistributionCenterCard/DistributionCenterCard'
// import HighlightedAreasCard from './erp/HighlightedAreasCard/HighlightedAreasCard'
// import HonorRolCard from './erp/HonorRolCard/HonorRolCard'
// import SameDayDeliveryCard from './erp/SameDayDeliveryCard/SameDayDeliveryCard'
// import BestSellerReminderCard from './erp/BestSellerReminderCard/BestSellerReminderCard'

// // sms组件
// import { SmsInspectStoreSingle } from './sms/smsInspectStoreSingle'
// import { SmsInspectStores } from './sms/smsInspectStores'
// import { SmsTopProblemSingle } from './sms/smsTopProblemSingle'
// import { SmsTopProblemSum } from './sms/smsTopProblemSum'
// import { SmsStoreRanking } from './sms/smsStoreRanking'
// import { SmsRegionRanking } from './sms/smsRegionRanking'
// import { SmsWarZoneRanking } from './sms/smsWarZoneRanking'
// import { SmsStoreTask } from './sms/smsStoreTask'

// //wms组件
// import OrderEcharts from './WmsCom/OrderEcharts'
// import StockTarget from './WmsCom/StockTarget'
// import ValidityWarning from './WmsCom/ValidityWarning'
// import HumanEffectivenessAnalysis from './WmsCom/HumanEffectivenessAnalysis'
// import PerformanceAnalysis from './WmsCom/PerformanceAnalysis'
// import OrderData from './WmsCom/OrderData'
// import StorehouseAreaOrder from './WmsCom/StorehouseAreaOrder'
// import LoadingProgress from './WmsCom/LoadingProgress'
// import ReceivedProgress from './WmsCom/ReceivedProgress'
// import DeliveryTarget from './WmsCom/DeliveryTarget'
// import ElectronContract from './WmsCom/ElectronContract'
// import ShippingEcharts from './WmsCom/ShippingEcharts'
// import CarrierPerformance from './WmsCom/CarrierPerformance'
// import ThroughputEcharts from './WmsCom/ThroughputEcharts'

// // ems 组件
// import EmsConstructionStage from './ems/EmsConstructionStage'
// import EmsCurrentProjects from './ems/EmsCurrentProjects'
// import EmsDeliveredProjects from './ems/EmsDeliveredProjects'
// import EmsMaintenanceWorkOrders from './ems/EmsMaintenanceWorkOrders'
// import EmsMaintenanceServices from './ems/EmsMaintenanceServices'
// import EmsMaintenanceUnits from './ems/EmsMaintenanceUnits'
// import EmsAddWorkOrder from './ems/EmsAddWorkOrder'

// 高阶组件，接收组件配置和子组件
interface IndexProps {
  customModule?: Component[];
  // homeCompMap: any
  loading?: boolean;
  scrollY?: number; // 添加滚动位置参数
}

/**
 *  首页组件映射配置
 */
const homeCompMap: Record<ComponentKey, React.FC<{}>> = {
  globalNews: props => <MessageItem {...props} />,
  globalFastEntrance: props => <BacklogItem {...props} />,
  globalAnnounce: props => <NoticeItem {...props} />,
  globalSwiper: props => <Swiper {...props}></Swiper>,
  globalApplicationList: props => (
    <QuickAccess readonly={true} {...props}></QuickAccess>
  ),
  crmIntentionClientAnalysis: getRemoteComponent(
    'RemoteAppSds',
    'CrmIntention',
  ),
  crmClientAnalysis: getRemoteComponent('RemoteAppSds', 'CrmOfficial'),
  crmStoreApplyTop: getRemoteComponent('RemoteAppSds', 'CrmShopTop'),
  crmStoreApplyAnalysis: getRemoteComponent('RemoteAppSds', 'CrmShopAnalysis'),
  memberData: getRemoteComponent('RemoteAppMem', 'MemberData'),
  campaignsData: getRemoteComponent('RemoteAppMem', 'CampaignsData'),
  couponData: getRemoteComponent('RemoteAppMem', 'CouponData'),
  sdsBadStoreRate: getRemoteComponent('RemoteAppSds', 'SdsBadStoreRate'),
  sdsDevelopTarget: getRemoteComponent('RemoteAppSds', 'SdsDevelopTarget'),
  sdsDevelopTop3: getRemoteComponent('RemoteAppSds', 'SdsDevelopTop3'),
  sdsMarketCapacity: getRemoteComponent('RemoteAppSds', 'SdsMarketCapacity'),
  sdsMarketRate: getRemoteComponent('RemoteAppSds', 'SdsMarketRate'),
  sdsRent: getRemoteComponent('RemoteAppSds', 'SdsRent'),
  sdsRentRate: getRemoteComponent('RemoteAppSds', 'SdsRentRate'),
  sdsflagMap: getRemoteComponent('RemoteAppSds', 'SdsflagMap'),
  bmsMyBalance: getRemoteComponent('RemoteAppBms', 'MyBalance'),
  bmsMerchantRefund: getRemoteComponent('RemoteAppBms', 'MerchantRefund'),
  bmsPayTransactions: getRemoteComponent('RemoteAppBms', 'PayTransactions'),
  erpAbnormalityAlarmCard: getRemoteComponent(
    'RemoteAppErp',
    'AbnormalityAlarmCard',
  ),
  erpBlackListCard: getRemoteComponent('RemoteAppErp', 'BlackListCard'),
  erpDeliveryAnalysisCard: getRemoteComponent(
    'RemoteAppErp',
    'DeliveryAnalysisCard',
  ),
  erpDistributionCenterCard: getRemoteComponent(
    'RemoteAppErp',
    'DistributionCenterCard',
  ),
  erpHighlightedAreasCard: getRemoteComponent(
    'RemoteAppErp',
    'HighlightedAreasCard',
  ),
  erpOrderCompletionAccuracy: getRemoteComponent(
    'RemoteAppErp',
    'OrderCompletionAccuracy',
  ),
  erpHonorRolCard: getRemoteComponent('RemoteAppErp', 'HonorRolCard'),
  erpSameDayDeliveryCard: getRemoteComponent(
    'RemoteAppErp',
    'SameDayDeliveryCard',
  ),
  erpBestSellerReminderCard: getRemoteComponent(
    'RemoteAppErp',
    'BestSellerReminderCard',
  ),
  erpSaleAnalysis: getRemoteComponent('RemoteAppBi', 'SaleAnalysis'),
  // erpBestSellerReminderCard: () => <BestSellerReminderCard />,

  wmsReceivedProgress: getRemoteComponent('RemoteAppScm', 'ReceivedProgress'),
  wmsThroughputTrend: getRemoteComponent('RemoteAppScm', 'ThroughputEcharts'), //toDo 待补充 吞吐量趋势图
  wmsStockTarget: getRemoteComponent('RemoteAppScm', 'StockTarget'),
  wmsValidityWarning: getRemoteComponent('RemoteAppScm', 'ValidityWarning'),
  wmsHumanEffectivenessAnalysis: getRemoteComponent(
    'RemoteAppScm',
    'HumanEffectivenessAnalysis',
  ),
  wmsPerformanceAnalysis: getRemoteComponent(
    'RemoteAppScm',
    'PerformanceAnalysis',
  ),
  tmsOrderData: getRemoteComponent('RemoteAppScm', 'OrderData'),
  tmsOrderEcharts: getRemoteComponent('RemoteAppScm', 'OrderEcharts'),
  tmsStorehouseAreaOrder: getRemoteComponent(
    'RemoteAppScm',
    'StorehouseAreaOrder',
  ),
  tmsLoadingProgress: getRemoteComponent('RemoteAppScm', 'LoadingProgress'),
  tmsDeliveryTarget: getRemoteComponent('RemoteAppScm', 'DeliveryTarget'),
  tmsElectronContract: getRemoteComponent('RemoteAppScm', 'ElectronContract'),
  tmsShippingEcharts: getRemoteComponent('RemoteAppScm', 'ShippingEcharts'),
  tmsCarrierPerformance: getRemoteComponent(
    'RemoteAppScm',
    'CarrierPerformance',
  ),

  smsInspectStoreSingle: getRemoteComponent(
    'RemoteAppSms',
    'SmsInspectStoreSingle',
  ),
  smsInspectStores: getRemoteComponent('RemoteAppSms', 'SmsInspectStores'),
  smsTopProblemSingle: getRemoteComponent(
    'RemoteAppSms',
    'SmsTopProblemSingle',
  ),
  smsTopProblemSum: getRemoteComponent('RemoteAppSms', 'SmsTopProblemSum'),
  smsStoreRanking: getRemoteComponent('RemoteAppSms', 'SmsStoreRanking'),
  smsRegionRanking: getRemoteComponent('RemoteAppSms', 'SmsRegionRanking'),
  smsWarZoneRanking: getRemoteComponent('RemoteAppSms', 'SmsWarZoneRanking'),
  smsStoreTask: getRemoteComponent('RemoteAppSms', 'SmsStoreTask'),
  // // 增加ems看板相关
  emsConstructionStage: getRemoteComponent(
    'RemoteAppEms',
    'EmsConstructionStage',
  ),
  emsCurrentProjects: getRemoteComponent('RemoteAppEms', 'EmsCurrentProjects'),
  emsDeliveredProjects: getRemoteComponent(
    'RemoteAppEms',
    'EmsDeliveredProjects',
  ),
  emsMaintenanceServices: getRemoteComponent(
    'RemoteAppEms',
    'EmsMaintenanceServices',
  ),
  emsMaintenanceWorkOrders: getRemoteComponent(
    'RemoteAppEms',
    'EmsMaintenanceWorkOrders',
  ),
  emsMaintenanceUnits: getRemoteComponent(
    'RemoteAppEms',
    'EmsMaintenanceUnits',
  ),
  emsAddWorkOrder: getRemoteComponent('RemoteAppEms', 'EmsAddWorkOrder'),
};

const CustomView: React.FC<IndexProps> = ({loading, scrollY, customModule}) => {
  // 如果没有组件数据，直接返回子组件
  const navigation: any = useNavigation();
  const {theme, setTheme, themeName} = useSystemStore((state: any) => state);
  // const customModule = [
  //   {
  //     key: 'globalNews',
  //   },
  //   {
  //     key: 'globalSwiper',
  //   },
  //   {
  //     key: 'globalFastEntrance',
  //   },
  // ];
  // 使用useMemo生成随机数，避免每次渲染都重新生成
  const randomCount = useMemo(() => {
    // 生成1-5之间的随机整数
    return Math.floor(Math.random() * 6) + 3;
  }, [loading]);

  useEffect(() => {
    global.__XLB__.event.on('master', (...args) => {
      console.log('接收到消息', args);
    });
  }, []);
  console.log('customModule1', customModule);

  return (
    <>
      {/* <XlbText onPress={() => console.log(getPreloadStatus())}>
        获取预加载状态
      </XlbText>
      <XlbText onPress={() => console.log(isAppPreloaded('RemoteAppBase'))}>
        检查应用是否已预加载
      </XlbText> */}
      {loading ? (
        Array.from({length: randomCount}).map((_, i) => {
          return (
            <XlbCard
              key={i}
              containerStyle={{
                marginTop: 0,
                marginHorizontal: 0,
                marginBottom: normalize(12),
              }}>
              <Skeleton
                loading
                //   active={false}
                paragraph={{
                  rows: 5,
                  widths: [
                    100, 100, 70, 100, 100, 20, 100, 100, 70, 100, 100, 70, 100,
                    100, 70, 100, 70, 100, 70, 100, 70, 70, 100, 70, 70, 100,
                  ],
                }}
              />
            </XlbCard>
          );
        })
      ) : (
        <>
          {customModule?.map((e: Component, index) => {
            const ComponentElement = homeCompMap?.[e.key];
            const auth =
              typeof e?.auth === 'function'
                ? e.auth()
                : e?.auth
                  ? useHasAuth(e.auth)
                  : true;

            if (!auth) return null;
            if (!ComponentElement) {
              return <Text key={e.key}>{e.key} not impl</Text>;
            }
            return (
              <LazyLoadComponent
                scrollY={scrollY}
                key={index}
                index={index}
                refreshing={loading}>
                <WithCustomerData key={e.key} componentData={e}>
                  <ComponentElement />
                </WithCustomerData>
              </LazyLoadComponent>
            );
          })}

          <TouchableOpacity
            onPress={() => navigation.navigate('customModule')}
            style={styles['btn-wrap']}>
            <View style={styles['btn']}>
              {/* <IconFont color={TOKEN.grey_45} name="mokuai" size={normalize(14)} style={{ marginRight: normalize(4) }} /> */}
              <Image
                style={{
                  width: normalize(14),
                  height: normalize(14),
                  marginRight: normalize(4),
                }}
                source={require('@xlb/common/src/assets/icons/buju.png')}
                // resizeMode={FastImage.resizeMode.contain}
              ></Image>
              <XlbText grey_45>布局调整</XlbText>
            </View>
          </TouchableOpacity>
        </>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  'btn-wrap': {
    // position: 'absolute',
    // bottom: 0,
    // left: 0,
    width: '100%',
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 24,
    // marginBottom: normalize(24),
  },
  btn: {
    borderColor: TOKEN.grey_1,
    borderWidth: 1,
    padding: normalize(8),
    borderRadius: normalize(8),
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
  },
});
export default React.memo(CustomView);
