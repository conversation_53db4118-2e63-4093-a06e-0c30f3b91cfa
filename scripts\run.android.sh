#!/bin/bash
set -euo pipefail

# 获取所有 .env.* 文件
ENV_FILES=(.env.*)

if [ ${#ENV_FILES[@]} -eq 0 ]; then
  echo "❌ 当前目录下没有找到任何 .env.* 文件"
  exit 1
fi

# 提示用户选择环境
echo "请选择要本地要使用的环境配置文件："
for i in "${!ENV_FILES[@]}"; do
  printf "  [%d] %s\n" "$i" "${ENV_FILES[$i]}"
done

read -rp "test(测试) staging(灰度) prod(正式) 输入编号（默认 0）: " SELECTED_INDEX
SELECTED_INDEX="${SELECTED_INDEX:-0}"

# 检查输入合法
if ! [[ "$SELECTED_INDEX" =~ ^[0-9]+$ ]] || [ "$SELECTED_INDEX" -lt 0 ] || [ "$SELECTED_INDEX" -ge "${#ENV_FILES[@]}" ]; then
  echo "❌ 输入不合法，请输入 0 ~ $(( ${#ENV_FILES[@]} - 1 )) 之间的数字"
  exit 1
fi

SELECTED_ENV="${ENV_FILES[$SELECTED_INDEX]}"
echo "✅ 你选择的是: $SELECTED_ENV"

# 拷贝为 .env
cp "$SELECTED_ENV" .env
echo "📋 已复制 $SELECTED_ENV 到 .env"

# 检查是否支持 react-native 命令
if ! command -v react-native >/dev/null 2>&1; then
  echo "❌ react-native 命令未找到，请确保你已安装并配置好 react-native-cli"
  exit 1
fi

# 启动 Android 项目
echo "🚀 正在运行 react-native run-android..."
react-native run-android
