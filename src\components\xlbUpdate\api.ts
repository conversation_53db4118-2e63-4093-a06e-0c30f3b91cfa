// 请求是否有新版本
import axios from 'axios';
import Config from 'react-native-config';
import {bundlePrefix} from "@/components/xlbUpdate/constant.ts";

const apkJsonUrl = `https://rn-bundle-cdn.xlbsoft.com/apk/${Config.BRANCH_NAME}/version.json`;
const requestCheckApk: () => Promise<any> = async () => {
  return axios.get(apkJsonUrl);
};

const requestCheckBundle: () => Promise<any> = async ()=> {
  return axios.get(`${bundlePrefix}/version.json`);
};

export const updateApi = {
  requestCheckApk,
  requestCheckBundle,
};
