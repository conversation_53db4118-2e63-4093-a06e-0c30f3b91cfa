import React, {useEffect, useMemo, useRef, useState} from 'react';
import NewHome from '@xlb/business-base/src/screens/home/<USER>';
import Data from '@xlb/business-base/src/screens/data';
import {
  DeviceEventEmitter,
  Platform,
  StatusBar,
  useWindowDimensions,
  View,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {SceneMap} from 'react-native-tab-view';
import {commonStyles, XText} from '../components';
import useGetPosition from '../hooks/useGetPosition';
import {authModel} from '@xlb/business-base/src/models/auth';
import Board from './Board';
import useHasAuth from '../hooks/useHasAuth';
import useScreenShot from 'src/useScreenShot';
import Staging from '@xlb/business-base/src/screens/staging';
import Hrs, {hrsAuthList} from '@xlb/business-base/src/screens/hrs';
import FastImage from 'react-native-fast-image';
import {useIsFocused} from '@react-navigation/native';
import useBack from 'src/useBack';
import useHomeStore from '@xlb/business-base/src/screens/home/<USER>';
import {Badge} from '@fruits-chain/react-native-xiaoshu';
import useSystemStore from '../models/system';
import {getRemoteComponent} from 'src/getRemoteComponent';
import {TabBar, TabView} from 'react-native-tab-view';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {colors, normalize} from '@xlb/components-rn/styles';

const MqttWrap = getRemoteComponent('RemoteAppIm', 'MqttWrap');
const ChatHome = getRemoteComponent('RemoteAppIm', 'ChatHome');
const isAndroid = Platform.OS === 'android';
const iconMaps: any = {
  'board-new-active': {
    default: require('@xlb/common/src/assets/icons/board-new-active.png'),
    blue: require('@xlb/common/src/assets/icons/board-new-active.png'),
    orange: require('@xlb/common/src/assets/icons/board-new-active-orange.png'),
    green: require('@xlb/common/src/assets/icons/board-new-active-green.png'),
  },
  'board-new': require('@xlb/common/src/assets/icons/board-new.png'),
  'app-new-active': {
    default: require('@xlb/common/src/assets/icons/app-new-active.png'),
    blue: require('@xlb/common/src/assets/icons/app-new-active.png'),
    orange: require('@xlb/common/src/assets/icons/app-new-active-orange.png'),
    green: require('@xlb/common/src/assets/icons/app-new-active-green.png'),
  },
  'app-new': require('@xlb/common/src/assets/icons/app-new.png'),

  'home-new-active': {
    default: require('@xlb/common/src/assets/icons/home-new-active.png'),
    blue: require('@xlb/common/src/assets/icons/home-new-active.png'),
    orange: require('@xlb/common/src/assets/icons/home-new-active-orange.png'),
    green: require('@xlb/common/src/assets/icons/home-new-active-green.png'),
  },
  'home-new': require('@xlb/common/src/assets/icons/home-new.png'),

  'group-active': {
    default: require('@xlb/common/src/assets/icons/group-active.png'),
    blue: require('@xlb/common/src/assets/icons/group-active.png'),
    orange: require('@xlb/common/src/assets/icons/group-active-orange.png'),
    green: require('@xlb/common/src/assets/icons/group-active-green.png'),
  },
  group: require('@xlb/common/src/assets/icons/group.png'),

  'hrs-active': {
    default: require('@xlb/common/src/assets/icons/hrs-active.png'),
    blue: require('@xlb/common/src/assets/icons/hrs-active.png'),
    orange: require('@xlb/common/src/assets/icons/hrs-active-orange.png'),
    green: require('@xlb/common/src/assets/icons/hrs-active-green.png'),
  },
  hrs: require('@xlb/common/src/assets/icons/hrs.png'),
  // ... 其他图标
};
function onTabPress(e: any) {
  switch (e.route.key) {
    case 'newHome':
    case 'newMine':
    case 'staging':
    case 'board':
    case 'messages':
    case 'data':
    case 'hrs':
      if (isAndroid) {
        // StatusBar.pushStackEntry({
        //   barStyle: 'dark-content',
        //   backgroundColor: 'transparent',
        //   translucent: true,
        // })
        StatusBar.setBarStyle('dark-content');
        StatusBar.setBackgroundColor('transparent');
        StatusBar.setTranslucent(true);
      }

      break;
    case 'newBoard':
      if (isAndroid) {
        StatusBar.setBarStyle('dark-content');
        StatusBar.setBackgroundColor('transparent');
        StatusBar.setTranslucent(true);
      }

      break;
    default:
      if (!isAndroid) return false;
      StatusBar.setTranslucent(false);
      StatusBar.setBackgroundColor('#1A6AFF');
      StatusBar.setBarStyle('light-content');
      break;
  }

  if (e.route.key === 'home') {
    DeviceEventEmitter.emit('NoticeRefresh');
    DeviceEventEmitter.emit('SCMWaitHandleRefresh');
    DeviceEventEmitter.emit('WMSWaitHandleRefresh');
    DeviceEventEmitter.emit('NoticeMessage');
  }
  if (e.route.key === 'mine' || e.route.key === 'newMine') {
    DeviceEventEmitter.emit('TMSCheckRefresh');
  }
  if (e.route.key === 'board') {
    // DeviceEventEmitter.emit('BoardRefresh')
    console.log('刷新了');
  }
}
const renderScene = SceneMap({
  hrs: Hrs,
  newHome: NewHome,
  board: Board,
  data: Data,
  staging: Staging,
  messages: ChatHome,
});

function HomeTabs() {
  const isFocused = useIsFocused();
  const {homeTabIndex, setHomeTabIndex} = useHomeStore(state => state);
  const [unreadLen, setUnreadLen] = useState(0);
  const [rooms, setRooms] = useState([]);

  const user = authModel.state.userInfos;

  const hasAuth =
    useHasAuth(100161) ||
    useHasAuth(100162) ||
    useHasAuth(100178) ||
    useHasAuth(100179) ||
    useHasAuth(100180) ||
    useHasAuth(100181) ||
    useHasAuth(100182) ||
    useHasAuth(100205);

  const hasDataAuth =
    useHasAuth(['日商分析', '查询']) ||
    useHasAuth(['营业收款报表', '查询']) ||
    useHasAuth(['APP消费券返款', '查询']) ||
    useHasAuth(['APP储值卡返款', '查询']) ||
    useHasAuth(['安装进度分析', '查询']) ||
    useHasAuth(['日销数据分析', '查询']) ||
    useHasAuth(['商品销售分析', '查询']) ||
    useHasAuth(['商品分层分析', '查询']) ||
    useHasAuth(['商品复购分析', '查询']) ||
    useHasAuth(['异常单品', '查询']) ||
    useHasAuth(['时段销售分析', '查询']) ||
    useHasAuth(['门店经营状况分析', '查询']) ||
    useHasAuth(['店横对比', '查询']) ||
    useHasAuth(['区域销售分析', '查询']) ||
    useHasAuth(['门店销售分级统计', '查询']) ||
    useHasAuth(['配送分析', '查询']) ||
    // useHasAuth(['BOSS报表', '查询']) ||
    useHasAuth(['已结账单查询', '查询']) ||
    useHasAuth(['异常收银分析', '查询']) ||
    useHasAuth(['单品业绩', '查询']) ||
    useHasAuth(['消费券分析', '查询']) ||
    useHasAuth(['短保商品报表', '查询']);

  const hasHrsAuth = hrsAuthList.some(item => useHasAuth(item));

  const [tabs, setTabs] = useState([
    {
      key: 'newHome',
      title: '首页',
    },
    // {
    //   key: 'newBoard',
    //   title: '新看板',
    // },
    ...(hasHrsAuth
      ? [
          {
            key: 'hrs',
            title: 'HRS',
          },
        ]
      : []),
    ...(hasDataAuth
      ? [
          {
            key: 'data',
            title: '数据',
          },
        ]
      : []),
    {
      key: 'staging',
      title: '工作台',
    },
    ...(hasAuth
      ? [
          {
            key: 'board',
            title: '集团',
          },
        ]
      : []),
  ]);

  const queryRooms = () => {
    import('RemoteAppIm/src/api').then(({queryRoomsV2}) => {
      queryRoomsV2(String(user.tel))
        .then(res => {
          setRooms((res as any) || []);
        })
        .catch(err => {
          console.log('错误信息', err);
        });
    });
  };

  /**
   * 获取未读消息数
   */
  useEffect(() => {
    const mqttHandlerer = DeviceEventEmitter.addListener('im-mqtt', message => {
      try {
        const payload = JSON.parse(message.payloadString);
        const {action, content} = payload;
        if (
          action === 'message-add' ||
          action === 'room-add' ||
          action === 'message-update'
        ) {
          queryRooms();
        }
      } catch (error) {
        console.log('error', error);
      }
    });
    queryRooms();
    return () => {
      mqttHandlerer.remove();
    };
  }, [isFocused]);
  // 如果rooms有length 就展示消息
  useEffect(() => {
    if (rooms?.length)
      setTabs(pre => {
        // 如果已经有消息就返回原本的 否则就添加
        if (pre.find(i => i.key === 'messages')) {
          return [...pre];
        }
        return [
          ...pre,
          {
            key: 'messages',
            title: '消息',
          },
        ];
      });
  }, [rooms]);
  const UnreadMessagesLen = () => {
    const l = rooms.reduce((total, room: any) => {
      return total + room.unreadCount;
    }, 0);
    setUnreadLen(l);
  };
  useEffect(() => {
    UnreadMessagesLen();
    const mqttHandlerer = DeviceEventEmitter.addListener(
      'im-mqtt__UnreadMessagesLen',
      () => {
        UnreadMessagesLen();
      },
    );
    return () => {
      mqttHandlerer.remove();
    };
  }, [rooms]);

  // useAppState({
  //   onForeground: () => {
  //     sendLogInfo()
  //   },
  // })
  // 存储当前的位置信息
  useGetPosition();

  useScreenShot();

  //监听返回事件
  useBack();

  useEffect(() => {
    if (Platform.OS == 'android') {
      // AnalyticsUtil.UMInit()
    }
    // 我的常用跳转工作台
    const jumpStaginghandle = DeviceEventEmitter.addListener(
      'jumpStaging',
      () => {
        setHomeTabIndex(tabs?.findIndex((item: any) => item.key === 'staging'));
      },
    );

    return () => {
      jumpStaginghandle.remove();
    };
  }, []);
  const insets = useSafeAreaInsets();
  const layout = useWindowDimensions();

  const tabItemWidth = useMemo(() => {
    return (1 / tabs.length) * layout.width;
  }, [tabs, layout.width]);
  const {themeName} = useSystemStore().getState();

  const currentTheme =
    themeName === 'default' || themeName === 'blue' ? 'default' : themeName;

  const themeMaps: any = {
    default: {
      color: '#1A6AFF',
    },
    blue: {
      color: '#1A6AFF',
    },
    orange: {
      color: 'rgba(255, 90, 0, 1)',
    },
    green: {
      color: '#0ECC54',
    },
  };
  return (
    <>
      <TabView
        animationEnabled={false}
        lazy={({route}) => route.key !== 'newHome'}
        navigationState={{index: homeTabIndex, routes: tabs}}
        onIndexChange={setHomeTabIndex}
        renderScene={renderScene}
        swipeEnabled={false}
        tabBarPosition="bottom"
        renderTabBar={props => {
          return (
            <TabBar
              {...props}
              scrollEnabled={false}
              bounces={true}
              tabStyle={{backgroundColor: 'white', width: tabItemWidth}}
              activeColor={themeMaps[themeName]?.color} //选中颜色
              inactiveColor={'#86909C'} //未选中颜色
              indicatorContainerStyle={{
                width: tabItemWidth,
                height: 0,
                backgroundColor: colors.primary,
                top: -3,
              }}
              style={{
                paddingBottom: 10,
                backgroundColor: colors.white,
              }}
              options={{
                newHome: {
                  icon: ({focused}) => (
                    <FastImage
                      source={
                        focused
                          ? iconMaps['home-new-active'][currentTheme] ||
                            iconMaps['home-new-active']['default']
                          : iconMaps['home-new']
                      }
                      style={{
                        width: 20,
                        height: 20,
                      }}></FastImage>
                  ),
                  label: ({focused, route}) => {
                    return (
                      <XText
                        style={{
                          color: focused
                            ? themeMaps[themeName]?.color
                            : '#86909C',
                          fontSize: 10,
                          fontWeight: focused ? '500' : 'normal',
                          width: 40,
                          justifyContent: 'center',
                          alignItems: 'center',
                          textAlign: 'center',
                        }}>
                        {route.title}
                      </XText>
                    );
                  },
                },

                hrs: {
                  icon: ({focused}) => (
                    <FastImage
                      source={
                        focused
                          ? iconMaps['hrs-active'][currentTheme] ||
                            iconMaps['hrs-active']['default']
                          : iconMaps['hrs']
                      }
                      style={{
                        width: 20,
                        height: 20,
                        marginBottom: 4,
                      }}></FastImage>
                  ),
                  label: ({focused, route}) => {
                    return (
                      <XText
                        style={{
                          color: focused
                            ? themeMaps[themeName]?.color
                            : '#86909C',
                          fontSize: 10,
                          fontWeight: focused ? '500' : 'normal',
                          width: 40,
                          justifyContent: 'center',
                          alignItems: 'center',
                          textAlign: 'center',
                        }}>
                        {route.title}
                      </XText>
                    );
                  },
                },
                data: {
                  icon: ({focused}) => (
                    <FastImage
                      source={
                        focused
                          ? iconMaps['board-new-active'][currentTheme] ||
                            iconMaps['board-new-active']['default']
                          : iconMaps['board-new']
                      }
                      style={{
                        width: 20,
                        height: 20,
                        marginBottom: 4,
                      }}></FastImage>
                  ),
                  label: ({focused, route}) => {
                    return (
                      <XText
                        style={{
                          color: focused
                            ? themeMaps[themeName]?.color
                            : '#86909C',
                          fontSize: 10,
                          fontWeight: focused ? '500' : 'normal',
                          width: 40,
                          justifyContent: 'center',
                          alignItems: 'center',
                          textAlign: 'center',
                        }}>
                        {route.title}
                      </XText>
                    );
                  },
                },
                staging: {
                  icon: ({focused}) => (
                    <FastImage
                      source={
                        focused
                          ? iconMaps['app-new-active'][currentTheme] ||
                            iconMaps['app-new-active']['default']
                          : iconMaps['app-new']
                      }
                      style={{
                        width: 20,
                        height: 20,
                        marginBottom: 4,
                      }}></FastImage>
                  ),
                  label: ({focused, route}) => {
                    return (
                      <XText
                        style={{
                          color: focused
                            ? themeMaps[themeName]?.color
                            : '#86909C',
                          fontSize: 10,
                          fontWeight: focused ? '500' : 'normal',
                          width: 40,
                          justifyContent: 'center',
                          alignItems: 'center',
                          textAlign: 'center',
                        }}>
                        {route.title}
                      </XText>
                    );
                  },
                },
                messages: {
                  icon: ({focused}) => (
                    <FastImage
                      source={
                        focused
                          ? require('@xlb/common/src/assets/icons/room-active.png')
                          : require('@xlb/common/src/assets/icons/room.png')
                      }
                      style={{
                        width: 20,
                        height: 20,
                        marginBottom: 4,
                      }}></FastImage>
                  ),
                  badge: ({}) => (
                    <View style={{marginRight: 20, marginTop: 5}}>
                      <Badge count={unreadLen} max={99}></Badge>
                    </View>
                  ),
                  label: ({focused, route}) => {
                    return (
                      <XText
                        style={{
                          color: focused
                            ? themeMaps[themeName]?.color
                            : '#86909C',
                          fontSize: 10,
                          fontWeight: focused ? '500' : 'normal',
                          width: 40,
                          justifyContent: 'center',
                          alignItems: 'center',
                          textAlign: 'center',
                        }}>
                        {route.title}
                      </XText>
                    );
                  },
                },
                board: {
                  icon: ({focused}) => (
                    <FastImage
                      source={
                        focused
                          ? iconMaps['group-active'][currentTheme] ||
                            iconMaps['group-active']['default']
                          : iconMaps['group']
                      }
                      style={{
                        width: 20,
                        height: 20,
                        marginBottom: 4,
                      }}></FastImage>
                  ),
                  label: ({focused, route}) => {
                    return (
                      <XText
                        style={{
                          color: focused
                            ? themeMaps[themeName]?.color
                            : '#86909C',
                          fontSize: 10,
                          fontWeight: focused ? '500' : 'normal',
                          width: 40,
                          justifyContent: 'center',
                          alignItems: 'center',
                          textAlign: 'center',
                        }}>
                        {route.title}
                      </XText>
                    );
                  },
                },
              }}
              indicatorStyle={{
                width: tabItemWidth,
                height: 0,
                backgroundColor: colors.primary,
                top: -3,
              }}
              onTabPress={onTabPress}
            />
          );
        }}
      />
      <View style={{display: 'none'}}>
        <MqttWrap></MqttWrap>
      </View>
    </>
  );
}

export default React.memo(HomeTabs);
