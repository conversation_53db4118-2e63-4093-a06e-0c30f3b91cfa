import React, { useEffect, useMemo, useRef, useState } from 'react'
import {
  DeviceEventEmitter,
  ImageBackground,
  NativeModules,
  Platform,
  StatusBar,
  Text,
  View,
  StyleSheet,
  Image,
  Pressable,
  TouchableHighlight,
  ScrollView,
  Animated,
} from 'react-native'
import { authModel, roleAuth } from '@xlb/business-base/src/models/auth'
import { commonStyles, Header, Item, XText, Files, ProText } from '@xlb/common/src/components'
import useAliCloudPush from '@xlb/common/src/hooks/useAliCloudPush'
import WebViewTemplate from './components/WebViewTemplate'
import { useIsFocused, useNavigation } from '@react-navigation/native'
import XlbIcon from '@xlb/common/src/assets/iconfont'
import { colors, normalize } from '@xlb/common/src/config/theme'
import useStausBarHeight from '@xlb/business-base/src/hook/useStatusBarHeight'
// import Swiper from '@xlbjs/react-native-swiper'
import { Cell, Dialog, NumberInput, Button, Blank, Space, Tag, Badge, Flex, Row, Col } from '@fruits-chain/react-native-xiaoshu'
import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'
import { useDebounceEffect, useRequest } from 'ahooks'
import dayjs from 'dayjs'
import { businessDeptListType } from '@xlb/common/src/config/utils'
import { background, position } from 'native-base/lib/typescript/theme/styled-system'
import { XlbIconfont, XlbBadge, TOKEN, XlbText, XlbButton } from '@xlb/components-rn'
// import { AnnouncementRoutes } from '@xlb/business-erp/src/screens/storeAnnouncement/routes'

import FastImage from 'react-native-fast-image'
import { ScreenWidth } from '@dplus/rn-ui'
// import { badgeColor } from '@xlb/business-erp/src/screens/product/confirmInOrderNew/define'
import flex from '@xlb/common/src/styles/utilities/flex'
// import { center } from 'turf'
import LinearGradient from 'react-native-linear-gradient'
const { height } = useStausBarHeight()

/**
 * 首页
 * @constructor
 */
const Home: React.FC = ({ customerData }) => {
  const noticeTypeEum = {
    INFORM: '通知类',
    POLICY: '政策类',
    EXECUTE: '执行类',
  }

  const navigation = useNavigation()
  const userInfos = authModel?.state?.userInfos
  const isFocused = useIsFocused()

  const [noticeDate, setNoticeDate] = useState<any>([]) //公告信息
  const [readNotice, setReadNotice] = useState<any>([]) //未读公告信息
  const system = !!authModel?.state?.userInfos.supplier
  const [dialogShow, setDialogShow] = useState(false) // 弹窗是否展示
  const [dialogMessage, setDialogMessage] = useState<any>(null) // 弹窗是否展示
  const [webViewHeight, setWebViewHeight] = useState(100) // webView 高度
  console.log('customerData', customerData)
  const scrollX = useRef(new Animated.Value(0)).current
  let noticeData = useMemo(() => {
    return noticeDate && noticeDate.length ? noticeDate : []
  }, [noticeDate])

  /**
   * 获取已读公告
   * @param type
   * @returns
   */
  const getNotice = async (type: any) => {
    // 首页会出现两个loading
    // Loading.show()
    const res = await ErpHttp.post<any>(`/${type}/hxl.${type}.usernotice.page`, {
      order_mode: 1,
    })
    if (res?.code == 0) {
      setNoticeDate(res.data.content)
      return Promise.resolve(res.data.content)
    }
    return Promise.resolve([])
  }

  /**
   * 获取未读公告
   * @param type
   * @returns
   */
  const getReadNotice = async (type: any) => {
    // 首页会出现两个loading
    // Loading.show()
    const res = await ErpHttp.post<any>(`/${type}/hxl.${type}.usernotice.page`, {
      read: false,
      order_mode: 1,
      // orders: [{ direction: 'DESC', property: 'create_time' }],
    })
    if (res?.code == 0) {
      setReadNotice(res.data.content)
      // 发布一个事件
      DeviceEventEmitter.emit('updateNotice', res.data.content)
      return Promise.resolve(res.data.content)
    }
    return Promise.resolve([])
  }

  // 获取详情页数据
  const getDetail = async (type: any, params: any) => {
    const res = await ErpHttp.post<any>(`/${type}/hxl.${type}.usernotice.read`, params)
    if (res?.code === 0 && res?.data) {
      setDialogMessage(res.data)
      setDialogShow(true)
    } else {
      setDialogShow(false)
      setDialogMessage(null)
    }
  }
  /**
   * 轮询获取未读公告
   */
  const getNoticeFn = () => {
    return getNotice(`${system ? 'scm' : 'erp'}`)
  }
  const { data, run, cancel } = useRequest(getNoticeFn, {
    pollingInterval: 1000 * 60 * 10,
    pollingErrorRetryCount: 3,
  })
  // 未读中选择第一个强制数据。
  useEffect(() => {
    // 获取强制提醒的数据
    const currentData = data?.find((d: any) => (d.force_remind || d.must_read_remind) && d.read != true)

    if (currentData) {
      getDetail(`${system ? 'scm' : 'erp'}`, {
        company_id: currentData.company_id,
        id: currentData.id,
        operator_store_id: authModel?.state?.userInfos?.store?.id,
      })
    }
  }, [data])

  useEffect(() => {
    const NoticeRefresh = DeviceEventEmitter.addListener('NoticeRefresh', () => {
      getNotice(`${system ? 'scm' : 'erp'}`)
      // getReadNotice(`${system ? 'scm' : 'erp'}`)
    })
    return () => {
      NoticeRefresh.remove()
    }
  }, [])

  useDebounceEffect(
    () => {
      getNotice(`${system ? 'scm' : 'erp'}`)
      // getReadNotice(`${system ? 'scm' : 'erp'}`)
    },
    [],
    {
      wait: 1000,
    }
  )

  return (
    <>
      {noticeData && noticeData.length && false ? (
        <>
          <View
            style={{
              borderRadius: 8,
              overflow: 'hidden',
              height: normalize(78),
              // marginBottom: normalize(12),
              backgroundColor: '#fff',
            }}
          >
            <Swiper
              containerStyle={{ flex: 1 }}
              autoplay={true}
              autoplayTimeout={5}
              showsPagination={true}
              paginationStyle={{ marginBottom: normalize(-18) }}
              dotStyle={{ height: 4, width: 4 }}
              activeDotStyle={{ height: 4, width: 8 }}
              // dot={<View style={{ height: 40, width: 40, backgroundColor: 'red' }}></View>}
              dotColor={TOKEN.primary_1}
              activeDotColor={TOKEN.primary_5}
            >
              {noticeDate
                .filter((e, i) => i < 4)
                .map((_, i) => (
                  <Pressable
                    onPress={() => {
                      navigation.navigate('RemoteAppBase.Details', { id: _.id, source: 'home' })
                    }}
                  >
                    <Flex align="start" style={styles.notice}>
                      <LinearGradient
                        colors={['#CCEEFF', '#E0EBFF']} //渐变
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                        style={{
                          marginLeft: 4,
                          marginRight: 12,
                          // backgroundColor: TOKEN.primary_05,
                          width: 28,
                          height: 28,
                          justifyContent: 'center',
                          alignItems: 'center',
                          borderRadius: 14,
                        }}
                      >
                        <FastImage
                          source={
                            !_?.read ? require('@xlb/common/src/assets/icons/icon-notice-gif.gif') : require('@xlb/common/src/assets/icons/icon-notice.png')
                          }
                          style={{ width: normalize(20), height: normalize(20) }}
                        ></FastImage>
                      </LinearGradient>

                      <View style={{ marginBottom: TOKEN.space_1, flexDirection: 'column', justifyContent: 'center', flex: 1, marginTop: 8 }}>
                        <XlbText font_size_5 grey_10 semiBold numberOfLines={1}>
                          {_.title}
                        </XlbText>

                        <View style={{ flexDirection: 'row', marginTop: 5 }}>
                          {_?.type && (
                            <XlbText
                              font_size_2
                              grey_10
                              style={{
                                borderWidth: 0.5,
                                borderRadius: 2,
                                paddingHorizontal: 4,
                                paddingVertical: 2,
                                borderColor: TOKEN.grey_15,
                                // width: 85,
                                marginRight: 8,
                              }}
                            >
                              {noticeTypeEum[_?.type] ?? _?.type ?? ''}
                            </XlbText>
                          )}
                          {_?.level == 0 && (
                            <XlbText
                              font_size_2
                              red_10
                              style={{ borderWidth: 0.5, borderRadius: 2, paddingHorizontal: 4, paddingVertical: 2, borderColor: TOKEN.red_5, width: 35 }}
                            >
                              {'置顶'}
                            </XlbText>
                          )}
                        </View>
                      </View>
                      {!_?.read ? <Badge dot color={TOKEN.red_10} style={{ marginRight: TOKEN.space_1, marginLeft: TOKEN.space_5 }} /> : null}
                      <XlbIconfont name="youjiantou" size={16} color={TOKEN.grey_1}></XlbIconfont>
                    </Flex>
                  </Pressable>
                ))}
            </Swiper>

            {/* <ScrollView
              pagingEnabled
              // horizontal
              style={{
                width: ScreenWidth - normalize(24),
                height: normalize(70),
              }}
              showsHorizontalScrollIndicator={false}
              showsVerticalScrollIndicator={false}
              onScroll={Animated.event(
                [
                  {
                    nativeEvent: {
                      contentOffset: {
                        x: scrollX,
                      },
                    },
                  },
                ],
                { useNativeDriver: false }
              )}
              scrollEventThrottle={1}
            >
       
            </ScrollView> */}
            {/* <View style={styles.indicatorContainer}>
              {noticeData
                .filter((e, i) => i < 4)
                .map((image, imageIndex) => {
                  const width = scrollX.interpolate({
                    inputRange: [
                      (ScreenWidth - normalize(24)) * (imageIndex - 1),
                      (ScreenWidth - normalize(24)) * imageIndex,
                      (ScreenWidth - normalize(24)) * (imageIndex + 1),
                    ],
                    outputRange: [4, 8, 4],
                    extrapolate: 'clamp',
                  })
                  const backgroundColor = scrollX.interpolate({
                    inputRange: [
                      (ScreenWidth - normalize(24)) * (imageIndex - 1),
                      (ScreenWidth - normalize(24)) * imageIndex,
                      (ScreenWidth - normalize(24)) * (imageIndex + 1),
                    ],
                    outputRange: ['rgba(26, 106, 255, 0.10)', TOKEN.primary_5, 'rgba(26, 106, 255, 0.10)'],
                    extrapolate: 'clamp',
                  })
                  return (
                    <Animated.View
                      key={imageIndex}
                      style={[
                        styles.normalDot,
                        { width, backgroundColor },
                        // {
                        //   backgroundColor: width == 16 ? '#fff' : 'rgba(255,255,255,0.3)',
                        // },
                      ]}
                    />
                  )
                })}
            </View> */}
          </View>
        </>
      ) : (
        <Item
          containerStyle={{
            paddingHorizontal: 0,
            paddingVertical: 0,
            borderRadius: 8,
            marginTop: 0,
            marginHorizontal: 0,
            marginBottom: normalize(0),
          }}
        >
          <View style={styles.item}>
            <>
              <View style={styles['item-hd']}>
                <Image source={require('@xlb/common/src/assets/icons/icon-notice.png')} style={{ width: normalize(24), height: normalize(24) }}></Image>
              </View>

              <View style={styles['item-bd']}>
                <Text style={styles['item-bd__text']}>暂无公告</Text>
              </View>
              <View style={styles['item-ft']}></View>
            </>
          </View>
        </Item>
      )}

      <Dialog.Keyboard
        visible={dialogShow}
        showConfirmButton={false}
        showCancelButton={false}
        closeOnPressOverlay={false}
        showClose={false}
        style={{ backgroundColor: 'transparent', paddingHorizontal: 0, paddingVertical: 0 }}
      >
        <View style={{ backgroundColor: '#fff', borderRadius: 12, marginTop: 50 }}>
          <View style={{ position: 'absolute', top: -80, flex: 1, width: '100%', padding: 0 }}>
            <Image source={require('./bg.png')} style={{ resizeMode: 'cover', flex: 1, height: 200, width: '100%' }}></Image>
          </View>
          <View style={{ justifyContent: 'center', overflow: 'hidden' }}>
            <Space style={{ paddingLeft: 20, paddingRight: 20, alignItems: 'center', marginTop: 18 }}>
              <Text style={{ fontSize: 17, color: '#000', fontWeight: '800', textAlign: 'center' }}>{dialogMessage?.title}</Text>
            </Space>
            <Space
              direction="horizontal"
              align="center"
              justify="space-between"
              style={{ paddingLeft: 20, paddingRight: 20, paddingBottom: 12, marginTop: 10 }}
            >
              <Space direction="horizontal" align="center" gapHorizontal={2}>
                <Tag type="hazy" color={'#FF2121'}>
                  {dialogMessage?.type}
                </Tag>
                {dialogMessage?.files?.length ? (
                  <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                    <XlbIcon name={'fujian'} size={normalize(16)} color={'rgba(97, 99, 103, 1)'}></XlbIcon>
                    <Text style={{ fontSize: 12, color: '#86909C' }}>{dialogMessage?.files?.length}</Text>
                  </View>
                ) : null}
              </Space>
              <Space direction="horizontal" align="center">
                {dialogMessage?.create_business_dept ? (
                  <XText size12 style={{ color: 'rgba(30, 33, 38, 0.70)' }}>
                    {businessDeptListType[dialogMessage.create_business_dept]}
                  </XText>
                ) : null}
                {dialogMessage?.create_time && (
                  <XText
                    size13
                    style={{
                      color: 'rgba(30, 33, 38, 0.45)',
                    }}
                  >
                    {dayjs(dialogMessage?.create_time).format('YYYY-MM-DD')}
                  </XText>
                )}
              </Space>
            </Space>

            <ScrollView
              bounces={false}
              keyboardShouldPersistTaps="handled"
              contentContainerStyle={{ flexGrow: 1 }}
              overScrollMode="never"
              style={{
                paddingLeft: 20,
                paddingRight: 20,
                paddingBottom: 12,
                maxHeight: 240,
                minHeight: 150,
                overflow: 'scroll',
                backgroundColor: 'transparent',
              }}
            >
              <WebViewTemplate
                bounces={false}
                scrollEnabled={false}
                style={{ height: webViewHeight, backgroundColor: 'transparent' }}
                originWhitelist={['*']}
                data={dialogMessage?.content}
                androidHardwareAccelerationDisabled
                javaScriptEnabled={true}
                onMessage={(event) => setWebViewHeight(Number(event.nativeEvent.data))}
              ></WebViewTemplate>
              {!!dialogMessage?.files?.length && (
                <View style={{ backgroundColor: 'transparent', paddingBottom: 0, paddingTop: 0 }}>
                  <Text style={{ fontSize: 12, color: '#86909C', paddingTop: 12, marginBottom: normalize(8) }}>{dialogMessage?.files?.length}个附件</Text>
                  <Files
                    containerStyle={{
                      borderRadius: normalize(8),
                      paddingHorizontal: normalize(12),
                      paddingVertical: normalize(8),
                    }}
                    nameComponent={(index) => {
                      return (
                        <ProText numberOfLines={1} style={{ color: '#26272A' }}>
                          {dialogMessage?.files?.[index]?.name}
                        </ProText>
                      )
                    }}
                    data={dialogMessage?.files?.map((v: any) => v.url)}
                  />
                </View>
              )}
            </ScrollView>

            <View style={{ backgroundColor: '#fff', padding: 20, paddingTop: 12, paddingBottom: 24, borderRadius: 12 }}>
              <Row gap={12}>
                <Col span={12}>
                  <Button text="稍后再读" color={'#1F2126'} type="outline" onPress={() => setDialogShow(false)} />
                </Col>
                <Col span={12}>
                  <Button
                    text="公告详情"
                    type="primary"
                    onPress={() => {
                      setDialogShow(false)
                      ;(navigation as any).navigate('RemoteAppBase.Details', { ...dialogMessage, id: dialogMessage.id })
                    }}
                  />
                </Col>
              </Row>
            </View>
          </View>
        </View>
      </Dialog.Keyboard>
    </>
  )
}

const styles = StyleSheet.create({
  notice: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    width: ScreenWidth - normalize(24),
    height: normalize(70),
    alignItems: 'center',
  },
  normalDot: {
    height: 4,
    width: 4,
    borderRadius: 4,
    backgroundColor: '#fff',
    marginHorizontal: 2,
  },
  indicatorContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 10,
    left: 0,
  },

  item: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  'item-hd': {
    width: normalize(24),
    height: normalize(24),
    marginRight: 8,
  },
  'item-bd': {
    flex: 1,
    marginRight: normalize(12),
    height: normalize(24),
  },
  'item-bd__text': {
    color: '#1F2126',
    fontWeight: '600',
    height: normalize(24),
    lineHeight: normalize(24),
    fontSize: normalize(15),
  },
  'item-bd__text--sub': {
    color: 'rgba(30,33,38,0.7)',
    fontWeight: '500',
    fontSize: normalize(14),
  },
  'item-ft': {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  'item-ft__icon': {
    marginLeft: 2,
    width: normalize(12),
    height: normalize(12),
  },
  'item-ft__text': {
    color: 'rgba(30,33,38,0.45)',
    fontSize: normalize(13),
  },
  'item-ft__dot': {
    width: normalize(6),
    height: normalize(6),
    marginLeft: 4,
    backgroundColor: '#FA3232',
    borderRadius: 6,
  },
})

export default Home
