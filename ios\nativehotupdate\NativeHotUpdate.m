//
//  NativeHotUpdate.m
//  a608
//
//  Created by 赵盛 on 2025/7/13.
//
#import <React/RCTBridgeModule.h>

@interface RCT_EXTERN_MODULE(NativeHotUpdate, NSObject)

// hasUpdate 返回 Promise<Bool>，需要写成带 resolve/reject 的形式
RCT_EXTERN_METHOD(hasUpdate:(NSString *)remoteVersion
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

// runDownload 返回 Promise<void>，执行完毕resolve，不返回值
RCT_EXTERN_METHOD(runDownload:(NSString *)remoteVersion
                  downloadUrl:(NSString *)downloadUrl
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

// installApk 返回 Promise<Bool>
RCT_EXTERN_METHOD(installApk:(NSString *)filePath
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

// getCurrentVersion 返回 Promise<String>
RCT_EXTERN_METHOD(getCurrentVersion:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

@end
