//
//  MABuniessEditDrawViewController.m
//  xlb
//
//  Created by 莱昂纳多·迪卡普里奥 on 2025/1/6.
//

#import "MABuniessEditDrawViewController.h"
#import <AMapNaviKit/MAMapKit.h>
#import "MAUserAnnotationView.h"
#import "MALocalAnnotation.h"
#import "MALocalAnnotationView.h"
#import "UIView+Common.h"
#import "MALoadWaveView.h"
#import "MAHttpTool.h"
#import "UIView+Toast.h"
#import "NSDictionary+Common.h"
#import "NSArray+Common.h"
#import <AMapFoundationKit/AMapFoundationKit.h>
#import <AMapNaviKit/MAMapKit.h>
#import <AMapLocationKit/AMapLocationKit.h>
#import "MACreatBuniessPolygon.h"
#import "MACreatBuniessPolygonRender.h"
#import "MACreatBuniessPolyline.h"
#import "MACreatBuniessDrawBoardView.h"
#import "MACreatBuniessView.h"
#import "MACreatBuniessPointAnnotation.h"
#import "MACreatBuniessPointAnnView.h"
#import "MATopToastView.h"
#import "MATopNotiflyView.h"
#import "MABussinessPolygon.h"
#import "MABussinessPolygonRenderer.h"
#import "MACenterBuniessAnnView.h"
#import "MACenterBuniessAnnotation.h"
#import "MALoadWaveView.h"

// 判断字符串是否为空
#define BCStringIsEmpty(str) ([str isKindOfClass:[NSNull class]] || str == nil || ![str isKindOfClass:[NSString class]] || [str length]< 1 ? YES : NO || [str isEqualToString:@"null"] || [str isEqualToString:@"<null>"] || [str isEqualToString:@"(null)"])
// 判断数组是否为空
#define BCArrayIsEmpty(array) (array == nil || [array isKindOfClass:[NSNull class]] || ![array isKindOfClass:[NSArray class]] || [array count] == 0)
// 判断字典是否为空
#define BCDictIsEmpty(dic) (dic == nil || [dic isKindOfClass:[NSNull class]] || ![dic isKindOfClass:[NSDictionary class]] || dic.allKeys.count == 0)

#define BCWidth   [UIScreen mainScreen].bounds.size.width
#define BCHeight  [UIScreen mainScreen].bounds.size.height
#define COLOR(R, G, B) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:1]
#define ACOLOR(R, G, B,A) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:A]

#ifdef DEBUG
#define DDLog(format, ...) printf("class: <%p %s:(%d) > method: %s \n%s\n", self, [[[NSString stringWithUTF8String:__FILE__] lastPathComponent] UTF8String], __LINE__, __PRETTY_FUNCTION__, [[NSString stringWithFormat:(format), ##__VA_ARGS__] UTF8String] )
#else
#define DDLog(format, ...)
#endif

#define kMapCoreAnimationDuration 0.5

@interface MABuniessEditDrawViewController ()<MAMapViewDelegate,MACreatBuniessDelegate,MADrawBoardDelegate,CAAnimationDelegate>
@property (nonatomic, assign) CGFloat heightTop;//导航度高度
@property (nonatomic, assign) CGFloat heightBottom;//底部安全区高度
@property (nonatomic, strong) MAMapView *mapView;//高德地图
@property (nonatomic, strong) MAUserAnnotationView *userLocationAnnotationView;//当前用户定位蓝点
@property (nonatomic, assign) CLLocationCoordinate2D userPostion;//记录用户定位坐标
@property (nonatomic, strong) MALoadWaveView *loadingView;//加载

@property (nonatomic, strong) MACreatBuniessView *creatBuniessView;//创建商圈的view
@property (nonatomic, strong) UIButton *closeBuniessButton;//关闭商圈按钮
@property (nonatomic, strong) UIView *closeBuniessView;//关闭商圈按钮
@property (nonatomic, strong) UIImageView *creatBuniessIM;//定点绘制时创建商圈大头针
@property (nonatomic, strong) NSMutableArray *buniessPointCoordinateArray;//保存创建商圈经纬度,提交给接口用
@property (nonatomic, strong) NSMutableArray *buniessPointAnnArray;//保存创建商圈大头针
@property (nonatomic, strong) MACreatBuniessPolygon *creatPolygon;//商圈多边形
@property (nonatomic, strong) NSMutableArray *buniessPolylineArray;//保存创建商圈线段
@property (nonatomic, assign) BOOL toastShow;//是否提示中
@property (nonatomic, strong) MACreatBuniessDrawBoardView *drawBoardView;//创建商圈画板

@property (nonatomic, strong) MATopToastView *statusView;//加载
@property (nonatomic, strong) MATopNotiflyView *notiflyView;//加载
@property (nonatomic, strong) UIImageView *locationIM;//定位

@property (nonatomic, strong) NSMutableArray *buniessPolygonArr;//商圈数据
@property (nonatomic, strong) NSMutableArray *pointPolygonArr;//商圈数据
@property (nonatomic, strong) MALoadWaveView *waveView;//加载
@end

@implementation MABuniessEditDrawViewController

- (void)viewDidLoad {
    [super viewDidLoad];
  if (@available(iOS 13.0, *)) {
    self.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
  }
  self.view.backgroundColor = [UIColor whiteColor];
  
  _heightTop = 54;
  _heightBottom = 34;
  if (@available(iOS 13.0, *)) {
    NSSet *set = [UIApplication sharedApplication].connectedScenes;
    UIWindowScene *windowScene = [set anyObject];
    if (windowScene) {
      UIWindow *window = windowScene.windows.firstObject;
    
      if (window.safeAreaInsets.top > 0) {
        _heightTop = window.safeAreaInsets.top;
        _heightBottom = window.safeAreaInsets.bottom;
      } else {
        _heightTop = 54;
        _heightBottom = 34;
      }
    }
    
  } else {
    UIWindow *window = [[UIApplication sharedApplication].windows firstObject];
    if (window.safeAreaInsets.top > 0) {
      _heightTop = window.safeAreaInsets.top;
      _heightBottom = window.safeAreaInsets.bottom;
    } else {
      _heightTop = 54;
      _heightBottom = 34;
    }
  }
  
  self.buniessPointAnnArray = [NSMutableArray array];
  self.buniessPointCoordinateArray = [NSMutableArray arrayWithArray:self.pointArray];
  self.buniessPolylineArray = [NSMutableArray array];
 
  self.buniessPolygonArr = [NSMutableArray array];
  self.pointPolygonArr = [NSMutableArray array];
  
  [self initToolBar];
  [self initBackUserView];
  [self initMap];
  

}


#pragma mark 初始化导航栏返回按钮
- (void)initToolBar{
  
  UIView *closeBuniessView = [[UIView alloc] initWithFrame:CGRectMake(0,0,BCWidth,self.heightTop + 44)];
  [self.view addSubview:closeBuniessView];
  
  CAGradientLayer *gl = [CAGradientLayer layer];
  gl.frame = CGRectMake(0,0,BCWidth,self.heightTop + 44);
  gl.startPoint = CGPointMake(0.5, 0);
  gl.endPoint = CGPointMake(0.5, 1);
  gl.colors = @[
    (__bridge id)[UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1].CGColor,
    (__bridge id)[UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:0].CGColor,];
  gl.locations = @[@(0), @(1.0f)];
  [closeBuniessView.layer addSublayer:gl];
  //  返回按钮
  UIButton *closeBuniessButton = [UIButton buttonWithType:UIButtonTypeCustom];
  closeBuniessButton.frame = CGRectMake(14, self.heightTop + 4, 40, 40);
  closeBuniessButton.backgroundColor = [UIColor whiteColor];
  [closeBuniessButton addTarget:self action:@selector(clickCloseBuniessView) forControlEvents:UIControlEventTouchUpInside];
  [closeBuniessButton setImage:[UIImage imageNamed:@"nav_back"] forState:UIControlStateNormal];
  [closeBuniessButton setImageEdgeInsets:UIEdgeInsetsMake(8, 8, 8, 8)];
  closeBuniessButton.imageView.contentMode = UIViewContentModeScaleAspectFit;
  closeBuniessButton.layer.cornerRadius = 8;
  if (@available(iOS 13.0, *)) {
    closeBuniessButton.layer.cornerCurve = kCACornerCurveContinuous;
  }
  closeBuniessButton.layer.borderWidth = 0.5;
  closeBuniessButton.layer.borderColor = [UIColor colorWithRed:229/255.0 green:230/255.0 blue:234/255.0 alpha:1].CGColor;
  [closeBuniessView addSubview: closeBuniessButton];
}

- (void)clickCloseBuniessView{
  
  [self.creatBuniessView hideModal];
  self.creatBuniessView.delegate = nil;
  self.creatBuniessView = nil;
  [self.creatBuniessIM removeFromSuperview];
  self.creatBuniessIM = nil;
  
  [self.drawBoardView hideModal];
  self.drawBoardView.delegate = nil;
  self.drawBoardView = nil;
  [self.mapView removeAnnotations:self.buniessPointAnnArray];
  [self.mapView removeOverlays:self.buniessPolylineArray];
  [self.mapView removeOverlay:self.creatPolygon];
  
  self.creatPolygon = nil;
  [self.buniessPolylineArray removeAllObjects];
  [self.buniessPointAnnArray removeAllObjects];
  [self.buniessPointCoordinateArray removeAllObjects];
  [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark 回到当前定位按钮
- (void)initBackUserView{
  
  UIView *backView = [[UIView alloc] init];
  backView.frame = CGRectMake(BCWidth - 52,170,40,40);
  backView.layer.backgroundColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1].CGColor;
  backView.layer.cornerRadius = 8;
  if (@available(iOS 13.0, *)) {
    backView.layer.cornerCurve = kCACornerCurveContinuous;
  }
  backView.layer.shadowColor = [UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.14].CGColor;
  backView.layer.shadowOffset = CGSizeMake(0,1);
  backView.layer.shadowOpacity = 1;
  backView.layer.shadowRadius = 4;
  [self.view addSubview:backView];
  
  
  UIButton *backButton = [UIButton buttonWithType:UIButtonTypeCustom];
  backButton.frame = CGRectMake(0, 0, 40 , 40);

  self.locationIM = [[UIImageView alloc] initWithFrame:CGRectMake(9, 9, 22, 22)];
  self.locationIM.image = [UIImage imageNamed:@"navtargeticon"];
  [backButton addSubview:self.locationIM];
  
  
  [backButton addTarget:self action:@selector(clickBack:) forControlEvents:UIControlEventTouchUpInside];
  [backView addSubview:backButton];
  
}

#pragma mark 点击回到当前定位
- (void)clickBack:(UIButton *)button{
 
  
  button.selected = !button.selected;
  button.userInteractionEnabled = NO;
  
  if (button.selected) {//回到当前定位
   
    [self.mapView setCenterCoordinate:self.userPostion animated:YES];
    [UIView animateWithDuration:0.25 animations:^{
      self.locationIM.transform = CGAffineTransformMakeScale(0.01, 0.01);
    } completion:^(BOOL finished) {
      self.locationIM.image = [UIImage imageNamed:@"back_user"];
      [UIView animateWithDuration:0.25 animations:^{
        self.locationIM.transform = CGAffineTransformIdentity;
      } completion:^(BOOL finished) {
        button.userInteractionEnabled = YES;
      }];
    }];
    
  } else {//回到目的地定位
    
    if (self.creatPolygon) {
      [self.mapView showOverlays:@[self.creatPolygon] edgePadding:UIEdgeInsetsMake(200, 100, 200, 100) animated:YES];
    }
    
    [UIView animateWithDuration:0.25 animations:^{
      self.locationIM.transform = CGAffineTransformMakeScale(0.01, 0.01);
    } completion:^(BOOL finished) {
      self.locationIM.image = [UIImage imageNamed:@"navtargeticon"];
      [UIView animateWithDuration:0.25 animations:^{
        self.locationIM.transform = CGAffineTransformIdentity;
      } completion:^(BOOL finished) {
        button.userInteractionEnabled = YES;
      }];
    }];
    
  }
}

#pragma mark 初始化地图
- (void)initMap{
  _mapView = [[MAMapView alloc] initWithFrame:self.view.bounds];
  _mapView.rotateCameraEnabled = NO;
  _mapView.showsScale = NO;
  _mapView.rotateEnabled = NO;
  _mapView.showsCompass = NO;
  _mapView.delegate = self;
  _mapView.mapType = MAMapTypeStandard;
  _mapView.zoomingInPivotsAroundAnchorPoint = YES;
  _mapView.customizeUserLocationAccuracyCircleRepresentation = YES;
  _mapView.userTrackingMode = MAUserTrackingModeFollow;
  _mapView.zoomLevel = 15.5;
  _mapView.showsUserLocation = YES;
  _mapView.distanceFilter = kCLLocationAccuracyHundredMeters;
  [self.view insertSubview:_mapView atIndex:0];
  
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    CLLocationCoordinate2D commonPolylineCoords[self.pointArray.count];
    for (int i = 0; i < self.pointArray.count; i ++) {
      
      NSDictionary *dic = [self.pointArray objectAtIndexCheck:i];
      CLLocationCoordinate2D coor = CLLocationCoordinate2DMake([[dic objectForKeyNil:@"latitude"] floatValue], [[dic objectForKeyNil:@"longitude"] floatValue]);
      commonPolylineCoords[i] = coor;
      
      if (self.isFromBuniessDetail) {//如果从商圈详情进来
        continue;
      }
      
      //    显示大头针小圆点
      MACreatBuniessPointAnnotation *ann = [[MACreatBuniessPointAnnotation alloc] init];
      ann.coordinate = coor;
      [self.mapView addAnnotation:ann];
      [self.buniessPointAnnArray addObject:ann];
      
      
//      显示线段
      if (i < self.pointArray.count - 1) {
        CLLocationCoordinate2D commonPolylines[2];
        NSDictionary *dic1 = [self.pointArray objectAtIndexCheck:i + 1];
        commonPolylines[0].latitude = [[dic objectForKeyNil:@"latitude"] floatValue];
        commonPolylines[0].longitude = [[dic objectForKeyNil:@"longitude"] floatValue];
        commonPolylines[1].latitude = [[dic1 objectForKeyNil:@"latitude"] floatValue];
        commonPolylines[1].longitude = [[dic1 objectForKeyNil:@"longitude"] floatValue];
        
        MACreatBuniessPolyline *commonPolyline = [MACreatBuniessPolyline polylineWithCoordinates:commonPolylines count:2];
        commonPolyline.endPoint = MAMapPointForCoordinate(commonPolylines[1]);
        commonPolyline.startPoint = MAMapPointForCoordinate(commonPolylines[0]);
        commonPolyline.pointId = i + 1;
        commonPolyline.lineType = 0;
        [self.mapView addOverlay:commonPolyline];
        [self.buniessPolylineArray addObject:commonPolyline];
      }
    

    }
    
    
//    显示已经绘制的商圈
    self.creatPolygon = [MACreatBuniessPolygon polygonWithCoordinates:commonPolylineCoords count:self.pointArray.count];
    if (self.isFromBuniessDetail) {
      self.creatPolygon.lineType = 1;
    } else {
      self.creatPolygon.lineType = 0;
    }
    [self.mapView addOverlay:self.creatPolygon];
    [self.mapView showOverlays:@[self.creatPolygon] edgePadding:UIEdgeInsetsMake(200, 100, 200, 100) animated:NO];
    
    if (self.isFromBuniessDetail) {//如果从商圈详情进来
      return;
    }
    
    //      显示定点大头针
    [self.view addSubview:self.creatBuniessIM];
    self.creatBuniessIM.hidden = NO;
    
    //  显示绘制商圈自定义组件
    [self.creatBuniessView showModal];
    self.creatBuniessView.moveV.hidden = YES;
    self.creatBuniessView.drawButton.hidden = YES;
    [self.creatBuniessView updatePointArr:self.buniessPointCoordinateArray];
    
    //    加载已经生成的可开店区域
    for (int i = 0; i < self.openStoreArray.count; i ++) {
      NSDictionary *itemDic = [self.openStoreArray objectAtIndexCheck:i];
      if (![[itemDic objectNilForKey:@"draw_type"] isEqualToString:@"DRAW_POINT"]) {
        continue;
      }
      
      NSArray *areaRange = [itemDic objectForKeyNil:@"area_range"];
      if (!BCArrayIsEmpty(areaRange)) {
        NSDictionary *dic = [areaRange firstObject];
        CLLocationCoordinate2D currentCoordinate = CLLocationCoordinate2DMake([[dic objectForKeyNil:@"latitude"] floatValue], [[dic objectForKeyNil:@"longitude"] floatValue]);
        MALocalAnnotation *addPointAnn = [[MALocalAnnotation alloc] init];
        addPointAnn.coordinate = currentCoordinate;
        addPointAnn.pointName = [itemDic objectForKeyNil:@"store_open_area_name"];
        addPointAnn.pointLevel = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"priority"]];
        addPointAnn.pointType = MarkerOpenStorePointType;
        [self.mapView addAnnotation:addPointAnn];
      }
      
    }
   
    
//    加载其他商圈
    [self getAllBuniess];
    
//    加载地图动画
//    [self.mapView addAnimationWith:nil
//                     zoomAnimation:[self zoomLevelAnimation]
//                   rotateAnimation:[self rotationDegreeKey]
//             cameraDegreeAnimation:[self cameraDegreeAnimation]];
    
    
    

  });
  
 
}

- (void)getAllBuniess{
  
  NSMutableDictionary * dict = [NSMutableDictionary dictionary];
  dict[@"org_id"] = self.orgId;
  dict[@"level"] = [NSString stringWithFormat:@"%0.1f",self.mapView.zoomLevel];//地图缩放层级
  MAMapRect visibleRect = self.mapView.visibleMapRect;
  CLLocationCoordinate2D topLeft = MACoordinateForMapPoint(visibleRect.origin);
  CLLocationCoordinate2D topRight = MACoordinateForMapPoint(MAMapPointMake(MAMapRectGetMaxX(visibleRect), MAMapRectGetMinY(visibleRect)));
  CLLocationCoordinate2D bottomLeft = MACoordinateForMapPoint(MAMapPointMake(MAMapRectGetMinX(visibleRect), MAMapRectGetMaxY(visibleRect)));
  CLLocationCoordinate2D bottomRight = MACoordinateForMapPoint(MAMapPointMake(MAMapRectGetMaxX(visibleRect), MAMapRectGetMaxY(visibleRect)));
  dict[@"top_left"] = [NSDictionary dictionaryWithObjectsAndKeys:[NSString stringWithFormat:@"%f",topLeft.latitude],@"latitude",[NSString stringWithFormat:@"%f",topLeft.longitude],@"longitude", nil];//地图左上角坐标
  dict[@"top_right"] = [NSDictionary dictionaryWithObjectsAndKeys:[NSString stringWithFormat:@"%f",topRight.latitude],@"latitude",[NSString stringWithFormat:@"%f",topRight.longitude],@"longitude", nil];//地图右上角坐标
  dict[@"bottom_left"] = [NSDictionary dictionaryWithObjectsAndKeys:[NSString stringWithFormat:@"%f",bottomLeft.latitude],@"latitude",[NSString stringWithFormat:@"%f",bottomLeft.longitude],@"longitude", nil];//地图左下角坐标
  dict[@"bottom_right"] = [NSDictionary dictionaryWithObjectsAndKeys:[NSString stringWithFormat:@"%f",bottomRight.latitude],@"latitude",[NSString stringWithFormat:@"%f",bottomRight.longitude],@"longitude", nil];//地图右下角坐标
  
  __weak typeof(self)weakSelf = self;
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.arearangedilute.find" Params:dict success:^(NSDictionary *successResult) {
  
    NSArray *businessArr = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
    if (BCArrayIsEmpty(businessArr)) {
      return;
    }
    
   
    [self.mapView removeOverlays:self.buniessPolygonArr];
    [self.mapView removeAnnotations:self.pointPolygonArr];
    
    [self.buniessPolygonArr removeAllObjects];
    [self.pointPolygonArr removeAllObjects];
    
    for (int i = 0; i < businessArr.count; i ++) {
      @autoreleasepool {
        NSDictionary *dic = [businessArr objectAtIndexCheck:i];
        NSArray *item = [dic objectNilForKey:@"area_range"];
        if (BCArrayIsEmpty(item)) {
          continue;
        }
        
       NSString *currentBuniessId = [NSString stringWithFormat:@"%@",[dic objectNilForKey:@"id"]];
        if ([currentBuniessId isEqualToString:self.buniessId]) {//过滤当前一模一样的商圈
          continue;
        }
        
        //          商圈
        CLLocationCoordinate2D coordinates[item.count];
        for (int j = 0; j < item.count; j ++) {
          
          NSDictionary *dicPoint = [item objectAtIndexCheck:j];
          coordinates[j].latitude = [[NSString stringWithFormat:@"%@",[dicPoint objectNilForKey:@"latitude"]] floatValue];
          coordinates[j].longitude = [[NSString stringWithFormat:@"%@",[dicPoint objectNilForKey:@"longitude"] ] floatValue];
          
        }
        
//        商圈
        MABussinessPolygon *poly = [MABussinessPolygon polygonWithCoordinates:coordinates count:item.count];
        poly.bussinesId = 20000 + i;
        if ([[dic objectNilForKey:@"state"] isEqualToString:@"INIT"]) {//待审批
          poly.buinessType = BuinessYellowType;
        } else if ([[dic objectNilForKey:@"state"] isEqualToString:@"SECOND_AUDIT"]) {//审批通过
          poly.buinessType = BuinessGreenType;
        } else if ([[dic objectNilForKey:@"state"] isEqualToString:@"REJECT"]) {//否决
          poly.buinessType = BuinessGrayType;
        } else {//制单
          poly.buinessType = BuinessBlueType;
        }
        poly.pointId = [NSString stringWithFormat:@"%@",[dic objectNilForKey:@"id"]];
        [self.buniessPolygonArr addObject:poly];
        
       
        //          商圈大头针
        MACenterBuniessAnnotation *pointAnn = [[MACenterBuniessAnnotation alloc] init];
        pointAnn.coordinate = poly.coordinate;
        pointAnn.pointName = [dic objectNilForKey:@"name"];
        [self.pointPolygonArr addObject:pointAnn];
        
       
      }
    }
    
    [self.mapView addOverlays:self.buniessPolygonArr];
    [self.mapView addAnnotations:self.pointPolygonArr];
    
    
  } failure:^(NSString *errorResult) {
   
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
    
  }];
  
  
}

#pragma mark 地图被用户完成移动或者缩放时
- (void)mapView:(MAMapView *)mapView regionDidChangeAnimated:(BOOL)animated wasUserAction:(BOOL)wasUserAction {
  
  if (self.isFromBuniessDetail) {//如果从商圈详情进来
    return;
  }
    [self getAllBuniess];
}

- (void)animationDidStop:(CAAnimation *)anim finished:(BOOL)flag
{
   
  //      显示定点大头针
  [self.view addSubview:self.creatBuniessIM];
  self.creatBuniessIM.hidden = NO;
  
  //  显示绘制商圈自定义组件
  [self.creatBuniessView showModal];
  self.creatBuniessView.moveV.hidden = YES;
  self.creatBuniessView.drawButton.hidden = YES;
  [self.creatBuniessView updatePointArr:self.buniessPointCoordinateArray];
  
  //    加载已经生成的可开店区域
  for (int i = 0; i < self.openStoreArray.count; i ++) {
    NSDictionary *itemDic = [self.openStoreArray objectAtIndexCheck:i];
    if (![[itemDic objectNilForKey:@"draw_type"] isEqualToString:@"DRAW_POINT"]) {
      continue;
    }
    
    NSArray *areaRange = [itemDic objectForKeyNil:@"area_range"];
    if (!BCArrayIsEmpty(areaRange)) {
      NSDictionary *dic = [areaRange firstObject];
      CLLocationCoordinate2D currentCoordinate = CLLocationCoordinate2DMake([[dic objectForKeyNil:@"latitude"] floatValue], [[dic objectForKeyNil:@"longitude"] floatValue]);
      MALocalAnnotation *addPointAnn = [[MALocalAnnotation alloc] init];
      addPointAnn.coordinate = currentCoordinate;
      addPointAnn.pointName = [itemDic objectForKeyNil:@"store_open_area_name"];
      addPointAnn.pointLevel = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"priority"]];
      addPointAnn.pointType = MarkerOpenStorePointType;
      [self.mapView addAnnotation:addPointAnn];
    }
    
  }
  
}


/* 生成 地图缩放级别的 CAKeyframeAnimation. */
- (CAKeyframeAnimation *)zoomLevelAnimation
{
    CAKeyframeAnimation *zoomLevelAnimation = [[CAKeyframeAnimation alloc] init];
    zoomLevelAnimation.duration = kMapCoreAnimationDuration;
    zoomLevelAnimation.delegate = self;
    zoomLevelAnimation.values   = @[@(17), @(15.5)];
    zoomLevelAnimation.keyTimes = @[@(0.f), @(1.0f)];
   
    return zoomLevelAnimation;
}

/*生成 地图旋转角度的 CABasicAnimation. */
-(CAKeyframeAnimation *)rotationDegreeKey
{
    CAKeyframeAnimation *rotationDegreeAnimation = [[CAKeyframeAnimation alloc] init];
    rotationDegreeAnimation.duration  = kMapCoreAnimationDuration;
    rotationDegreeAnimation.values = @[@(-60.f), @(0.f)];
    rotationDegreeAnimation.keyTimes = @[@(0.f), @(1.f)];
    rotationDegreeAnimation.timingFunctions = [NSArray arrayWithObjects:
                                               [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear],
                                               nil];
    
    return rotationDegreeAnimation;
}

/* 生成 地图摄像机俯视角度的 CABasicAnimation. */
- (CAKeyframeAnimation *)cameraDegreeAnimation
{
    CAKeyframeAnimation *cameraDegreeAnimation = [[CAKeyframeAnimation alloc] init];
    cameraDegreeAnimation.duration = kMapCoreAnimationDuration;
    cameraDegreeAnimation.values = @[@(60.f), @(0.f)];
    cameraDegreeAnimation.keyTimes = @[@(0.f), @(1.f)];
    cameraDegreeAnimation.timingFunctions = [NSArray arrayWithObjects:
                                             [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear],
                                             nil];
    
    return cameraDegreeAnimation;
}

#pragma mark 获取定位更新
- (void)mapView:(MAMapView *)mapView didUpdateUserLocation:(MAUserLocation *)userLocation updatingLocation:(BOOL)updatingLocation
{
  

  
  if (!updatingLocation && self.userLocationAnnotationView != nil)
  {
    
    double degree = userLocation.heading.trueHeading - self.mapView.rotationDegree;
    self.userLocationAnnotationView.userImageView.transform = CGAffineTransformMakeRotation(degree * M_PI / 180.f );
  }
  
  if (userLocation.location && mapView.userLocationVisible) {
    _userPostion = CLLocationCoordinate2DMake(userLocation.coordinate.latitude, userLocation.coordinate.longitude);
    
  }
  
}

#pragma mark 地图大头针显示代理
- (MAAnnotationView *)mapView:(MAMapView *)mapView viewForAnnotation:(id<MAAnnotation>)annotation {
  
  if ([annotation isKindOfClass:[MAUserLocation class]]){//自定义当前定位
    
    static NSString *userLocationStyleReuseIndetifier = @"userLocationStyleReuseIndetifier";
    MAUserAnnotationView *annotationView = (MAUserAnnotationView *)[mapView dequeueReusableAnnotationViewWithIdentifier:userLocationStyleReuseIndetifier];
    if (annotationView == nil)
    {
      annotationView = [[MAUserAnnotationView alloc] initWithAnnotation:annotation
                                                        reuseIdentifier:userLocationStyleReuseIndetifier];
    }
    
    self.userLocationAnnotationView = annotationView;
    return annotationView;
  }
  
  if ([annotation isKindOfClass:[MACreatBuniessPointAnnotation class]]) {//在地图上创建商圈打点用到的
    static NSString *reuseIndetifier = @"MACreatBuniessPointAnnotationReuseIndetifier";
    MACreatBuniessPointAnnView *annotationView = (MACreatBuniessPointAnnView *)[mapView dequeueReusableAnnotationViewWithIdentifier:reuseIndetifier];
    if (!annotationView){
      annotationView = [[MACreatBuniessPointAnnView alloc] initWithAnnotation:annotation
                                                              reuseIdentifier:reuseIndetifier];
    }
    return annotationView;
  }
  
  if ([annotation isKindOfClass:[MALocalAnnotation class]]) {//自定义本地图小兔子标点
    MALocalAnnotation *pointMarker = (MALocalAnnotation *)annotation;
    static NSString *reuseIndetifier = @"annotationReuseIndetifierLocal";
    MALocalAnnotationView *headAnnotationV = (MALocalAnnotationView *)[mapView dequeueReusableAnnotationViewWithIdentifier:reuseIndetifier];
    if (!headAnnotationV){
      headAnnotationV = [[MALocalAnnotationView alloc] initImageSize:1 withShowLabel:YES withShowTime:NO WithAnnotation:annotation reuseIdentifier:reuseIndetifier];
      headAnnotationV.enabled = NO;
    }
    
   
    if (pointMarker.pointType == MarkerOpenStorePointType) {//可开店区域新建点位
     
      if ([pointMarker.pointLevel isEqualToString:@"1"]) {
        headAnnotationV.pointIM.image = [UIImage imageNamed:@"openstore_top"];
        headAnnotationV.zIndex = 900;
      } else if ([pointMarker.pointLevel isEqualToString:@"2"]) {
        headAnnotationV.zIndex = 800;
        headAnnotationV.pointIM.image = [UIImage imageNamed:@"openstore_mid"];
      } else {
        headAnnotationV.zIndex = 0;
        headAnnotationV.pointIM.image = [UIImage imageNamed:@"openstore_bottom"];
      }
      
      [headAnnotationV resetText:pointMarker.pointName];
      
    }
    
    return headAnnotationV;
    
  }
  
  if ([annotation isKindOfClass:[MACenterBuniessAnnotation class]]) {//显示商圈名称
    
    MACenterBuniessAnnotation *pointMarker = (MACenterBuniessAnnotation *)annotation;
    static NSString *reuseIndetifierCenter = @"annotationReuseIndetifierCenter";
    MACenterBuniessAnnView *headAnnotationV = (MACenterBuniessAnnView *)[mapView dequeueReusableAnnotationViewWithIdentifier:reuseIndetifierCenter];
    if (!headAnnotationV) {
      headAnnotationV = [[MACenterBuniessAnnView alloc] initWithAnnotation:annotation reuseIdentifier:reuseIndetifierCenter];
    }
    headAnnotationV.pointL.text = pointMarker.pointName;
    
    return headAnnotationV;
  }
  
  return nil;
  
}

#pragma mark 地图商圈显示代理
- (MAOverlayRenderer *)mapView:(MAMapView *)mapView rendererForOverlay:(id<MAOverlay>)overlay{
  //  新建商圈用到的多边形
  if ([overlay isKindOfClass:[MACreatBuniessPolygon class]])
  {
    MACreatBuniessPolygon *pol = (MACreatBuniessPolygon *)overlay;
    MACreatBuniessPolygonRender *polygonRenderer = [[MACreatBuniessPolygonRender alloc] initWithPolygon:overlay];
  
    polygonRenderer.lineWidth   = 2;
    if (pol.lineType == 0) {//定点绘制
      polygonRenderer.strokeColor = [UIColor clearColor];
    } else {//自由绘制
      polygonRenderer.strokeColor = [UIColor colorWithRed:255/255.0 green:33/255.0 blue:33/255.0 alpha:1];
    }
    polygonRenderer.fillColor   = [UIColor colorWithRed:255/255.0 green:33/255.0 blue:33/255.0 alpha:0.2];
    
    return polygonRenderer;
  }
  
  //  新建商圈用到的线段
  if ([overlay isKindOfClass:[MACreatBuniessPolyline class]]){
    
    MACreatBuniessPolyline *pol = (MACreatBuniessPolyline *)overlay;
    MAPolylineRenderer *polylineRenderer = [[MAPolylineRenderer alloc] initWithPolyline:overlay];
    polylineRenderer.lineWidth = 2.f;
    polylineRenderer.strokeColor = [UIColor colorWithRed:255/255.0 green:33/255.0 blue:33/255.0 alpha:1];
    if (pol.lineType == 0) {//实线
      polylineRenderer.lineDashType = kMALineDashTypeNone;
    } else {//虚线
      polylineRenderer.lineDashType = kMALineDashTypeSquare;
    }
    return polylineRenderer;
  }
  
  if ([overlay isKindOfClass:[MABussinessPolygon class]])//已经渲染的商圈
  {
    MABussinessPolygon *pol = (MABussinessPolygon *)overlay;
    MABussinessPolygonRenderer *polygonRenderer = [[MABussinessPolygonRenderer alloc] initWithPolygon:overlay];
    polygonRenderer.lineWidth   = 2.f;
    if (pol.buinessType == BuinessBlueType) {
      polygonRenderer.strokeColor = [UIColor colorWithRed:26/255.0 green:106/255.0 blue:255/255.0 alpha:1];
      polygonRenderer.fillColor   = [UIColor colorWithRed:26/255.0 green:106/255.0 blue:255/255.0 alpha:0.1];
    } else if (pol.buinessType == BuinessGrayType) {
      polygonRenderer.strokeColor = [UIColor colorWithRed:134/255.0 green:144/255.0 blue:156/255.0 alpha:1];
      polygonRenderer.fillColor   = [UIColor colorWithRed:134/255.0 green:144/255.0 blue:156/255.0 alpha:0.1];
    } else if (pol.buinessType == BuinessYellowType) {
      
      polygonRenderer.strokeColor = [UIColor colorWithRed:255/255.0 green:125/255.0 blue:1/255.0 alpha:1];
      polygonRenderer.fillColor   = [UIColor colorWithRed:255/255.0 green:125/255.0 blue:1/255.0 alpha:0.1];
    } else {
      polygonRenderer.strokeColor = [UIColor colorWithRed:0/255.0 green:180/255.0 blue:43/255.0 alpha:1];
      if (pol.isChoose) {
        polygonRenderer.fillColor   = [UIColor colorWithRed:0/255.0 green:180/255.0 blue:43/255.0 alpha:0.4];
      } else {
        polygonRenderer.fillColor   = [UIColor colorWithRed:0/255.0 green:180/255.0 blue:43/255.0 alpha:0.1];
      }
    }
    return polygonRenderer;
    
  }
  
  return nil;
}


- (void)mapView:(MAMapView *)mapView didAddAnnotationViews:(NSArray *)views{
  
  
  for (MALocalAnnotationView *aV in views) {
    
    
    if (aV == nil) {
      continue;
    }
    
    if (![aV isKindOfClass:[MALocalAnnotationView class]]) {
      continue;
    }
    
    MALocalAnnotation *ann = (MALocalAnnotation *)aV.annotation;
    
    if (ann.pointType == MarkerOpenStorePointType || ann.pointType == MarkerOpenStoreLineType || ann.pointType == MarkerOpenStoreAreaType) {
     
      aV.pointIM.transform = CGAffineTransformMakeScale(0.1 , 0.1);
      aV.pointL.alpha = 0;
      [UIView animateWithDuration:0.3 animations:^{
        
        aV.pointIM.transform = CGAffineTransformIdentity;
        aV.pointL.alpha = 1;
       
      } completion:^(BOOL finished) {
        
      }];
      
    }
    
  }
}
#pragma mark ============创建商圈代理========
- (void)changeDrawType:(NSInteger)type{
  
  //  每次切换都移除一次已经绘制的数据
  [self.mapView removeAnnotations:self.buniessPointAnnArray];
  [self.buniessPointCoordinateArray removeAllObjects];
  [self.buniessPointAnnArray removeAllObjects];
  
  //  移除线段和商圈
  [self.mapView removeOverlays:self.buniessPolylineArray];
  [self.buniessPolylineArray removeAllObjects];
  [self.mapView removeOverlay:self.creatPolygon];
  self.creatPolygon = nil;
  
  
  if (type == 1) {//自由绘制
    self.creatBuniessIM.hidden = YES;
  } else {//定点绘制
    self.creatBuniessIM.hidden = NO;
    [self.drawBoardView hideModal];
    self.drawBoardView = nil;
  }
  
  [self.creatBuniessView updatePointArr:self.buniessPointCoordinateArray];
  
}

// 点击定点
- (void)clickFixedPoint{
  
  if (self.toastShow) {
    return;
  }
  
  //  先判断2点之间距离
  if (self.buniessPointAnnArray.count >= 1) {
    MACreatBuniessPointAnnotation *ann = [self.buniessPointAnnArray lastObject];
    MAMapPoint lastPoint = MAMapPointForCoordinate(ann.coordinate);
    MAMapPoint currentPoint = MAMapPointForCoordinate(self.mapView.centerCoordinate);
    //2.计算距离
    CLLocationDistance distance = MAMetersBetweenMapPoints(lastPoint,currentPoint);
    if (distance < 1) {
      [self.notiflyView showModal:NotiflyFail andTitle:@"连续定点距离过近,请重新绘制"];
      return;
    }
    
  }
  
  MACreatBuniessPointAnnotation *ann = [[MACreatBuniessPointAnnotation alloc] init];
  ann.coordinate = self.mapView.centerCoordinate;
  [self.mapView addAnnotation:ann];
  
  NSDictionary *dic = [NSDictionary dictionaryWithObjectsAndKeys:[NSString stringWithFormat:@"%f",self.mapView.centerCoordinate.latitude],@"latitude",[NSString stringWithFormat:@"%f",self.mapView.centerCoordinate.longitude],@"longitude", nil];
  [self.buniessPointCoordinateArray addObject:dic];
  [self.buniessPointAnnArray addObject:ann];
  [self.creatBuniessView updatePointArr:self.buniessPointCoordinateArray];
  
  
  //  点的数量大于2个时候画线
  BOOL isIntersects = NO;
  if (self.buniessPointCoordinateArray.count >= 2) {
    
    MACreatBuniessPointAnnotation *lastAnn = [self.buniessPointAnnArray lastObject];
    MACreatBuniessPointAnnotation *lastOneAnn = [self.buniessPointAnnArray objectAtIndexCheck:self.buniessPointAnnArray.count - 2];
    CLLocationCoordinate2D commonPolylineCoords[2];
    commonPolylineCoords[0].latitude = lastOneAnn.coordinate.latitude;
    commonPolylineCoords[0].longitude = lastOneAnn.coordinate.longitude;
    commonPolylineCoords[1].latitude = lastAnn.coordinate.latitude;
    commonPolylineCoords[1].longitude = lastAnn.coordinate.longitude;
    
    MACreatBuniessPolyline *commonPolyline = [MACreatBuniessPolyline polylineWithCoordinates:commonPolylineCoords count:2];
    commonPolyline.endPoint = MAMapPointForCoordinate(lastAnn.coordinate);
    commonPolyline.startPoint = MAMapPointForCoordinate(lastOneAnn.coordinate);
    commonPolyline.pointId = self.buniessPolylineArray.count + 1;
    
    CGPoint startP = [self.mapView convertCoordinate:lastOneAnn.coordinate toPointToView:self.view];
    CGPoint endP = [self.mapView convertCoordinate:lastAnn.coordinate toPointToView:self.view];
    
    if (self.buniessPolylineArray.count >= 2) {//判断是否和已经添加的线段相交
      
      for (MACreatBuniessPolyline *line in self.buniessPolylineArray) {
        
        if (MAMapPointEqualToPoint(line.startPoint, commonPolyline.endPoint) || MAMapPointEqualToPoint(line.endPoint, commonPolyline.startPoint)) {//如果是首尾在某条线上，则不是相交
          
        } else {
          
          CLLocationCoordinate2D startC =  MACoordinateForMapPoint(line.startPoint);
          CLLocationCoordinate2D endC =  MACoordinateForMapPoint(line.endPoint);
          
          CGPoint lineStart = [self.mapView convertCoordinate:startC toPointToView:self.view];
          CGPoint lineEnd = [self.mapView convertCoordinate:endC toPointToView:self.view];
          
          BOOL intersec = [self checkLineIntersection:startP with:endP with:lineStart with:lineEnd];
          if (intersec) {
            isIntersects = YES;
            break;
          }
        }
        
      }
      
      if (isIntersects) {//如果相交
        
        commonPolyline.lineType = 1;
        [self.mapView addOverlay:commonPolyline];
        [self.notiflyView showModal:NotiflyFail andTitle:@"线与线不可重叠"];
        self.toastShow = YES;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
          self.toastShow = NO;
          
          [self.mapView removeOverlay:commonPolyline];
          [self.mapView removeAnnotation:ann];
          
          [self.buniessPointCoordinateArray removeLastObject];
          [self.buniessPointAnnArray removeLastObject];
        });
        
      } else {
        
        commonPolyline.lineType = 0;
        [self.mapView addOverlay:commonPolyline];
        [self.buniessPolylineArray addObject:commonPolyline];
      }
      
      
    } else {
      commonPolyline.lineType = 0;
      [self.mapView addOverlay:commonPolyline];
      [self.buniessPolylineArray addObject:commonPolyline];
    }
  }
  
  if (isIntersects) {//如果相交，不在改变多边形
    return;
  }
  
  //点的数量大于3个的时候画polygon
  if (self.buniessPointCoordinateArray.count >= 3) {
    
    //    先移除已经添加到地图上的
    [self.mapView removeOverlay:self.creatPolygon];
    self.creatPolygon = nil;
    
    //    再添加新的
    CLLocationCoordinate2D coordinates[self.buniessPointCoordinateArray.count];
    for (int j = 0; j < self.buniessPointCoordinateArray.count; j ++) {
      
      NSDictionary *dicPoint = [self.buniessPointCoordinateArray objectAtIndexCheck:j];
      coordinates[j].latitude = [[NSString stringWithFormat:@"%@",[dicPoint objectNilForKey:@"latitude"]] floatValue];
      coordinates[j].longitude = [[NSString stringWithFormat:@"%@",[dicPoint objectNilForKey:@"longitude"] ] floatValue];
      
    }
    self.creatPolygon = [MACreatBuniessPolygon polygonWithCoordinates:coordinates count:self.buniessPointCoordinateArray.count];
    self.creatPolygon.lineType = 0;
    [self.mapView addOverlay:self.creatPolygon];
    
  }
}

// 点击撤回
- (void)clickWithdrawButton:(NSInteger)type{
  
  if (self.toastShow) {
    return;
  }
  
  if (type == 0) {//重画
    
    [self.buniessPointCoordinateArray removeAllObjects];
    self.creatBuniessView.moveV.hidden = NO;
    [self.mapView removeOverlay:self.creatPolygon];
    self.creatPolygon = nil;
    
    [self.drawBoardView showModal];
    
    
  } else {//撤销
    
    //    移除点位大头针
    [self.mapView removeAnnotation:[self.buniessPointAnnArray lastObject]];
    [self.buniessPointCoordinateArray removeLastObject];
    [self.buniessPointAnnArray removeLastObject];
    //  移除线段
    if (self.buniessPolylineArray.count > 0) {
      [self.mapView removeOverlay:[self.buniessPolylineArray lastObject]];
      [self.buniessPolylineArray removeLastObject];
    }
    //    移除商圈
    [self.mapView removeOverlay:self.creatPolygon];
    self.creatPolygon = nil;
    
    if (self.buniessPointCoordinateArray.count >= 3) {
      
      CLLocationCoordinate2D coordinates[self.buniessPointCoordinateArray.count];
      for (int j = 0; j < self.buniessPointCoordinateArray.count; j ++) {
        
        NSDictionary *dicPoint = [self.buniessPointCoordinateArray objectAtIndexCheck:j];
        coordinates[j].latitude = [[NSString stringWithFormat:@"%@",[dicPoint objectNilForKey:@"latitude"]] floatValue];
        coordinates[j].longitude = [[NSString stringWithFormat:@"%@",[dicPoint objectNilForKey:@"longitude"] ] floatValue];
        
      }
      self.creatPolygon = [MACreatBuniessPolygon polygonWithCoordinates:coordinates count:self.buniessPointCoordinateArray.count];
      [self.mapView addOverlay:self.creatPolygon];
      
    }
    
  }
  
  [self.creatBuniessView updatePointArr:self.buniessPointCoordinateArray];
}

// 点击完成
- (void)clickDoneButton:(NSInteger)type{
  
  if (self.toastShow) {
    return;
  }
  
  if (self.buniessPointCoordinateArray.count < 3) {
    [self.notiflyView showModal:NotiflyFail andTitle:@"请至少绘制3个点"];
    return;
  }
  
  if ([self.buniessPointCoordinateArray isEqual:self.pointArray]) {
    [self.creatBuniessView hideModal];
    self.creatBuniessView.delegate = nil;
    self.creatBuniessView = nil;
    [self.creatBuniessIM removeFromSuperview];
    self.creatBuniessIM = nil;
    
    [self.drawBoardView hideModal];
    self.drawBoardView.delegate = nil;
    self.drawBoardView = nil;
    [self.mapView removeAnnotations:self.buniessPointAnnArray];
    [self.mapView removeOverlays:self.buniessPolylineArray];
    [self.mapView removeOverlay:self.creatPolygon];
    
    self.creatPolygon = nil;
    [self.buniessPolylineArray removeAllObjects];
    [self.buniessPointAnnArray removeAllObjects];
    [self.buniessPointCoordinateArray removeAllObjects];
    [self.navigationController popViewControllerAnimated:YES];
    return;
  }
  
  //  提交接口，校验商圈
  NSMutableDictionary *dict = [NSMutableDictionary dictionary];
  MAMapRect visibleRect = self.mapView.visibleMapRect;
  CLLocationCoordinate2D topLeft = MACoordinateForMapPoint(visibleRect.origin);
  CLLocationCoordinate2D topRight = MACoordinateForMapPoint(MAMapPointMake(MAMapRectGetMaxX(visibleRect), MAMapRectGetMinY(visibleRect)));
  CLLocationCoordinate2D bottomLeft = MACoordinateForMapPoint(MAMapPointMake(MAMapRectGetMinX(visibleRect), MAMapRectGetMaxY(visibleRect)));
  CLLocationCoordinate2D bottomRight = MACoordinateForMapPoint(MAMapPointMake(MAMapRectGetMaxX(visibleRect), MAMapRectGetMaxY(visibleRect)));
  dict[@"top_left"] = [NSDictionary dictionaryWithObjectsAndKeys:[NSString stringWithFormat:@"%f",topLeft.latitude],@"latitude",[NSString stringWithFormat:@"%f",topLeft.longitude],@"longitude", nil];//地图左上角坐标
  dict[@"top_right"] = [NSDictionary dictionaryWithObjectsAndKeys:[NSString stringWithFormat:@"%f",topRight.latitude],@"latitude",[NSString stringWithFormat:@"%f",topRight.longitude],@"longitude", nil];//地图右上角坐标
  dict[@"bottom_left"] = [NSDictionary dictionaryWithObjectsAndKeys:[NSString stringWithFormat:@"%f",bottomLeft.latitude],@"latitude",[NSString stringWithFormat:@"%f",bottomLeft.longitude],@"longitude", nil];//地图左下角坐标
  dict[@"bottom_right"] = [NSDictionary dictionaryWithObjectsAndKeys:[NSString stringWithFormat:@"%f",bottomRight.latitude],@"latitude",[NSString stringWithFormat:@"%f",bottomRight.longitude],@"longitude", nil];//地图右下角坐标
  dict[@"area_range"] = self.buniessPointCoordinateArray;
  dict[@"id"] = self.buniessId;
  
  NSString *logString=[[NSString alloc] initWithData:[NSJSONSerialization dataWithJSONObject:dict options:NSJSONWritingPrettyPrinted error:nil] encoding:NSUTF8StringEncoding];
  
  DDLog(@"====%@===编辑",logString);
  
  __weak typeof(self)weakSelf = self;
  [self.loadingView showModal];
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.arearange.check" Params:dict success:^(NSDictionary *successResult) {

    [self.loadingView hideModal];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"EDITDRAWBUNIESS" object:nil userInfo:@{@"pointArray":[self.buniessPointCoordinateArray mutableCopy]}];
    
    [self.creatBuniessView hideModal];
    self.creatBuniessView.delegate = nil;
    self.creatBuniessView = nil;
    [self.creatBuniessIM removeFromSuperview];
    self.creatBuniessIM = nil;
    
    [self.drawBoardView hideModal];
    self.drawBoardView.delegate = nil;
    self.drawBoardView = nil;
    [self.mapView removeAnnotations:self.buniessPointAnnArray];
    [self.mapView removeOverlays:self.buniessPolylineArray];
    [self.mapView removeOverlay:self.creatPolygon];
    
    self.creatPolygon = nil;
    [self.buniessPolylineArray removeAllObjects];
    [self.buniessPointAnnArray removeAllObjects];
    [self.buniessPointCoordinateArray removeAllObjects];
    [self.navigationController popViewControllerAnimated:YES];

  } failure:^(NSString *errorResult) {
    [self.loadingView hideModal];
    [self.statusView showModal:ToastFail andTitle:errorResult];
  }];
  
  
}
//点击开始绘制
- (void)clickStartDraw{
  
  [self.drawBoardView showModal];
  
}

//点击移动画面
- (void)clickMoveDrawView{
  [self.drawBoardView hideModal];
  self.drawBoardView = nil;
}

//商圈绘制完成时
- (void)drawDone:(NSArray *)points{
  
  [self.mapView removeOverlay:self.creatPolygon];
  self.creatPolygon = nil;
  
  [self.buniessPointCoordinateArray removeAllObjects];
  CLLocationCoordinate2D commonPolylineCoords[points.count];
  for (int i = 0; i < points.count; i ++) {
    
    NSString *pointStr = [points objectAtIndexCheck:i];
    CGPoint point = CGPointFromString(pointStr);
    CLLocationCoordinate2D coor = [self.mapView convertPoint:point toCoordinateFromView:self.drawBoardView];
    commonPolylineCoords[i] = coor;
    
    NSDictionary *dic = [NSDictionary dictionaryWithObjectsAndKeys:[NSString stringWithFormat:@"%f",coor.latitude],@"latitude",[NSString stringWithFormat:@"%f",coor.longitude],@"longitude", nil];
    [self.buniessPointCoordinateArray addObject:dic];
  }
  
//  绘制到地图上
  self.creatPolygon = [MACreatBuniessPolygon polygonWithCoordinates:commonPolylineCoords count:points.count];
  self.creatPolygon.lineType = 1;
  [self.mapView addOverlay:self.creatPolygon];
  
//  关闭绘制区域
  [self.drawBoardView hideModal];
  self.drawBoardView = nil;
  
//  隐藏移动画面按钮
  self.creatBuniessView.moveV.hidden = YES;
  
  [self.creatBuniessView updatePointArr:self.buniessPointCoordinateArray];
  
}

#pragma mark - 线段相交测试(p1,p2是线段1的端点，p3,p4是线段2的端点)
-(BOOL)checkLineIntersection:(CGPoint)p1 with:(CGPoint)p2 with:(CGPoint)p3 with:(CGPoint)p4
{
    CGFloat denominator = (p4.y - p3.y) * (p2.x - p1.x) - (p4.x - p3.x) * (p2.y - p1.y);
      
    // In this case the lines are parallel so we assume they don't intersect~
    if (denominator <= (1e-6) && denominator >= -(1e-6))
    {
        return YES;
    }
      
    // amazing~
    CGFloat ua = ((p4.x - p3.x) * (p1.y - p3.y) - (p4.y - p3.y) * (p1.x - p3.x)) / denominator;
    CGFloat ub = ((p2.x - p1.x) * (p1.y - p3.y) - (p2.y - p1.y) * (p1.x - p3.x)) / denominator;
      
    if (ua >= 0.0f && ua <= 1.0f && ub >= 0.0f && ub <= 1.0f)
    {
        return YES;
    }
    return NO;
}



- (void)dealloc{
  NSLog(@"编辑地图释放了");
  _mapView.showsUserLocation = NO;
  [_mapView.layer removeAllAnimations];
  [_mapView removeAnnotations:_mapView.annotations];
  [_mapView removeOverlays:_mapView.overlays];
  [_mapView removeFromSuperview];
  _mapView.delegate = nil;
  _mapView = nil;
  
}

- (UIStatusBarStyle)preferredStatusBarStyle{
  if (@available(iOS 13.0, *)) {
    return  UIStatusBarStyleDarkContent;
  } else {
    return UIStatusBarStyleDefault;
  }
}
- (MALoadWaveView *)loadingView{
  if (!_loadingView) {
    _loadingView = [[MALoadWaveView alloc] initWithFrame:[UIScreen mainScreen].bounds];
  }
  return _loadingView;
}
- (MACreatBuniessView *)creatBuniessView{
  if (!_creatBuniessView) {
    _creatBuniessView = [[MACreatBuniessView alloc] initWithBottom:self.heightBottom];
    _creatBuniessView.delegate = self;
  }
  return _creatBuniessView;
}

- (MACreatBuniessDrawBoardView *)drawBoardView{
  if (!_drawBoardView) {
    _drawBoardView = [[MACreatBuniessDrawBoardView alloc] initWithFrame:CGRectMake(0, self.heightTop + 44, BCWidth, BCHeight - self.heightTop - self.heightBottom - 88 - 130)];
    _drawBoardView.delegate = self;
  }
  return _drawBoardView;
}
- (UIImageView *)creatBuniessIM{
  if (!_creatBuniessIM) {
    _creatBuniessIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 36, 49.5)];
    _creatBuniessIM.image = [UIImage imageNamed:@"creat_buniess"];
    CGPoint point = CGPointMake((BCWidth/2), (BCHeight)/2);
    _creatBuniessIM.center = CGPointMake(point.x, point.y - 49.5/2);
  }
  return _creatBuniessIM;
}

- (MATopToastView *)statusView{
  if (!_statusView) {
    _statusView = [[MATopToastView alloc] initCustomViewWithDuration:1];
  }
  
  return _statusView;
}

- (MATopNotiflyView *)notiflyView{
  if (!_notiflyView) {
    _notiflyView = [[MATopNotiflyView alloc] initCustomViewWithDuration:1];
  }
  return _notiflyView;
}

- (MALoadWaveView *)waveView{
  if (!_waveView) {
    _waveView = [[MALoadWaveView alloc] initWithFrame:[UIScreen mainScreen].bounds];
  }
  return _waveView;
}
@end
