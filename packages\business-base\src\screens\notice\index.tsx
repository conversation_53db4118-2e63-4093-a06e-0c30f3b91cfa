import React, { useCallback, useEffect, useRef, useState } from 'react'
import useEntranceStore from '../entrance/useEntranceStore'
import {
  DeviceEventEmitter,
  ImageBackground,
  NativeModules,
  Platform,
  StatusBar,
  Text,
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Pressable,
} from 'react-native'
import { authModel, roleAuth } from '@xlb/business-base/src/models/auth'
import { commonStyles, EmptyBox, Header, Item, XIcon, XLoading, XText } from '@xlb/common/src/components'
import { useIsFocused, useNavigation } from '@react-navigation/native'
import { colors, normalize } from '@xlb/common/src/config/theme'
import {
  XlbIconfont,
  XlbCard,
  XlbTag,
  XlbDatePickerView,
  XlbSearch,
  XlbDropdown,
  XlbButton,
  TOKEN,
  XlbText,
  XlbIconfontNew,
  XlbSelector,
} from '@xlb/components-rn'
import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'
import dayjs from 'dayjs'
import useAliCloudPush from '@xlb/common/src/hooks/useAliCloudPush'
import { DatePicker, Provider } from '@ant-design/react-native'
import { Dialog, Dropdown, Portal, Tag, Space, Button, Row, Col, Badge, Toast } from '@fruits-chain/react-native-xiaoshu'
// import { businessDeptListType } from '@xlb/business-erp/src/config/utils'
import Flex from '@xlb/common/src/styles/utilities/flex'
import XlbIcon from '@xlb/common/src/assets/iconfont'
import SelectDate from '../../components/selectDate'
import { dateMap } from '../../components/selectDate/config'
import DragSortTab from './components/DragSortTab'
import { FlatList, Image } from 'native-base'
// import { center } from 'turf'
import noticeVC, { titles_def } from './modes'
import Api from './api'
import { cloneDeep } from 'lodash'
// import { opacity } from 'react-native-reanimated'
/**
 * 首页
 * @constructor
 */
const Index: React.FC = () => {
  const navigation = useNavigation()
  const { initPush } = useAliCloudPush()
  const userInfos = authModel?.state?.userInfos

  const isFocused = useIsFocused()
  const [read, setRead] = useState<any>(false)
  const [noticeDate, setNoticeDate] = useState<any>([]) //公告信息
  const [noticeDataIds, setNoticeDataIds] = useState<any>([]) // 公告id列表，用于传入详情页时点击上一页下一页
  const [searchText, setSearchText] = useState<string>('')
  const [popupProps, setPopupProps] = useState({
    targetHeight: 0,
    targetPageY: 0,
    visible: false,
  })
  const [loading, setLoading] = useState(false)
  const [noticeTypeList, setNoticeTypeList] = useState<any>([])
  const [noticeType, setNoticeType] = useState<any>('')
  const [readTipShow, setReadTipShow] = useState(false) // 控制一键已读规则
  const [allReadShow, setAllReadShow] = useState(false) // 控制 一键已读 确认弹窗
  const [allReadLoading, setAllReadLoading] = useState(false) // 控制一键已读按钮的loading状态

  const [dateType, setDateType] = useState<any>('DAY')
  const [dateValue, setDateValue] = useState([dayjs(new Date()).format('YYYY-MM-DD'), dayjs(new Date()).format('YYYY-MM-DD')])
  const [tableKey, setTableKey] = useState<any>('ALL')
  const table_titles = noticeVC((state: any) => state.table_titles)
  const set_table_titles = noticeVC((state: any) => state.set_table_titles)
  const reset_table_titles = noticeVC((state: any) => state.reset_table_titles)

  const ViewRef = useRef<View>(null)

  const system = !!authModel?.state?.userInfos.supplier

  const noticeTypeEum = {
    INFORM: '通知类',
    POLICY: '政策类',
    EXECUTE: '执行类',
  }
  // 获取用户公告
  const getUserNotice = async (type: any) => {
    setLoading(true)
    // 首页会出现两个loading
    // Loading.show()
    const res = await ErpHttp.post<any>(`/${type}/hxl.${type}.usernotice.page`, {
      read: read == null ? null : read,
      order_mode: 2,
      type: noticeType,
      create_date: dateValue,
      create_business_dept: tableKey == 'ALL' ? null : tableKey,
    })
    console.log('res.data.content', res.data.content)
    if (res?.code == 0) {
      setNoticeDate(res.data.content)
    }
    let tmp = cloneDeep(table_titles).map((e) => {
      if (e.key == tableKey) {
        e.badge = res?.data?.content.filter((n: any) => !n.read).length ?? 0
      } else {
        e.badge = 0
      }
      return e
    })
    set_table_titles(tmp)
    setLoading(false)
  }

  // 获取公告类型
  const getNoticeType = async () => {
    const res = await ErpHttp.post<any>('/erp/hxl.erp.noticetype.find', {})
    if (res?.code === 0) {
      let arr = res.data.map((v) => ({ value: v.name, label: v.name, active: false }))
      arr.unshift({ value: '', label: '不限', active: true })
      setNoticeTypeList(arr)
    }
  }
  // 设置全部已读
  const postAllRead = async () => {
    setAllReadLoading(true)
    const res = await ErpHttp.post<any>('/erp/hxl.erp.usernotice.allread', {
      force_notice_read: false,
      user_id: userInfos.id,
    }).finally(() => {
      setAllReadLoading(false)
    })
    if (res?.code === 0) {
      Toast({
        message: '已将所有公告标记为已读',
        forbidPress: true,
      })
    }
    getUserNotice(`${system ? 'scm' : 'erp'}`)
  }

  /**
   * @description: 点击一键已读事件
   * @return {*}
   */
  const handleAllRead = () => {
    // 如果没有未读
    const notReadLen = noticeDate.filter((n: any) => !n.read).length
    if (notReadLen > 0) setAllReadShow(true)
    else
      Toast({
        message: '暂无未读公告',
        forbidPress: true,
      })
  }
  /**
   * @description:
   * @return {*}
   */
  useEffect(() => {
    getTablelist()
    getNoticeType()
  }, [])
  /**
   * @description: 监听noticeDate，只要变化就更新NoticeDataIds
   * @return {*}
   */
  useEffect(() => {
    const notice = noticeDate.filter((e) => {
      return e?.title?.includes(searchText) || e?.digest?.includes(searchText)
    })
    const ids = notice.map((n: any) => n.id)
    setNoticeDataIds(ids)
  }, [noticeDate])

  useEffect(() => {
    if (isFocused) {
      getUserNotice(`${system ? 'scm' : 'erp'}`)
    }
  }, [isFocused, read, dateValue, tableKey])

  const onPressShade = useCallback(() => {
    setPopupProps((s) => ({
      ...s,
      visible: false,
    }))
  }, [])

  const getTablelist = async () => {
    const res = await Api<Result<any>>('/erp/hxl.erp.usercolumn.get', { name: '/hxl.erp.notice.table.find' })
    if (res?.code === 0 && res?.data?.details) {
      let tmp = res?.data?.details.map((e: any) => {
        //做映射，利用以前的接口，关键词无含义
        let item: any = {}
        item.key = e.code
        item.id = e.id
        item.title = e.name
        item.id = e.index
        item.disabledDrag = e.hidden
        item.disabledReSorted = e.fix
        return item
      })
      set_table_titles(tmp)
    } else {
    }
  }
  const tableHandleSave = async (data: any) => {
    let tmp = data.map((e: any) => {
      let item: any = {}
      item.code = e.key
      item.id = e.id
      item.name = e.title
      item.index = e.id
      item.hidden = e.disabledDrag
      item.fix = e.disabledReSorted
      return item
    })
    const res = await Api<Result<any>>('/erp/hxl.erp.usercolumn.update', { details: tmp, name: '/hxl.erp.notice.table.find' })
    if (res?.code === 0) {
    } else {
    }
  }

  return (
    <>
      <View style={{ flex: 1 }}>
        <XLoading loading={loading} />
        {/* <StatusBar  hidden={false} translucent={false} backgroundColor={'#fff'} barStyle={'dark-content'} /> */}
        <Header
          headerBgColor="#fff"
          centerComponent={
            <>
              <XText size16 semiBold>
                公告
              </XText>
              <Pressable
                onPress={handleAllRead}
                style={{
                  width: 24,
                  height: 24,
                  borderRadius: 12,
                  backgroundColor: TOKEN.grey_05,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginLeft: 5,
                }}
              >
                <Image source={require('@xlb/common/src/assets/icons/clean_all.png')} style={{ width: normalize(14), height: normalize(14) }}></Image>
              </Pressable>
            </>
          }
          leftComponent={
            <XIcon
              name={'back'}
              color="#000"
              onPress={() => {
                navigation.goBack()
              }}
            />
          }
          rightComponent={
            <XlbIconfont
              name="sousuo"
              size={normalize(20)}
              style={{ marginLeft: TOKEN.space_3, marginRight: 17 }}
              onPress={() => {
                navigation.navigate('NoticeSearch')
              }}
            ></XlbIconfont>
          }
        ></Header>

        <View collapsable={false}>
          <DragSortTab
            onTableSelectCallBack={(item: any) => {
              setTableKey(item?.key)
            }}
            onDataChangeCallBack={(data: any) => {
              set_table_titles(data)
              tableHandleSave(data)
            }}
            onResetCallBack={() => {
              reset_table_titles()
              tableHandleSave(titles_def)
            }}
            titles={table_titles}
            fixedItems={[0]}
            selectedItem={table_titles.find((element) => element?.key == tableKey)}
          ></DragSortTab>
          {/* <XlbSearch
            placeholder="搜索标题/内容"
            onChange={(e) => {
              setSearchText(e)
            }}
            extend={
              <TouchableOpacity
                activeOpacity={0.5}
                style={{ flexDirection: 'row', alignItems: 'center' }}
                onPress={() => {
                  ViewRef.current?.measure((x, y, width, height, pageX, pageY) => {
                    setPopupProps((s) => ({
                      ...s,
                      targetHeight: height,
                      targetPageY: pageY,
                      visible: true,
                    }))
                  })
                }}
              >
                <XlbIcon name="shaixuan" size={normalize(16)} style={{ marginLeft: TOKEN.space_3, marginRight: TOKEN.space_1 }}></XlbIcon>
                <XlbText font_size_4 grey_10>
                  筛选
                </XlbText>
              </TouchableOpacity>
            }
          ></XlbSearch> */}
        </View>

        <View ref={ViewRef} style={styles['operate-warp']}>
          {/* <TouchableOpacity
            style={styles['operate-hd']}
            onPress={() => {
              Dialog.confirm({
                title: '将所有通知标记为已读？',
              }).then((action) => {
                if (action === 'confirm') {
                  postAllRead()
                }
              })
            }}
          >
            <XlbIconfontNew name="qingchusuoyou" color={TOKEN.grey_7} size={normalize(14)} style={{ marginRight: TOKEN.space_1 }}></XlbIconfontNew>
            <XlbText font_size_4 grey_7 primary_10>
              全部已读
            </XlbText>
          </TouchableOpacity> */}
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <XlbButton
              size="xs"
              square
              style={
                read == null ? { backgroundColor: TOKEN.primary_05, marginRight: normalize(8) } : { backgroundColor: TOKEN.grey_05, marginRight: normalize(8) }
              }
              type={read == null ? 'ghost' : 'default'}
              onPress={() => {
                setRead(null)
              }}
            >
              全部
            </XlbButton>
            <View style={{ marginRight: 10 }}>
              <XlbButton
                size="xs"
                square
                type={read == true ? 'ghost' : 'default'}
                style={read == true ? { backgroundColor: TOKEN.primary_05 } : { backgroundColor: TOKEN.grey_05 }}
                onPress={() => {
                  setRead(true)
                }}
              >
                已读
              </XlbButton>
            </View>
            <Badge count={0} max={99} style={{ marginRight: 10 }}>
              <XlbButton
                size="xs"
                square
                type={read == false ? 'ghost' : 'default'}
                style={read == false ? { backgroundColor: TOKEN.primary_05 } : { backgroundColor: TOKEN.grey_05 }}
                onPress={() => {
                  setRead(false)
                }}
              >
                未读
              </XlbButton>
            </Badge>
            <SelectDate
              style={{ width: 80 }}
              type={dateType}
              date={dateValue}
              dateOption={undefined}
              onChange={(date, dateType) => {
                console.log(date, dateType)
                setDateType(dateType)
                setDateValue(date)
              }}
            >
              <View style={{ alignItems: 'center', flexDirection: 'row', width: 70 }}>
                <XText semiBold style={{ marginRight: normalize(4) }}>
                  {dateMap[dateType].getDateTypeText(dateValue)}
                </XText>
                <XlbIconfont name="zhankai" size={normalize(12)} />
              </View>
            </SelectDate>
          </View>
          <Space direction="horizontal" align="center">
            <TouchableOpacity
              activeOpacity={0.5}
              style={{ flexDirection: 'row', alignItems: 'center' }}
              onPress={() => {
                ViewRef.current?.measure((x, y, width, height, pageX, pageY) => {
                  setPopupProps((s) => ({
                    ...s,
                    targetHeight: height,
                    targetPageY: pageY,
                    visible: true,
                  }))
                })
              }}
            >
              <XlbIcon name="shaixuan" size={normalize(16)} style={{ marginLeft: TOKEN.space_3, marginRight: TOKEN.space_1 }}></XlbIcon>
              <XlbText font_size_4 grey_10>
                筛选
              </XlbText>
            </TouchableOpacity>
          </Space>
        </View>

        {/* <XlbDatePickerView></XlbDatePickerView> */}
        <FlatList
          style={{ flex: 1, marginBottom: normalize(8) }}
          data={noticeDate.filter((e) => {
            return e?.title?.includes(searchText) || e?.digest?.includes(searchText)
          })}
          renderItem={({ item, index }: { item: any; index: number }) => {
            let { create_time, title, type, read, digest, id, read_time, create_business_dept, file_count, level } = item
            return (
              <XlbCard
                containerStyle={{ opacity: read ? 0.5 : 1 }}
                title={
                  <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                    {!read && (
                      <View
                        style={{
                          backgroundColor: TOKEN.red_10,
                          width: normalize(6),
                          height: normalize(6),
                          borderRadius: normalize(6),
                          marginRight: TOKEN.space_1,
                        }}
                      ></View>
                    )}

                    <XlbText font_size_6 semiBold>
                      {title}
                    </XlbText>
                    {file_count ? (
                      <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                        <XlbIcon name={'fujian'} size={normalize(16)} color={'rgba(97, 99, 103, 1)'}></XlbIcon>
                        <Text style={{ fontSize: 12, color: '#86909C' }}>{file_count}</Text>
                      </View>
                    ) : null}
                  </View>
                }
                onPress={() => {
                  navigation.navigate('RemoteAppBase.Details', { id: id, ids: noticeDataIds })
                }}
                footer={
                  <>
                    <Space direction="horizontal" align="center" justify="space-between">
                      <Space direction="horizontal" align="center" gapHorizontal={2}>
                        <Tag type="ghost" color={TOKEN.primary_10}>
                          {noticeTypeEum[type] ?? type}
                        </Tag>
                        {level == 0 && (
                          <Tag type="ghost" color={'#FF2121'} style={{ marginLeft: 8 }}>
                            {'置顶'}
                          </Tag>
                        )}

                        <Text style={[styles['item-footer__text1'], { marginLeft: 8 }]}>
                          {create_business_dept && businessDeptListType[create_business_dept]}
                        </Text>
                      </Space>
                      <Space direction="horizontal" align="center" justify="space-between">
                        <Text style={styles['item-footer__text2']}>{dayjs(create_time).format('YYYY-MM-DD')}</Text>
                      </Space>
                    </Space>
                  </>
                }
              >
                <View
                  style={{
                    marginTop: normalize(4),
                  }}
                >
                  <Text style={styles['item-text']} numberOfLines={3}>
                    {digest}
                  </Text>
                </View>
              </XlbCard>
            )
          }}
          ListEmptyComponent={() => <EmptyBox></EmptyBox>}
        ></FlatList>
      </View>

      <Portal>
        <XlbDropdown.Popup
          {...popupProps}
          onPressShade={onPressShade}
          onPressOverlay={onPressShade}
          contentStyle={{
            borderBottomLeftRadius: normalize(16),
            borderBottomRightRadius: normalize(16),
          }}
        >
          <ScrollView style={{ maxHeight: 600, borderBottomLeftRadius: normalize(16), borderBottomRightRadius: normalize(16) }}>
            <View style={{ width: '100%' }}>
              <View
                style={{
                  paddingHorizontal: TOKEN.space_3,
                  borderBottomColor: TOKEN.grey_1,
                  borderBottomWidth: StyleSheet.hairlineWidth,
                  paddingBottom: TOKEN.space_1,
                  paddingTop: TOKEN.space_3,
                }}
              >
                <XlbSelector
                  title={'筛选'}
                  value={noticeType}
                  option={noticeTypeList}
                  onChange={(e) => {
                    setNoticeType(e)
                  }}
                ></XlbSelector>
              </View>
              <View style={{ flexDirection: 'row', paddingHorizontal: normalize(12), paddingVertical: normalize(12) }}>
                <XlbButton
                  square
                  style={{ flex: 1, marginRight: normalize(8), height: normalize(40) }}
                  onPress={() => {
                    setNoticeType('')
                  }}
                >
                  重置
                </XlbButton>
                <XlbButton
                  square
                  style={{ flex: 1, height: normalize(40) }}
                  type="primary"
                  onPress={() => {
                    getUserNotice(`${system ? 'scm' : 'erp'}`)
                    onPressShade()
                  }}
                >
                  确认
                </XlbButton>
              </View>
            </View>
          </ScrollView>
        </XlbDropdown.Popup>
      </Portal>
      {/* 一键已读规则 */}
      <Dialog.Component
        title="一键已读规则"
        message="一键已读操作只会忽略普通公告，强制提醒类的公告，需要您阅读后才可以忽略"
        visible={readTipShow}
        showConfirmButton={false}
        showCancelButton={false}
        closeOnPressOverlay={false}
        showClose={false}
      >
        <View style={{ backgroundColor: '#fff', padding: 20, paddingTop: 12, paddingBottom: 24, borderRadius: 12 }}>
          <Button
            text="知道了"
            type="primary"
            onPress={() => {
              setReadTipShow(false)
            }}
          />
        </View>
      </Dialog.Component>
      {/* 一键已读 */}
      <Dialog.Component
        title="确定将所有公告标记为已读？"
        visible={allReadShow}
        showConfirmButton={false}
        showCancelButton={false}
        closeOnPressOverlay={false}
        showClose={false}
        style={{ marginLeft: 40, marginRight: 40 }}
      >
        <View style={{ backgroundColor: '#fff', padding: 20, paddingTop: 12, paddingBottom: 24, borderRadius: 12 }}>
          <Row gap={10} style={{ marginTop: 10 }}>
            <Col span={12}>
              <Button text="取消" color={'#1F2126'} type="outline" onPress={() => setAllReadShow(false)} />
            </Col>
            <Col span={12}>
              <Button
                text="确定"
                type="primary"
                onPress={() => {
                  setAllReadShow(false)
                  postAllRead()
                }}
              />
            </Col>
          </Row>
        </View>
      </Dialog.Component>
    </>
  )
}

const styles = StyleSheet.create({
  'operate-warp': {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: TOKEN.space_3,
    paddingVertical: TOKEN.space_2,
  },
  'operate-hd': {
    flexDirection: 'row',
    alignItems: 'center',
  },
  'select-icon': {
    position: 'absolute',
    right: normalize(12),
    top: '50%',
    marginTop: normalize(4),
  },
  'item-footer': {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  'item-footer__text1': {
    fontSize: normalize(12),
    color: 'rgba(30,33,38,0.45)',
  },
  'item-footer__text2': {
    fontSize: normalize(12),
    color: 'rgba(30, 33, 38, 0.45)',
  },
  'item-text': {
    color: 'rgba(30, 33, 38, 0.70)',
    fontSize: normalize(13),
    marginTop: normalize(4),
  },
})

export default Index
