//
//  MAPointDetailViewController.m
//  xlb
//
//  Created by 莱昂纳多·迪卡普里奥 on 2024/10/12.
//

#import "MAPointDetailViewController.h"
#import "PushZoomScaleTranstion.h"
#import "UIImageView+WebCache.h"
#import "MALoadWaveView.h"
#import "UIView+Common.h"
#import <AVKit/AVKit.h>
#import "MALookPointViewController.h"
#import "UIView+Toast.h"
#import "MAHttpTool.h"
#import "NSDictionary+Common.h"
#import "NSArray+Common.h"
#import "ZKPhotoBrowser.h"
#import "MAUIScrollView.h"
#import "UIButton+Common.h"
#import "MAH5ViewController.h"
#import "PushCellScaleTranstion.h"
#import "MABuniessDetailViewController.h"
#import "MAShareView.h"
#import "MABottomSingleSelectAlert.h"
#import "MANativeAlert.h"
#import "MAAddLandlordViewController.h"
#import <CoreLocation/CoreLocation.h>
#import "MATakeLookViewController.h"
#import "MAWriteFollowUpViewController.h"
#import "MAWriteMeassageView.h"
#import "MABottomOpereateView.h"
#import "MAEditPointViewController.h"
#import "MAWriteReplyView.h"
#import "MAApprovalRecordView.h"
#import "MASearchPoiViewController.h"
#import "MATopToastView.h"
#import "MATopNotiflyView.h"
#import "MACustomTableView.h"
#import "MAChooseLocationViewController.h"

#define BCWidth   [UIScreen mainScreen].bounds.size.width
#define BCHeight  [UIScreen mainScreen].bounds.size.height
#define COLOR(R, G, B) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:1]
#define ACOLOR(R, G, B,A) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:A]
#define RandomColor  [UIColor colorWithRed:arc4random_uniform(256)/255.0 green:arc4random_uniform(256)/255.0 blue:arc4random_uniform(256)/255.0 alpha:1]

// 判断字符串是否为空
#define BCStringIsEmpty(str) ([str isKindOfClass:[NSNull class]] || str == nil || ![str isKindOfClass:[NSString class]] || [str length]< 1 ? YES : NO || [str isEqualToString:@"null"] || [str isEqualToString:@"<null>"] || [str isEqualToString:@"(null)"])
// 判断数组是否为空
#define BCArrayIsEmpty(array) (array == nil || [array isKindOfClass:[NSNull class]] || ![array isKindOfClass:[NSArray class]] || [array count] == 0)
// 判断字典是否为空
#define BCDictIsEmpty(dic) (dic == nil || [dic isKindOfClass:[NSNull class]] || ![dic isKindOfClass:[NSDictionary class]] || dic.allKeys.count == 0)

#define RealSize(value)  MAX(round(value * [UIScreen mainScreen].bounds.size.width / 400.0), value)
#define MutilFont(value)  [UIScreen mainScreen].bounds.size.width > 420 ? (value + 2) : value

#ifdef DEBUG
#define DDLog(format, ...) printf("class: <%p %s:(%d) > method: %s \n%s\n", self, [[[NSString stringWithUTF8String:__FILE__] lastPathComponent] UTF8String], __LINE__, __PRETTY_FUNCTION__, [[NSString stringWithFormat:(format), ##__VA_ARGS__] UTF8String] )
#else
#define DDLog(format, ...)
#endif

@interface MAPointDetailViewController ()<UIGestureRecognizerDelegate,UITableViewDelegate,UITableViewDataSource,UIScrollViewDelegate,BHInfiniteScrollViewDelegate,UIDocumentInteractionControllerDelegate>

@property (nonatomic, strong) UIView *toolbar;
@property (nonatomic, strong) MALoadWaveView *loadingView;//加载
@property (nonatomic, strong) AVPlayerViewController *playerViewController; /**< 媒体播放控制器 */
@property (nonatomic, assign) BOOL draggingDownToDismiss;//是否开启下拉关闭
@property (nonatomic, strong) NSArray *fileUrls;//轮播图对应的数据源
@property (nonatomic, strong) NSMutableArray *previewUrls;//预览用的
@property (nonatomic, assign) CGFloat heightTop;//导航栏高度
@property (nonatomic, strong) UIView *alphaV;//导航栏渐变用到的view
@property (nonatomic, strong) UIScrollView *topTabV;//顶部选项卡
@property (nonatomic, strong) UIView *indicateLayer;//选项卡指示器
@property (nonatomic, strong) UIButton *selectTabButton;//控制单选模板按钮
@property (nonatomic, strong) UIButton *followBtn;//关注按钮

@property (nonatomic, strong) UIScrollView *horizontalScrollView;//底部横向视图
@property (nonatomic, strong) UITableView *mainTableView;//底部视图
@property (nonatomic, strong) MAUIScrollView *homeScrollView;//首页
@property (nonatomic, strong) MAUIScrollView *landlordScrollView;//房东
@property (nonatomic, strong) MAUIScrollView *pointStateScrollView;//点位动态
@property (nonatomic, strong) MAUIScrollView *lookScrollView;//带看记录
@property (nonatomic, strong) MAUIScrollView *shareScrollView;//分享记录
@property (nonatomic, strong) MAUIScrollView *auditScrollView;//审批记录

@property (nonatomic, strong) UILabel *indexLabel;//轮播图计数
@property (nonatomic, strong) CALayer *swipeLayer;//轮播图滑块
@property (nonatomic, strong) UIButton *selectBannerButton;//控制单选轮播图标签按钮
@property (nonatomic, strong) NSMutableArray *bannerLabels;//控制轮播图标签数组
@property (nonatomic, strong) UIView *bannerLabelV;//轮播图标签
@property (nonatomic, strong) UIView *selectPointStateV;//点位状态选中的view

@property (nonatomic, strong) NSDictionary *detailDic;//点位详情数据源
@property (nonatomic, strong) NSArray *itemChangeArr;//变动项数据源
@property (nonatomic, strong) NSDictionary *itemChangeDic;//变动项返回值数据源
@property (nonatomic, strong) NSArray *lookArr;//带看记录返回值数据源
@property (nonatomic, strong) NSArray *shareArr;//分享记录返回值数据源
@property (nonatomic, strong) NSString *isFollow;//是否关注当前点位
@property (nonatomic, strong) NSArray *allPointArr;//全部点位动态数据源
@property (nonatomic, strong) NSArray *changePointArr;//变更点位动态数据源
@property (nonatomic, strong) NSArray *operatePointArr;//操作点位动态数据源
@property (nonatomic, strong) NSArray *followPointArr;//跟进点位动态数据源
@property (nonatomic, strong) NSArray *transferPointArr;//转移记录返回值数据源

@property (nonatomic, assign) BOOL isFirstLoad;//是否首次加载
@property (nonatomic, assign) CGFloat beginOffsetX;// 起始偏移量,为了判断滑动方向
@property(nonatomic, strong) NSMutableArray *bannerImages;//轮播图数组
@property (nonatomic, assign) CGFloat timeDelay;//延时

@property (nonatomic, copy) NSString *orgId;//组织id，非必传
@property (nonatomic, strong) UIButton *shareBtn;//分享
@property (nonatomic, strong) MABottomSingleSelectAlert *followAlert;//跟进人弹窗
@property (nonatomic, strong) UILabel *followLabel;//跟进人

@property (nonatomic, strong) UIScreenEdgePanGestureRecognizer *panLeft;
@property (nonatomic, strong) UIScreenEdgePanGestureRecognizer *panRight;
@property (nonatomic, assign) BOOL isTakeLook;//是否需要勘察打卡
@property (nonatomic, assign) BOOL isHaveMeassage;//是否需要审批意见
@property (nonatomic, strong) UIView *bottomOpereateV;//底部操作view
@property (nonatomic, strong) MABottomOpereateView *operatV;//底部操作view

@property (nonatomic, strong) NSArray *auditArr;//审批记录数据

@property (nonatomic, assign) BOOL levelDisplay;//店铺等级是否显示
@property (nonatomic, assign) BOOL attDisplay;//店铺属性是否显示
@property (nonatomic, assign) BOOL doorDisplay;//是否翻牌门店是否显示
@property (nonatomic, strong) MATopToastView *statusView;//加载
@property (nonatomic, strong) MATopNotiflyView *notiflyView;//加载
@end

@implementation MAPointDetailViewController

- (void)viewDidLoad {
  [super viewDidLoad];
  if (@available(iOS 13.0, *)) {
    self.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
  }
  self.view.backgroundColor = [UIColor whiteColor];
  self.view.layer.cornerRadius = 8;
  self.view.clipsToBounds = YES;
  
  
  self.bannerLabels =  [NSMutableArray array];
  self.previewUrls = [NSMutableArray array];
  self.bannerImages = [NSMutableArray array];
  
  _heightTop = 54;
  if (@available(iOS 13.0, *)) {
    NSSet *set = [UIApplication sharedApplication].connectedScenes;
    UIWindowScene *windowScene = [set anyObject];
    if (windowScene) {
      UIWindow *window = windowScene.windows.firstObject;
      if (window.safeAreaInsets.top > 0) {
        _heightTop = window.safeAreaInsets.top;
      } else {
        _heightTop = 54;
      }
    }
    
  } else {
    UIWindow *window = [[UIApplication sharedApplication].windows firstObject];
    if (window.safeAreaInsets.top > 0) {
      _heightTop = window.safeAreaInsets.top;
    } else {
      _heightTop = 54;
    }
  }
  
  //  占位图片
  if (self.pushType != PushAnimationTypeNone) { //需要动画的时候
    
    _tmpIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, 307)];
    NSString *imageUrl = [self.tmpImageUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    [_tmpIM  sd_setImageWithURL:[NSURL URLWithString:imageUrl] placeholderImage:[UIImage imageNamed:@"icon_pla"]];
    [self.view addSubview:_tmpIM ];
    
    //  侧滑手势
    self.draggingDownToDismiss = YES;
    _panLeft = [[UIScreenEdgePanGestureRecognizer alloc] initWithTarget:self action:@selector(popBack:)];
    _panLeft.edges = UIRectEdgeLeft;
    _panLeft.maximumNumberOfTouches = 1;
    [self.view addGestureRecognizer:_panLeft];
    
    
    _panRight = [[UIScreenEdgePanGestureRecognizer alloc] initWithTarget:self action:@selector(popBack:)];
    _panRight.edges = UIRectEdgeRight;
    _panRight.maximumNumberOfTouches = 1;
    [self.view addGestureRecognizer:_panRight];
    
    self.timeDelay = 0.4;
    
  } else {//不需要动画的时候,解决侧滑手势优先级问题
    
    self.timeDelay = 0;
    
  }
  
  
  
  [self initToolBar];
  //  转场动画执行完再去请求接口
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(self.timeDelay * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    
    [self.loadingView showModal];
    [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storeplan.read" Params:@{@"id":self.pointId} success:^(NSDictionary *successResult) {
      NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (![receiveData isKindOfClass:[NSDictionary class]]) {
        return;
      }
      
      if (BCDictIsEmpty(receiveData)) {
        return;
      }
      self.detailDic = receiveData;
      self.orgId = [NSString stringWithFormat:@"%@",[self.detailDic objectForKeyNil:@"org_id"]];
      
      [self loadMoreRequest];
      
    } failure:^(NSString *errorResult) {
      
      [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
      [self.loadingView hideModal];
      if ([errorResult isEqualToString:@"点位不存在！"]) {
        [self.navigationController popViewControllerAnimated:YES];
      }
    }];
  });
  
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(resetView) name:@"REFRESHPOINTDETAILDATA" object:nil];
  
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(scrollToShare) name:@"POINTDETAILGOTOSHARE" object:nil];
  
}

- (void)scrollToShare{
  
  UIButton *btn = [self.topTabV viewWithTag:1014 + 4];
  [self titleClick:btn];
  
}

- (void)popBack:(UIScreenEdgePanGestureRecognizer *)pan{
  
  
  
  if (!self.draggingDownToDismiss) {
    return;
  }
  
  CGPoint tran = [pan translationInView:self.view];
  CGFloat prosess = tran.x/self.view.bounds.size.width;
  
  prosess = fabs(prosess);
  if (prosess >= 0.1) {
    NSLog(@"返回了几次");
    [[NSNotificationCenter defaultCenter] postNotificationName:@"GETPOINTDETAILINDEX" object:self userInfo:@{@"currentIndex":[NSString stringWithFormat:@"%ld",(long)self.bannerView.currentPageIndex]}];
    [self.navigationController popViewControllerAnimated:YES];
    self.draggingDownToDismiss = NO;
    return;
  }
  
  
  CGFloat targetShrinkScale = 0.6;
  CGFloat currentScale = 1 - (1 - targetShrinkScale) * prosess;
  
  
  switch (pan.state)
  {
    case UIGestureRecognizerStateBegan:
    {
      self.mainTableView.scrollEnabled = NO;
      self.horizontalScrollView.scrollEnabled = NO;
      self.homeScrollView.scrollEnabled = NO;
      self.auditScrollView.scrollEnabled = NO;
      self.landlordScrollView.scrollEnabled = NO;
      self.pointStateScrollView.scrollEnabled = NO;
      self.lookScrollView.scrollEnabled = NO;
      self.shareScrollView.scrollEnabled = NO;
      [self.bannerView stopAutoScrollPage];
      
      break;
    }
    case UIGestureRecognizerStateChanged:
      
    {
      self.mainTableView.scrollEnabled = NO;
      self.horizontalScrollView.scrollEnabled = NO;
      self.homeScrollView.scrollEnabled = NO;
      self.auditScrollView.scrollEnabled = NO;
      self.landlordScrollView.scrollEnabled = NO;
      self.pointStateScrollView.scrollEnabled = NO;
      self.lookScrollView.scrollEnabled = NO;
      self.shareScrollView.scrollEnabled = NO;
      [self.bannerView stopAutoScrollPage];
      self.view.transform = CGAffineTransformMakeScale(currentScale, currentScale);
      self.view.layer.cornerRadius = (50 * prosess);
      
      break;
    }
    case UIGestureRecognizerStateEnded:
    {
      
      if (prosess < 0.1) {
        [UIView animateWithDuration:0.2 animations:^{
          self.view.transform = CGAffineTransformIdentity;
          self.view.layer.cornerRadius = 0;
        }];
        
        [self.bannerView startAutoScrollPage];
        self.mainTableView.scrollEnabled = YES;
        self.horizontalScrollView.scrollEnabled = YES;
        self.homeScrollView.scrollEnabled = YES;
        self.auditScrollView.scrollEnabled = YES;
        self.landlordScrollView.scrollEnabled =YES;
        self.pointStateScrollView.scrollEnabled = YES;
        self.lookScrollView.scrollEnabled = YES;
        self.shareScrollView.scrollEnabled = YES;
      }
      break;
    }
    default:
      break;
  }
}

- (void)viewWillDisappear:(BOOL)animated{
  [super viewWillDisappear:animated];
  self.mainTableView.scrollEnabled = NO;
  self.horizontalScrollView.scrollEnabled = NO;
  self.homeScrollView.scrollEnabled = NO;
  self.auditScrollView.scrollEnabled = NO;
  self.landlordScrollView.scrollEnabled = NO;
  self.pointStateScrollView.scrollEnabled = NO;
  self.lookScrollView.scrollEnabled = NO;
  self.shareScrollView.scrollEnabled = NO;
  
}

- (void)viewWillAppear:(BOOL)animated{
  [super viewWillAppear:animated];
  self.mainTableView.scrollEnabled = YES;
  self.horizontalScrollView.scrollEnabled = YES;
  self.homeScrollView.scrollEnabled = YES;
  self.auditScrollView.scrollEnabled = YES;
  self.landlordScrollView.scrollEnabled =YES;
  self.pointStateScrollView.scrollEnabled = YES;
  self.lookScrollView.scrollEnabled = YES;
  self.shareScrollView.scrollEnabled = YES;
}

- (void)viewDidAppear:(BOOL)animated
{
  [super viewDidAppear:animated];
  if (!_isFirstLoad) {
    self.isFirstLoad = YES;
  } else {
    [self.bannerView startAutoScrollPage];
  }
  
  if (self.pushType == PushAnimationTypeNone) {//不需要动画的时候走系统侧滑手势
    self.navigationController.interactivePopGestureRecognizer.delegate = (id)self;//侧滑手势
    if ([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
      self.navigationController.interactivePopGestureRecognizer.enabled = YES;
    }
  } else {
    if ([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
      self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    }
  }
  
}

- (void)viewDidDisappear:(BOOL)animated{
  [super viewDidDisappear:animated];
  
  if (_isFirstLoad) {
    [self.bannerView stopAutoScrollPage];
  }
}


#pragma mark 同时发起多个网络请求，完成之后才刷新页面
- (void)loadMoreRequest{
  
  
  __weak typeof(self)weakSelf = self;
  dispatch_group_t group = dispatch_group_create();
  
  //  是否关注
  [self request:@"kms/hxl.kms.interestuser.flag.find" group:group pamares:@{@"business_id":self.pointId,@"module":@"STORE_PLAN"}];
  
  NSDictionary *chndic;
  if (BCStringIsEmpty(self.orgId)) {
    chndic = @{@"type":@"STORE",@"store_plan_id":self.pointId,@"store_apply":@(0)};
  } else {
    chndic = @{@"type":@"STORE",@"store_plan_id":self.pointId,@"store_apply":@(0),@"org_id":self.orgId};
  }
  //  变动项接口
  [self request:@"kms/hxl.kms.itemchange.display.new.find" group:group pamares:chndic];
  
  
  NSDictionary *followDic;
  if (BCStringIsEmpty(self.orgId)) {
    followDic = @{};
  } else {
    followDic = @{@"org_id":self.orgId};
  }
  
  //  获取系统参数接口
  [self request:@"kms/hxl.kms.clientclueparam.read" group:group pamares:followDic];
  
  //  变更跟进人接口
  [self request:@"kms/hxl.kms.transfer.user.find" group:group pamares:followDic];
  
  //  带看记录接口
  [self request:@"kms/hxl.kms.storelookrecord.storeplan.find" group:group pamares:@{@"id":self.pointId}];
  
  //  分享记录接口
  [self request:@"kms/hxl.kms.storeplan.sharerecord.find" group:group pamares:@{@"id":self.pointId}];
  
  //  获取跟进记录
  [self request:@"kms/hxl.kms.storefollowrecord.find" group:group pamares:@{@"store_plan_id":self.pointId}];
  
  //  获取操作记录
  [self request:@"kms/hxl.kms.storeplan.auditrecord.find" group:group pamares:@{@"id":self.pointId}];
  
  //  获取变更记录
  [self request:@"kms/hxl.kms.storeplan.change.find" group:group pamares:@{@"id":self.pointId}];
  
  //  获取转移记录
  [self request:@"kms/hxl.kms.transferbusiness.find" group:group pamares:@{@"ref_id":self.pointId,@"type":@"STORE_PLAN"}];
  
  //  获取全部点位动态
  [self request:@"kms/hxl.kms.flag.storeplan.find" group:group pamares:@{@"store_plan_id":self.pointId}];
  
  //  审批记录接口
  NSDictionary *centerDic = @{@"id":self.pointId,@"process_definition_key": @"STORE_PLAN",@"business_key":self.pointId};
  [self request:@"kms/hxl.kms.approvataskhistory.find" group:group pamares:centerDic];
  
  //  所有的网络请求完成,渲染界面
  dispatch_group_notify(group, dispatch_get_main_queue(), ^{
    
    //    处理变动项值
    NSMutableDictionary *valueDic = [NSMutableDictionary dictionaryWithCapacity:100];
    if ([[weakSelf.detailDic objectForKeyNil:@"content"] isKindOfClass:[NSArray class]]) {
      for (NSDictionary *dic in [weakSelf.detailDic objectForKeyNil:@"content"]) {
        [valueDic addEntriesFromDictionary:[dic objectForKeyNil:@"details"]];
      }
    }
    
    weakSelf.followBtn.selected = [self.isFollow isEqualToString:@"1"] ? YES : NO;//是否关注按钮
    weakSelf.itemChangeDic = valueDic;//变动项值
    
    //    控制分享按钮显示与否
    if ([[self.detailDic objectNilForKey:@"state"] isEqualToString:@"SECOND_AUDIT"]) {
      weakSelf.shareBtn.hidden = NO;
    } else {
      weakSelf.shareBtn.hidden = YES;
    }
    
    
    //    开始加载全部界面
    UIView *headV = [weakSelf BannerHeader];
    weakSelf.mainTableView.tableHeaderView = headV;
    [weakSelf.view insertSubview:weakSelf.mainTableView atIndex:0];//加载主tableview
    [weakSelf loadBannerLabel:[weakSelf.detailDic objectForKeyNil:@"flag_all_files"]];//加载轮播图上的标注
    
    [weakSelf.horizontalScrollView addSubview:weakSelf.homeScrollView];
    [weakSelf.horizontalScrollView addSubview:weakSelf.landlordScrollView];
    [weakSelf.horizontalScrollView addSubview:weakSelf.pointStateScrollView];
    [weakSelf.horizontalScrollView addSubview:weakSelf.lookScrollView];
    [weakSelf.horizontalScrollView addSubview:weakSelf.shareScrollView];
    [weakSelf.horizontalScrollView addSubview:weakSelf.auditScrollView];
    [weakSelf loadDataWithScrollView];//加载全部页面数据
    weakSelf.mainTableView.tableFooterView = weakSelf.horizontalScrollView;
    [weakSelf.horizontalScrollView addObserver:weakSelf forKeyPath:@"contentOffset" options:NSKeyValueObservingOptionNew context:nil];
    [weakSelf showBottomButton];
    
    //        动画渐变，完成之后加载弹窗关闭
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      
      [weakSelf.loadingView hideModal];
      [weakSelf.bannerView scrollToPageAtIndex:weakSelf.scrollIndex Animation:NO];//轮播图滚到指定位置
      
      if (self.pushType != PushAnimationTypeNone) {//兼容手势问题
        [self.horizontalScrollView.panGestureRecognizer requireGestureRecognizerToFail:self.panLeft];
        [self.topTabV.panGestureRecognizer requireGestureRecognizerToFail:self.panLeft];
        [self.horizontalScrollView.panGestureRecognizer requireGestureRecognizerToFail:self.panRight];
        [self.topTabV.panGestureRecognizer requireGestureRecognizerToFail:self.panRight];
      } else {
        [self.horizontalScrollView.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
        [self.topTabV.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
      }
      
    });
    
    //  动画渐变
    [UIView animateWithDuration:0.2 animations:^{
      
      weakSelf.toolbar.alpha = 1;//导航栏
      weakSelf.mainTableView.alpha = 1;
      
      //      if (self.preSelect != 0) {
      //        UIButton *sender = [self.topTabV viewWithTag:1014 + self.preSelect];
      //        [self titleClick:sender];
      //      }
      
      //      刷新上一次选中的tab
      
    } completion:^(BOOL finished) {
      weakSelf.tmpIM.hidden = YES;
      [weakSelf.bannerView startAutoScrollPage];
    }];
    
  });
  
  
}

- (void)request:(NSString *)url group:(dispatch_group_t)group pamares:(NSDictionary *)para{
  
  dispatch_group_enter(group);
  [[MAHttpTool defaultManagerTool] postWithURL:url Params:para success:^(NSDictionary * _Nonnull successResult) {
    dispatch_group_leave(group);
    
    if ([url isEqualToString: @"kms/hxl.kms.storeplan.read"]) {//点位详情接口
      
      
    } else if ([url isEqualToString:@"kms/hxl.kms.itemchange.display.new.find"]){//变动项接口
      
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      NSMutableArray *itemArr = [NSMutableArray array];
      for (int i = 0; i < receiveData.count; i ++) {
        
        NSDictionary *dic = [receiveData objectAtIndexCheck:i];
        
        if ([[dic objectForKeyNil:@"name"] isEqualToString:@"基本信息"]) {//判断基本信息必填必显
          
          NSArray *basicArr = [dic objectForKeyNil:@"details"];
          if (!BCArrayIsEmpty(basicArr)) {
            //    apiname为key，显隐 必填为内容
            for (NSDictionary *detailDic in basicArr) {
              if ([[detailDic objectForKeyNil:@"api_name"] isEqualToString:@"basic_info_store_level"]) {
                self.levelDisplay = [[detailDic objectNilForKey:@"store_plan_display"] integerValue] == 1 ? YES :NO;
              
                
              } else if ([[detailDic objectForKeyNil:@"api_name"] isEqualToString:@"basic_info_store_attributes"]){
                
                self.attDisplay = [[detailDic objectNilForKey:@"store_plan_display"] integerValue] == 1 ? YES :NO;
                
              } else if ([[detailDic objectForKeyNil:@"api_name"] isEqualToString:@"basic_info_flipped_store"]){
                self.doorDisplay = [[detailDic objectNilForKey:@"store_plan_display"] integerValue] == 1 ? YES :NO;
              }
              
            }
          }
        }
        
        if ([[dic objectForKeyNil:@"acquiesce"] isEqual:@(1)]) {//跳过系统预设的变动项，基本信息，房东信息等等
          continue;
        }
        
        if ([[dic objectForKeyNil:@"enable"] isEqual:@(0)]) {//未启用的也要跳过
          continue;
        }
        
        if ([[dic objectNilForKey:@"name"] isEqualToString:@"房东信息"]) {
          continue;
        }
        
        [itemArr addObject:dic];
      }
      
      self.itemChangeArr = itemArr;//变动项数据
      
    } else if ([url isEqualToString:@"kms/hxl.kms.storelookrecord.storeplan.find"]){//带看记录
      
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      NSMutableArray *dataArr = [NSMutableArray arrayWithCapacity:100];
      for (NSDictionary *lookDic in receiveData) {
        NSArray *subArr = [lookDic objectForKeyNil:@"list"];
        if ([subArr isKindOfClass:[NSArray class]] ) {
          [dataArr addObjectsFromArray:subArr];
        }
      }
      self.lookArr = dataArr;
      
    } else if ([url isEqualToString:@"kms/hxl.kms.storeplan.sharerecord.find"]){//分享记录接口
      
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      self.shareArr = receiveData;
      
    }else if ([url isEqualToString:@"kms/hxl.kms.interestuser.flag.find"]){//是否关注接口
      NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (![receiveData isKindOfClass:[NSDictionary class]]) {
        return;
      }
      
      if (BCDictIsEmpty(receiveData)) {
        return;
      }
      self.isFollow = [NSString stringWithFormat:@"%@",[receiveData objectNilForKey:@"flag"]];
      
    } else if ([url isEqualToString:@"kms/hxl.kms.storeplan.auditrecord.find"]){//获取操作记录
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      self.operatePointArr = receiveData;
      
    } else if ([url isEqualToString:@"kms/hxl.kms.transferbusiness.find"]){//获取转移记录
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      self.transferPointArr = receiveData;
      
    } else if ([url isEqualToString:@"kms/hxl.kms.storeplan.change.find"]){//获取变更记录
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      NSMutableArray *arr = [NSMutableArray arrayWithCapacity:1000];
      for (NSDictionary *dic in receiveData) {
        NSArray *tmpArr = [dic objectForKeyNil:@"detail_changes"];
        if ([tmpArr isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(tmpArr)) {
          [arr addObjectsFromArray:tmpArr];
        }
        
      }
      
      self.changePointArr = arr;
      
    }else if ([url isEqualToString:@"kms/hxl.kms.storefollowrecord.find"]){//获取跟进记录
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      self.followPointArr = receiveData;
      
    } else if ([url isEqualToString:@"kms/hxl.kms.flag.storeplan.find"]){//获取全部点位动态记录
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      self.allPointArr = receiveData;
      
      
      
    } else if ([url isEqualToString:@"kms/hxl.kms.transfer.user.find"]){//变更跟进人
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      BOOL showS = receiveData.count < 15 ? NO : YES;
      self.followAlert = [[MABottomSingleSelectAlert alloc] initWithTitle:@"选择跟进人" showKey:@"name" showSearch:showS maskClose:YES andData:receiveData endChooseItem:@{}];
      
    } else if ([url isEqualToString:@"kms/hxl.kms.clientclueparam.read"]){//系统参数
      
      NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (BCDictIsEmpty(receiveData)) {
        return;
      }
     
      self.isTakeLook = [[receiveData objectForKeyNil:@"take_look"] isEqual:@(1)] ? YES : NO;
      self.isHaveMeassage = [[receiveData objectForKeyNil:@"target_audit_stock"] isEqual:@(1)] ? YES : NO;
   
    } else if ([url isEqualToString:@"kms/hxl.kms.approvataskhistory.find"]){//审批记录
     
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      NSArray *oldArr = [receiveData mutableCopy];
      NSMutableArray *newArr = [NSMutableArray array];
      NSInteger lastEndIndex = 0;
      NSInteger lenght = oldArr.count;
      
      for (int i = 0; i < receiveData.count; i ++) {
        NSDictionary *dic = [receiveData objectAtIndexCheck:i];
        if ([[dic objectForKeyNil:@"node_type"] isEqualToString:@"END"]) {
          
          NSArray *tmpArr = [oldArr subarrayWithRange:NSMakeRange(lastEndIndex, (i + 1) - lastEndIndex)];
          [newArr addObject:tmpArr];
          lastEndIndex = i + 1;
        }
      }
      
      if (oldArr.count > lastEndIndex + 1) {
        [newArr addObject:[oldArr subarrayWithRange:NSMakeRange(lastEndIndex + 1, lenght  - (lastEndIndex + 1))]];
      }
      
      self.auditArr = [newArr mutableCopy];
      
    }
    
  } failure:^(NSString * _Nonnull errorResult) {
   
    
    dispatch_group_leave(group);
  }];
}
#pragma mark 初始化导航栏
- (void)initToolBar{
  
  //  导航栏
  _toolbar = [[UIView alloc] initWithFrame: CGRectMake(0, 0, BCWidth, self.heightTop + 44)];
  if (self.pushType == PushAnimationTypeNone) {
    _toolbar.alpha = 1;
  } else {
    _toolbar.alpha = 0;
  }
  [self.view addSubview:_toolbar];
  
  _alphaV = [[UIView alloc] initWithFrame:_toolbar.bounds];
  _alphaV.backgroundColor = [UIColor whiteColor];
  _alphaV.alpha = 0;
  [_toolbar insertSubview:_alphaV atIndex:0];
  
  //  返回按钮
  UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
  switchMap.frame = CGRectMake(RealSize(14),self.heightTop + 4, RealSize(32), RealSize(32));
  switchMap.backgroundColor = [UIColor whiteColor];
  [switchMap addTarget:self action:@selector(clickBack) forControlEvents:UIControlEventTouchUpInside];
  UIImageView *backIM = [[UIImageView alloc] initWithFrame:CGRectMake((RealSize(32) - 18)/2, (RealSize(32) - 18)/2, 18, 18)];
  backIM.image = [UIImage imageNamed:@"nav_back"];
  [switchMap addSubview:backIM];
  switchMap.layer.cornerRadius = RealSize(32)/2;
  [_toolbar addSubview:switchMap];
  
  //    省略号按钮
  //  UIButton *moreBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  //  moreBtn.frame = CGRectMake(BCWidth - 46,self.heightTop + 4, 32, 32);
  //  moreBtn.backgroundColor = [UIColor whiteColor];
  //  [moreBtn addTarget:self action:@selector(showBottomSheet) forControlEvents:UIControlEventTouchUpInside];
  //  moreBtn.layer.cornerRadius = 16;
  //  UIImageView *moreIM = [[UIImageView alloc] initWithFrame:CGRectMake(7, 7, 18, 18)];
  //  moreIM.image = [UIImage imageNamed:@"icon_more"];
  //  [moreBtn addSubview:moreIM];
  //  [_toolbar addSubview:moreBtn];
  
  //  关注按钮
  _followBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  _followBtn.frame = CGRectMake(BCWidth - RealSize(32) - RealSize(14),self.heightTop + 4, RealSize(32), RealSize(32));
  _followBtn.backgroundColor = [UIColor whiteColor];
  [_followBtn addTarget:self action:@selector(clickFollow:) forControlEvents:UIControlEventTouchUpInside];
  _followBtn.layer.cornerRadius = RealSize(32)/2;
  [_followBtn setImage:[UIImage imageNamed:@"point_follow"] forState:UIControlStateNormal];
  [_followBtn setImage:[UIImage imageNamed:@"point_follows"] forState:UIControlStateSelected];
  _followBtn.imageEdgeInsets = UIEdgeInsetsMake(7, 7, 7, 7);
  [_toolbar addSubview: _followBtn];
  
  //  分享
  _shareBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  _shareBtn.frame = CGRectMake(_followBtn.left - RealSize(32) - RealSize(16),self.heightTop + 4, RealSize(32), RealSize(32));
  _shareBtn.backgroundColor = [UIColor whiteColor];
  [_shareBtn addTarget:self action:@selector(clickToShare) forControlEvents:UIControlEventTouchUpInside];
  _shareBtn.layer.cornerRadius = RealSize(32)/2;
  UIImageView *shareIM = [[UIImageView alloc] initWithFrame:CGRectMake((RealSize(32) - 18)/2, (RealSize(32) - 18)/2, 18, 18)];
  shareIM.image = [UIImage imageNamed:@"icon_share"];
  [_shareBtn addSubview:shareIM];
  NSString *sharePoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"SHARE_POINT"];
  if ([sharePoint isEqualToString:@"1"]) {
    [_toolbar addSubview:_shareBtn];
  }
  
  //    分割线
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(0, self.heightTop + RealSize(43),BCWidth , 1)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [_alphaV addSubview:lineV];
  
}

#pragma mark 底部操作按钮
- (void)showBottomButton{
  
  self.bottomOpereateV = [[UIView alloc] initWithFrame:CGRectMake(0, BCHeight - 90 , BCWidth, 90)];
  self.bottomOpereateV.backgroundColor = [UIColor whiteColor];
  [self.view addSubview:self.bottomOpereateV];
  
  
  NSString *stateStr = [self changeState:[self.detailDic objectNilForKey:@"state"]];
  NSArray *titleArr = @[];
  NSArray *imageArr = @[];
  if ([stateStr isEqualToString:@"制单"]) {
    titleArr = @[@"编辑",@"发起审批",@"添加跟进",@"变更跟进人",@"更多"];
    imageArr = @[@"bottom_edit",@"bottom_approve",@"bottom_follow",@"bottom_change",@"bottom_more"];
  } else if ([stateStr isEqualToString:@"待审批"]) {
    titleArr = @[@"审批通过",@"添加跟进",@"变更跟进人",@"撤销审批",@"复制"];
    imageArr = @[@"bottom_agree",@"bottom_follow",@"bottom_change",@"bottom_revoke",@"bottom_copy"];
  } else if ([stateStr isEqualToString:@"审批通过"]) {
    NSString *transPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"SECOND_EDIT_POINT"];
    if (![transPoint isEqualToString:@"1"]) {
      titleArr = @[@"否决",@"添加跟进",@"变更跟进人",@"复制"];
      imageArr = @[@"bottom_reject",@"bottom_follow",@"bottom_change",@"bottom_copy"];
    } else {
      titleArr = @[@"编辑",@"否决",@"添加跟进",@"变更跟进人",@"复制"];
      imageArr = @[@"bottom_edit",@"bottom_reject",@"bottom_follow",@"bottom_change",@"bottom_copy"];
    }
    
  } else if ([stateStr isEqualToString:@"否决"]) {
    titleArr = @[@"重新打开",@"添加跟进",@"变更跟进人",@"复制"];
    imageArr = @[@"bottom_reopen",@"bottom_follow",@"bottom_change",@"bottom_copy"];
  } else if ([stateStr isEqualToString:@"已单签"] || [stateStr isEqualToString:@"已双签"]) {
    titleArr = @[@"变更跟进人",@"复制"];
    imageArr = @[@"bottom_change",@"bottom_copy"];
  }
  
  CGFloat width = BCWidth/titleArr.count;
  for (int i = 0; i < titleArr.count; i ++) {
    
    UIButton *btn = [[UIButton alloc] initWithFrame:CGRectMake(i * width, 0, width, 56)];
    [self.bottomOpereateV addSubview:btn];
    [btn addTarget:self action:@selector(clickBottomOpereate:) forControlEvents:UIControlEventTouchUpInside];
    
    UIImageView *leftIM = [[UIImageView alloc] initWithFrame:CGRectMake((width - 20)/2, 8, 20, 20)];
    leftIM.image = [UIImage imageNamed:[imageArr objectAtIndexCheck:i]];
    leftIM.tag = 444;
    [btn addSubview:leftIM];
    
    UILabel *timeStartL = [[UILabel alloc] initWithFrame:CGRectMake(0, leftIM.bottom + 2, width, 17)];
    timeStartL.text = [titleArr objectAtIndexCheck:i];
    timeStartL.textAlignment = NSTextAlignmentCenter;
    timeStartL.font = [UIFont systemFontOfSize:MutilFont(12)];
    timeStartL.tag = 333;
    timeStartL.textColor = COLOR(29, 33, 41);
    [btn addSubview:timeStartL];
  }
  
  
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(0, 0, BCWidth , 1)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [self.bottomOpereateV addSubview:lineV];
  
  if (titleArr.count == 0) {
    self.bottomOpereateV.hidden = YES;
  }
}

- (void)clickBottomOpereate:(UIButton *)sender{
  
  __weak typeof(self)weakSelf = self;
  
  UILabel *leftL = [sender viewWithTag:333];
  UIImageView *leftIM = [sender viewWithTag:444];
  
  if ([leftL.text isEqualToString:@"添加跟进"]) {
    [self.operatV hideModal];
    [self clickToFollowUp];
    
  } else  if ([leftL.text isEqualToString:@"变更跟进人"]) {
    
    NSString *transPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"TRANS_POINT"];
    if (![transPoint isEqualToString:@"1"]) {
      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有变更跟进人权限"];
      return;
    }
    [self.operatV hideModal];
    [self showFollowView];
    
  } else  if ([leftL.text isEqualToString:@"编辑"]) {
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"EDIT_POINT"];
    if (![editPoint isEqualToString:@"1"]) {
      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有点位编辑权限"];
      return;
    }
    
    [self.operatV hideModal];
    NSString *pointState = [NSString stringWithFormat:@"%@",[self changeState:[self.detailDic objectNilForKey:@"state"]]];
    MAEditPointViewController *vc = [[MAEditPointViewController alloc] init];
    vc.dataDic = self.detailDic;
    vc.pointId = self.pointId;
    vc.orgId = self.orgId;
    vc.state = pointState;
    vc.draftitemChangeDic = self.itemChangeDic;
    vc.followState = [self.detailDic objectNilForKey:@"follow_state"];
    [self.navigationController pushViewController:vc animated:YES];
    
  } else  if ([leftL.text isEqualToString:@"撤销审批"]) {
    
    NSString *userName = [[NSUserDefaults standardUserDefaults] objectForKey:@"MAP_USERNAME"];
    if (![userName isEqualToString:[self.detailDic objectForKeyNil:@"update_by"]]) {
      [self.notiflyView showModal:NotiflyFail andTitle:@"您不是当前操作人，无法撤销审批"];
      return;
    }
    
    [self.operatV hideModal];
    [self clickRevoke];
  } else if ([leftL.text isEqualToString:@"重新打开"]) {
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"EDIT_POINT"];
    if (![editPoint isEqualToString:@"1"]) {
      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有点位编辑权限"];
      return;
    }
    
    [self.operatV hideModal];
    
    if (self.isHaveMeassage) {//需要审批意见
      MAWriteMeassageView *meassgaeV = [[MAWriteMeassageView alloc] initWithName:@"审批意见"];
      [meassgaeV showModal];
      meassgaeV.okBlock = ^(NSString *dataString) {
        [weakSelf clickReopen:dataString];
      };
    } else {//不需要
      [self clickReopen:@""];
    }
    
    
  } else if ([leftL.text isEqualToString:@"否决"]) {
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"REJECT_POINT"];
    if (![editPoint isEqualToString:@"1"]) {
      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有点位否决权限"];
      return;
    }
    
    [self.operatV hideModal];
    
    if (self.isHaveMeassage) {//需要审批意见
      MAWriteMeassageView *meassgaeV = [[MAWriteMeassageView alloc] initWithName:@"审批意见"];
      [meassgaeV showModal];
      meassgaeV.okBlock = ^(NSString *dataString) {
        [weakSelf clickRejectButton:dataString];
      };
    } else {//不需要
      [self clickRejectButton:@""];
    }
    
  } else if ([leftL.text isEqualToString:@"发起审批"]) {
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"AUDIT_POINT"];
    if (![editPoint isEqualToString:@"1"]) {
      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有点位发起审批权限"];
      return;
    }
    
    [self.operatV hideModal];
    
    [self clickStartApply];
    
  } else if ([leftL.text isEqualToString:@"审批通过"]) {
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"SECOND_AUDIT_POINT"];
    if (![editPoint isEqualToString:@"1"]) {
      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有点位审批通过权限"];
      return;
    }
    
    [self.operatV hideModal];
    
    if (self.isHaveMeassage) {//需要审批意见
      MAWriteMeassageView *meassgaeV = [[MAWriteMeassageView alloc] initWithName:@"审核意见"];
      [meassgaeV showModal];
      meassgaeV.okBlock = ^(NSString *dataString) {
        [weakSelf clickSecondAudit:dataString];
      };
    } else {//不需要
      [self clickSecondAudit:@""];
    }
    
  } else if ([leftL.text isEqualToString:@"更多"]) {
    
    leftL.text = @"收起";
    [UIView animateWithDuration:0.25 animations:^{
      leftIM.transform = CGAffineTransformMakeRotation(M_PI);
    } completion:nil];
    [self.operatV showModal];
    
    self.operatV.hideBlock = ^(NSString *dataString) {
      leftL.text = @"更多";
      [UIView animateWithDuration:0.25 animations:^{
        leftIM.transform = CGAffineTransformIdentity;
      } completion:nil];
    };
    
    self.operatV.okBlock = ^(NSString *dataString) {
      if ([dataString isEqualToString:@"删除"]) {
        
        NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"DELETE_POINT"];
        if (![editPoint isEqualToString:@"1"]) {
          [weakSelf.notiflyView showModal:NotiflyFail andTitle:@"您没有点位删除权限"];
          return;
        }
        
        
        [weakSelf.operatV hideModal];
        [weakSelf clickDeletePoint];
        
      } else if ([dataString isEqualToString:@"复制"]) {
        NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"EDIT_POINT"];
        if (![editPoint isEqualToString:@"1"]) {
          [weakSelf.notiflyView showModal:NotiflyFail andTitle:@"您没有点位编辑权限"];
          return;
        }
        
        [weakSelf.operatV hideModal];
        
        [weakSelf clickToCopy];
      }
    };
    
  } else if ([leftL.text isEqualToString:@"收起"]) {
   
    [weakSelf.operatV hideModal];
    
  } else if ([leftL.text isEqualToString:@"复制"]) {
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"EDIT_POINT"];
    if (![editPoint isEqualToString:@"1"]) {
//      [[UIApplication sharedApplication].keyWindow makeToast:@"您没有点位编辑权限" duration:1 position:CSToastPositionCenter];
      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有点位编辑权限"];
      return;
    }
    
    [self.operatV hideModal];
    
    [self clickToCopy];
  }
  
}



- (void)clickRejectButton:(NSString *)reason{
  __weak typeof(self)weakSelf = self;
  
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定要否决吗?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf requestRejectButton:reason];
  };
}
- (void)requestRejectButton:(NSString *)reason{
  
  NSDictionary *dic = @{@"id":self.pointId,@"memo":reason};
  
  [self.loadingView showModal];
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storeplan.reject" Params:dic success:^(NSDictionary *successResult) {
    
    [self.loadingView hideModal];
    
   
    
    [self.statusView showModal:ToastSuccess andTitle:@"否决成功"];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHMAPDATA" object:nil];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self resetView];
    });
    
    
  } failure:^(NSString *errorResult) {
    [self.loadingView hideModal];
    
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}

- (void)clickToCopy{
  
  MAEditPointViewController *vc = [[MAEditPointViewController alloc] init];
  vc.dataDic = self.detailDic;
  vc.pointId = self.pointId;
  vc.orgId = self.orgId;
  vc.state = @"复制";
  vc.draftitemChangeDic = self.itemChangeDic;
  vc.followState = [self.detailDic objectNilForKey:@"follow_state"];
  [self.navigationController pushViewController:vc animated:YES];
}
- (void)clickStartApply{
    
  MAEditPointViewController *vc = [[MAEditPointViewController alloc] init];
  vc.dataDic = self.detailDic;
  vc.pointId = self.pointId;
  vc.orgId = self.orgId;
  vc.state = @"发起审批";
  vc.draftitemChangeDic = self.itemChangeDic;
  vc.followState = [self.detailDic objectNilForKey:@"follow_state"];
  [self.navigationController pushViewController:vc animated:YES];
}

- (void)requestStartApply{
  
  NSDictionary *dic = @{@"id":self.pointId,@"memo":@""};
  
  [self.loadingView showModal];
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storeplan.initiateapproval" Params:dic success:^(NSDictionary *successResult) {
    
    [self.loadingView hideModal];
    
   
    
    [self.statusView showModal:ToastSuccess andTitle:@"发起审批成功"];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHMAPDATA" object:nil];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self resetView];
    });
    
    
  } failure:^(NSString *errorResult) {
    [self.loadingView hideModal];
    
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
}



- (void)clickSecondAudit:(NSString *)reason{
  
  __weak typeof(self)weakSelf = self;
  
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定要审批通过吗?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf requestSecondAudit:reason];
  };
  
}

- (void)requestSecondAudit:(NSString *)reason{
  
  
  NSDictionary *dic = @{@"id":self.pointId,@"memo":reason};
  
  [self.loadingView showModal];
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storeplan.secondaudit" Params:dic success:^(NSDictionary *successResult) {
    
    [self.loadingView hideModal];
    
   
    
    [self.statusView showModal:ToastSuccess andTitle:@"审批通过成功"];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHMAPDATA" object:nil];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self resetView];
    });
    
    
  } failure:^(NSString *errorResult) {
    [self.loadingView hideModal];
    
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}

- (void)clickDeletePoint{
  
  __weak typeof(self)weakSelf = self;
  
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定要删除点位吗?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf requesDeletePoint];
  };
}

- (void)requesDeletePoint{
  
  NSDictionary *dic = @{@"ids":@[self.pointId]};
  
  [self.loadingView showModal];
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storeplan.delete" Params:dic success:^(NSDictionary *successResult) {
    
    [self.loadingView hideModal];
    
   
    [self.statusView showModal:ToastSuccess andTitle:@"删除点位成功"];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHMAPDATA" object:nil];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self.navigationController popViewControllerAnimated:YES];
    });
    
    
  } failure:^(NSString *errorResult) {
    [self.loadingView hideModal];
    
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}

#pragma mark 点击去分享
- (void)clickToShare{
  
  if ([[self.detailDic objectForKeyNil:@"allow_look"] isEqual:@(0)]) {
    
//    [[UIApplication sharedApplication].keyWindow makeToast:@"该点位不允许带看，无法分享" duration:1 position:CSToastPositionCenter];
    [self.notiflyView showModal:NotiflyFail andTitle:@"该点位不允许带看，无法分享"];
    return;
  }
  
  __weak typeof(self)weakSelf = self;
  NSDictionary *dic;
  if (BCStringIsEmpty(self.orgId)) {
    dic = @{@"store_plan_area_code":[NSString stringWithFormat:@"%@",[self.detailDic objectNilForKey:@"area_code"]],@"is_share":@(1),@"types": @[@"NORMAL", @"INTENT"],@"intent_store_count":@"1",@"intent_store_count_symbol":@">=",@"page_size":@(200)};
  } else {
    dic = @{@"store_plan_area_code":[NSString stringWithFormat:@"%@",[self.detailDic objectNilForKey:@"area_code"]],@"is_share":@(1),@"types": @[@"NORMAL", @"INTENT"],@"intent_store_count":@"1",@"intent_store_count_symbol":@">=",@"page_size":@(200),@"org_id":self.orgId};
  }
  
  [self.loadingView showModal];
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.client.share.page" Params:dic success:^(NSDictionary * _Nonnull successResult) {
    [self.loadingView hideModal];
    
    NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
    if (BCDictIsEmpty(receiveData)) {
      return;
    }
    NSArray *contentArr = [receiveData objectNilForKey:@"content"];
  
    if (![contentArr isKindOfClass:[NSArray class]]) {
      return;
    }
    
    NSString *name = [NSString stringWithFormat:@"%@",[self.detailDic objectNilForKey:@"name"]];
    MAShareView *shareView = [[MAShareView alloc] initWithName:name andShare:contentArr isTakeLook:self.isTakeLook];
    [shareView showModal];
    shareView.okBlock = ^(NSDictionary *dataDic) {
      [weakSelf getShareUrl:dataDic];
    };
    
  } failure:^(NSString * _Nonnull errorResult) {
    [self.loadingView hideModal];
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}

- (void)getShareUrl:(NSDictionary *)paras{
  
  
  NSString *url = @"";
  if ([[[NSUserDefaults standardUserDefaults] objectForKey:@"DEVENV"] isEqualToString:@"PROD"]) {
    url = @"https://web.xlbsoft.com/join";
  } else {
    url = @"https://web-tesast.xlbsoft.com/join";
  }
  
  NSDictionary *dic = @{
    @"store_plan_id":self.pointId,
    @"client_ids":[paras objectForKeyNil:@"client_id"],
    @"valid_period":[paras objectForKeyNil:@"valid_period"],
    @"url":url
  };
 
  [self.loadingView showModal];
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storeplan.sharerecord.save" Params:dic success:^(NSDictionary * _Nonnull successResult) {
    [self.loadingView hideModal];
    
    NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
   
    if (self.isTakeLook || BCDictIsEmpty(receiveData)) {
     
    
      [self.statusView showModal:ToastSuccess andTitle:@"分享成功"];
   
    } else {

      UIPasteboard *pp = [UIPasteboard generalPasteboard];
      pp.string = [NSString stringWithFormat:@"%@",[dic objectForKeyNil:@"url"]];
      
      [self.statusView showModal:ToastSuccess andTitle:@"分享成功，分享链接已复制"];
    }
    
    [self resetView];
    
  } failure:^(NSString * _Nonnull errorResult) {
    [self.loadingView hideModal];
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}

#pragma mark 网络请求回来加载轮播图上的标签
- (void)loadBannerLabel:(NSArray *)files{
  
  [self.bannerLabels removeAllObjects];
  [self.bannerImages removeAllObjects];
  [self.previewUrls removeAllObjects];
  if ([files isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(files)) {
   
    
    NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
    NSArray *videArr = @[@"mp4",@"mov",@"MP4",@"MOV",@"WMV",@"AVI",@"MKV",@"wmv",@"avi",@"mkv"];
    
    NSMutableArray *headerUrls = [NSMutableArray arrayWithCapacity:20];
    NSMutableArray *frontUrls = [NSMutableArray arrayWithCapacity:20];
    NSMutableArray *leftUrls = [NSMutableArray arrayWithCapacity:20];
    NSMutableArray *rightUrls = [NSMutableArray arrayWithCapacity:20];
    NSMutableArray *roomUrls = [NSMutableArray arrayWithCapacity:20];
    NSMutableArray *distircitUrls = [NSMutableArray arrayWithCapacity:20];
    NSMutableArray *otherUrls = [NSMutableArray arrayWithCapacity:20];
    
    for (NSDictionary *urlDic  in files) {
      
      if ([[urlDic objectNilForKey:@"ref_sub_type"] isEqualToString:@"pointStoreHead"] || [[urlDic objectNilForKey:@"ref_sub_type"] isEqualToString:@"storeHeader"]) {//门头
        if ([temArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {//图片
          
          if ([[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"svg"] || [[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"SVG"]) {
            [headerUrls addObject:[urlDic objectNilForKey:@"ref_img"]];
          } else {
            [headerUrls addObject:[urlDic objectNilForKey:@"url"]];
          }
        } else {//是视频，需要转换生成一下封面
          
          if ([videArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {
            NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[urlDic objectNilForKey:@"url"]];
            [headerUrls addObject:videoUrl];
          }
          
        }
        
      } else  if ([[urlDic objectNilForKey:@"ref_sub_type"] isEqualToString:@"storeBrandFront"] ) {//店正
        if ([temArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {//图片
          
          if ([[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"svg"] || [[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"SVG"]) {
            [frontUrls addObject:[urlDic objectNilForKey:@"ref_img"]];
          } else {
            [frontUrls addObject:[urlDic objectNilForKey:@"url"]];
          }
        } else {//是视频，需要转换生成一下封面
          
          if ([videArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {
            NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[urlDic objectNilForKey:@"url"]];
            [frontUrls addObject:videoUrl];
          }
          
        }
        
      } else  if ([[urlDic objectNilForKey:@"ref_sub_type"] isEqualToString:@"storeBrandLeft"] ) {//店左
        if ([temArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {//图片
          
          if ([[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"svg"] || [[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"SVG"]) {
            [leftUrls addObject:[urlDic objectNilForKey:@"ref_img"]];
          } else {
            [leftUrls addObject:[urlDic objectNilForKey:@"url"]];
          }
        } else {//是视频，需要转换生成一下封面
          
          if ([videArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {
            NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[urlDic objectNilForKey:@"url"]];
            [leftUrls addObject:videoUrl];
          }
          
        }
        
      }else  if ([[urlDic objectNilForKey:@"ref_sub_type"] isEqualToString:@"storeBrandRight"] ) {//店右
        if ([temArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {//图片
          
          if ([[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"svg"] || [[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"SVG"]) {
            [rightUrls addObject:[urlDic objectNilForKey:@"ref_img"]];
          } else {
            [rightUrls addObject:[urlDic objectNilForKey:@"url"]];
          }
        } else {//是视频，需要转换生成一下封面
          
          if ([videArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {
            NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[urlDic objectNilForKey:@"url"]];
            [rightUrls addObject:videoUrl];
          }
          
        }
        
      }else  if ([[urlDic objectNilForKey:@"ref_sub_type"] isEqualToString:@"inRoom"] ) {//室内
        if ([temArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {//图片
          
          if ([[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"svg"] || [[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"SVG"]) {
            [roomUrls addObject:[urlDic objectNilForKey:@"ref_img"]];
          } else {
            [roomUrls addObject:[urlDic objectNilForKey:@"url"]];
          }
        } else {//是视频，需要转换生成一下封面
          
          if ([videArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {
            NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[urlDic objectNilForKey:@"url"]];
            [roomUrls addObject:videoUrl];
          }
          
        }
        
      }else  if ([[urlDic objectNilForKey:@"ref_sub_type"] isEqualToString:@"businessDistircitEnviroment"] ) {//周边
        if ([temArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {//图片
          
          if ([[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"svg"] || [[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"SVG"]) {
            [distircitUrls addObject:[urlDic objectNilForKey:@"ref_img"]];
          } else {
            [distircitUrls addObject:[urlDic objectNilForKey:@"url"]];
          }
        } else {//是视频，需要转换生成一下封面
          
          if ([videArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {
            NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[urlDic objectNilForKey:@"url"]];
            [distircitUrls addObject:videoUrl];
          }
          
        }
      } else {//其他变动项里的图片
        if ([temArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {//图片
          
          if ([[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"svg"] || [[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"SVG"]) {
            [otherUrls addObject:[urlDic objectNilForKey:@"ref_img"]];
          } else {
            [otherUrls addObject:[urlDic objectNilForKey:@"url"]];
          }
        } else {//是视频，需要转换生成一下封面
          
          if ([videArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {
            NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[urlDic objectNilForKey:@"url"]];
            [otherUrls addObject:videoUrl];
          }
          
        }
      }
    }
    
    //      分类完所有数据,处理最终数据
    NSInteger tmpIndex = 0;
    if (!BCArrayIsEmpty(headerUrls)) {
      [self.bannerLabels addObject:@{@"title":@"门头",@"minIndex":@"0",@"maxIndex":[NSString stringWithFormat:@"%lu",(unsigned long)headerUrls.count]}];
      tmpIndex += headerUrls.count;
    }
    
    if (!BCArrayIsEmpty(frontUrls)) {
      [self.bannerLabels addObject:@{@"title":@"近景",@"minIndex":[NSString stringWithFormat:@"%lu",(unsigned long)tmpIndex],@"maxIndex":[NSString stringWithFormat:@"%lu",(unsigned long)(frontUrls.count + tmpIndex)]}];
      tmpIndex += frontUrls.count;
    }
    
    if (!BCArrayIsEmpty(leftUrls)) {
      [self.bannerLabels addObject:@{@"title":@"中景",@"minIndex":[NSString stringWithFormat:@"%lu",(unsigned long)tmpIndex],@"maxIndex":[NSString stringWithFormat:@"%lu",(unsigned long)(leftUrls.count + tmpIndex)]}];
      tmpIndex += leftUrls.count;
    }
    
    if (!BCArrayIsEmpty(rightUrls)) {
      [self.bannerLabels addObject:@{@"title":@"远景",@"minIndex":[NSString stringWithFormat:@"%lu",(unsigned long)tmpIndex],@"maxIndex":[NSString stringWithFormat:@"%lu",(unsigned long)(rightUrls.count + tmpIndex)]}];
      tmpIndex += rightUrls.count;
    }
    
    if (!BCArrayIsEmpty(roomUrls)) {
      [self.bannerLabels addObject:@{@"title":@"室内",@"minIndex":[NSString stringWithFormat:@"%lu",(unsigned long)tmpIndex],@"maxIndex":[NSString stringWithFormat:@"%lu",(unsigned long)(roomUrls.count + tmpIndex)]}];
      tmpIndex += roomUrls.count;
    }
    
    if (!BCArrayIsEmpty(distircitUrls)) {
      [self.bannerLabels addObject:@{@"title":@"周边",@"minIndex":[NSString stringWithFormat:@"%lu",(unsigned long)tmpIndex],@"maxIndex":[NSString stringWithFormat:@"%lu",(unsigned long)(distircitUrls.count + tmpIndex)]}];
      tmpIndex += distircitUrls.count;
    }
    if (!BCArrayIsEmpty(otherUrls)) {
      [self.bannerLabels addObject:@{@"title":@"更多",@"minIndex":[NSString stringWithFormat:@"%lu",(unsigned long)tmpIndex],@"maxIndex":[NSString stringWithFormat:@"%lu",(unsigned long)(otherUrls.count + tmpIndex)]}];
      
    }
    
    
    //      分类完所有数据处理轮播图数组
    [self.bannerImages addObjectsFromArray:headerUrls];
    [self.bannerImages addObjectsFromArray:frontUrls];
    [self.bannerImages addObjectsFromArray:leftUrls];
    [self.bannerImages addObjectsFromArray:rightUrls];
    [self.bannerImages addObjectsFromArray:roomUrls];
    [self.bannerImages addObjectsFromArray:distircitUrls];
    [self.bannerImages addObjectsFromArray:otherUrls];
    for (NSString *url in self.bannerImages) {//预览轮播图
      NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
      if ([temArr containsObject:[url pathExtension]]) {
        [self.previewUrls addObject:[url stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
      }
    }
    self.bannerView.imagesArray = self.bannerImages;
    _indexLabel.text = [NSString stringWithFormat:@"%ld/%lu",(long)(self.scrollIndex + 1),(unsigned long)self.bannerImages.count];
    
    //    渲染轮播图标签
    CGFloat itemW = 44;
    _bannerLabelV = [[UIView alloc] initWithFrame:CGRectMake(8, 307 - 24 - 7, itemW * self.bannerLabels.count, 24)];
    _bannerLabelV.layer.cornerRadius = 12;
    _bannerLabelV.backgroundColor = ACOLOR(0, 0, 0, 0.5);
    [self.bannerView addSubview: _bannerLabelV];
    
    //  滑块
    _swipeLayer = [[CALayer alloc] init];
    _swipeLayer.cornerRadius = 10;
    _swipeLayer.backgroundColor = [UIColor whiteColor].CGColor;
    [_bannerLabelV.layer addSublayer:_swipeLayer];
    
    
    for (int i = 0; i < self.bannerLabels.count; i ++) {
      
      NSDictionary  *dic = self.bannerLabels[i];
      UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
      switchMap.frame = CGRectMake(itemW * i, 0, itemW, 24);
      [switchMap setTitle:[dic objectNilForKey:@"title"] forState:UIControlStateNormal];
      [switchMap setTitleColor:COLOR(29, 33, 41) forState:UIControlStateSelected ];
      [switchMap setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
      [switchMap addTarget:self action:@selector(changeValue:) forControlEvents:UIControlEventTouchUpInside];
      switchMap.titleLabel.font = [UIFont systemFontOfSize:MutilFont(12) weight:UIFontWeightMedium];
      switchMap.tag = 600 + i;
      [_bannerLabelV addSubview:switchMap];
      if (self.scrollIndex >= [[dic objectNilForKey:@"minIndex"] integerValue] && self.scrollIndex < [[dic objectNilForKey:@"maxIndex"] integerValue]) {
        switchMap.selected = YES;
        self.selectBannerButton = switchMap;
        _swipeLayer.frame =  CGRectMake(switchMap.frame.origin.x + 2, 2, switchMap.frame.size.width - 4, 20);
      }
      
    }
    
  } else {
    
    [self.bannerImages addObject:@"https://hxl-applet.oss-cn-hangzhou.aliyuncs.com/插旗系統/img_v3_02g0_6c7950c5-c78f-40e3-997a-332bba8d1f9g.png"];
    self.bannerView.imagesArray = self.bannerImages;
    _indexLabel.text = [NSString stringWithFormat:@"%ld/%lu",(long)(self.scrollIndex + 1),(unsigned long)self.bannerImages.count];
    
    
  }
  
  
}
// 点击选中轮播图标签
- (void)changeValue:(UIButton *)button {
  
  
  if (button != self.selectBannerButton) {
    
    [self.bannerView stopAutoScrollPage];
    
    self.selectBannerButton.selected = NO;
    button.selected = YES;
    self.selectBannerButton = button;
    
    //    滑块动画
    [UIView animateWithDuration:0.25 animations:^{
      self.swipeLayer.frame = CGRectMake(button.frame.origin.x + 2, 2, button.frame.size.width - 4, 20);
    }];
    //    滚动轮播图
    NSDictionary *dic = [self.bannerLabels objectAtIndexCheck:button.tag - 600];
    [self.bannerView scrollToPageAtIndex:[[dic objectNilForKey:@"minIndex"] integerValue] Animation:NO];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self.bannerView startAutoScrollPage];
    });
  }
  
}

#pragma mark 所有网络请求结束之后初始化左右滚动scrollview界面
- (void)loadDataWithScrollView{
  
  //  加载首页scrollview
  UIView *homeBasicV = [self loadBasicInfo:self.detailDic];
  [self.homeScrollView addSubview:homeBasicV];
  //  变动项
  UIView *changeV = [self loadChangeItem:homeBasicV.bottom];
  [self.homeScrollView addSubview:changeV];
  //  房东信息
  UIView *landlordV = [self loadLandlord:changeV.bottom];
  [self.homeScrollView addSubview:landlordV];
  //  点位动态
  UIView *pointStateV = [self loadPointState:landlordV.bottom];
  [self.homeScrollView addSubview:pointStateV];
  //  带看记录
  UIView *lookV = [self loadLook:pointStateV.bottom];
  [self.homeScrollView addSubview:lookV];
  //  分享记录
  UIView *shareV = [self loadShare:lookV.bottom];
  [self.homeScrollView addSubview:shareV];
  
  //  审批记录
  UIView *auditV = [self loadAudit:shareV.bottom];
  [self.homeScrollView addSubview:auditV];
  self.homeScrollView.contentSize = CGSizeMake(0, auditV.bottom  + 40);
  
  //  tab下的房东信息
  [self loadLandlordRecord];
  
  //  tab下的点位动态
  [self loadPointStateRecord];
  
  //  tab下的带看记录
  [self loadLookRecord];
  
  //  tab下的分享记录
  [self loadShareRecord];
  
  //  tab下的审核记录
  [self loadAuditRecord];
  
}
#pragma mark 所有网络请求回来加载首页基础信息界面
- (UIView *)loadBasicInfo:(NSDictionary *)receiveData{
  
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,12,BCWidth,240)];
  contentView.backgroundColor = [UIColor whiteColor];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,100,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = @"基本信息";
  [contentView addSubview:nameLabel];
  
  //    分割线
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,49.5, BCWidth - 16, 0.5)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [contentView addSubview:lineV];
  
  
  //      底部多个
  NSMutableArray *leftArr = [NSMutableArray arrayWithArray:@[@"所属商圈",@"所属组织"]];
  if (self.levelDisplay) {
    [leftArr addObject:@"店铺等级"];
  }
  
  if (self.attDisplay) {
    [leftArr addObject:@"店铺属性"];
  }
  
  if (self.doorDisplay) {
    [leftArr addObject:@"是否翻牌门店"];
  }
  
  
  UIView *bottomV = [[UIView alloc]initWithFrame:CGRectMake(16, nameLabel.bottom + 14, BCWidth - 32, 35 * leftArr.count)];
  [contentView addSubview:bottomV];
  for (int i = 0; i < leftArr.count; i ++) {
    
    UILabel *leftL = [[UILabel alloc] initWithFrame:CGRectMake(0, (21 + 14) *i, 120, 21)];
    leftL.text = [leftArr objectAtIndex:i];
    leftL.font = [UIFont systemFontOfSize:MutilFont(15)];
    leftL.textColor = ACOLOR(30, 33, 38, 0.45);
    [bottomV addSubview:leftL];
    
    UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6, (21 + 14) *i, CGRectGetWidth(bottomV.bounds) - leftL.right - 6, 21)];
    rightL.text = [leftArr objectAtIndex:i];
    rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
    rightL.textColor = COLOR(31, 33, 38);
    rightL.textAlignment = NSTextAlignmentRight;
    [bottomV addSubview:rightL];
    
    if (i == 0) {
      rightL.textColor = [UIColor colorWithRed:26/255.0 green:106/255.0 blue:255/255.0 alpha:1];
      NSString *tmpStr = [NSString stringWithFormat:@"%@",[receiveData objectNilForKey:@"business_plan_name"]];
      rightL.text = BCStringIsEmpty(tmpStr) ? @"-" : tmpStr;
      UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(clickToBuniess)];
      [rightL addGestureRecognizer:tap];
      rightL.userInteractionEnabled = YES;
    } else if (i == 1) {
      NSString *tmpStr = [NSString stringWithFormat:@"%@",[receiveData objectNilForKey:@"org_name"]];
      rightL.text = (![tmpStr isKindOfClass: [NSString class]] || BCStringIsEmpty(tmpStr)) ? @"-" : tmpStr;
    }else if (i == 4) {
      NSString *tmpStr = [NSString stringWithFormat:@"%@",[receiveData objectNilForKey:@"store_level"]];
      rightL.text = BCStringIsEmpty(tmpStr) ? @"-" : [NSString stringWithFormat:@"第%@顺位",[self numberToChinese:tmpStr]];
    } else if (i == 3) {
      NSString *tmpStr = [NSString stringWithFormat:@"%@",[receiveData objectNilForKey:@"store_attributes"]];
      rightL.text = BCStringIsEmpty(tmpStr) ? @"-" : [self getMarket:tmpStr];
    } else if (i == 2) {
      rightL.text = [[receiveData objectNilForKey:@"flipped_store"] isEqual:@(1)] ? @"翻牌":@"非翻牌";
    }
    
  }
  
  contentView.height = 35 * leftArr.count + 14 + 50;
  return contentView;
}

- (NSString *)getMarket:(NSString *)type{
  if ([type isEqualToString:@"MARKET"]) {
    return @"-";
  } else if ([type isEqualToString:@"COMPREHENSIVE"]) {
    return @"综合店";
  }else if ([type isEqualToString:@"STANDARD"]) {
    return @"标准店";
  } else {
    return @"旗舰店";
  }
}
#pragma mark 跳转到所属商圈
- (void)clickToBuniess{
  
  NSString *planId = [NSString stringWithFormat:@"%@",[self.detailDic objectNilForKey:@"business_plan_id"]];
  if (BCStringIsEmpty(planId)) {
    [self.notiflyView showModal:NotiflyFail andTitle:@"暂无所属商圈"];
    return;
  }
  MABuniessDetailViewController *sec = [[MABuniessDetailViewController alloc] init];
  sec.pushType = PushAnimationTypeNone;
  sec.pointId = planId;
  sec.scrollIndex = 0;
  [self.navigationController pushViewController:sec animated:YES];
  
}
#pragma mark 所有网络请求回来加载首页变动项界面
- (UIView *)loadChangeItem:(CGFloat)top{
  
  __weak typeof(self)weakSelf = self;
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,top + 12,BCWidth,0)];
  if (BCArrayIsEmpty(self.itemChangeArr)) {
    
    return contentView;;
  }
  
  
  CGFloat itemH = 0;
  
  for (int i = 0; i < self.itemChangeArr.count; i ++) {
    
    NSDictionary *dic = [self.itemChangeArr objectAtIndexCheck:i];
    if ([[dic objectForKeyNil:@"acquiesce"] isEqual:@(1)]) {//跳过系统预设的变动项，基本信息，房东信息等等
      continue;
    }
    
    if ([[dic objectForKeyNil:@"enable"] isEqual:@(0)]) {//未启用的也要跳过
      continue;
    }
    
    //    每一个变动项
    UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH , BCWidth , 0)];
    bottomV.backgroundColor = [UIColor whiteColor];
    [contentView addSubview:bottomV];
    
    //    变动项标题
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.frame = CGRectMake(16,0,BCWidth - 32,50);
    nameLabel.textColor = COLOR(31, 33, 38);
    nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
    nameLabel.text = [NSString stringWithFormat:@"%@",[dic objectNilForKey:@"name"]];
    [bottomV addSubview:nameLabel];
    
    //    分割线
    UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,50, BCWidth - 16, 0.5)];
    lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
    [bottomV addSubview:lineV];
    
    //    变动项
    NSArray *items = [dic objectForKeyNil:@"details"];
    if (BCArrayIsEmpty(items)) {
      continue;
    }
    
    CGFloat LabelH = 50 + 14;
    for (int j = 0; j < items.count; j ++) {
      
      NSDictionary *itemDic = [items objectAtIndexCheck:j];
     
            
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
      NSString *titleNameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"title"]];
      CGFloat height = [titleNameStr boundingRectWithSize:CGSizeMake(120, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      height = height < 21 ? 21 : height;
      UILabel *leftL = [[UILabel alloc] initWithFrame:CGRectMake(16, LabelH, 120, height)];
      leftL.text = titleNameStr;
      leftL.numberOfLines = 0;
      leftL.font = [UIFont systemFontOfSize:MutilFont(15)];
      leftL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:leftL];
      
      if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"IMAGE"]) {//如果是图片
        
        NSArray *imageArr = [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
        
        if (![self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] || ![imageArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(imageArr)) {//如果没有图片，展示-
          
          UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,LabelH, CGRectGetWidth(bottomV.bounds) - leftL.right - 22, 21)];
          rightL.textAlignment = NSTextAlignmentRight;
          rightL.text = [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] ? [NSString stringWithFormat:@"%@",[self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]] : @"-";
          rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
          rightL.textColor = COLOR(31, 33, 38);
          [bottomV addSubview:rightL];
          if (height > 21) {
            LabelH += height + 14;
          } else {
            LabelH += 35;
          }
          
        } else {//如果有图片
          
          UIView *imageV = [[UIView alloc] initWithFrame:CGRectMake(16, leftL.bottom, BCWidth - 32, 0)];
          [bottomV addSubview:imageV];
          CGFloat imageH = (BCWidth - 32 - 24)/4;
          CGFloat paddingL = 8.0; //button 间距
          CGFloat paddingT = 10; //button 间距
          CGFloat pointX = 0; //button X坐标
          CGFloat pointY = 10; //button Y坐标
          
          NSMutableArray *previewUrls = [NSMutableArray arrayWithCapacity:100];
          for (NSDictionary *previewDic in imageArr) {
            [previewUrls addObject:[previewDic objectNilForKey:@"url"]];
          }
          
          for (int k = 0; k < imageArr.count; k ++) {
            
            NSDictionary *imageDic = [imageArr objectAtIndexCheck:k];
            if (pointX + imageH > (BCWidth - 32)) {//换行
              pointX = 0;//X从新开始
              pointY += (imageH + paddingT);//换行后Y+
            }
            
            UIButton *imageBtn = [[UIButton alloc] initWithFrame:CGRectMake(pointX, pointY, imageH, imageH)];
            imageBtn.layer.cornerRadius = 4;
            imageBtn.clipsToBounds = YES;
            UIImageView *imageI = [[UIImageView alloc] initWithFrame:CGRectMake(0,0, imageH, imageH)];
            [imageI sd_setImageWithURL:[NSURL URLWithString:[[imageDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
            [imageBtn addSubview:imageI];
            [imageV addSubview:imageBtn];
            [imageBtn addtargetBlock:^(UIButton *button) {//预览图片
              [ZKPhotoBrowser showWithImageUrls:previewUrls currentPhotoIndex:k sourceSuperView:button];
            }];
            
            pointX += (imageH + paddingL);
          }
          
          imageV.height = pointY + imageH ;
          if (height > 21) {
            LabelH += height + 14 + imageV.height;
          } else {
            LabelH +=  imageV.height + 14 + 21;
          }
          
        }
        
        
      } else if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"VIDEO"]) {//如果是视频
        
        NSArray *videoArr = [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
        
        if (![self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] || ![videoArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(videoArr)) {//如果没有视频，展示-
          
          UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,LabelH, CGRectGetWidth(bottomV.bounds) - leftL.right - 22, 21)];
          rightL.textAlignment = NSTextAlignmentRight;
          rightL.text = [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] ? [NSString stringWithFormat:@"%@",[self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]] : @"-";
          rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
          rightL.textColor = COLOR(31, 33, 38);
          [bottomV addSubview:rightL];
          if (height > 21) {
            LabelH += height + 14;
          } else {
            LabelH += 35;
          }
          
        } else {//如果有视频
          
          UIView *imageV = [[UIView alloc] initWithFrame:CGRectMake(16, leftL.bottom, BCWidth - 32, 0)];
          [bottomV addSubview:imageV];
          CGFloat imageH = (BCWidth - 32 - 24)/4;
          CGFloat paddingL = 8.0; //button 间距
          CGFloat paddingT = 10; //button 间距
          CGFloat pointX = 0; //button X坐标
          CGFloat pointY = 10; //button Y坐标
          
          for (int k = 0; k < videoArr.count; k ++) {
            
            NSDictionary *imageDic = [videoArr objectAtIndexCheck:k];
            if (pointX + imageH > (BCWidth - 32)) {//换行
              pointX = 0;//X从新开始
              pointY += (imageH + paddingT);//换行后Y+
            }
            
            UIButton *imageBtn = [[UIButton alloc] initWithFrame:CGRectMake(pointX, pointY, imageH, imageH)];
            imageBtn.layer.cornerRadius = 4;
            imageBtn.clipsToBounds = YES;
            NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[imageDic objectNilForKey:@"url"]];
            UIImageView *imageI = [[UIImageView alloc] initWithFrame:CGRectMake(0,0, imageH, imageH)];
            [imageI sd_setImageWithURL:[NSURL URLWithString:[videoUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
            [imageBtn addSubview:imageI];
            
            UIImageView *playIM = [[UIImageView alloc] initWithFrame:CGRectMake((imageH - 25)/2, (imageH - 25)/2, 25, 25)];
            playIM.image = [UIImage imageNamed:@"play_video"];
            [imageBtn addSubview:playIM];
            
            [imageV addSubview:imageBtn];
            [imageBtn addtargetBlock:^(UIButton *button) {
              NSURL * url = [NSURL URLWithString:[[imageDic objectForKeyNil:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
              AVPlayerViewController * pVC = [[AVPlayerViewController alloc] init];
              pVC.player = [AVPlayer playerWithURL:url];
              [weakSelf presentViewController:pVC animated:YES completion:nil];
              [pVC.player play];
            }];
            
            
            pointX += (imageH + paddingL);
          }
          
          imageV.height = pointY + imageH ;
          if (height > 21) {
            LabelH += height + 14 + imageV.height;
          } else {
            LabelH +=  imageV.height + 14 + 21;
          }
          
          
        }
        
      } else if([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"TABLE"]){
        
        NSArray *arr = @[];
        if (!BCArrayIsEmpty([self.itemChangeDic objectForKeyNil:[itemDic objectNilForKey:@"id"]])) {
          arr = [self.itemChangeDic objectForKeyNil:[itemDic objectNilForKey:@"id"]];
        }
       
        UIView *tableV = [self getTableView:itemDic andRightArr:arr endTop:leftL.top];
        [bottomV addSubview:tableV];
        LabelH +=  tableV.height;
        
      } else if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"LOCATION"]) {//如果是定位组件
        
        NSDictionary *dic = [self.itemChangeDic objectForKeyNil:[itemDic objectNilForKey:@"id"]];
        NSString *rightStr = @"-";
        if (!BCDictIsEmpty(dic)) {
          rightStr = [NSString stringWithFormat:@"%@",[dic objectNilForKey:@"address"]];
        }
        
        CGFloat rightHeight = [rightStr boundingRectWithSize:CGSizeMake(CGRectGetWidth(bottomV.bounds) - 146, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        rightHeight = rightHeight < 21 ? 21 : rightHeight;
        
        UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,LabelH, CGRectGetWidth(bottomV.bounds) - leftL.right - 22,rightHeight)];
        rightL.textAlignment = NSTextAlignmentRight;
        rightL.text = rightStr;
        rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
        rightL.textColor =  COLOR(26, 106, 255);
        rightL.numberOfLines = 0;
        [bottomV addSubview:rightL];
        
        if (rightHeight > height) {
          LabelH += rightHeight + 14;
        } else {
          if (height > 21) {
            LabelH += height + 14;
          } else {
            LabelH += 35;
          }
        }
        
        [rightL addTapGestureWithBlock:^{
          
          if ([rightStr isEqualToString:@"-"]) {
            [weakSelf.notiflyView showModal:NotiflyFail andTitle:@"定位地址为空，无法查看定位"];
            return;
          }
          
          MALookPointViewController *lookVC = [[MALookPointViewController alloc] init];
          lookVC.pointName = @"查看定位";
          lookVC.pointAddress = [dic objectNilForKey:@"address"];
          lookVC.pointLatitude = [dic objectNilForKey:@"latitude"];
          lookVC.pointLongitude = [dic objectNilForKey:@"longitude"];
          [weakSelf.navigationController pushViewController:lookVC animated:YES];
        }];
        
      } else if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"DATA_ASSOCIATION"]){//数据关联
        
        CGFloat rightHeight = 21;
        UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,LabelH, CGRectGetWidth(bottomV.bounds) - leftL.right - 22,rightHeight)];
        rightL.textAlignment = NSTextAlignmentRight;
        rightL.text = @"暂不支持该类型数据展示";
        rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
        rightL.textColor = COLOR(31, 33, 38);
        rightL.numberOfLines = 0;
        [bottomV addSubview:rightL];
        
        if (rightHeight > height) {
          LabelH += rightHeight + 14;
        } else {
          if (height > 21) {
            LabelH += height + 14;
          } else {
            LabelH += 35;
          }
        }
        
      } else {//剩下的全是文本类型
        NSString *rightStr = @"";
        if ([[self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] isKindOfClass:[NSArray class]]) {//如果是多选
          NSMutableArray *rightStrArr = [[NSMutableArray alloc] initWithCapacity:100];
          for (NSDictionary *rightStrDic in [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]) {
            [rightStrArr addObject:[rightStrDic objectNilForKey:@"label"]];
          }
          rightStr = [rightStrArr componentsJoinedByString:@","];
        } else {//正常字符串
          rightStr =  [NSString stringWithFormat:@"%@",[self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]];
        }
        CGFloat rightHeight = [rightStr boundingRectWithSize:CGSizeMake(CGRectGetWidth(bottomV.bounds) - 146, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        rightHeight = rightHeight < 21 ? 21 : rightHeight;
        
        UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,LabelH, CGRectGetWidth(bottomV.bounds) - leftL.right - 22,rightHeight)];
        rightL.textAlignment = NSTextAlignmentRight;
        rightL.text = [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] ? [NSString stringWithFormat:@"%@",rightStr] : @"-";
        rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
        rightL.textColor = COLOR(31, 33, 38);
        rightL.numberOfLines = 0;
        [bottomV addSubview:rightL];
        
        if (rightHeight > height) {
          LabelH += rightHeight + 14;
        } else {
          if (height > 21) {
            LabelH += height + 14;
          } else {
            LabelH += 35;
          }
        }
        
      }
      
      if (j == items.count - 1) {
        bottomV.height = LabelH ;
        itemH += bottomV.height + 12 ;
      }
    }
  }
  
  contentView.height = itemH;
  
  return contentView;
}

#pragma mark 根据数据源返回表格
- (UIView *)getTableView:(NSDictionary *)leftDic andRightArr:(NSArray *)rightArr endTop:(CGFloat)top{
  
  UIView *mainV = [[UIView alloc] initWithFrame:CGRectMake(0, top, BCWidth, 50)];
  mainV.backgroundColor = [UIColor whiteColor];
  
  if (BCArrayIsEmpty(rightArr)) {//如果表格没有数据
    
    UIView *topV = [[UIView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, 20)];
    [mainV addSubview:topV];
    
    NSString *titleStr = [NSString stringWithFormat:@"%@",[leftDic objectNilForKey:@"title"]];
    UILabel *leftTL = [[UILabel alloc] initWithFrame:CGRectMake(16, 0, BCWidth - 32, 20)];
    leftTL.text = titleStr;
    leftTL.numberOfLines = 1;
    leftTL.textColor = COLOR(31, 33, 38);
    leftTL.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
    [topV addSubview:leftTL];
    
    NSArray *items = [leftDic objectForKeyNil:@"details"];
    if (BCArrayIsEmpty(items)) {
      return mainV;
    }
    
    UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(16, topV.bottom + 8, BCWidth - 32, 50)];
    bottomV.layer.cornerRadius = 8;
    bottomV.layer.borderWidth = 1;
    bottomV.layer.borderColor = ACOLOR(31, 33, 38, 0.1).CGColor;
    [mainV addSubview:bottomV];
    
    __weak typeof(self)weakSelf = self;
    UIView *lastInV;//里面的每一小项选项
    for (int j = 0; j < items.count; j ++) {
      
      NSDictionary *itemDic = [items objectAtIndexCheck:j];
      if (BCDictIsEmpty(itemDic)) {
        continue;
      }
      
      //      右边的根据类型返回不同的组件
      NSString *rightStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"component_type"]];

      //      每一小项的id
      NSString *itemId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
      
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
      NSString *titleNameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"title"]];
      CGFloat height = [titleNameStr boundingRectWithSize:CGSizeMake(120, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      height = height < 22 ? 22 : height;
      
      
      UIView *itemV = [[UIView alloc] initWithFrame:CGRectMake(0, lastInV ? lastInV.bottom + 14: 0, bottomV.width, height + 14)];
      itemV.clipsToBounds = YES;
      [bottomV addSubview:itemV];
      
//      左侧标题
      UILabel *leftL = [[UILabel alloc] initWithFrame:CGRectMake(16, 14, 120, height)];
      leftL.text = titleNameStr;
      leftL.numberOfLines = 0;
      leftL.font = [UIFont systemFontOfSize:MutilFont(15)];
      leftL.textColor = ACOLOR(30, 33, 38, 0.45);
      [itemV addSubview:leftL];
      
//      右侧
      UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6, leftL.top, itemV.width - leftL.right - 22, height)];
      rightL.text = @"-";
      rightL.textAlignment = NSTextAlignmentRight;
      rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rightL.textColor = COLOR(31, 33, 38);
      [itemV addSubview:rightL];
      
      if (j > 0) {
        UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, itemV.width - 16, 0.5)];
        lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
        [itemV addSubview:lineV];
      }
      
      lastInV = itemV;
      
    }
    
    bottomV.height = lastInV.bottom + 14;
    mainV.height = bottomV.bottom + 14;
    
  } else {//表格有数据
    
    
    UIView *lastOutV;
    for (int i = 0; i < rightArr.count; i ++) {
      
      NSDictionary *dataDic = [rightArr objectAtIndexCheck:i];//表格里面的值
      
      
      UIView *contentV = [[UIView alloc] initWithFrame:CGRectMake(0, lastOutV?lastOutV.bottom + 14 : 0, BCWidth, 50)];
      [mainV addSubview:contentV];
      
      UIView *topV = [[UIView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, 20)];
      [contentV addSubview:topV];
      
      NSString *titleStr = [NSString stringWithFormat:@"%@",[leftDic objectNilForKey:@"title"]];
      UILabel *leftTL = [[UILabel alloc] initWithFrame:CGRectMake(16, 0, BCWidth - 32, 20)];
      leftTL.text = rightArr.count == 1? titleStr : [NSString stringWithFormat:@"%@%d",titleStr,i + 1];
      leftTL.numberOfLines = 1;
      leftTL.textColor = COLOR(31, 33, 38);
      leftTL.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
      [topV addSubview:leftTL];
      
      NSArray *items = [leftDic objectForKeyNil:@"details"];
      if (BCArrayIsEmpty(items)) {
        return mainV;
      }
      
      UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(16, topV.bottom + 8, BCWidth - 32, 50)];
      bottomV.layer.cornerRadius = 8;
      bottomV.layer.borderWidth = 1;
      bottomV.layer.borderColor = ACOLOR(31, 33, 38, 0.1).CGColor;
      [contentV addSubview:bottomV];
      
      __weak typeof(self)weakSelf = self;
      UIView *lastInV;//里面的每一小项选项
      for (int j = 0; j < items.count; j ++) {
        
        NSDictionary *itemDic = [items objectAtIndexCheck:j];
        if (BCDictIsEmpty(itemDic)) {
          continue;
        }
        
        //      右边的根据类型返回不同的组件
        NSString *rightStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"component_type"]];

        //      每一小项的id
        NSString *itemId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
        
        NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
        NSString *titleNameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"title"]];
        CGFloat height = [titleNameStr boundingRectWithSize:CGSizeMake(120, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        height = ceil(height);
        height = height < 22 ? 22 : height;
        
        
        UIView *itemV = [[UIView alloc] initWithFrame:CGRectMake(0, lastInV ? lastInV.bottom + 14: 0, bottomV.width, height + 14)];
        itemV.clipsToBounds = YES;
        [bottomV addSubview:itemV];
        
  //      左侧标题
        UILabel *leftL = [[UILabel alloc] initWithFrame:CGRectMake(16, 14, 120, height)];
        leftL.text = titleNameStr;
        leftL.numberOfLines = 0;
        leftL.font = [UIFont systemFontOfSize:MutilFont(15)];
        leftL.textColor = ACOLOR(30, 33, 38, 0.45);
        [itemV addSubview:leftL];
        
  //      右侧
        if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"IMAGE"]) {//如果是图片
          
          NSArray *imageArr = [dataDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
          
          if (BCArrayIsEmpty(imageArr)) {//如果没有图片，展示-
            
            UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,leftL.top, CGRectGetWidth(bottomV.bounds) - leftL.right - 22, height)];
            rightL.textAlignment = NSTextAlignmentRight;
            rightL.text = [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] ? [NSString stringWithFormat:@"%@",[self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]] : @"-";
            rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
            rightL.textColor = COLOR(31, 33, 38);
            [itemV addSubview:rightL];
            
            
          } else {//如果有图片
            
            UIView *imageV = [[UIView alloc] initWithFrame:CGRectMake(16, leftL.bottom, BCWidth - 32, 0)];
            [itemV addSubview:imageV];
            CGFloat imageH = (BCWidth - 32 - 24)/4;
            CGFloat paddingL = 8.0; //button 间距
            CGFloat paddingT = 10; //button 间距
            CGFloat pointX = 0; //button X坐标
            CGFloat pointY = 10; //button Y坐标
            
            NSMutableArray *previewUrls = [NSMutableArray arrayWithCapacity:100];
            for (NSDictionary *previewDic in imageArr) {
              [previewUrls addObject:[previewDic objectNilForKey:@"url"]];
            }
            
            for (int k = 0; k < imageArr.count; k ++) {
              
              NSDictionary *imageDic = [imageArr objectAtIndexCheck:k];
              if (pointX + imageH > (BCWidth - 32)) {//换行
                pointX = 0;//X从新开始
                pointY += (imageH + paddingT);//换行后Y+
              }
              
              UIButton *imageBtn = [[UIButton alloc] initWithFrame:CGRectMake(pointX, pointY, imageH, imageH)];
              imageBtn.layer.cornerRadius = 4;
              imageBtn.clipsToBounds = YES;
              UIImageView *imageI = [[UIImageView alloc] initWithFrame:CGRectMake(0,0, imageH, imageH)];
              [imageI sd_setImageWithURL:[NSURL URLWithString:[[imageDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
              [imageBtn addSubview:imageI];
              [imageV addSubview:imageBtn];
              [imageBtn addtargetBlock:^(UIButton *button) {//预览图片
                [ZKPhotoBrowser showWithImageUrls:previewUrls currentPhotoIndex:k sourceSuperView:button];
              }];
              
              pointX += (imageH + paddingL);
            }
            
            imageV.height = pointY + imageH ;
            itemV.height = imageV.bottom;
            
          }
          
          
        } else if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"VIDEO"]) {//如果是视频
          
          NSArray *videoArr = [dataDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
          
          if (BCArrayIsEmpty(videoArr)) {//如果没有视频，展示-
            
            UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,leftL.top, CGRectGetWidth(bottomV.bounds) - leftL.right - 22, height)];
            rightL.textAlignment = NSTextAlignmentRight;
            rightL.text = [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] ? [NSString stringWithFormat:@"%@",[self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]] : @"-";
            rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
            rightL.textColor = COLOR(31, 33, 38);
            [itemV addSubview:rightL];
            
            
          } else {//如果有视频
            
            UIView *imageV = [[UIView alloc] initWithFrame:CGRectMake(16, leftL.bottom, itemV.width - 32, 0)];
            [itemV addSubview:imageV];
            CGFloat imageH = (BCWidth - 32 - 24)/4;
            CGFloat paddingL = 8.0; //button 间距
            CGFloat paddingT = 10; //button 间距
            CGFloat pointX = 0; //button X坐标
            CGFloat pointY = 10; //button Y坐标
            
            for (int k = 0; k < videoArr.count; k ++) {
              
              NSDictionary *imageDic = [videoArr objectAtIndexCheck:k];
              if (pointX + imageH > (BCWidth - 32)) {//换行
                pointX = 0;//X从新开始
                pointY += (imageH + paddingT);//换行后Y+
              }
              
              UIButton *imageBtn = [[UIButton alloc] initWithFrame:CGRectMake(pointX, pointY, imageH, imageH)];
              imageBtn.layer.cornerRadius = 4;
              imageBtn.clipsToBounds = YES;
              NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[imageDic objectNilForKey:@"url"]];
              UIImageView *imageI = [[UIImageView alloc] initWithFrame:CGRectMake(0,0, imageH, imageH)];
              [imageI sd_setImageWithURL:[NSURL URLWithString:[videoUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
              [imageBtn addSubview:imageI];
              
              UIImageView *playIM = [[UIImageView alloc] initWithFrame:CGRectMake((imageH - 25)/2, (imageH - 25)/2, 25, 25)];
              playIM.image = [UIImage imageNamed:@"play_video"];
              [imageBtn addSubview:playIM];
              
              [imageV addSubview:imageBtn];
              [imageBtn addtargetBlock:^(UIButton *button) {
                NSURL * url = [NSURL URLWithString:[[imageDic objectForKeyNil:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
                AVPlayerViewController * pVC = [[AVPlayerViewController alloc] init];
                pVC.player = [AVPlayer playerWithURL:url];
                [weakSelf presentViewController:pVC animated:YES completion:nil];
                [pVC.player play];
              }];
              
              
              pointX += (imageH + paddingL);
            }
            
            imageV.height = pointY + imageH ;
            itemV.height = imageV.bottom;
            
          }
          
        } else if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"LOCATION"]) {//如果是定位组件
          
          NSDictionary *dic = [dataDic objectForKeyNil:[itemDic objectNilForKey:@"id"]];
          NSString *rightStr = @"-";
          if (!BCDictIsEmpty(dic)) {
            rightStr = [NSString stringWithFormat:@"%@",[dic objectNilForKey:@"address"]];
          }
          
          CGFloat rightHeight = [rightStr boundingRectWithSize:CGSizeMake(CGRectGetWidth(bottomV.bounds) - 146, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
          rightHeight = rightHeight < 22 ? 22 : rightHeight;
          
          UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,leftL.top, CGRectGetWidth(bottomV.bounds) - leftL.right - 22,rightHeight)];
          rightL.textAlignment = NSTextAlignmentRight;
          rightL.text = rightStr;
          rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
          rightL.textColor =  COLOR(26, 106, 255);
          rightL.numberOfLines = 0;
          [itemV addSubview:rightL];
          
          if (rightHeight > height) {
            itemV.height += rightHeight - height;
          }
          [rightL addTapGestureWithBlock:^{
            
            if ([rightStr isEqualToString:@"-"]) {
              [weakSelf.notiflyView showModal:NotiflyFail andTitle:@"定位地址为空，无法查看定位"];
              return;
            }
            
            MALookPointViewController *lookVC = [[MALookPointViewController alloc] init];
            lookVC.pointName = @"查看定位";
            lookVC.pointAddress = [dic objectNilForKey:@"address"];
            lookVC.pointLatitude = [dic objectNilForKey:@"latitude"];
            lookVC.pointLongitude = [dic objectNilForKey:@"longitude"];
            [weakSelf.navigationController pushViewController:lookVC animated:YES];
          }];
          
        } else {//其他的全是文本
          
          NSString *rightStr = @"";
          if ([[dataDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] isKindOfClass:[NSArray class]]) {//如果是多选
            NSMutableArray *rightStrArr = [[NSMutableArray alloc] initWithCapacity:100];
            for (NSDictionary *rightStrDic in [dataDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]) {
              [rightStrArr addObject:[rightStrDic objectNilForKey:@"label"]];
            }
            rightStr = [rightStrArr componentsJoinedByString:@","];
          } else {//正常字符串
            rightStr =  [NSString stringWithFormat:@"%@",[dataDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]];
          }
          CGFloat rightHeight = [rightStr boundingRectWithSize:CGSizeMake(CGRectGetWidth(bottomV.bounds) - 146, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
          rightHeight = rightHeight < 22 ? 22 : rightHeight;
          
          UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,leftL.top, CGRectGetWidth(bottomV.bounds) - leftL.right - 22,rightHeight)];
          rightL.textAlignment = NSTextAlignmentRight;
          rightL.text = BCStringIsEmpty(rightStr)?  @"-" : rightStr;
          rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
          rightL.textColor = COLOR(31, 33, 38);
          rightL.numberOfLines = 0;
          [itemV addSubview:rightL];
          
          if (rightHeight > height) {
            itemV.height += rightHeight - height;
            
          }
          
        }
       
        
        if (j > 0) {
          UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, itemV.width - 16, 0.5)];
          lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
          [itemV addSubview:lineV];
        }
        
        lastInV = itemV;
        
      }
      
      bottomV.height = lastInV.bottom + 14;
      contentV.height = bottomV.bottom;
     
      
      if (i == rightArr.count - 1) {
        contentV.height += 14;
      }
      
      lastOutV = contentV;
    }
    
    mainV.height = lastOutV.bottom;
    
    
  }
  
  return mainV;
  
}

#pragma mark 所有网络请求回来加载首页房东信息界面
- (UIView *)loadLandlord:(CGFloat)top{
  
  __weak typeof(self)weakSelf = self;
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,top,BCWidth,50)];
  contentView.backgroundColor = [UIColor whiteColor];
  NSArray *arr = [self.detailDic objectForKeyNil:@"landlords"];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,200,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = [NSString stringWithFormat:@"房东信息（%lu）",(unsigned long)((![arr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(arr)) ? 0:arr.count)];
  [contentView addSubview:nameLabel];
  
  UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
  switchMap.frame = CGRectMake(BCWidth - 20 - RealSize(62) - 15, 0, 82, 50);
  [switchMap addTarget:self action:@selector(addLandlord) forControlEvents:UIControlEventTouchUpInside];
  NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"EDIT_POINT"];
  if ([editPoint isEqualToString:@"1"] && ![[self.detailDic objectNilForKey:@"state"] isEqualToString:@"REJECT"]) {
    [contentView addSubview:switchMap];
  }
  
  UIImageView *leftIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 16, 18, 18)];
  leftIM.image = [UIImage imageNamed:@"add_landlord"];
  [switchMap addSubview:leftIM];
  
  UILabel *switchlabel = [[UILabel alloc] init];
  switchlabel.frame = CGRectMake(20,0,RealSize(62),50);
  switchlabel.text = @"添加房东";
  switchlabel.textColor = COLOR(26, 106, 255);
  switchlabel.font = [UIFont systemFontOfSize:MutilFont(15)];
  [switchMap addSubview:switchlabel];
  
  if (BCArrayIsEmpty(arr)) {
    return contentView;
  }
  
  CGFloat itemH = 50;
  for (int i = 0; i < (arr.count > 3 ? 3 :arr.count); i ++) {
    
    NSDictionary *itemDic = [arr objectAtIndexCheck:i];
    if (BCDictIsEmpty(itemDic)) {
      continue;
    }
    
    UIButton *bottomV = [[UIButton alloc] initWithFrame:CGRectMake(16, itemH, BCWidth - 32, 100)];
    bottomV.tag = 999 + i;
    [bottomV addTarget:self action:@selector(clickEditLandlord:) forControlEvents:UIControlEventTouchUpInside];
    [contentView addSubview:bottomV];
    
    UIImageView *headIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 14, 72, 72)];
    headIM.layer.cornerRadius = 4;
    headIM.clipsToBounds = YES;
    NSArray *mentouArr = [itemDic objectForKeyNil:@"urls"];
    if (!BCArrayIsEmpty(mentouArr)) {
      [headIM sd_setImageWithURL:[NSURL URLWithString:[[mentouArr firstObject] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] placeholderImage:[UIImage imageNamed:@"icon_pla"]];
    } else {
      headIM.image = [UIImage imageNamed:@"icon_pla"];
    }
    [bottomV addSubview:headIM];
    
    UILabel *nameL = [[UILabel alloc] initWithFrame:CGRectMake(headIM.right + 12.5, 14, bottomV.width - headIM.right - 16, 22)];
    nameL.textColor  = COLOR(31, 33, 38);
    nameL.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
    nameL.text = [NSString stringWithFormat:@"%@  %@",[itemDic objectNilForKey:@"name"],[itemDic objectForKeyNil:@"phone"]];
    [bottomV addSubview:nameL];
    
    
    UILabel *rentL = [[UILabel alloc] initWithFrame:CGRectMake(headIM.right + 12.5, nameL.bottom + 6, bottomV.width - headIM.right - 16, 18)];
    rentL.textColor  = ACOLOR(30, 33, 38, 0.7);
    rentL.font = [UIFont systemFontOfSize:MutilFont(13)];
    rentL.text = [NSString stringWithFormat:@"租金：%.2f",[[itemDic objectForKeyNil:@"rent_money"] floatValue]];
    [bottomV addSubview:rentL];
    
    UILabel *transferL = [[UILabel alloc] initWithFrame:CGRectMake(headIM.right + 12.5, nameL.bottom + 6, bottomV.width - headIM.right - 16, 18)];
    transferL.textAlignment = NSTextAlignmentRight;
    transferL.textColor  = ACOLOR(30, 33, 38, 0.7);
    transferL.font = [UIFont systemFontOfSize:MutilFont(13)];
    transferL.text = [NSString stringWithFormat:@"转让费：%.2f",[[itemDic objectForKeyNil:@"transfer_fee"] floatValue] ];
    [bottomV addSubview:transferL];
    
    NSString *memo = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
    NSString *memoStr = [NSString stringWithFormat:@"备注：%@",BCStringIsEmpty(memo) ? @"-" : memo];
    NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(13)]};
    CGFloat height = [memoStr boundingRectWithSize:CGSizeMake(BCWidth - 116, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
    height = ceil(height);
    UILabel *memoLabel= [[UILabel alloc] initWithFrame:CGRectMake(headIM.right + 12.5, transferL.bottom + 2, BCWidth - 116, height)];
    memoLabel.textColor = ACOLOR(30, 33, 38, 0.7);
    memoLabel.font = [UIFont systemFontOfSize:MutilFont(13)];
    memoLabel.text = memoStr;
    memoLabel.numberOfLines = 0;
    [bottomV addSubview:memoLabel];
    
    //    分割线
    UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(0,0, BCWidth - 16, 1)];
    lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
    [bottomV addSubview:lineV];
    
    if (height > 19) {
      bottomV.height = memoLabel.bottom + 14;
    }
    itemH += bottomV.height;
    
    
  }
  
  
  
  //  查看全部按钮
  UIButton *allBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  allBtn.frame = CGRectMake(0, itemH, BCWidth , 48);
  [contentView addSubview:allBtn];
  [allBtn addtargetBlock:^(UIButton *button) {
    UIButton *tabBtn = [weakSelf.topTabV viewWithTag:1015];
    [tabBtn sendActionsForControlEvents:UIControlEventTouchUpInside];
  }];
  
  
  UILabel *switchlabel1 = [[UILabel alloc] init];
  switchlabel1.frame = CGRectMake((BCWidth - 76)/2,0,RealSize(58),48);
  switchlabel1.text = @"显示全部";
  switchlabel1.textColor = COLOR(26, 106, 255);
  switchlabel1.font = [UIFont systemFontOfSize:MutilFont(14)];
  [allBtn addSubview:switchlabel1];
  
  
  UIImageView *leftIM1 = [[UIImageView alloc] initWithFrame:CGRectMake(switchlabel1.right + 4, 17, 14, 14)];
  leftIM1.image = [UIImage imageNamed:@"look_landlord"];
  [allBtn addSubview:leftIM1];
  
  UIView *lineV1 = [[UIView alloc] initWithFrame:CGRectMake(0,itemH, BCWidth , 1)];
  lineV1.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [contentView addSubview:lineV1];
  
  
  contentView.height = itemH + 48;
  return contentView;
}

#pragma mark 所有网络请求回来加载首页点位动态界面
- (UIView *)loadPointState:(CGFloat)top{
  
  __weak typeof(self)weakSelf = self;
  NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
  NSArray *colorArr = @[@[(__bridge id)COLOR(158, 171, 255).CGColor,(__bridge id)COLOR(45, 78, 238).CGColor],
                        @[(__bridge id)COLOR(27, 236, 212).CGColor,(__bridge id)COLOR(0, 196, 170).CGColor],
                        @[(__bridge id)COLOR(178, 132, 255).CGColor,(__bridge id)COLOR(127, 60, 237).CGColor],
                        @[(__bridge id)COLOR(158, 225, 255).CGColor,(__bridge id)COLOR(36, 78, 241).CGColor],
                        @[(__bridge id)COLOR(255, 174, 132).CGColor,(__bridge id)COLOR(237, 60, 60).CGColor],
                        @[(__bridge id)COLOR(255, 183, 100).CGColor,(__bridge id)COLOR(240, 130, 3).CGColor],
  ];
  
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,top + 12,BCWidth,50)];
  contentView.backgroundColor = [UIColor whiteColor];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,200,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = [NSString stringWithFormat:@"点位动态（%lu）",(unsigned long)((![self.allPointArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.allPointArr)) ? 0:self.allPointArr.count)];
  [contentView addSubview:nameLabel];
  
  UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
  switchMap.frame = CGRectMake(BCWidth - 67 - 15, 0, 82, 50);
//  [contentView addSubview:switchMap];
  
  UIImageView *leftIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 16, 18, 18)];
  leftIM.image = [UIImage imageNamed:@"point_add"];
  [switchMap addSubview:leftIM];
  
  UILabel *switchlabel = [[UILabel alloc] init];
  switchlabel.frame = CGRectMake(20,0,47,50);
  switchlabel.text = @"写跟进";
  switchlabel.textColor = COLOR(26, 106, 255);
  switchlabel.font = [UIFont systemFontOfSize:15];
  [switchMap addSubview:switchlabel];
  
  if (![self.allPointArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.allPointArr)) {
    return contentView;
  }
  
  //    分割线
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,49, BCWidth - 16, 1)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [contentView addSubview:lineV];
  
  CGFloat itemH = 50 + 14;
  for (int i = 0; i < (self.allPointArr.count > 3 ? 3 :self.allPointArr.count); i ++) {
    
    NSDictionary *pointDic = [self.allPointArr objectAtIndexCheck:i];
    
    UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth, 178)];
    [contentView addSubview:bottomV];
    
    NSString *typeStr = [pointDic objectNilForKey:@"type"];
    
    if ([typeStr isEqualToString:@"TRANSFER_RECORD"]) {
      
      NSDictionary *itemDic = [pointDic objectForKeyNil:@"data"];
      
      NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
      [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
      [dateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
      NSString *creatTimeStr = [NSString stringWithFormat:@"%@",[pointDic objectNilForKey:@"create_time"]];
      NSDate *creatDate = [dateFormatter dateFromString:creatTimeStr];
      
      NSDateFormatter *newdateFormatter = [[NSDateFormatter alloc] init];
      [newdateFormatter setDateFormat:@"yyyy-MM-dd HH:mm"];
      [newdateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
      NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
      NSString *followResult = [NSString stringWithFormat:@"%@ · 转移 · %@",nameStr,[newdateFormatter stringFromDate:creatDate]];
      NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
      [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
      
      NSInteger randomNum = arc4random_uniform(6);
      UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
      headImageV.layer.cornerRadius = 12;
      headImageV.clipsToBounds = YES;
      [bottomV addSubview:headImageV];
      
      CAGradientLayer *gl = [CAGradientLayer layer];
      gl.frame = headImageV.bounds;
      gl.startPoint = CGPointMake(0.0, 0);
      gl.endPoint = CGPointMake(1, 1);
      gl.colors = [colorArr objectAtIndexCheck:randomNum];
      gl.locations = @[@(0), @(1.0f)];
      [headImageV.layer addSublayer:gl];
      
      UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
      headL.textColor = [UIColor whiteColor];
      headL.textAlignment = NSTextAlignmentCenter;
      headL.font = [UIFont systemFontOfSize:10];
      headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
      [headImageV addSubview:headL];
      
      UILabel *switchlabel = [[UILabel alloc] init];
      switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - headImageV.right - 22,20);
      switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
      switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
      [bottomV addSubview:switchlabel];
      switchlabel.attributedText = string;
      
      //        变更字段
      NSString *beforeStr = @"跟进人";
      NSString *beforeResult = [NSString stringWithFormat:@"变更字段：%@",beforeStr];
      NSMutableAttributedString *beforeString = [[NSMutableAttributedString alloc] initWithString:beforeResult];
      [beforeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, beforeStr.length)];
      
      UILabel *beforelabel = [[UILabel alloc] init];
      beforelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 6,BCWidth - 80,20);
      beforelabel.textColor = ACOLOR(30, 33, 38, 0.45);
      beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:beforelabel];
      beforelabel.attributedText = beforeString;
      
      //        原负责人
      NSString *beforePeopleStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"before_change"]];
      NSString *beforePeopleResult = [NSString stringWithFormat:@"原跟进人：%@",BCStringIsEmpty(beforePeopleStr) ? @"-" : beforePeopleStr];
      NSMutableAttributedString *resultString = [[NSMutableAttributedString alloc] initWithString:beforePeopleResult];
      [resultString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5,BCStringIsEmpty(beforePeopleStr) ? 1 : beforePeopleStr.length)];
      
      UILabel *resultlabel = [[UILabel alloc] init];
      resultlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 80,20);
      resultlabel.textColor = ACOLOR(30, 33, 38, 0.45);
      resultlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:resultlabel];
      resultlabel.attributedText = resultString;
      
      //      新负责人
      NSString *timeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"after_change"]];
      NSString *timeResult = [NSString stringWithFormat:@"新跟进人：%@",timeStr];
      NSMutableAttributedString *timeString = [[NSMutableAttributedString alloc] initWithString:timeResult];
      [timeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, timeStr.length)];
      
      UILabel *timelabel = [[UILabel alloc] init];
      timelabel.frame = CGRectMake(headImageV.right + 6,resultlabel.bottom + 4,BCWidth - 80,20);
      timelabel.textColor = ACOLOR(30, 33, 38, 0.45);
      timelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:timelabel];
      timelabel.attributedText = timeString;
      
      //        转交原因
      NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"change_reason"]];
      NSString *memoResult = [NSString stringWithFormat:@"转交原因：%@",!BCStringIsEmpty(memoStr) ? memoStr : @"-"];
      NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:memoResult];
      NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
      [paragraphStyle setLineSpacing:4];//调整行间距
      paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
      [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, memoResult.length)];
      [opereateResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 5)];
      
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:paragraphStyle};
      CGFloat height = [memoResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      height = height < 20 ? 20 : height;
      
      UILabel *memolabel = [[UILabel alloc] init];
      memolabel.frame = CGRectMake(headImageV.right + 6 , timelabel.bottom + 4,BCWidth - 62,height);
      memolabel.textColor = ACOLOR(31, 33, 38, 1);
      memolabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      memolabel.numberOfLines = 0;
      [bottomV addSubview:memolabel];
      memolabel.attributedText = opereateResultStr;
      
      //      分割线
      if (i < 2 && self.allPointArr.count > 1 ) {
        UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, memolabel.bottom + 14, BCWidth - 16, 0.5)];
        lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
        [bottomV addSubview:lineV];
      }
      
      bottomV.height = memolabel.bottom + 14 + 14;
      if (i == (self.allPointArr.count > 3 ? 3 :self.allPointArr.count) - 1) {
        bottomV.height = memolabel.bottom + 14;
      }
      itemH += bottomV.height;
      
    } else if ([typeStr isEqualToString:@"FOLLOW_RECORD"]) {//跟进
      NSDictionary *itemDic = [pointDic objectForKeyNil:@"data"];
      NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
      NSString *followResult = [NSString stringWithFormat:@"%@ · 跟进 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
      NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
      [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
      
      NSInteger randomNum = arc4random_uniform(6);
      UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
      headImageV.layer.cornerRadius = 12;
      headImageV.clipsToBounds = YES;
      [bottomV addSubview:headImageV];
      
      CAGradientLayer *gl = [CAGradientLayer layer];
      gl.frame = headImageV.bounds;
      gl.startPoint = CGPointMake(0.0, 0);
      gl.endPoint = CGPointMake(1, 1);
      gl.colors = [colorArr objectAtIndexCheck:randomNum];
      gl.locations = @[@(0), @(1.0f)];
      [headImageV.layer addSublayer:gl];
      
      UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
      headL.textColor = [UIColor whiteColor];
      headL.textAlignment = NSTextAlignmentCenter;
      headL.font = [UIFont systemFontOfSize:10];
      headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
      [headImageV addSubview:headL];
      
      UILabel *switchlabel = [[UILabel alloc] init];
      switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
      switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
      switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
      [bottomV addSubview:switchlabel];
      switchlabel.attributedText = string;
      
      //        备注
      NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
      CGFloat height = [memoStr boundingRectWithSize:CGSizeMake(BCWidth - 92, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      UILabel *memoLabel= [[UILabel alloc] initWithFrame:CGRectMake(headImageV.right + 6, headImageV.bottom + 6, BCWidth - 92,BCStringIsEmpty(memoStr) ? 0 : height)];
      memoLabel.textColor = ACOLOR(29, 33, 41, 1);
      memoLabel.font = [UIFont systemFontOfSize:MutilFont(15)];
      memoLabel.text = memoStr;
      memoLabel.numberOfLines = 0;
      [bottomV addSubview:memoLabel];
      
      //        跟进状态
      NSString *beforeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"state"]];
      NSString *followStr = BCStringIsEmpty(beforeStr) ? @"-" : [beforeStr isEqualToString:@"FOLLOW"] ? @"跟进中":[beforeStr isEqualToString:@"FINISH"] ? @"已完成" : @"关闭";
      NSString *beforeResult = [NSString stringWithFormat:@"跟进状态：%@",followStr];
      NSMutableAttributedString *beforeString = [[NSMutableAttributedString alloc] initWithString:beforeResult];
      [beforeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, followStr.length)];
      
      UILabel *beforelabel = [[UILabel alloc] init];
      beforelabel.frame = CGRectMake(headImageV.right + 6,memoLabel.bottom + 6,BCWidth - 80,20);
      beforelabel.textColor = ACOLOR(30, 33, 38, 0.45);
      beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:beforelabel];
      beforelabel.attributedText = beforeString;
      
     
      
      //        跟进结果
      NSString *resultStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"result"]];
      NSString *resultResult = [NSString stringWithFormat:@"跟进结果：%@",BCStringIsEmpty(resultStr) ? @"-" : resultStr];
      NSMutableAttributedString *resultString = [[NSMutableAttributedString alloc] initWithString:resultResult];
      [resultString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5,BCStringIsEmpty(resultStr) ? 1 : resultStr.length)];
      
      UILabel *resultlabel = [[UILabel alloc] init];
      resultlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 80,20);
      resultlabel.textColor = ACOLOR(30, 33, 38, 0.45);
      resultlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:resultlabel];
      resultlabel.attributedText = resultString;
      
      //        跟进时间
      NSString *timeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"follow_time"]];
      timeStr = [timeStr substringWithRange:NSMakeRange(0, timeStr.length - 3)];
      NSString *timeResult = [NSString stringWithFormat:@"跟进时间：%@",timeStr];
      NSMutableAttributedString *timeString = [[NSMutableAttributedString alloc] initWithString:timeResult];
      [timeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, timeStr.length)];
      
      UILabel *timelabel = [[UILabel alloc] init];
      timelabel.frame = CGRectMake(headImageV.right + 6,resultlabel.bottom + 4,BCWidth - 80,20);
      timelabel.textColor = ACOLOR(30, 33, 38, 0.45);
      timelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:timelabel];
      timelabel.attributedText = timeString;
      
      //        附件信息
      NSArray *fileArr = [itemDic objectForKeyNil:@"files"];
      if ([fileArr isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(fileArr)) {
        
        
        for (int j = 0; j <fileArr.count; j ++) {
          
          NSDictionary *deedDic = [fileArr objectAtIndexCheck:j];
          UIButton *leaseImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(headImageV.right + 6, timelabel.bottom + 8 + (44 + 10) * j, BCWidth - 62, 44)];
          leaseImageBtn.layer.cornerRadius = 4;
          leaseImageBtn.clipsToBounds = YES;
          leaseImageBtn.backgroundColor = COLOR(244, 245, 247);
          [bottomV addSubview:leaseImageBtn];
          
          UIImageView *leaseIM = [[UIImageView alloc] initWithFrame:CGRectMake(16, 10 ,24, 24)];
          [leaseImageBtn addSubview:leaseIM];
          
          UILabel *leaseL = [[UILabel alloc] initWithFrame:CGRectMake(leaseIM.right + 10,0, leaseImageBtn.width - leaseIM.right - 26, 44)];
          leaseL.text = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"name"]];
          leaseL.font = [UIFont systemFontOfSize:MutilFont(15)];
          leaseL.textColor = ACOLOR(31, 33, 38,1);
          leaseL.lineBreakMode = NSLineBreakByTruncatingMiddle;
          [leaseImageBtn addSubview:leaseL];
          
          if ([temArr containsObject:[deedDic objectNilForKey:@"suffix_type"]]) {//如果是图片
            
            leaseIM.image = [UIImage imageNamed:@"file_img"];
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
              [ZKPhotoBrowser showWithImageUrls:@[[[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] currentPhotoIndex:0 sourceSuperView:button];
            }];
          } else {//是其他文本 pdf  word
            
            NSString *fileStr = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"suffix_type"] ];
            if ([fileStr containsString:@"pdf"]) {
              leaseIM.image = [UIImage imageNamed:@"file_pdf"];
            } else  if ([fileStr containsString:@"doc"] || [fileStr containsString:@"docx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_word"];
            } else  if ([fileStr containsString:@"xls"] || [fileStr containsString:@"xls"]) {
              leaseIM.image = [UIImage imageNamed:@"file_excel"];
            }else  if ([fileStr containsString:@"ppt"] || [fileStr containsString:@"pptx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_ppt"];
            }else {
              leaseIM.image = [UIImage imageNamed:@"file_no"];
            }
            
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
              NSString *url = [[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
              [weakSelf openSFDoc:url];
            }];
          }
          
        }
      }
      
      //        计算高度
      CGFloat bottomH = 0;
      if (![fileArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(fileArr)) {//没有附件
        bottomH  = timelabel.bottom  + 14;
      } else {
        bottomH  = timelabel.bottom + 8 + (44 + 10) * fileArr .count - 8 + 14;
      }
      
      bottomV.height = bottomH + 14;
      
      //      分割线
      if (i < 2 && self.allPointArr.count > 1 ) {
        UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, bottomH, BCWidth - 16, 0.5)];
        lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
        [bottomV addSubview:lineV];
      }
      
      if (i == (self.allPointArr.count > 3 ? 3 :self.allPointArr.count) - 1) {
        bottomV.height = bottomH;
      }
      
      itemH += bottomV.height ;
    } else if ([typeStr isEqualToString:@"CHANGE_RECORD"]) {//变更
      
      NSDictionary *itemDic = [pointDic objectForKeyNil:@"data"];
      NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
      NSString *followResult = [NSString stringWithFormat:@"%@ · 变更 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
      NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
      [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
      
      NSInteger randomNum = arc4random_uniform(6);
      UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
      headImageV.layer.cornerRadius = 12;
      headImageV.clipsToBounds = YES;
      [bottomV addSubview:headImageV];
      
      CAGradientLayer *gl = [CAGradientLayer layer];
      gl.frame = headImageV.bounds;
      gl.startPoint = CGPointMake(0.0, 0);
      gl.endPoint = CGPointMake(1, 1);
      gl.colors = [colorArr objectAtIndexCheck:randomNum];
      gl.locations = @[@(0), @(1.0f)];
      [headImageV.layer addSublayer:gl];
      
      UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
      headL.textColor = [UIColor whiteColor];
      headL.textAlignment = NSTextAlignmentCenter;
      headL.font = [UIFont systemFontOfSize:10];
      headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
      [headImageV addSubview:headL];
      
      UILabel *switchlabel = [[UILabel alloc] init];
      switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
      switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
      switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
      [bottomV addSubview:switchlabel];
      switchlabel.attributedText = string;
      
      NSString *operateStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"field_name"]];
      NSString *operateResult = [NSString stringWithFormat:@"变更字段：%@",operateStr];
      NSMutableAttributedString *operateString = [[NSMutableAttributedString alloc] initWithString:operateResult];
      [operateString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, operateStr.length)];
      
      UILabel *operatelabel = [[UILabel alloc] init];
      operatelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 8,BCWidth - 80,20);
      operatelabel.textColor = ACOLOR(30, 33, 38, 0.45);
      operatelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:operatelabel];
      operatelabel.attributedText = operateString;
      
      
      //       原值
      NSString *beforeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"before_change"]];
      if ([beforeStr containsString:@"https"] || [beforeStr containsString:@"http"] || BCStringIsEmpty(beforeStr)) {
        beforeStr = @"-";
      }
      
      NSString *beforeResult = [NSString stringWithFormat:@"原值：%@", beforeStr];
      NSMutableAttributedString *beforeResultStr = [[NSMutableAttributedString alloc] initWithString:beforeResult];
      NSMutableParagraphStyle *beforeParagraphStyle = [[NSMutableParagraphStyle alloc] init];
      [beforeParagraphStyle setLineSpacing:4];//调整行间距
      beforeParagraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
      [beforeResultStr addAttribute:NSParagraphStyleAttributeName value:beforeParagraphStyle range:NSMakeRange(0, beforeResult.length)];
      [beforeResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 3)];
      
      NSDictionary *beforeAttributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:beforeParagraphStyle};
      CGFloat beforeHeight = [beforeResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:beforeAttributes context:nil].size.height;
      beforeHeight= ceil(beforeHeight);
      beforeHeight = beforeHeight < 20 ? 20 : beforeHeight;
      
      UILabel *beforelabel = [[UILabel alloc] init];
      beforelabel.frame = CGRectMake(headImageV.right + 6 , operatelabel.bottom + 4,BCWidth - 62,beforeHeight);
      beforelabel.textColor = COLOR(31, 33, 38);
      beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      beforelabel.numberOfLines = 0;
      [bottomV addSubview:beforelabel];
      beforelabel.attributedText = beforeResultStr;
      
      //        新值
      NSString *afterStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"after_change"]];
      if ([afterStr containsString:@"https"] || [afterStr containsString:@"http"]  || BCStringIsEmpty(beforeStr)) {
        afterStr = @"-";
      }
      
      NSString *afterResult = [NSString stringWithFormat:@"新值：%@", afterStr];
      NSMutableAttributedString *afterResultStr = [[NSMutableAttributedString alloc] initWithString:afterResult];
      NSMutableParagraphStyle *afterParagraphStyle = [[NSMutableParagraphStyle alloc] init];
      [afterParagraphStyle setLineSpacing:4];//调整行间距
      afterParagraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
      [afterResultStr addAttribute:NSParagraphStyleAttributeName value:afterParagraphStyle range:NSMakeRange(0, afterResult.length)];
      [afterResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 3)];
      
      NSDictionary *afterAttributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:afterParagraphStyle};
      CGFloat afterHeight = [afterResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:afterAttributes context:nil].size.height;
      afterHeight = ceil(afterHeight);
      afterHeight = afterHeight < 20 ? 20 : afterHeight;
      
      UILabel *afterlabel = [[UILabel alloc] init];
      afterlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 62,afterHeight);
      afterlabel.textColor = COLOR(31, 33, 38);
      afterlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      afterlabel.numberOfLines = 0;
      [bottomV addSubview:afterlabel];
      afterlabel.attributedText = afterResultStr;
      
      
      //      分割线
      if (i < 2 && self.allPointArr.count > 1 ) {
        UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, afterlabel.bottom + 14, BCWidth - 16, 0.5)];
        lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
        [bottomV addSubview:lineV];
      }
      
      bottomV.height = afterlabel.bottom + 14 + 14;
      if (i == (self.allPointArr.count > 3 ? 3 :self.allPointArr.count) - 1) {
        bottomV.height = afterlabel.bottom + 14;
      }
      itemH += bottomV.height;
      
    } else {//操作
      
      NSDictionary *itemDic = [pointDic objectForKeyNil:@"data"];
      NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
      NSString *followResult = [NSString stringWithFormat:@"%@ · 操作 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
      NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
      [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
      
      NSInteger randomNum = arc4random_uniform(6);
      UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
      headImageV.layer.cornerRadius = 12;
      headImageV.clipsToBounds = YES;
      [bottomV addSubview:headImageV];
      
      CAGradientLayer *gl = [CAGradientLayer layer];
      gl.frame = headImageV.bounds;
      gl.startPoint = CGPointMake(0.0, 0);
      gl.endPoint = CGPointMake(1, 1);
      gl.colors = [colorArr objectAtIndexCheck:randomNum];
      gl.locations = @[@(0), @(1.0f)];
      [headImageV.layer addSublayer:gl];
      
      UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
      headL.textColor = [UIColor whiteColor];
      headL.textAlignment = NSTextAlignmentCenter;
      headL.font = [UIFont systemFontOfSize:10];
      headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
      [headImageV addSubview:headL];
      
      UILabel *switchlabel = [[UILabel alloc] init];
      switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
      switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
      switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
      [bottomV addSubview:switchlabel];
      switchlabel.attributedText = string;
      
      NSString *operateStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"model_state"]];
      NSString *operateResult = [NSString stringWithFormat:@"操作：%@",operateStr];
      NSMutableAttributedString *operateString = [[NSMutableAttributedString alloc] initWithString:operateResult];
      [operateString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(3, operateStr.length)];
      
      UILabel *operatelabel = [[UILabel alloc] init];
      operatelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 8,BCWidth - 80,20);
      operatelabel.textColor = ACOLOR(30, 33, 38, 0.45);
      operatelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:operatelabel];
      operatelabel.attributedText = operateString;
      
      NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
      NSString *memoResult = [NSString stringWithFormat:@"审批意见：%@",!BCStringIsEmpty(memoStr) ? memoStr : @"-"];
      NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:memoResult];
      NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
      [paragraphStyle setLineSpacing:4];//调整行间距
      paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
      [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, memoResult.length)];
      [opereateResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 5)];
      
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:paragraphStyle};
      CGFloat height = [memoResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      height = height < 20 ? 20 : height;
      
      UILabel *memolabel = [[UILabel alloc] init];
      memolabel.frame = CGRectMake(headImageV.right + 6 , operatelabel.bottom + 4,BCWidth - 62,height);
      memolabel.textColor = ACOLOR(31, 33, 38, 1);
      memolabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      memolabel.numberOfLines = 0;
      [bottomV addSubview:memolabel];
      memolabel.attributedText = opereateResultStr;
      
      
      //      分割线
      if (i < 2 && self.allPointArr.count > 1 ) {
        UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, memolabel.bottom + 14, BCWidth - 16, 0.5)];
        lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
        [bottomV addSubview:lineV];
      }
      bottomV.height = memolabel.bottom + 14 + 14;
      if (i == (self.allPointArr.count > 3 ? 3 :self.allPointArr.count) - 1) {
        bottomV.height = memolabel.bottom + 14;
      }
      
      itemH += bottomV.height ;
    }
    
  }
  
  //  查看全部按钮
  UIButton *allBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  allBtn.frame = CGRectMake(0, itemH, BCWidth , 48);
  [contentView addSubview:allBtn];
  [allBtn addtargetBlock:^(UIButton *button) {
    UIButton *tabBtn = [weakSelf.topTabV viewWithTag:1016];
    [tabBtn sendActionsForControlEvents:UIControlEventTouchUpInside];
  }];
  
  
  UILabel *switchlabel1 = [[UILabel alloc] init];
  switchlabel1.frame = CGRectMake((BCWidth - 76)/2,0,RealSize(58),48);
  switchlabel1.text = @"显示全部";
  switchlabel1.textColor = COLOR(26, 106, 255);
  switchlabel1.font = [UIFont systemFontOfSize:MutilFont(14)];
  [allBtn addSubview:switchlabel1];
  
  
  UIImageView *leftIM1 = [[UIImageView alloc] initWithFrame:CGRectMake(switchlabel1.right + 4, 17, 14, 14)];
  leftIM1.image = [UIImage imageNamed:@"look_landlord"];
  [allBtn addSubview:leftIM1];
  
  UIView *lineV1 = [[UIView alloc] initWithFrame:CGRectMake(0,itemH, BCWidth , 0.5)];
  lineV1.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [contentView addSubview:lineV1];
  
  contentView.height = itemH + 48;
  return contentView;
}

#pragma mark 所有网络请求回来加载首页带看记录界面
- (UIView *)loadLook:(CGFloat)top{
  
  __weak typeof(self)weakSelf = self;
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,top + 12,BCWidth,50)];
  contentView.backgroundColor = [UIColor whiteColor];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,200,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = [NSString stringWithFormat:@"带看记录（%lu）",(unsigned long)((![self.lookArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.lookArr)) ? 0:self.lookArr.count)];
  [contentView addSubview:nameLabel];
  
  
  if (![self.lookArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.lookArr)) {
    return contentView;
  }
  
  CGFloat itemH = 50;
  for (int i = 0; i < (self.lookArr.count > 3 ? 3 :self.lookArr.count); i ++) {
    NSDictionary *itemDic = [self.lookArr objectAtIndexCheck:i];
    
    UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(16, itemH, BCWidth - 32, 50)];
    [contentView addSubview:bottomV];
    
    //    分割线
    UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,itemH, BCWidth - 16, 0.5)];
    lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
    [contentView addSubview:lineV];
    
    //    带看客户
    UILabel *leftL = [[UILabel alloc] initWithFrame:CGRectMake(0, 14, 200, 20)];
    leftL.text = [NSString stringWithFormat:@"带看客户  %@",[itemDic objectNilForKey:@"client_name"]];
    leftL.font = [UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium];
    leftL.textColor = ACOLOR(31, 33, 38, 1);
    [bottomV addSubview:leftL];
    
    //    状态
    UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(0,14, BCWidth - 32, 20)];
    rightL.font = [UIFont systemFontOfSize:MutilFont(14)];
    rightL.textAlignment = NSTextAlignmentRight;
    rightL.text = [[itemDic objectNilForKey:@"result"] isEqualToString:@"YES"] ? @"可签约" : [[itemDic objectNilForKey:@"result"] isEqualToString:@"NO"] ? @"无意向" :@"持续关注";
    rightL.textColor = [[itemDic objectNilForKey:@"result"] isEqualToString:@"YES"] ? COLOR(0, 180, 43) : [[itemDic objectNilForKey:@"result"] isEqualToString:@"NO"] ?  COLOR(245, 63, 63) : COLOR(255, 125, 1);
    [bottomV addSubview:rightL];
    
    //    备注
    NSString *memoNewStr =  [NSString stringWithFormat:@"%@", [itemDic objectNilForKey:@"look_memo"]];
    NSString *str = [NSString stringWithFormat:@"备注：%@", memoNewStr];
    NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)]};
    CGFloat height = [str boundingRectWithSize:CGSizeMake(BCWidth - 32, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
    height = ceil(height);
    UILabel *memoL = [[UILabel alloc] initWithFrame:CGRectMake(0, leftL.bottom + 10, BCWidth - 32,BCStringIsEmpty(memoNewStr) ? 0 : height)];
    memoL.font = [UIFont systemFontOfSize:MutilFont(14)];
    memoL.textColor = ACOLOR(30, 33, 38, 0.70);
    memoL.text = str;
    memoL.numberOfLines = 0;
    [bottomV addSubview:memoL];
    
    //    带看人
    UILabel *lookL = [[UILabel alloc] initWithFrame:CGRectMake(0,BCStringIsEmpty(memoNewStr) ? leftL.bottom + 12 :memoL.bottom + 12, BCWidth - 16, 18)];
    lookL.font = [UIFont systemFontOfSize:MutilFont(13)];
    lookL.textColor = ACOLOR(30, 33, 38, 0.45);
    NSString *timeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"actual_date"]];
    timeStr = [timeStr substringWithRange:NSMakeRange(0, timeStr.length - 9)];
    lookL.text = [NSString stringWithFormat:@"%@ · %@",[itemDic objectNilForKey:@"user_name"],timeStr];
    [bottomV addSubview:lookL];
    itemH += lookL.bottom + 14;
  }
  
  
  //  查看全部按钮
  UIButton *allBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  allBtn.frame = CGRectMake(0, itemH,BCWidth , 48);
  [contentView addSubview:allBtn];
  [allBtn addtargetBlock:^(UIButton *button) {
    UIButton *tabBtn = [weakSelf.topTabV viewWithTag:1017];
    [tabBtn sendActionsForControlEvents:UIControlEventTouchUpInside];
  }];
  
  
  UILabel *switchlabel1 = [[UILabel alloc] init];
  switchlabel1.frame = CGRectMake((BCWidth - 76)/2,0,58,48);
  switchlabel1.text = @"显示全部";
  switchlabel1.textColor = COLOR(26, 106, 255);
  switchlabel1.font = [UIFont systemFontOfSize:14];
  [allBtn addSubview:switchlabel1];
  
  
  UIImageView *leftIM1 = [[UIImageView alloc] initWithFrame:CGRectMake(switchlabel1.right + 4, 17, 14, 14)];
  leftIM1.image = [UIImage imageNamed:@"look_landlord"];
  [allBtn addSubview:leftIM1];
  
  UIView *lineV1 = [[UIView alloc] initWithFrame:CGRectMake(0,itemH, BCWidth , 0.5)];
  lineV1.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [contentView addSubview:lineV1];
  
  contentView.height = itemH + 48;
  return contentView;
}

#pragma mark 所有网络请求回来加载首页分享记录界面
- (UIView *)loadShare:(CGFloat)top{
  
  __weak typeof(self)weakSelf = self;
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,top + 12,BCWidth,50)];
  contentView.backgroundColor = [UIColor whiteColor];
  
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,200,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = [NSString stringWithFormat:@"分享记录（%lu）",(unsigned long)((![self.shareArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.shareArr)) ? 0:self.shareArr.count)];
  [contentView addSubview:nameLabel];
  
  if (![self.shareArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.shareArr)) {
    return contentView;
  }
  
  CGFloat itemH = 50;
  
  for (int i = 0; i < (self.shareArr.count > 3 ? 3 :self.shareArr.count); i ++) {
    
    NSDictionary *itemDic = [self.shareArr objectAtIndexCheck:i];
    
   
    UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth, 182)];
    bottomV.backgroundColor = [UIColor whiteColor];
    [contentView addSubview:bottomV];
    
    UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,itemH, BCWidth - 16, 1)];
    lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
    [contentView addSubview:lineV];

    
//      带看客户
    NSString *nameC = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"client_name"]];
    UILabel *leftNameL = [[UILabel alloc] initWithFrame:CGRectMake(16, 14, 200, 22)];
    leftNameL.text = BCStringIsEmpty(nameC) ? @"-" : nameC;
    leftNameL.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    leftNameL.textColor = COLOR(31, 33, 38);
    [bottomV addSubview:leftNameL];
    
    //    状态
    UILabel *rightStateL = [[UILabel alloc] initWithFrame:CGRectMake(bottomV.width - 44 - 16,15, 44, 20)];
    rightStateL.font = [UIFont systemFontOfSize:MutilFont(12)];
    rightStateL.layer.cornerRadius = 4;
    rightStateL.clipsToBounds = YES;
    rightStateL.textAlignment = NSTextAlignmentCenter;
    rightStateL.text = [[itemDic objectForKeyNil:@"valid"] isEqual:@(1)] ? @"生效中" :@"已失效";
    rightStateL.backgroundColor = [[itemDic objectForKeyNil:@"valid"] isEqual:@(1)] ? COLOR(26, 106, 255)  : ACOLOR(30, 33, 38, 0.45);
    rightStateL.textColor = [UIColor whiteColor];
    [bottomV addSubview:rightStateL];
    
//      查看次数
    UILabel *leftTimeL = [[UILabel alloc] initWithFrame:CGRectMake(16, leftNameL.bottom + 12, 100, 20)];
    leftTimeL.text = @"查看次数";
    leftTimeL.font = [UIFont systemFontOfSize:MutilFont(14)];
    leftTimeL.textColor = ACOLOR(30, 33, 38, 0.7);
    [bottomV addSubview:leftTimeL];
    
    UILabel *rightTimeL = [[UILabel alloc] initWithFrame:CGRectMake(leftTimeL.right + 6, leftNameL.bottom + 12, bottomV.width - leftTimeL.right - 6, 20)];
    rightTimeL.text =  [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"times"]];
    rightTimeL.font = [UIFont systemFontOfSize:MutilFont(14)];
    rightTimeL.textColor = COLOR(31, 33, 38);
    [bottomV addSubview:rightTimeL];
    
//    有效时长
    UILabel *leftPeriodL = [[UILabel alloc] initWithFrame:CGRectMake(16, leftTimeL.bottom + 5, 100, 20)];
    leftPeriodL.text = @"有效期限";
    leftPeriodL.font = [UIFont systemFontOfSize:MutilFont(14)];
    leftPeriodL.textColor = ACOLOR(30, 33, 38, 0.7);
    [bottomV addSubview:leftPeriodL];
    
    UILabel *rightPeriodL = [[UILabel alloc] initWithFrame:CGRectMake(leftPeriodL.right + 6, leftTimeL.bottom + 5, bottomV.width - leftPeriodL.right - 6, 20)];
    rightPeriodL.text =  [NSString stringWithFormat:@"%0.0f小时",[[itemDic objectNilForKey:@"valid_period"] floatValue]/60];
    rightPeriodL.font = [UIFont systemFontOfSize:MutilFont(14)];
    rightPeriodL.textColor = COLOR(31, 33, 38);
    [bottomV addSubview:rightPeriodL];
    
//      分享人
    UILabel *leftShareL = [[UILabel alloc] initWithFrame:CGRectMake(16, leftPeriodL.bottom + 5, 100, 20)];
    leftShareL.text = @"分享人";
    leftShareL.font = [UIFont systemFontOfSize:MutilFont(14)];
    leftShareL.textColor = ACOLOR(30, 33, 38, 0.7);
    [bottomV addSubview:leftShareL];
    
    UILabel *rightShareL = [[UILabel alloc] initWithFrame:CGRectMake(leftShareL.right + 6, leftPeriodL.bottom + 5, bottomV.width - leftShareL.right - 6, 20)];
    rightShareL.text =  [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
    rightShareL.font = [UIFont systemFontOfSize:MutilFont(14)];
    rightShareL.textColor = COLOR(31, 33, 38);
    [bottomV addSubview:rightShareL];
    
//      分享时间
    UILabel *leftShareTL = [[UILabel alloc] initWithFrame:CGRectMake(16, leftShareL.bottom + 5, 100, 20)];
    leftShareTL.text = @"分享时间";
    leftShareTL.font = [UIFont systemFontOfSize:MutilFont(14)];
    leftShareTL.textColor = ACOLOR(30, 33, 38, 0.7);
    [bottomV addSubview:leftShareTL];
    
    UILabel *rightShareTL = [[UILabel alloc] initWithFrame:CGRectMake(leftShareL.right + 6, leftShareL.bottom + 5, bottomV.width - leftShareL.right - 6, 20)];
    rightShareTL.text =  [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_time"]];
    rightShareTL.font = [UIFont systemFontOfSize:MutilFont(14)];
    rightShareTL.textColor = COLOR(31, 33, 38);
    [bottomV addSubview:rightShareTL];
    
    CGFloat heightB = rightShareTL.bottom;
    if (!BCStringIsEmpty([itemDic objectForKeyNil:@"look_time"])) {
      
      UILabel *leftLookTL = [[UILabel alloc] initWithFrame:CGRectMake(16, leftShareTL.bottom + 5, 100, 20)];
      leftLookTL.text = @"勘察打卡时间";
      leftLookTL.font = [UIFont systemFontOfSize:MutilFont(14)];
      leftLookTL.textColor = ACOLOR(30, 33, 38, 0.7);
      [bottomV addSubview:leftLookTL];
      
      UILabel *rightLookTL = [[UILabel alloc] initWithFrame:CGRectMake(leftShareL.right + 6, leftLookTL.top, bottomV.width - leftShareL.right - 6, 20)];
      rightLookTL.text =  [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"look_time"]];
      rightLookTL.font = [UIFont systemFontOfSize:MutilFont(14)];
      rightLookTL.textColor = COLOR(31, 33, 38);
      [bottomV addSubview:rightLookTL];
      
      UILabel *leftLookRL = [[UILabel alloc] initWithFrame:CGRectMake(16, leftLookTL.bottom + 5, 100, 20)];
      leftLookRL.text = @"勘察结果";
      leftLookRL.font = [UIFont systemFontOfSize:MutilFont(14)];
      leftLookRL.textColor = ACOLOR(30, 33, 38, 0.7);
      [bottomV addSubview:leftLookRL];
      
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)]};
      NSString *titleNameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"look_result"]];
      CGFloat height = [titleNameStr boundingRectWithSize:CGSizeMake(bottomV.width - leftShareL.right - 22, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      height = height < 21 ? 21 : height;
      
      UILabel *rightLookRL = [[UILabel alloc] initWithFrame:CGRectMake(leftLookRL.right + 6, leftLookRL.top, bottomV.width - leftLookRL.right - 22, height)];
      rightLookRL.text =  titleNameStr;
      rightLookRL.numberOfLines = 0;
      rightLookRL.font = [UIFont systemFontOfSize:MutilFont(14)];
      rightLookRL.textColor = COLOR(31, 33, 38);
      [bottomV addSubview:rightLookRL];
      
      UILabel *leftLookVL = [[UILabel alloc] initWithFrame:CGRectMake(16, rightLookRL.bottom + 5, 100, 20)];
      leftLookVL.text = @"现场勘察视频";
      leftLookVL.font = [UIFont systemFontOfSize:MutilFont(14)];
      leftLookVL.textColor = ACOLOR(30, 33, 38, 0.7);
      [bottomV addSubview:leftLookVL];
      
      CGFloat imageH = (BCWidth - 32 - 16)/3;
      CGFloat paddingL = 8.0; //button 间距
      CGFloat paddingT = 8.0; //button 间距
      CGFloat pointX = 16; //button X坐标
      CGFloat pointY = leftLookVL.bottom + 8; //button Y坐标
      NSArray *videoArr = [itemDic objectForKeyNil:@"look_files"];
      if (BCArrayIsEmpty(videoArr)) {
        continue;
      }
//        视频
      for (int k = 0; k < videoArr.count; k ++) {
        NSDictionary *imageDic = [videoArr objectAtIndexCheck:k];
        if (pointX + imageH > (BCWidth - 16)) {//换行
          pointX = 16;//X从新开始
          pointY += (imageH + paddingT);//换行后Y+
        }
        UIButton *imageBtn = [[UIButton alloc] initWithFrame:CGRectMake(pointX, pointY, imageH, imageH)];
        imageBtn.layer.cornerRadius = 4;
        imageBtn.clipsToBounds = YES;
        NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[imageDic objectNilForKey:@"url"]];
        UIImageView *imageI = [[UIImageView alloc] initWithFrame:CGRectMake(0,0, imageH, imageH)];
        [imageI sd_setImageWithURL:[NSURL URLWithString:[videoUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
        [imageBtn addSubview:imageI];
        
        UIImageView *playIM = [[UIImageView alloc] initWithFrame:CGRectMake((imageH - 25)/2, (imageH - 25)/2, 25, 25)];
        playIM.image = [UIImage imageNamed:@"play_video"];
        [imageBtn addSubview:playIM];
        
        [bottomV addSubview:imageBtn];
        [imageBtn addtargetBlock:^(UIButton *button) {
          NSURL * url = [NSURL URLWithString:[[imageDic objectForKeyNil:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
          AVPlayerViewController * pVC = [[AVPlayerViewController alloc] init];
          pVC.player = [AVPlayer playerWithURL:url];
          [weakSelf presentViewController:pVC animated:YES completion:nil];
          [pVC.player play];
        }];
        
        pointX += (imageH + paddingL);
        
      }
      
      heightB = pointY + imageH;
    }
    
    
    if ([[itemDic objectForKeyNil:@"valid"] isEqual:@(1)]) {//生效
      NSString *userName = [[NSUserDefaults standardUserDefaults] objectForKey:@"MAP_USERNAME"];
//        2个按钮
      UIButton *leftBtn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - 110 - 84, heightB + 14, 84, 32)];
      leftBtn.layer.cornerRadius = 4;
      leftBtn.layer.borderWidth = 1;
      leftBtn.tag = 66666 + i;
      [leftBtn addTarget:self action:@selector(clickStopShare:) forControlEvents:UIControlEventTouchUpInside];
      leftBtn.layer.borderColor = ACOLOR(30, 33, 38, 0.15).CGColor;
      [leftBtn setTitle:@"终止分享" forState:UIControlStateNormal];
      [leftBtn setTitleColor:COLOR(31, 33, 38) forState:UIControlStateNormal];
      leftBtn.titleLabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      if ([userName isEqualToString:[itemDic objectForKeyNil:@"create_by"]]) {
        [bottomV addSubview:leftBtn];
      }
     
      
      UIButton *rightBtn = [[UIButton alloc] initWithFrame:CGRectMake(leftBtn.right + 10, heightB + 14, 84, 32)];
      rightBtn.tag = 77777 + i;
      rightBtn.layer.cornerRadius = 4;
      rightBtn.layer.borderWidth = 1;
      rightBtn.layer.borderColor = ACOLOR(26, 106, 255, 1).CGColor;
      [rightBtn addTarget:self action:@selector(clickTakeLook:) forControlEvents:UIControlEventTouchUpInside];
      if (!BCStringIsEmpty([itemDic objectForKeyNil:@"look_time"])) {//如果已经有勘查打卡了.复制链接隐藏
        [rightBtn setTitle:@"" forState:UIControlStateNormal];
        rightBtn.hidden = YES;
        leftBtn.left = BCWidth - 84 - 16;
      } else {
      
        if (self.isTakeLook && [userName isEqualToString:[itemDic objectForKeyNil:@"create_by"]]) {
          [rightBtn setTitle:@"勘查打卡" forState:UIControlStateNormal];
        } else {
          [rightBtn setTitle:@"复制链接" forState:UIControlStateNormal];
        }
        
      }
      [rightBtn setTitleColor:ACOLOR(26, 106, 255, 1) forState:UIControlStateNormal];
      rightBtn.titleLabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:rightBtn];
      
      bottomV.height = leftBtn.bottom + 14;
      itemH += bottomV.height;
      
    } else {//不生效
      bottomV.height = heightB + 14;
      itemH += bottomV.height;
    }
    
  }
  
  //  查看全部按钮
  UIButton *allBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  allBtn.frame = CGRectMake(0, itemH, BCWidth , 48);
  [contentView addSubview:allBtn];
  [allBtn addtargetBlock:^(UIButton *button) {
    UIButton *tabBtn = [weakSelf.topTabV viewWithTag:1018];
    [tabBtn sendActionsForControlEvents:UIControlEventTouchUpInside];
  }];
  
  
  UILabel *switchlabel1 = [[UILabel alloc] init];
  switchlabel1.frame = CGRectMake((BCWidth - 76)/2,0,RealSize(58),48);
  switchlabel1.text = @"显示全部";
  switchlabel1.textColor = COLOR(26, 106, 255);
  switchlabel1.font = [UIFont systemFontOfSize:MutilFont(14)];
  [allBtn addSubview:switchlabel1];
  
  
  UIImageView *leftIM1 = [[UIImageView alloc] initWithFrame:CGRectMake(switchlabel1.right + 4, 17, 14, 14)];
  leftIM1.image = [UIImage imageNamed:@"look_landlord"];
  [allBtn addSubview:leftIM1];
  
  UIView *lineV1 = [[UIView alloc] initWithFrame:CGRectMake(0,itemH, BCWidth , 0.5)];
  lineV1.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [contentView addSubview:lineV1];
  
  
  contentView.height = itemH + 48;
  return contentView;
}

#pragma mark 打开附件
- (void)openSFDoc:(NSString *)url{
  
  if (BCStringIsEmpty(url)) {
    [self.notiflyView showModal:NotiflyFail andTitle:@"附件地址出错"];
    return;
  }
  

  MAH5ViewController *webVC = [[MAH5ViewController alloc] init];
  webVC.url = url;
  webVC.titleStr = @"预览附件";
  webVC.modalPresentationStyle = UIModalPresentationPopover;
  [self presentViewController:webVC animated:YES completion:^{
    
  }];

  
}
#pragma mark 所有网络请求回来加载首页审批记录界面
- (UIView *)loadAudit:(CGFloat)top{
  __weak typeof(self)weakSelf = self;
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,top + 12,BCWidth,50)];
  contentView.backgroundColor = [UIColor whiteColor];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,100,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = [NSString stringWithFormat:@"审批记录"];
  [contentView addSubview:nameLabel];
  

  if (!BCArrayIsEmpty(self.auditArr)) {
    //    分割线
    UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,49.5, BCWidth - 16, 0.5)];
    lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
    [contentView addSubview:lineV];
    
   
    UIView *mainV = [[UIView alloc] initWithFrame:CGRectMake(0, 50, BCWidth, 400)];
    mainV.backgroundColor = COLOR(242, 243, 245);
    [contentView addSubview:mainV];
    

    CGFloat itemH = 0;
    for (int i = 0; i < self.auditArr.count; i ++) {
     
      NSArray *itemArr = [self.auditArr objectAtIndexCheck:i];

      MAApprovalRecordView *appV = [[MAApprovalRecordView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth, 200) andArray:itemArr];
      [mainV addSubview:appV];
      itemH += appV.height + 12;

    }
    
    mainV.height = itemH;
   
    contentView.height = mainV.bottom;
  
  }
  
 
  return contentView;
}

- (NSString *)changeLandlord:(NSString *)type{
  
  if ([type isEqualToString:@"NATURAL_PERSON"]) {
    return @"自然人";
  } else if ([type isEqualToString:@"STATE_OWNED_ENTERPRISE"]) {
    return @"国企";
  } else if ([type isEqualToString:@"PRIVATE_ENTERPRISE"]) {
    return @"民企";
  } else if ([type isEqualToString:@"GOVERNMENT_AGENCY"]) {
    return @"政府机构";
  } else if ([type isEqualToString:@"OTHER_ORGANIZATION"]) {
    return @"其他机构";
  } else if ([type isEqualToString:@"PUBLIC_INSTITUTION"]) {
    return @"事业单位";
  }
  
  return @"-";
}

- (NSString *)changeLandlordIdentity:(NSString *)type{
  
  if ([type isEqualToString:@"PROPERTY_OWNER"]) {
    return @"产权人";
  } else if ([type isEqualToString:@"SECONDARY_TENANT"]) {
    return @"二房东";
  } else if ([type isEqualToString:@"THIRD_PARTY_TENANT"]) {
    return @"三房东";
  } else if ([type isEqualToString:@"OTHER"]) {
    return @"其他";
  }
  
  return @"-";
}

#pragma mark 所有网络请求回来加载tab下的房东信息界面
- (void)loadLandlordRecord{
  
  __weak typeof(self)weakSelf = self;
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,12,BCWidth,50)];
  contentView.backgroundColor = [UIColor whiteColor];
  NSArray *arr = [self.detailDic objectForKeyNil:@"landlords"];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,200,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = [NSString stringWithFormat:@"房东信息（%lu）",(unsigned long)((![arr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(arr)) ? 0:arr.count)];
  [contentView addSubview:nameLabel];
  
  UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
  switchMap.frame = CGRectMake(BCWidth - RealSize(62) - 20 - 15, 0, 82, 50);
  [switchMap addTarget:self action:@selector(addLandlord) forControlEvents:UIControlEventTouchUpInside];
  NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"EDIT_POINT"];
  if ([editPoint isEqualToString:@"1"] && ![[self.detailDic objectNilForKey:@"state"] isEqualToString:@"REJECT"]) {
    [contentView addSubview:switchMap];
  }
  
  UIImageView *leftIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 16, 18, 18)];
  leftIM.image = [UIImage imageNamed:@"add_landlord"];
  [switchMap addSubview:leftIM];
  
  UILabel *switchlabel = [[UILabel alloc] init];
  switchlabel.frame = CGRectMake(20,0,RealSize(62),50);
  switchlabel.text = @"添加房东";
  switchlabel.textColor = COLOR(26, 106, 255);
  switchlabel.font = [UIFont systemFontOfSize:MutilFont(15)];
  [switchMap addSubview:switchlabel];
  [self.landlordScrollView addSubview:contentView];
  
  if (![arr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(arr)) {//空视图
    
    UIImageView *emptyView = [[UIImageView alloc] initWithFrame:CGRectMake((BCWidth - 160)/2, self.heightTop + 144, 160, 180)];
    emptyView.image = [UIImage imageNamed:@"tab_nodata"];
    [self.landlordScrollView addSubview:emptyView];
    
    UILabel *emptyL = [[UILabel alloc] initWithFrame:CGRectMake(0, emptyView.bottom - 40, BCWidth, 20)];
    emptyL.textColor = ACOLOR(30, 33, 38, 0.45);
    emptyL.font = [UIFont systemFontOfSize:MutilFont(14)];
    emptyL.text = @"暂无房东信息";
    emptyL.textAlignment = NSTextAlignmentCenter;
    [self.landlordScrollView addSubview:emptyL];
    

  } else {//有房东信息
    
    //    分割线
    UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,49, BCWidth - 16, 1)];
    lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
    [contentView addSubview:lineV];
    
    
    
    CGFloat itemH = 62;
    for (int i = 0; i < arr.count ; i ++) {
      
      NSDictionary *itemDic = [arr objectAtIndexCheck:i];
      UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth , 2550)];
      bottomV.backgroundColor = [UIColor whiteColor];
      [self.landlordScrollView addSubview:bottomV];
      
      //   姓名
      UILabel *nameL = [[UILabel alloc] initWithFrame:CGRectMake(16, 14, 120, 21)];
      nameL.font = [UIFont systemFontOfSize:MutilFont(15)];
      nameL.textColor = ACOLOR(30, 33, 38, 0.45);
      nameL.text = @"房东姓名";
      [bottomV addSubview:nameL];
      
      UILabel *nameVL = [[UILabel alloc] initWithFrame:CGRectMake(nameL.right + 6, 14, BCWidth - nameL.right - 22, 21)];
      nameVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      nameVL.textAlignment = NSTextAlignmentRight;
      nameVL.textColor = ACOLOR(31, 33, 38, 1);
      nameVL.text = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"name"]];
      [bottomV addSubview:nameVL];
      
      //      电话
      UILabel *phoneL = [[UILabel alloc] initWithFrame:CGRectMake(16, nameL.bottom + 14, 120, 21)];
      phoneL.font = [UIFont systemFontOfSize:MutilFont(15)];
      phoneL.textColor = ACOLOR(30, 33, 38, 0.45);
      phoneL.text = @"房东电话";
      [bottomV addSubview:phoneL];
      
      UILabel *phoneVL = [[UILabel alloc] initWithFrame:CGRectMake(phoneL.right + 6, nameL.bottom + 14, BCWidth - phoneL.right - 22, 21)];
      phoneVL.textAlignment = NSTextAlignmentRight;
      phoneVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      phoneVL.textColor = ACOLOR(31, 33, 38, 1);
      phoneVL.text = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"phone"]];
      [bottomV addSubview:phoneVL];
      
      //      房东身份证正反面
      UILabel *landCardL = [[UILabel alloc] initWithFrame:CGRectMake(16,phoneL.bottom +  14, 200, 20)];
      landCardL.text = @"房东身份证";
      landCardL.font = [UIFont systemFontOfSize:MutilFont(15)];
      landCardL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:landCardL];
      
      CGFloat landImageW = (BCWidth - 32 - 8)/2;
      NSArray *landImageArr = @[[[itemDic objectNilForKey:@"owner_id_number_front_url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]],[[itemDic objectNilForKey:@"owner_id_number_back_url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
      for (int j = 0; j < landImageArr.count; j ++) {
        
        UIButton *landImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(16 + (landImageW + 8) * j, landCardL.bottom + 8, landImageW, 112)];
        landImageBtn.layer.cornerRadius = 4;
        landImageBtn.clipsToBounds = YES;
        landImageBtn.backgroundColor = COLOR(244, 245, 247);
        UIImageView *landIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0 ,landImageW,  112)];
        [landIM sd_setImageWithURL:[NSURL URLWithString:[landImageArr objectAtIndexCheck:j]] placeholderImage:[UIImage imageNamed:@"icon_pla"]];
        [landImageBtn addSubview:landIM];
        [bottomV addSubview:landImageBtn];
        [landImageBtn addtargetBlock:^(UIButton *button) {
          if ([landImageArr isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(landImageArr)) {
            [ZKPhotoBrowser showWithImageUrls:landImageArr currentPhotoIndex:j sourceSuperView:button];
          }
        }];
      }
      
//      房东性质
      UILabel *landlordNatureL = [[UILabel alloc] initWithFrame:CGRectMake(16,landCardL.bottom + 134, 120, 20)];
      landlordNatureL.text = @"房东性质";
      landlordNatureL.font = [UIFont systemFontOfSize:MutilFont(15)];
      landlordNatureL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:landlordNatureL];
      
      NSString *landlordNatureStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"landlord_type"]];
      UILabel *landlordNatureVL = [[UILabel alloc] initWithFrame:CGRectMake(136,landCardL.bottom + 134, BCWidth - landlordNatureL.right - 22, 20)];
      landlordNatureVL.text = BCStringIsEmpty(landlordNatureStr) ? @"-" : [self changeLandlord:landlordNatureStr];
      landlordNatureVL.textAlignment = NSTextAlignmentRight;
      landlordNatureVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      landlordNatureVL.textColor = ACOLOR(31, 33, 38,1);
      [bottomV addSubview:landlordNatureVL];
      
      
//      房东身份
      UILabel *landlordIdentityL = [[UILabel alloc] initWithFrame:CGRectMake(16,landlordNatureL.bottom + 14, 120, 20)];
      landlordIdentityL.text = @"房东身份";
      landlordIdentityL.font = [UIFont systemFontOfSize:MutilFont(15)];
      landlordIdentityL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:landlordIdentityL];
      
      NSString *landlordIdentityStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"landlord_identity"]];
      UILabel *landlordIdentityVL = [[UILabel alloc] initWithFrame:CGRectMake(136,landlordNatureL.bottom + 14, BCWidth -   landlordIdentityL.right - 22, 20)];
      landlordIdentityVL.text = BCStringIsEmpty(landlordIdentityStr) ? @"-" : [self changeLandlordIdentity:landlordIdentityStr];
      landlordIdentityVL.textAlignment = NSTextAlignmentRight;
      landlordIdentityVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      landlordIdentityVL.textColor = ACOLOR(31, 33, 38,1);
      [bottomV addSubview:landlordIdentityVL];
      
//      是否有排他协议
      UILabel *exclusiveAgreementL = [[UILabel alloc] initWithFrame:CGRectMake(16, landlordIdentityL.bottom + 14, 120, 20)];
      exclusiveAgreementL.text = @"是否有排他协议";
      exclusiveAgreementL.font = [UIFont systemFontOfSize:MutilFont(15)];
      exclusiveAgreementL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:exclusiveAgreementL];
      
      NSString *exclusiveAgreementStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"is_exclusive_agreement"]];
      UILabel *exclusiveAgreementVL = [[UILabel alloc] initWithFrame:CGRectMake(136,landlordIdentityL.bottom + 14, BCWidth - exclusiveAgreementL.right - 22, 20)];
      exclusiveAgreementVL.text = BCStringIsEmpty(exclusiveAgreementStr) ? @"-" : [exclusiveAgreementStr isEqualToString:@"1"] ? @"是":@"否";
      exclusiveAgreementVL.textAlignment = NSTextAlignmentRight;
      exclusiveAgreementVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      exclusiveAgreementVL.textColor = ACOLOR(31, 33, 38,1);
      [bottomV addSubview: exclusiveAgreementVL];

//      是否分租
      UILabel *divideRentL = [[UILabel alloc] initWithFrame:CGRectMake(16,exclusiveAgreementL.bottom + 14, 120, 20)];
      divideRentL.text = @"是否分租";
      divideRentL.font = [UIFont systemFontOfSize:MutilFont(15)];
      divideRentL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:divideRentL];
      
      NSString *divideRentStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"is_rent_divided"]];
      UILabel *divideRentVL = [[UILabel alloc] initWithFrame:CGRectMake(136,exclusiveAgreementL.bottom + 14, BCWidth - divideRentL.right - 22, 20)];
      divideRentVL.text = BCStringIsEmpty(divideRentStr) ? @"-" : [exclusiveAgreementStr isEqualToString:@"1"] ? @"是":@"否";
      divideRentVL.textAlignment = NSTextAlignmentRight;
      divideRentVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      divideRentVL.textColor = ACOLOR(31, 33, 38,1);
      [bottomV addSubview: divideRentVL];
      
      
      //      租金
      UILabel *rentTL = [[UILabel alloc] initWithFrame:CGRectMake(16,divideRentVL.bottom + 14, 120, 20)];
      rentTL.text = @"租金";
      rentTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:rentTL];
      
      UILabel *rentVL = [[UILabel alloc] initWithFrame:CGRectMake(rentTL.right + 6,divideRentVL.bottom+ 14, BCWidth - rentTL.right - 22, 20)];
      NSNumberFormatter *formatter = [[NSNumberFormatter alloc]init];
      formatter.numberStyle = NSNumberFormatterDecimalStyle;
      NSString *money = [formatter stringFromNumber:[NSNumber numberWithDouble:[[itemDic objectNilForKey:@"rent_money"] doubleValue]]];
      rentVL.text = [NSString stringWithFormat:@"%@元",money];
      rentVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentVL.textColor = ACOLOR(31, 33, 38,1);
      rentVL.textAlignment = NSTextAlignmentRight;
      [bottomV addSubview:rentVL];
      //      大写
      UILabel *rentCapL = [[UILabel alloc] initWithFrame:CGRectMake(rentTL.right + 6,rentVL.bottom , BCWidth - rentTL.right - 22, 20)];
      NSString *chineseAmount = [self getAmountInWords:[NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"rent_money"]]];
      rentCapL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentCapL.textAlignment = NSTextAlignmentRight;
      rentCapL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:rentCapL];
      NSMutableAttributedString *rentString = [[NSMutableAttributedString alloc] initWithString:chineseAmount];
      [rentString addAttributes:@{NSForegroundColorAttributeName: COLOR(255, 111, 0)} range:[chineseAmount rangeOfString:@"万"]];
      [rentString addAttributes:@{NSForegroundColorAttributeName: COLOR(255, 111, 0)} range:[chineseAmount rangeOfString:@"仟"]];
      rentCapL.attributedText = rentString;
      
      //      转让费
      UILabel *transferTL = [[UILabel alloc] initWithFrame:CGRectMake(16,rentTL.bottom + 34, 120, 20)];
      transferTL.text = @"转让费";
      transferTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      transferTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:transferTL];
      
      UILabel *transferVL = [[UILabel alloc] initWithFrame:CGRectMake(transferTL.right + 6,rentTL.bottom + 34, BCWidth - transferTL.right - 22, 20)];
      NSString *money1 = [formatter stringFromNumber:[NSNumber numberWithDouble:[[itemDic objectNilForKey:@"transfer_fee"] doubleValue]]];
      transferVL.text = [NSString stringWithFormat:@"%@元",money1];
      transferVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      transferVL.textAlignment = NSTextAlignmentRight;
      transferVL.textColor = ACOLOR(31, 33, 38,1);
      [bottomV addSubview:transferVL];
      //      大写
      UILabel *transferCapL = [[UILabel alloc] initWithFrame:CGRectMake(transferTL.right + 6,transferVL.bottom , BCWidth - transferTL.right - 22, 20)];
      NSString *chineseAmount1 = [self getAmountInWords:[NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"transfer_fee"]]];
      transferCapL.font = [UIFont systemFontOfSize:MutilFont(15)];
      transferCapL.textAlignment = NSTextAlignmentRight;
      transferCapL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:transferCapL];
      NSMutableAttributedString *rentString1 = [[NSMutableAttributedString alloc] initWithString:chineseAmount1];
      [rentString1 addAttributes:@{NSForegroundColorAttributeName: COLOR(255, 111, 0)} range:[chineseAmount1 rangeOfString:@"万"]];
      [rentString1 addAttributes:@{NSForegroundColorAttributeName: COLOR(255, 111, 0)} range:[chineseAmount1 rangeOfString:@"仟"]];
      transferCapL.attributedText = rentString1;
      
      //     租房合同
      UILabel *leaseTL = [[UILabel alloc] initWithFrame:CGRectMake(16,transferTL.bottom + 34, 120, 20)];
      leaseTL.text = @"租房合同";
      leaseTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      leaseTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:leaseTL];
      
      //     租房合同附件
      NSArray *leaseArr = [itemDic objectForKeyNil:@"lease_contract_files"];
      if (![leaseArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(leaseArr)) {//没有租房合同附件
        
        UILabel *leaseVL = [[UILabel alloc] initWithFrame:CGRectMake(136,transferTL.bottom + 34, BCWidth - leaseTL.right - 22, 20)];
        leaseVL.text = @"-";
        leaseVL.textAlignment = NSTextAlignmentRight;
        leaseVL.font = [UIFont systemFontOfSize:MutilFont(15)];
        leaseVL.textColor = ACOLOR(31, 33, 38,1);
        [bottomV addSubview:leaseVL];
        
      } else {//有附件
        NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
        
        for (int j = 0; j < leaseArr.count; j ++) {
          
          NSDictionary *leaseDic = [leaseArr objectAtIndexCheck:j];
          
          UIButton *leaseImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(16, leaseTL.bottom + 8 + (44 + 10) * j, BCWidth - 32, 44)];
          leaseImageBtn.layer.cornerRadius = 4;
          leaseImageBtn.clipsToBounds = YES;
          leaseImageBtn.backgroundColor = COLOR(244, 245, 247);
          [bottomV addSubview:leaseImageBtn];
          
          UIImageView *leaseIM = [[UIImageView alloc] initWithFrame:CGRectMake(16, 10 ,24, 24)];
          [leaseImageBtn addSubview:leaseIM];
          
          UILabel *leaseL = [[UILabel alloc] initWithFrame:CGRectMake(leaseIM.right + 10,0, BCWidth - 80, 44)];
          leaseL.text = [NSString stringWithFormat:@"%@",[leaseDic objectNilForKey:@"name"]];
          leaseL.font = [UIFont systemFontOfSize:MutilFont(15)];
          leaseL.textColor = ACOLOR(31, 33, 38,1);
          leaseL.lineBreakMode = NSLineBreakByTruncatingMiddle;
          [leaseImageBtn addSubview:leaseL];
          
          if ([temArr containsObject:[leaseDic objectNilForKey:@"suffix_type"]]) {//如果是图片
            
            leaseIM.image = [UIImage imageNamed:@"file_img"];
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
              [ZKPhotoBrowser showWithImageUrls:@[[[leaseDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] currentPhotoIndex:0 sourceSuperView:button];
            }];
          } else {//是其他文本 pdf  word
            
            NSString *fileStr = [NSString stringWithFormat:@"%@",[leaseDic objectNilForKey:@"suffix_type"] ];
            if ([fileStr containsString:@"pdf"]) {
              leaseIM.image = [UIImage imageNamed:@"file_pdf"];
            } else  if ([fileStr containsString:@"doc"] || [fileStr containsString:@"docx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_word"];
            } else  if ([fileStr containsString:@"xls"] || [fileStr containsString:@"xls"]) {
              leaseIM.image = [UIImage imageNamed:@"file_excel"];
            }else  if ([fileStr containsString:@"ppt"] || [fileStr containsString:@"pptx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_ppt"];
            }else {
              leaseIM.image = [UIImage imageNamed:@"file_no"];
            }
            
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
              
              NSString *url = [[leaseDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
              [weakSelf openSFDoc:url];
              
            }];
            
            
          }
          
          
          
        }
        
      }
      
      //     产权证
      CGFloat deedH = 0;
      if (![leaseArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(leaseArr)) {//没有租房合同附件
        deedH = leaseTL.bottom  + 14;
      } else {
        deedH = leaseTL.bottom + 8 + (44 + 10) * leaseArr.count - 10 + 14;
      }
      
      UILabel *deedTL = [[UILabel alloc] initWithFrame:CGRectMake(16,deedH, 120, 20)];
      deedTL.text = @"产权证";
      deedTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      deedTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:deedTL];
      
      //     产权证附件
      NSArray *deedArr = [itemDic objectForKeyNil:@"title_certificate_files"];
      if (![deedArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(deedArr)) {//没有产权证附件
        
        UILabel *leaseVL = [[UILabel alloc] initWithFrame:CGRectMake(136,deedH, BCWidth - deedTL.right - 22, 20)];
        leaseVL.text = @"-";
        leaseVL.textAlignment = NSTextAlignmentRight;
        leaseVL.font = [UIFont systemFontOfSize:MutilFont(15)];
        leaseVL.textColor = ACOLOR(31, 33, 38,1);
        [bottomV addSubview:leaseVL];
        
      } else {//有附件
        
        NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
        
        for (int j = 0; j < deedArr.count; j ++) {
          
          NSDictionary *deedDic = [deedArr objectAtIndexCheck:j];
          UIButton *leaseImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(16, deedTL.bottom + 8 + (44 + 10) * j, BCWidth - 32, 44)];
          leaseImageBtn.layer.cornerRadius = 4;
          leaseImageBtn.clipsToBounds = YES;
          leaseImageBtn.backgroundColor = COLOR(244, 245, 247);
          [bottomV addSubview:leaseImageBtn];
          
          UIImageView *leaseIM = [[UIImageView alloc] initWithFrame:CGRectMake(16, 10 ,24, 24)];
          [leaseImageBtn addSubview:leaseIM];
          
          UILabel *leaseL = [[UILabel alloc] initWithFrame:CGRectMake(leaseIM.right + 10,0, BCWidth - 80, 44)];
          leaseL.text = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"name"]];
          leaseL.font = [UIFont systemFontOfSize:MutilFont(15)];
          leaseL.textColor = ACOLOR(31, 33, 38,1);
          leaseL.lineBreakMode = NSLineBreakByTruncatingMiddle;
          [leaseImageBtn addSubview:leaseL];
          
          if ([temArr containsObject:[deedDic objectNilForKey:@"suffix_type"]]) {//如果是图片
            
            leaseIM.image = [UIImage imageNamed:@"file_img"];
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
              [ZKPhotoBrowser showWithImageUrls:@[[[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] currentPhotoIndex:0 sourceSuperView:button];
            }];
          } else {//是其他文本 pdf  word
            
            NSString *fileStr = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"suffix_type"] ];
            if ([fileStr containsString:@"pdf"]) {
              leaseIM.image = [UIImage imageNamed:@"file_pdf"];
            } else  if ([fileStr containsString:@"doc"] || [fileStr containsString:@"docx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_word"];
            } else  if ([fileStr containsString:@"xls"] || [fileStr containsString:@"xls"]) {
              leaseIM.image = [UIImage imageNamed:@"file_excel"];
            }else  if ([fileStr containsString:@"ppt"] || [fileStr containsString:@"pptx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_ppt"];
            }else {
              leaseIM.image = [UIImage imageNamed:@"file_no"];
            }
            
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
            
              NSString *url = [[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
              [weakSelf openSFDoc:url];
              
            }];
            
            
          }
          
          
        }
        
      }
      
      //      门头照
      NSArray *headUrls = [itemDic objectForKeyNil:@"urls"];
      NSString *headUrl = @"";
      if ([headUrls isKindOfClass: [NSArray class]] && !BCArrayIsEmpty(headUrls)) {
        headUrl = [[headUrls objectAtIndexCheck:0] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
      }
      CGFloat memoH = 0;
      if (![deedArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(deedArr)) {//没有产权证附件
        memoH = deedTL.bottom  + 14;
      } else {
        memoH = deedTL.bottom + 8 + (44 + 10) * deedArr.count - 10 + 14;
      }
      
      UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(16,memoH, 120, 20)];
      headL.text = @"门头照";
      headL.font = [UIFont systemFontOfSize:MutilFont(15)];
      headL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:headL];
      
      UIButton *imageBtn = [[UIButton alloc] initWithFrame:CGRectMake(16, headL.bottom + 8, BCWidth - 32, 220)];
      imageBtn.layer.cornerRadius = 4;
      imageBtn.clipsToBounds = YES;
      imageBtn.backgroundColor = COLOR(244, 245, 247);
      UIImageView *headIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0 ,BCWidth - 32,  220)];
      [headIM sd_setImageWithURL:[NSURL URLWithString:headUrl]  placeholderImage:[UIImage imageNamed:@"icon_pla"]];
      [imageBtn addSubview:headIM];
      [bottomV addSubview:imageBtn];
      [imageBtn addtargetBlock:^(UIButton *button) {
        if (!BCStringIsEmpty(headUrl)) {
          [ZKPhotoBrowser showWithImageUrls:@[headUrl] currentPhotoIndex:0 sourceSuperView:button];
        }
      }];
      
      //      备注
      UILabel *memoTL = [[UILabel alloc] initWithFrame:CGRectMake(16,imageBtn.bottom + 14, 120, 20)];
      memoTL.text = @"备注";
      memoTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      memoTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:memoTL];
      
      NSString *memoSring = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
      CGFloat height = [memoSring boundingRectWithSize:CGSizeMake(BCWidth - 160, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      UILabel *memoVL = [[UILabel alloc] initWithFrame:CGRectMake(memoTL.right + 6,imageBtn.bottom + 14,BCWidth - memoTL.right - 22, height)];
      memoVL.text = !BCStringIsEmpty(memoSring) ? memoSring : @"-";
      memoVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      memoVL.textAlignment = NSTextAlignmentRight;
      memoVL.textColor = COLOR(31, 33, 38);
      [bottomV addSubview:memoVL];
      
      //      当前租户姓名
      UILabel *rentNameTL = [[UILabel alloc] initWithFrame:CGRectMake(16,memoVL.bottom + 14, 120, 20)];
      rentNameTL.text = @"当前租户姓名";
      rentNameTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentNameTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:rentNameTL];
      
      NSString *rnSring = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"rent_name"]];
      UILabel *rentNameVL = [[UILabel alloc] initWithFrame:CGRectMake(rentNameTL.right + 6,memoVL.bottom + 14,BCWidth - rentNameTL.right - 22, 20)];
      rentNameVL.text = !BCStringIsEmpty(rnSring) ? rnSring : @"-";
      rentNameVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentNameVL.textColor = COLOR(31, 33, 38);
      rentNameVL.textAlignment = NSTextAlignmentRight;
      [bottomV addSubview:rentNameVL];
      
      //      租户联系方式
      UILabel *rentPhoneTL = [[UILabel alloc] initWithFrame:CGRectMake(16,rentNameTL.bottom + 14, 120, 20)];
      rentPhoneTL.text = @"租户联系方式";
      rentPhoneTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentPhoneTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:rentPhoneTL];
      
      NSString *rpSring = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"rent_phone"]];
      UILabel *rentPhoneVL = [[UILabel alloc] initWithFrame:CGRectMake(rentPhoneTL.right + 6,rentNameTL.bottom + 14,BCWidth - rentPhoneTL.right - 22, 20)];
      rentPhoneVL.text = !BCStringIsEmpty(rpSring) ? rpSring: @"-";
      rentPhoneVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentPhoneVL.textColor = COLOR(31, 33, 38);
      rentPhoneVL.textAlignment = NSTextAlignmentRight;
      [bottomV addSubview:rentPhoneVL];
      
      //      出租截止日期
      UILabel *rentDeadlineTL = [[UILabel alloc] initWithFrame:CGRectMake(16,rentPhoneTL.bottom + 14, 120, 20)];
      rentDeadlineTL.text = @"出租截止日期";
      rentDeadlineTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentDeadlineTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:rentDeadlineTL];
      
      NSString *rdSring = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"rent_deadline_date"]];
      UILabel *rentDeadlineVL = [[UILabel alloc] initWithFrame:CGRectMake(rentDeadlineTL.right + 6,rentPhoneTL.bottom + 14,BCWidth - rentDeadlineTL.right - 22, 20)];
      rentDeadlineVL.text = !BCStringIsEmpty(rdSring ) ? [rdSring substringToIndex:10] : @"-";
      rentDeadlineVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentDeadlineVL.textColor = COLOR(31, 33, 38);
      rentDeadlineVL.textAlignment = NSTextAlignmentRight;
      [bottomV addSubview:rentDeadlineVL];
      
      //      当前租户收款银行账户
      UILabel *rentBankTL = [[UILabel alloc] initWithFrame:CGRectMake(16,rentDeadlineTL.bottom + 14, 120, RealSize(40))];
      rentBankTL.text = @"当前租户收款\n银行账户";
      rentBankTL.numberOfLines = 0;
      rentBankTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentBankTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:rentBankTL];
      
      NSString *rbSring = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"bank_account"]];
      UILabel *rentBankVL = [[UILabel alloc] initWithFrame:CGRectMake(rentBankTL.right + 6,rentDeadlineTL.bottom + 14,BCWidth - rentBankTL.right - 22, 20)];
      rentBankVL.text = !BCStringIsEmpty(rbSring) ? rbSring: @"-";
      rentBankVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentBankVL.textColor = COLOR(31, 33, 38);
      rentBankVL.textAlignment = NSTextAlignmentRight;
      [bottomV addSubview:rentBankVL];
      
      //      当前租户收款银行账户户名
      UILabel *rentBankNameTL = [[UILabel alloc] initWithFrame:CGRectMake(16,rentBankTL.bottom + 14, 120, RealSize(40))];
      rentBankNameTL.text = @"当前租户收款\n银行账户户名";
      rentBankNameTL.numberOfLines = 0;
      rentBankNameTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentBankNameTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:rentBankNameTL];
      
      NSString *rbnSring = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"bank_account_name"]];
      UILabel *rentBankNameVL = [[UILabel alloc] initWithFrame:CGRectMake(rentBankNameTL.right + 6,rentBankTL.bottom + 14,BCWidth - rentBankNameTL.right - 22, 20)];
      rentBankNameVL.text = !BCStringIsEmpty(rbnSring) ? rbnSring : @"-";
      rentBankNameVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentBankNameVL.textColor = COLOR(31, 33, 38);
      rentBankNameVL.textAlignment = NSTextAlignmentRight;
      [bottomV addSubview:rentBankNameVL];
      
      //      当前租户涉及营业执照
      UILabel *rentLicenseTL = [[UILabel alloc] initWithFrame:CGRectMake(16,rentBankNameTL.bottom + 14, 220, 20)];
      rentLicenseTL.text = @"当前租户涉及营业执照";
      rentLicenseTL.numberOfLines = 0;
      rentLicenseTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentLicenseTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:rentLicenseTL];
      
      NSString *rentLicenseUrl = @"";
      if ([[itemDic objectNilForKey:@"license_url"] isKindOfClass: [NSString class]] && !BCStringIsEmpty([itemDic objectNilForKey:@"license_url"])) {
        rentLicenseUrl = [[itemDic objectNilForKey:@"license_url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
      }
      UIButton *rentLicenseBtn = [[UIButton alloc] initWithFrame:CGRectMake(16, rentLicenseTL.bottom + 8, BCWidth - 32, 220)];
      rentLicenseBtn.layer.cornerRadius = 4;
      rentLicenseBtn.clipsToBounds = YES;
      rentLicenseBtn.backgroundColor = COLOR(244, 245, 247);
      UIImageView *rentLicenseIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0 ,BCWidth - 32,  220)];
      [rentLicenseIM sd_setImageWithURL:[NSURL URLWithString:rentLicenseUrl]  placeholderImage:[UIImage imageNamed:@"icon_pla"]];
      [rentLicenseBtn addSubview:rentLicenseIM];
      [bottomV addSubview:rentLicenseBtn];
      [rentLicenseBtn addtargetBlock:^(UIButton *button) {
        if (!BCStringIsEmpty(rentLicenseUrl)) {
          [ZKPhotoBrowser showWithImageUrls:@[rentLicenseUrl] currentPhotoIndex:0 sourceSuperView:button];
        }
      }];
      
      
      //      当前租户身份证
      UILabel *rentCardTL = [[UILabel alloc] initWithFrame:CGRectMake(16,rentLicenseBtn.bottom + 14, 120, 20)];
      rentCardTL.text = @"当前租户身份证";
      rentCardTL.numberOfLines = 0;
      rentCardTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentCardTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:rentCardTL];
      
      CGFloat rentCardW = (BCWidth - 32 - 8)/2;
      NSArray *rentCardArr = @[[[itemDic objectNilForKey:@"tenant_number_front_url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]],[[itemDic objectNilForKey:@"tenant_number_back_url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
      for (int j = 0; j < rentCardArr.count; j ++) {
        
        UIButton *landImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(16 + (rentCardW + 8) * j, rentCardTL.bottom + 8, rentCardW, 112)];
        landImageBtn.layer.cornerRadius = 4;
        landImageBtn.clipsToBounds = YES;
        landImageBtn.backgroundColor = COLOR(244, 245, 247);
        UIImageView *landIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0 ,rentCardW,  112)];
        [landIM sd_setImageWithURL:[NSURL URLWithString:[rentCardArr objectAtIndexCheck:j]] placeholderImage:[UIImage imageNamed:@"icon_pla"]];
        [landImageBtn addSubview:landIM];
        [bottomV addSubview:landImageBtn];
        [landImageBtn addtargetBlock:^(UIButton *button) {
          if ([rentCardArr isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(rentCardArr)) {
            [ZKPhotoBrowser showWithImageUrls:rentCardArr currentPhotoIndex:j sourceSuperView:button];
          }
        }];
      }
      
      //      执照法人与租户收款账户同一人
      UILabel *legalTL = [[UILabel alloc] initWithFrame:CGRectMake(16,rentCardTL.bottom + 134, 120, RealSize(60))];
      legalTL.text = @"执照法人与租\n户收款账户同\n一人";
      legalTL.numberOfLines = 0;
      legalTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      legalTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:legalTL];
      
      UILabel *legalVL = [[UILabel alloc] initWithFrame:CGRectMake(leaseTL.right + 6,rentCardTL.bottom + 136,BCWidth - leaseTL.right - 22, 20)];
      if ([[itemDic objectForKeyNil:@"identical"] isKindOfClass:[NSString class]] && [[itemDic objectForKeyNil:@"identical"] isEqualToString:@""]) {
        legalVL.text = @"-";
      } else {
        legalVL.text =  [[itemDic objectForKeyNil:@"identical"] isEqual:@(1)] ? @"是":@"否";
      }
      legalVL.textAlignment = NSTextAlignmentRight;
      legalVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      legalVL.textColor = COLOR(31, 33, 38);
      [bottomV addSubview:legalVL];
      
      //      法人姓名
      UILabel *legalNameTL = [[UILabel alloc] initWithFrame:CGRectMake(16,legalTL.bottom + 14, 120, 20)];
      legalNameTL.text = @"法人姓名";
      legalNameTL.numberOfLines = 0;
      legalNameTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      legalNameTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:legalNameTL];
      
      NSString *legalNameString = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"legal_person"]];
      UILabel *legalNameVL = [[UILabel alloc] initWithFrame:CGRectMake(legalNameTL.right + 6,legalTL.bottom + 14,BCWidth - legalNameTL.right - 22, 20)];
      legalNameVL.text = BCStringIsEmpty(legalNameString) ? @"-" : legalNameString;
      legalNameVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      legalNameVL.textColor = COLOR(31, 33, 38);
      legalNameVL.textAlignment = NSTextAlignmentRight;
      [bottomV addSubview:legalNameVL];
      
      //      法人联系方式
      UILabel *legalPhoneTL = [[UILabel alloc] initWithFrame:CGRectMake(16,legalNameTL.bottom + 14, 120, 20)];
      legalPhoneTL.text = @"法人联系方式";
      legalPhoneTL.numberOfLines = 0;
      legalPhoneTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      legalPhoneTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:legalPhoneTL];
      
      NSString *legalPhoneString = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"legal_person_phone"]];
      UILabel *legalPhoneVL = [[UILabel alloc] initWithFrame:CGRectMake(legalPhoneTL.right + 6,legalNameTL.bottom + 14,BCWidth - legalPhoneTL.right - 22, 20)];
      legalPhoneVL.text = BCStringIsEmpty(legalPhoneString) ? @"-" : legalPhoneString;
      legalPhoneVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      legalPhoneVL.textColor = COLOR(31, 33, 38);
      legalPhoneVL.textAlignment = NSTextAlignmentRight;
      [bottomV addSubview:legalPhoneVL];
      
      //      法人身份证
      UILabel *legalCardTL = [[UILabel alloc] initWithFrame:CGRectMake(16,legalPhoneTL.bottom + 14, 120, 20)];
      legalCardTL.text = @"法人身份证";
      legalCardTL.numberOfLines = 0;
      legalCardTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      legalCardTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:legalCardTL];
      
      CGFloat legalCardW = (BCWidth - 32 - 8)/2;
      NSArray *legalCardArr = @[[[itemDic objectNilForKey:@"id_number_front_url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]],[[itemDic objectNilForKey:@"id_number_back_url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
      for (int j = 0; j < legalCardArr.count; j ++) {
        
        UIButton *landImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(16 + (legalCardW + 8) * j,legalCardTL.bottom + 8, legalCardW, 112)];
        landImageBtn.layer.cornerRadius = 4;
        landImageBtn.clipsToBounds = YES;
        landImageBtn.backgroundColor = COLOR(244, 245, 247);
        UIImageView *landIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0 ,legalCardW,  112)];
        [landIM sd_setImageWithURL:[NSURL URLWithString:[legalCardArr objectAtIndexCheck:j]] placeholderImage:[UIImage imageNamed:@"icon_pla"]];
        [landImageBtn addSubview:landIM];
        [bottomV addSubview:landImageBtn];
        [landImageBtn addtargetBlock:^(UIButton *button) {
          if ([legalCardArr isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(legalCardArr)) {
            [ZKPhotoBrowser showWithImageUrls:legalCardArr currentPhotoIndex:j sourceSuperView:button];
          }
        }];
      }
      
      //      当前租户与房东的租房合同
      UILabel *rentRoomTL = [[UILabel alloc] initWithFrame:CGRectMake(16,legalCardTL.bottom + 134, RealSize(200), 20)];
      rentRoomTL.text = @"当前租户与房东的租房合同";
      rentRoomTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rentRoomTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:rentRoomTL];
      
      NSArray *rentRoomArr = [itemDic objectForKeyNil:@"rental_contract_files"];
      if (![rentRoomArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(rentRoomArr)) {//没有产权证附件
        
        UILabel *leaseVL = [[UILabel alloc] initWithFrame:CGRectMake(rentRoomTL.right + 6,legalCardTL.bottom + 134, BCWidth - rentRoomTL.right - 22, 20)];
        leaseVL.text = @"-";
        leaseVL.font = [UIFont systemFontOfSize:MutilFont(15)];
        leaseVL.textColor = ACOLOR(31, 33, 38,1);
        leaseVL.textAlignment = NSTextAlignmentRight;
        [bottomV addSubview:leaseVL];
        
      } else {//有附件
        
        NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
        
        for (int j = 0; j <rentRoomArr.count; j ++) {
          
          NSDictionary *deedDic = [rentRoomArr objectAtIndexCheck:j];
          UIButton *leaseImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(16, rentRoomTL.bottom + 8 + (44 + 10) * j, BCWidth - 32, 44)];
          leaseImageBtn.layer.cornerRadius = 4;
          leaseImageBtn.clipsToBounds = YES;
          leaseImageBtn.backgroundColor = COLOR(244, 245, 247);
          [bottomV addSubview:leaseImageBtn];
          
          UIImageView *leaseIM = [[UIImageView alloc] initWithFrame:CGRectMake(16, 10 ,24, 24)];
          [leaseImageBtn addSubview:leaseIM];
          
          UILabel *leaseL = [[UILabel alloc] initWithFrame:CGRectMake(leaseIM.right + 10,0, BCWidth - 80, 44)];
          leaseL.text = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"name"]];
          leaseL.font = [UIFont systemFontOfSize:MutilFont(15)];
          leaseL.textColor = ACOLOR(31, 33, 38,1);
          leaseL.lineBreakMode = NSLineBreakByTruncatingMiddle;
          [leaseImageBtn addSubview:leaseL];
          
          if ([temArr containsObject:[deedDic objectNilForKey:@"suffix_type"]]) {//如果是图片
            
            leaseIM.image = [UIImage imageNamed:@"file_img"];
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
              [ZKPhotoBrowser showWithImageUrls:@[[[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] currentPhotoIndex:0 sourceSuperView:button];
            }];
          } else {//是其他文本 pdf  word
            
            NSString *fileStr = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"suffix_type"] ];
            if ([fileStr containsString:@"pdf"]) {
              leaseIM.image = [UIImage imageNamed:@"file_pdf"];
            } else  if ([fileStr containsString:@"doc"] || [fileStr containsString:@"docx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_word"];
            } else  if ([fileStr containsString:@"xls"] || [fileStr containsString:@"xls"]) {
              leaseIM.image = [UIImage imageNamed:@"file_excel"];
            }else  if ([fileStr containsString:@"ppt"] || [fileStr containsString:@"pptx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_ppt"];
            }else {
              leaseIM.image = [UIImage imageNamed:@"file_no"];
            }
            
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
             
              NSString *url = [[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
              [weakSelf openSFDoc:url];
            }];
            
            
          }
          
          
        }
        
      }
      
      //      租房合同签约与收款账户户名同一人
      CGFloat rentRoomH = 0;
      if (![rentRoomArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(rentRoomArr)) {//没有附件
        rentRoomH  = rentRoomTL.bottom  + 14;
      } else {
        rentRoomH = rentRoomTL.bottom + 8 + (44 + 10) * rentRoomArr.count - 10 + 14;
      }
      
      UILabel *samePeopleTL = [[UILabel alloc] initWithFrame:CGRectMake(16,rentRoomH, 120, RealSize(60))];
      samePeopleTL.text = @"租房合同签约\n与收款账户户\n名同一人";
      samePeopleTL.numberOfLines = 0;
      samePeopleTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      samePeopleTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:samePeopleTL];
      
      UILabel *samePeopleVL = [[UILabel alloc] initWithFrame:CGRectMake(samePeopleTL.right + 6,rentRoomH + 2,BCWidth - samePeopleTL.right - 22, 20)];
      if ([[itemDic objectForKeyNil:@"identical_user"] isKindOfClass:[NSString class]] && [[itemDic objectForKeyNil:@"identical_user"] isEqualToString:@""]) {
        samePeopleVL.text = @"-";
      } else {
        samePeopleVL.text =  [[itemDic objectForKeyNil:@"identical_user"] isEqual:@(1)] ? @"是":@"否";
      }
      samePeopleVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      samePeopleVL.textColor = COLOR(31, 33, 38);
      samePeopleVL.textAlignment = NSTextAlignmentRight;
      [bottomV addSubview:samePeopleVL];
      
      //      收款户名与合同签署人关系证明
      UILabel *relationTL = [[UILabel alloc] initWithFrame:CGRectMake(16,samePeopleTL.bottom + 14, 120, RealSize(60))];
      relationTL.text = @"收款户名与合\n同签署人关系\n证明";
      relationTL.numberOfLines = 0;
      relationTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      relationTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:relationTL];
      
      NSString *relationString = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"relationship_proof"]];
      UILabel *relationVL = [[UILabel alloc] initWithFrame:CGRectMake(relationTL.right + 6,samePeopleTL.bottom + 16,BCWidth - relationTL.right - 22, 20)];
      if ([relationString isEqualToString:@""]) {
        relationVL.text = @"-";
      } else {
        relationVL.text = [relationString isEqualToString:@"HOUSEHOLD_REGISTER"] ? @"户口本": [relationString isEqualToString:@"MARRIAGE_CERTIFICATE"] ? @"夫妻结婚证":@"合伙关系证明";
      }
      
      relationVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      relationVL.textColor = COLOR(31, 33, 38);
      relationVL.textAlignment = NSTextAlignmentRight;
      [bottomV addSubview:relationVL];
      
      //      关系证明
      UILabel *relationProofTL = [[UILabel alloc] initWithFrame:CGRectMake(16,relationTL.bottom + 14, 120, 20)];
      relationProofTL.text = @"关系证明";
      relationProofTL.numberOfLines = 0;
      relationProofTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      relationProofTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:relationProofTL];
      
      NSArray *relationProofArr = [itemDic objectForKeyNil:@"relationship_proof_files"];
      if (![relationProofArr  isKindOfClass:[NSArray class]] || BCArrayIsEmpty(relationProofArr )) {//没有附件
        
        UILabel *leaseVL = [[UILabel alloc] initWithFrame:CGRectMake(relationProofTL.right + 6,relationTL.bottom + 14, BCWidth - relationProofTL.right - 22, 20)];
        leaseVL.text = @"-";
        leaseVL.font = [UIFont systemFontOfSize:MutilFont(15)];
        leaseVL.textColor = ACOLOR(31, 33, 38,1);
        leaseVL.textAlignment = NSTextAlignmentRight;
        [bottomV addSubview:leaseVL];
        
      } else {//有附件
        NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
        
        for (int j = 0; j <relationProofArr.count; j ++) {
          
          NSDictionary *deedDic = [relationProofArr objectAtIndexCheck:j];
          UIButton *leaseImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(16, relationProofTL.bottom + 8 + (44 + 10) * j, BCWidth - 32, 44)];
          leaseImageBtn.layer.cornerRadius = 4;
          leaseImageBtn.clipsToBounds = YES;
          leaseImageBtn.backgroundColor = COLOR(244, 245, 247);
          [bottomV addSubview:leaseImageBtn];
          
          UIImageView *leaseIM = [[UIImageView alloc] initWithFrame:CGRectMake(16, 10 ,24, 24)];
          [leaseImageBtn addSubview:leaseIM];
          
          UILabel *leaseL = [[UILabel alloc] initWithFrame:CGRectMake(leaseIM.right + 10,0, BCWidth - 80, 44)];
          leaseL.text = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"name"]];
          leaseL.font = [UIFont systemFontOfSize:MutilFont(15)];
          leaseL.textColor = ACOLOR(31, 33, 38,1);
          leaseL.lineBreakMode = NSLineBreakByTruncatingMiddle;
          [leaseImageBtn addSubview:leaseL];
          
          if ([temArr containsObject:[deedDic objectNilForKey:@"suffix_type"]]) {//如果是图片
            
            leaseIM.image = [UIImage imageNamed:@"file_img"];
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
              [ZKPhotoBrowser showWithImageUrls:@[[[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] currentPhotoIndex:0 sourceSuperView:button];
            }];
          } else {//是其他文本 pdf  word
            
            NSString *fileStr = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"suffix_type"] ];
            if ([fileStr containsString:@"pdf"]) {
              leaseIM.image = [UIImage imageNamed:@"file_pdf"];
            } else  if ([fileStr containsString:@"doc"] || [fileStr containsString:@"docx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_word"];
            } else  if ([fileStr containsString:@"xls"] || [fileStr containsString:@"xls"]) {
              leaseIM.image = [UIImage imageNamed:@"file_excel"];
            }else  if ([fileStr containsString:@"ppt"] || [fileStr containsString:@"pptx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_ppt"];
            }else {
              leaseIM.image = [UIImage imageNamed:@"file_no"];
            }
            
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
             
              NSString *url = [[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
              [weakSelf openSFDoc:url];
             
            }];
            
            
          }
          
          
        }
      }
      
      //      转让费是否包含其他费用
      CGFloat otherMoneyH = 0;
      if (![relationProofArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(relationProofArr)) {//没有附件
        otherMoneyH = relationProofTL.bottom  + 14;
      } else {
        otherMoneyH = relationProofTL.bottom + 8 + (44 + 10) * relationProofArr.count - 10 + 14;
      }
      UILabel *otherMoneyTL = [[UILabel alloc] initWithFrame:CGRectMake(16,otherMoneyH, 120, RealSize(40))];
      otherMoneyTL.text = @"转让费是否包\n含其他费用";
      otherMoneyTL.numberOfLines = 0;
      otherMoneyTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      otherMoneyTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:otherMoneyTL];
      
      UILabel *otherMoneyVL = [[UILabel alloc] initWithFrame:CGRectMake(otherMoneyTL.right + 6,otherMoneyH +2,BCWidth - otherMoneyTL.right - 22, 20)];
      if ([[itemDic objectForKeyNil:@"additional"] isKindOfClass:[NSString class]] && [[itemDic objectForKeyNil:@"additional"] isEqualToString:@""]) {
        otherMoneyVL.text = @"-";
      } else {
        otherMoneyVL.text =  [[itemDic objectForKeyNil:@"additional"] isEqual:@(1)] ? @"是":@"否";
      }
      
      otherMoneyVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      otherMoneyVL.textColor = COLOR(31, 33, 38);
      otherMoneyVL.textAlignment = NSTextAlignmentRight;
      [bottomV addSubview:otherMoneyVL];
      
      //      其他备注
      UILabel *otherMemoTL = [[UILabel alloc] initWithFrame:CGRectMake(16,otherMoneyTL.bottom + 14, 120, 20)];
      otherMemoTL.text = @"备注";
      otherMemoTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      otherMemoTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:otherMemoTL];
      
      UILabel *otherMemoVL = [[UILabel alloc] initWithFrame:CGRectMake(otherMemoTL.right + 6,otherMoneyTL.bottom + 14,BCWidth - otherMemoTL.right - 22, 20)];
      otherMemoVL.text =  [NSString stringWithFormat:@"%@",BCStringIsEmpty([itemDic objectForKeyNil:@"additional_memo"])? @"-" : [itemDic objectForKeyNil:@"additional_memo"]];
      otherMemoVL.font = [UIFont systemFontOfSize:MutilFont(15)];
      otherMemoVL.textColor = COLOR(31, 33, 38);
      otherMemoVL.textAlignment = NSTextAlignmentRight;
      [bottomV addSubview:otherMemoVL];
      
      //      转让协议
      UILabel *agreementTL = [[UILabel alloc] initWithFrame:CGRectMake(16,otherMemoVL.bottom + 14, 120, 20)];
      agreementTL.text = @"转让协议";
      agreementTL.font = [UIFont systemFontOfSize:MutilFont(15)];
      agreementTL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:agreementTL];
      
      NSArray *agreementArr = [itemDic objectForKeyNil:@"agreement_files"];
      if (![agreementArr  isKindOfClass:[NSArray class]] || BCArrayIsEmpty(agreementArr)) {//没有附件
        
        UILabel *agreementVL = [[UILabel alloc] initWithFrame:CGRectMake(agreementTL.right + 6,otherMemoVL.bottom + 14, BCWidth - agreementTL.right - 22, 20)];
        agreementVL.text = @"-";
        agreementVL.font = [UIFont systemFontOfSize:MutilFont(15)];
        agreementVL.textColor = ACOLOR(31, 33, 38,1);
        agreementVL.textAlignment = NSTextAlignmentRight;
        [bottomV addSubview:agreementVL];
        
      } else {//有附件
        NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
        
        for (int j = 0; j <agreementArr.count; j ++) {
          
          NSDictionary *deedDic = [agreementArr objectAtIndexCheck:j];
          UIButton *leaseImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(16, agreementTL.bottom + 8 + (44 + 10) * j, BCWidth - 32, 44)];
          leaseImageBtn.layer.cornerRadius = 4;
          leaseImageBtn.clipsToBounds = YES;
          leaseImageBtn.backgroundColor = COLOR(244, 245, 247);
          [bottomV addSubview:leaseImageBtn];
          
          UIImageView *leaseIM = [[UIImageView alloc] initWithFrame:CGRectMake(16, 10 ,24, 24)];
          [leaseImageBtn addSubview:leaseIM];
          
          UILabel *leaseL = [[UILabel alloc] initWithFrame:CGRectMake(leaseIM.right + 10,0, BCWidth - 80, 44)];
          leaseL.text = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"name"]];
          leaseL.font = [UIFont systemFontOfSize:MutilFont(15)];
          leaseL.textColor = ACOLOR(31, 33, 38,1);
          leaseL.lineBreakMode = NSLineBreakByTruncatingMiddle;
          [leaseImageBtn addSubview:leaseL];
          
          if ([temArr containsObject:[deedDic objectNilForKey:@"suffix_type"]]) {//如果是图片
            
            leaseIM.image = [UIImage imageNamed:@"file_img"];
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
              [ZKPhotoBrowser showWithImageUrls:@[[[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] currentPhotoIndex:0 sourceSuperView:button];
            }];
          } else {//是其他文本 pdf  word
            
            NSString *fileStr = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"suffix_type"] ];
            if ([fileStr containsString:@"pdf"]) {
              leaseIM.image = [UIImage imageNamed:@"file_pdf"];
            } else  if ([fileStr containsString:@"doc"] || [fileStr containsString:@"docx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_word"];
            } else  if ([fileStr containsString:@"xls"] || [fileStr containsString:@"xls"]) {
              leaseIM.image = [UIImage imageNamed:@"file_excel"];
            }else  if ([fileStr containsString:@"ppt"] || [fileStr containsString:@"pptx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_ppt"];
            }else {
              leaseIM.image = [UIImage imageNamed:@"file_no"];
            }
            
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
             
              NSString *url = [[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
              [weakSelf openSFDoc:url];
              
            }];
            
            
          }
          
          
        }
      }
      
      //      分割线
      CGFloat bottomH = 0;
      if (![agreementArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(agreementArr)) {//没有附件
        bottomH  = agreementTL.bottom  + 14;
      } else {
        bottomH  = agreementTL.bottom + 8 + (44 + 10) * agreementArr .count - 10 + 14;
      }
      
      bottomV.height = bottomH;
      itemH += bottomH + 12;
      
    }
    
    //    全部加载完成
    self.landlordScrollView.contentSize = CGSizeMake(0, itemH + 40);
    
  }
  
}

#pragma mark 所有网络请求回来加载tab下的点位动态界面
- (void)loadPointStateRecord{
  
  __weak typeof(self)weakSelf = self;
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,12,BCWidth,114)];
  contentView.backgroundColor = [UIColor whiteColor];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,100,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = @"点位动态";
  [contentView addSubview:nameLabel];
  
  UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
  switchMap.frame = CGRectMake(BCWidth - 67 - 15, 0, 82, 50);
  [switchMap addTarget:self action:@selector(clickToFollowUp) forControlEvents:UIControlEventTouchUpInside];
//  [contentView addSubview:switchMap];
  
  
  UIImageView *leftIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 16, 18, 18)];
  leftIM.image = [UIImage imageNamed:@"point_add"];
  [switchMap addSubview:leftIM];
  
  UILabel *switchlabel = [[UILabel alloc] init];
  switchlabel.frame = CGRectMake(20,0,47,50);
  switchlabel.text = @"写跟进";
  switchlabel.textColor = COLOR(26, 106, 255);
  switchlabel.font = [UIFont systemFontOfSize:15];
  [switchMap addSubview:switchlabel];
  [self.pointStateScrollView addSubview:contentView];
  
  //    分割线
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,49.5, BCWidth - 16, 0.5)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [contentView addSubview:lineV];
  
  
  
  //    4个操作按钮
  NSArray *opeationArr = @[@"全部",@"跟进",@"变更",@"操作",@"转移"];
  UISegmentedControl *segment = [[UISegmentedControl alloc]initWithItems:opeationArr];
  segment.selectedSegmentIndex = 0;
  segment.frame = CGRectMake(16, lineV.bottom + 14, BCWidth - 32, 36);
  [segment addTarget:self action:@selector(changeSegment:) forControlEvents:UIControlEventValueChanged];
  [contentView addSubview:segment];
  [segment setTitleTextAttributes:@{NSFontAttributeName :[UIFont systemFontOfSize:MutilFont(14)],NSForegroundColorAttributeName:COLOR(29, 33, 41)}forState:UIControlStateNormal];
  [segment setTitleTextAttributes:@{NSFontAttributeName :[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium],NSForegroundColorAttributeName:COLOR(26, 106, 255)}forState:UIControlStateSelected];

  
  _selectPointStateV = [[UIView alloc] initWithFrame:CGRectMake(0, contentView.bottom, BCWidth, BCHeight - self.heightTop - 44 - 70 - 12 - 114)];
  [self.pointStateScrollView addSubview:_selectPointStateV];
  [self reloadSelectSegment:0];
}

#pragma mark 去写跟进
- (void)clickToFollowUp{
  
  MAWriteFollowUpViewController *toVC = [[MAWriteFollowUpViewController alloc] init];
  toVC.pointId = self.pointId;
  if (self.isTakeLook) {
    BOOL submit = NO;
    for (NSDictionary *dic in self.shareArr) {
      NSArray *arr = [dic objectForKeyNil:@"look_files"];
      if (BCArrayIsEmpty(arr)) {
        continue;
      }
      if (arr.count > 0) {
        submit = YES;
        break;
      }
    }
    toVC.isSubmit = submit;
  } else {
    toVC.isSubmit = YES;
  }
  NSString *stateStr = [self changeState:[self.detailDic objectNilForKey:@"state"]];
  BOOL isFinish = [[self.detailDic objectNilForKey:@"follow_state"] isEqualToString:@"FINISH"] ? YES : NO;
  toVC.followState = stateStr;
  toVC.state = [self.detailDic objectNilForKey:@"follow_state"];
  toVC.isFinish = isFinish;
  NSString *userName = [[NSUserDefaults standardUserDefaults] objectForKey:@"MAP_USERNAME"];
  toVC.isFollow = [userName isEqualToString:[self.detailDic objectForKeyNil:@"follow_by"]];
  [self.navigationController pushViewController:toVC animated:YES];
}
// 点击分段视图
- (void)changeSegment:(UISegmentedControl *)segment {
  NSInteger Index = segment.selectedSegmentIndex;
  self.pointStateScrollView.contentSize = CGSizeMake(0, 0);
  [self reloadSelectSegment:Index];
}

- (void)reloadSelectSegment:(NSInteger)segeIndex{
  
  __weak typeof(self)weakSelf = self;
  NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
  NSArray *arr = @[];
  
  switch (segeIndex) {
    case 0:
      arr = _allPointArr;
      break;
    case 1:
      arr = _followPointArr;
      break;
      
    case 2:
      arr = _changePointArr;
      break;
      
    case 3:
      arr = _operatePointArr;
      break;
    case 4:
      arr = _transferPointArr;
      break;
      
    default:
      break;
  }
  
  [self.selectPointStateV.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  
  if (![arr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(arr)) {//空视图
    
    UIImageView *emptyView = [[UIImageView alloc] initWithFrame:CGRectMake((BCWidth - 160)/2, 100, 160, 180)];
    emptyView.image = [UIImage imageNamed:@"tab_nodata"];
    [self.selectPointStateV addSubview:emptyView];
    
    UILabel *emptyL = [[UILabel alloc] initWithFrame:CGRectMake(0, emptyView.bottom - 40, BCWidth, 20)];
    emptyL.textColor = ACOLOR(30, 33, 38, 0.45);
    emptyL.font = [UIFont systemFontOfSize:MutilFont(14)];
    emptyL.text = @"暂无点位动态";
    emptyL.textAlignment = NSTextAlignmentCenter;
    [self.selectPointStateV addSubview:emptyL];
    
    self.selectPointStateV.height =  BCHeight - self.heightTop - 44 - 70 - 12 - 114;
    self.pointStateScrollView.contentSize = CGSizeMake(0, 0);
    
  } else {//有点位动态
    
    NSArray *colorArr = @[@[(__bridge id)COLOR(158, 171, 255).CGColor,(__bridge id)COLOR(45, 78, 238).CGColor],
                          @[(__bridge id)COLOR(27, 236, 212).CGColor,(__bridge id)COLOR(0, 196, 170).CGColor],
                          @[(__bridge id)COLOR(178, 132, 255).CGColor,(__bridge id)COLOR(127, 60, 237).CGColor],
                          @[(__bridge id)COLOR(158, 225, 255).CGColor,(__bridge id)COLOR(36, 78, 241).CGColor],
                          @[(__bridge id)COLOR(255, 174, 132).CGColor,(__bridge id)COLOR(237, 60, 60).CGColor],
                          @[(__bridge id)COLOR(255, 183, 100).CGColor,(__bridge id)COLOR(240, 130, 3).CGColor],
    ];
    
    CGFloat itemH = 0;
    CGFloat changeH = 0;
    CGFloat operationH = 0;
    for (int i = 0; i < arr.count; i ++) {
     
      if (segeIndex == 4) {//转移记录
        
        NSDictionary *itemDic = [arr objectAtIndexCheck:i];
        UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth, 126)];
        bottomV.backgroundColor = [UIColor whiteColor];
        [self.selectPointStateV addSubview:bottomV];
        
        NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
        [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
        [dateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
        NSString *creatTimeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_time"]];
        NSDate *creatDate = [dateFormatter dateFromString:creatTimeStr];
        
        NSDateFormatter *newdateFormatter = [[NSDateFormatter alloc] init];
        [newdateFormatter setDateFormat:@"yyyy-MM-dd HH:mm"];
        [newdateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
        NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
        NSString *followResult = [NSString stringWithFormat:@"%@ · 转移 · %@",nameStr,[newdateFormatter stringFromDate:creatDate]];
        NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
        [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
        
        NSInteger randomNum = arc4random_uniform(6);
        UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
        headImageV.layer.cornerRadius = 12;
        headImageV.clipsToBounds = YES;
        [bottomV addSubview:headImageV];
        
        CAGradientLayer *gl = [CAGradientLayer layer];
        gl.frame = headImageV.bounds;
        gl.startPoint = CGPointMake(0.0, 0);
        gl.endPoint = CGPointMake(1, 1);
        gl.colors = [colorArr objectAtIndexCheck:randomNum];
        gl.locations = @[@(0), @(1.0f)];
        [headImageV.layer addSublayer:gl];
        
        UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        headL.textColor = [UIColor whiteColor];
        headL.textAlignment = NSTextAlignmentCenter;
        headL.font = [UIFont systemFontOfSize:10];
        headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
        [headImageV addSubview:headL];
        
        UILabel *switchlabel = [[UILabel alloc] init];
        switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - headImageV.right - 22,20);
        switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
        switchlabel.font = [UIFont systemFontOfSize:13];
        [bottomV addSubview:switchlabel];
        switchlabel.attributedText = string;
        
        //        变更字段
        NSString *beforeStr = @"跟进人";
        NSString *beforeResult = [NSString stringWithFormat:@"变更字段：%@",beforeStr];
        NSMutableAttributedString *beforeString = [[NSMutableAttributedString alloc] initWithString:beforeResult];
        [beforeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, beforeStr.length)];
        
        UILabel *beforelabel = [[UILabel alloc] init];
        beforelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 6,BCWidth - 80,20);
        beforelabel.textColor = ACOLOR(30, 33, 38, 0.45);
        beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:beforelabel];
        beforelabel.attributedText = beforeString;
        
        //        原负责人
        NSString *beforePeopleStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"before_change"]];
        NSString *beforePeopleResult = [NSString stringWithFormat:@"原跟进人：%@",BCStringIsEmpty(beforePeopleStr) ? @"-" : beforePeopleStr];
        NSMutableAttributedString *resultString = [[NSMutableAttributedString alloc] initWithString:beforePeopleResult];
        [resultString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5,BCStringIsEmpty(beforePeopleStr) ? 1 : beforePeopleStr.length)];
        
        UILabel *resultlabel = [[UILabel alloc] init];
        resultlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 80,20);
        resultlabel.textColor = ACOLOR(30, 33, 38, 0.45);
        resultlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:resultlabel];
        resultlabel.attributedText = resultString;
        
//      新负责人
        NSString *timeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"after_change"]];
        NSString *timeResult = [NSString stringWithFormat:@"新跟进人：%@",timeStr];
        NSMutableAttributedString *timeString = [[NSMutableAttributedString alloc] initWithString:timeResult];
        [timeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, timeStr.length)];
        
        UILabel *timelabel = [[UILabel alloc] init];
        timelabel.frame = CGRectMake(headImageV.right + 6,resultlabel.bottom + 4,BCWidth - 80,20);
        timelabel.textColor = ACOLOR(30, 33, 38, 0.45);
        timelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:timelabel];
        timelabel.attributedText = timeString;
        
//        转交原因
        NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"change_reason"]];
        NSString *memoResult = [NSString stringWithFormat:@"转交原因：%@",!BCStringIsEmpty(memoStr) ? memoStr : @"-"];
        NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:memoResult];
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        [paragraphStyle setLineSpacing:4];//调整行间距
        paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
        [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, memoResult.length)];
        [opereateResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 5)];
        
        NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:paragraphStyle};
        CGFloat height = [memoResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        height = ceil(height);
        height = height < 20 ? 20 : height;
        
        UILabel *memolabel = [[UILabel alloc] init];
        memolabel.frame = CGRectMake(headImageV.right + 6 , timelabel.bottom + 4,BCWidth - 62,height);
        memolabel.textColor = ACOLOR(31, 33, 38, 1);
        memolabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        memolabel.numberOfLines = 0;
        [bottomV addSubview:memolabel];
        memolabel.attributedText = opereateResultStr;
        
        //      分割线
        if (i < arr.count - 1) {
          UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, memolabel.bottom + 14, BCWidth - 16, 0.5)];
          lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
          [bottomV addSubview:lineV];
        }
        
        bottomV.height = memolabel.bottom + 14 + 14;
        itemH += memolabel.bottom + 14 + 14;
        if (i == arr.count - 1) {
          bottomV.height = memolabel.bottom + 14 ;
          self.selectPointStateV.height = itemH - 14;
          if ( self.selectPointStateV.bottom  + 114 > self.pointStateScrollView.height) {
            self.pointStateScrollView.contentSize = CGSizeMake(0, self.selectPointStateV.bottom + 40);
          }
        }
        
      } else if (segeIndex == 3) {//操作记录
        
        NSDictionary *itemDic = [arr objectAtIndexCheck:i];
        UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, operationH, BCWidth, 102)];
        bottomV.backgroundColor = [UIColor whiteColor];
        [self.selectPointStateV addSubview:bottomV];
        
        NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
        NSString *followResult = [NSString stringWithFormat:@"%@ · 操作 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
        NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
        [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
        
        NSInteger randomNum = arc4random_uniform(6);
        UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
        headImageV.layer.cornerRadius = 12;
        headImageV.clipsToBounds = YES;
        [bottomV addSubview:headImageV];
        
        CAGradientLayer *gl = [CAGradientLayer layer];
        gl.frame = headImageV.bounds;
        gl.startPoint = CGPointMake(0.0, 0);
        gl.endPoint = CGPointMake(1, 1);
        gl.colors = [colorArr objectAtIndexCheck:randomNum];
        gl.locations = @[@(0), @(1.0f)];
        [headImageV.layer addSublayer:gl];
        
        UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        headL.textColor = [UIColor whiteColor];
        headL.textAlignment = NSTextAlignmentCenter;
        headL.font = [UIFont systemFontOfSize:10];
        headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
        [headImageV addSubview:headL];
        
        UILabel *switchlabel = [[UILabel alloc] init];
        switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
        switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
        switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
        [bottomV addSubview:switchlabel];
        switchlabel.attributedText = string;
        
        NSString *operateStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"model_state"]];
        NSString *operateResult = [NSString stringWithFormat:@"操作：%@",operateStr];
        NSMutableAttributedString *operateString = [[NSMutableAttributedString alloc] initWithString:operateResult];
        [operateString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(3, operateStr.length)];
        
        UILabel *operatelabel = [[UILabel alloc] init];
        operatelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 8,BCWidth - 80,20);
        operatelabel.textColor = ACOLOR(30, 33, 38, 0.45);
        operatelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:operatelabel];
        operatelabel.attributedText = operateString;
        
        NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
        NSString *memoResult = [NSString stringWithFormat:@"审批意见：%@",!BCStringIsEmpty(memoStr) ? memoStr : @"-"];
        NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:memoResult];
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        [paragraphStyle setLineSpacing:4];//调整行间距
        paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
        [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, memoResult.length)];
        [opereateResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 5)];
        
        NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:paragraphStyle};
        CGFloat height = [memoResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        height = ceil(height);
        height = height < 20 ? 20 : height;
        
        UILabel *memolabel = [[UILabel alloc] init];
        memolabel.frame = CGRectMake(headImageV.right + 6 , operatelabel.bottom + 4,BCWidth - 62,height);
        memolabel.textColor = ACOLOR(31, 33, 38, 1);
        memolabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        memolabel.numberOfLines = 0;
        [bottomV addSubview:memolabel];
        memolabel.attributedText = opereateResultStr;
        
        //      分割线
        if (i < arr.count - 1) {
          UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, memolabel.bottom + 14, BCWidth - 16, 0.5)];
          lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
          [bottomV addSubview:lineV];
        }
        
        bottomV.height = memolabel.bottom + 14 + 14;
        operationH += memolabel.bottom + 14 + 14;
        if (i == arr.count - 1) {
          bottomV.height = memolabel.bottom + 14 ;
          self.selectPointStateV.height = operationH - 14;
          if ( self.selectPointStateV.bottom  + 114 > self.pointStateScrollView.height) {
            self.pointStateScrollView.contentSize = CGSizeMake(0, self.selectPointStateV.bottom + 40);
          }
        }
        
      } else  if (segeIndex == 2) {//变更记录
        
        NSDictionary *itemDic = [arr objectAtIndexCheck:i];
        
        UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, changeH, BCWidth, 126)];
        bottomV.backgroundColor = [UIColor whiteColor];
        [self.selectPointStateV addSubview:bottomV];
        
        
        NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
        NSString *followResult = [NSString stringWithFormat:@"%@ · 变更 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
        NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
        [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
        
        NSInteger randomNum = arc4random_uniform(6);
        UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
        headImageV.layer.cornerRadius = 12;
        headImageV.clipsToBounds = YES;
        [bottomV addSubview:headImageV];
        
        CAGradientLayer *gl = [CAGradientLayer layer];
        gl.frame = headImageV.bounds;
        gl.startPoint = CGPointMake(0.0, 0);
        gl.endPoint = CGPointMake(1, 1);
        gl.colors = [colorArr objectAtIndexCheck:randomNum];
        gl.locations = @[@(0), @(1.0f)];
        [headImageV.layer addSublayer:gl];
        
        UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        headL.textColor = [UIColor whiteColor];
        headL.textAlignment = NSTextAlignmentCenter;
        headL.font = [UIFont systemFontOfSize:10];
        headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
        [headImageV addSubview:headL];
        
        UILabel *switchlabel = [[UILabel alloc] init];
        switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
        switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
        switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
        [bottomV addSubview:switchlabel];
        switchlabel.attributedText = string;
        
        
        NSString *operateStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"field_name"]];
        NSString *operateResult = [NSString stringWithFormat:@"变更字段：%@",operateStr];
        NSMutableAttributedString *operateString = [[NSMutableAttributedString alloc] initWithString:operateResult];
        [operateString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, operateStr.length)];
        
        UILabel *operatelabel = [[UILabel alloc] init];
        operatelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 8,BCWidth - 80,20);
        operatelabel.textColor = ACOLOR(30, 33, 38, 0.45);
        operatelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:operatelabel];
        operatelabel.attributedText = operateString;
        
        //       原值
        NSString *beforeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"before_change"]];
        if ([beforeStr containsString:@"https"] || [beforeStr containsString:@"http"]  || BCStringIsEmpty(beforeStr)) {
          beforeStr = @"-";
        }
        
        NSString *beforeResult = [NSString stringWithFormat:@"原值：%@", beforeStr];
        NSMutableAttributedString *beforeResultStr = [[NSMutableAttributedString alloc] initWithString:beforeResult];
        NSMutableParagraphStyle *beforeParagraphStyle = [[NSMutableParagraphStyle alloc] init];
        [beforeParagraphStyle setLineSpacing:4];//调整行间距
        beforeParagraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
        [beforeResultStr addAttribute:NSParagraphStyleAttributeName value:beforeParagraphStyle range:NSMakeRange(0, beforeResult.length)];
        [beforeResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 3)];
        
        NSDictionary *beforeAttributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:beforeParagraphStyle};
        CGFloat beforeHeight = [beforeResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:beforeAttributes context:nil].size.height;
        beforeHeight= ceil(beforeHeight);
        beforeHeight = beforeHeight < 20 ? 20 : beforeHeight;
        
        UILabel *beforelabel = [[UILabel alloc] init];
        beforelabel.frame = CGRectMake(headImageV.right + 6 , operatelabel.bottom + 4,BCWidth - 62,beforeHeight);
        beforelabel.textColor = COLOR(31, 33, 38);
        beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        beforelabel.numberOfLines = 0;
        [bottomV addSubview:beforelabel];
        beforelabel.attributedText = beforeResultStr;
        
        //        新值
        NSString *afterStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"after_change"]];
        if ([afterStr containsString:@"https"] || [afterStr containsString:@"http"]  || BCStringIsEmpty(beforeStr)) {
          afterStr = @"-";
        }
        
        NSString *afterResult = [NSString stringWithFormat:@"新值：%@", afterStr];
        NSMutableAttributedString *afterResultStr = [[NSMutableAttributedString alloc] initWithString:afterResult];
        NSMutableParagraphStyle *afterParagraphStyle = [[NSMutableParagraphStyle alloc] init];
        [afterParagraphStyle setLineSpacing:4];//调整行间距
        afterParagraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
        [afterResultStr addAttribute:NSParagraphStyleAttributeName value:afterParagraphStyle range:NSMakeRange(0, afterResult.length)];
        [afterResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 3)];
        
        NSDictionary *afterAttributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:afterParagraphStyle};
        CGFloat afterHeight = [afterResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:afterAttributes context:nil].size.height;
        afterHeight = ceil(afterHeight);
        afterHeight = afterHeight < 20 ? 20 : afterHeight;
        
        UILabel *afterlabel = [[UILabel alloc] init];
        afterlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 62,afterHeight);
        afterlabel.textColor = COLOR(31, 33, 38);
        afterlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        afterlabel.numberOfLines = 0;
        [bottomV addSubview:afterlabel];
        afterlabel.attributedText = afterResultStr;
        
        
        //      分割线
        if (i < arr.count - 1 ) {
          UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, afterlabel.bottom + 14, BCWidth - 16, 0.5)];
          lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
          [bottomV addSubview:lineV];
        }
        bottomV.height = afterlabel.bottom + 14 + 14;
        changeH += afterlabel.bottom + 14 + 14;
        if (i == arr.count - 1) {
          bottomV.height = afterlabel.bottom + 14 ;
          self.selectPointStateV.height = changeH - 14;
          if ( self.selectPointStateV.bottom  + 114 > self.pointStateScrollView.height) {
            self.pointStateScrollView.contentSize = CGSizeMake(0, self.selectPointStateV.bottom + 40);
          }
        }
        
      } else  if (segeIndex == 1) {//跟进记录
        
        NSDictionary *itemDic = [arr objectAtIndexCheck:i];
        UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth, 126)];
        bottomV.backgroundColor = [UIColor whiteColor];
        [self.selectPointStateV addSubview:bottomV];
        
        NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
        [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
        [dateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
        NSString *creatTimeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_time"]];
        NSDate *creatDate = [dateFormatter dateFromString:creatTimeStr];
        
        NSDateFormatter *newdateFormatter = [[NSDateFormatter alloc] init];
        [newdateFormatter setDateFormat:@"yyyy-MM-dd HH:mm"];
        [newdateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
        
        
        NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
        NSString *followResult = [NSString stringWithFormat:@"%@ · 跟进 · %@",nameStr,[newdateFormatter stringFromDate:creatDate]];
        NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
        [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
        
        NSInteger randomNum = arc4random_uniform(6);
        UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
        headImageV.layer.cornerRadius = 12;
        headImageV.clipsToBounds = YES;
        [bottomV addSubview:headImageV];
        
        CAGradientLayer *gl = [CAGradientLayer layer];
        gl.frame = headImageV.bounds;
        gl.startPoint = CGPointMake(0.0, 0);
        gl.endPoint = CGPointMake(1, 1);
        gl.colors = [colorArr objectAtIndexCheck:randomNum];
        gl.locations = @[@(0), @(1.0f)];
        [headImageV.layer addSublayer:gl];
        
        UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        headL.textColor = [UIColor whiteColor];
        headL.textAlignment = NSTextAlignmentCenter;
        headL.font = [UIFont systemFontOfSize:10];
        headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
        [headImageV addSubview:headL];
        
        UILabel *switchlabel = [[UILabel alloc] init];
        switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 116,20);
        switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
        switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
        [bottomV addSubview:switchlabel];
        switchlabel.attributedText = string;
        
//        评论
        UIButton *commentBtn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - 60, switchlabel.top, 60, 20)];
        [bottomV addSubview:commentBtn];
        [commentBtn addtargetBlock:^(UIButton *button) {
          
          MAWriteReplyView *replyVV = [[MAWriteReplyView alloc] initWithName:@"评论" andPla:[NSString stringWithFormat:@"回复%@：",nameStr]];
          [replyVV showModal];
          replyVV.okBlock = ^(NSString *dataString) {
            [weakSelf replyString:[itemDic objectNilForKey:@"id"] andMemo:dataString andIndex:1];
          };
        }];
        
        UIImageView *commentIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 2, 16, 16)];
        commentIM.image = [UIImage imageNamed:@"detail_comment"];
        [commentBtn addSubview:commentIM];
        
        UILabel *commentL = [[UILabel alloc] initWithFrame:CGRectMake(commentIM.right + 4,0,30,20)];
        commentL.text = @"评论";
        commentL.font = [UIFont systemFontOfSize:MutilFont(12)];
        commentL.textColor = COLOR(134, 144, 156);
        [commentBtn addSubview:commentL];
        
        
        //        备注
        NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
        NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
        CGFloat height = [memoStr boundingRectWithSize:CGSizeMake(BCWidth - 92, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        height = ceil(height);
        UILabel *memoLabel= [[UILabel alloc] initWithFrame:CGRectMake(headImageV.right + 6, headImageV.bottom + 6, BCWidth - 92,BCStringIsEmpty(memoStr) ? 0 : height)];
        memoLabel.textColor = ACOLOR(29, 33, 41, 1);
        memoLabel.font = [UIFont systemFontOfSize:MutilFont(15)];
        memoLabel.text = memoStr;
        memoLabel.numberOfLines = 0;
        [bottomV addSubview:memoLabel];
        
        //        跟进状态
        NSString *beforeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"state"]];
        NSString *followStr = BCStringIsEmpty(beforeStr) ? @"-" : [beforeStr isEqualToString:@"FOLLOW"] ? @"跟进中":[beforeStr isEqualToString:@"FINISH"] ? @"已完成" : @"关闭";
        NSString *beforeResult = [NSString stringWithFormat:@"跟进状态：%@",followStr];
        NSMutableAttributedString *beforeString = [[NSMutableAttributedString alloc] initWithString:beforeResult];
        [beforeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, followStr.length)];
        
        UILabel *beforelabel = [[UILabel alloc] init];
        beforelabel.frame = CGRectMake(headImageV.right + 6,memoLabel.bottom + 6,BCWidth - 80,20);
        beforelabel.textColor = ACOLOR(30, 33, 38, 0.45);
        beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:beforelabel];
        beforelabel.attributedText = beforeString;
        
      
        //        跟进结果
        NSString *resultStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"result"]];
        NSString *resultResult = [NSString stringWithFormat:@"跟进结果：%@",BCStringIsEmpty(resultStr) ? @"-" : resultStr];
        NSMutableAttributedString *resultString = [[NSMutableAttributedString alloc] initWithString:resultResult];
        [resultString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5,BCStringIsEmpty(resultStr) ? 1 : resultStr.length)];
        
        UILabel *resultlabel = [[UILabel alloc] init];
        resultlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 80,20);
        resultlabel.textColor = ACOLOR(30, 33, 38, 0.45);
        resultlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:resultlabel];
        resultlabel.attributedText = resultString;
        
        //        跟进时间
        NSString *timeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"follow_time"]];
        timeStr = [timeStr substringWithRange:NSMakeRange(0, timeStr.length - 3)];
        NSString *timeResult = [NSString stringWithFormat:@"跟进时间：%@",timeStr];
        NSMutableAttributedString *timeString = [[NSMutableAttributedString alloc] initWithString:timeResult];
        [timeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, timeStr.length)];
        
        UILabel *timelabel = [[UILabel alloc] init];
        timelabel.frame = CGRectMake(headImageV.right + 6,resultlabel.bottom + 4,BCWidth - 80,20);
        timelabel.textColor = ACOLOR(30, 33, 38, 0.45);
        timelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:timelabel];
        timelabel.attributedText = timeString;
        
        //        附件信息
        NSArray *fileArr = [itemDic objectForKeyNil:@"files"];
        if ([fileArr isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(fileArr)) {
          
          
          for (int j = 0; j <fileArr.count; j ++) {
            
            NSDictionary *deedDic = [fileArr objectAtIndexCheck:j];
            UIButton *leaseImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(headImageV.right + 6, timelabel.bottom + 8 + (44 + 10) * j, BCWidth - 62, 44)];
            leaseImageBtn.layer.cornerRadius = 4;
            leaseImageBtn.clipsToBounds = YES;
            leaseImageBtn.backgroundColor = COLOR(244, 245, 247);
            [bottomV addSubview:leaseImageBtn];
            
            UIImageView *leaseIM = [[UIImageView alloc] initWithFrame:CGRectMake(16, 10 ,24, 24)];
            [leaseImageBtn addSubview:leaseIM];
            
            UILabel *leaseL = [[UILabel alloc] initWithFrame:CGRectMake(leaseIM.right + 10,0, leaseImageBtn.width - leaseIM.right - 26, 44)];
            leaseL.text = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"name"]];
            leaseL.font = [UIFont systemFontOfSize:MutilFont(14)];
            leaseL.textColor = ACOLOR(31, 33, 38,1);
            leaseL.lineBreakMode = NSLineBreakByTruncatingMiddle;
            [leaseImageBtn addSubview:leaseL];
            
            if ([temArr containsObject:[deedDic objectNilForKey:@"suffix_type"]]) {//如果是图片
              
              leaseIM.image = [UIImage imageNamed:@"file_img"];
              [leaseImageBtn addtargetBlock:^(UIButton *button) {
                [ZKPhotoBrowser showWithImageUrls:@[[[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] currentPhotoIndex:0 sourceSuperView:button];
              }];
            } else {//是其他文本 pdf  word
              
              NSString *fileStr = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"suffix_type"] ];
              if ([fileStr containsString:@"pdf"]) {
                leaseIM.image = [UIImage imageNamed:@"file_pdf"];
              } else  if ([fileStr containsString:@"doc"] || [fileStr containsString:@"docx"]) {
                leaseIM.image = [UIImage imageNamed:@"file_word"];
              } else  if ([fileStr containsString:@"xls"] || [fileStr containsString:@"xls"]) {
                leaseIM.image = [UIImage imageNamed:@"file_excel"];
              }else  if ([fileStr containsString:@"ppt"] || [fileStr containsString:@"pptx"]) {
                leaseIM.image = [UIImage imageNamed:@"file_ppt"];
              }else {
                leaseIM.image = [UIImage imageNamed:@"file_no"];
              }
              
              [leaseImageBtn addtargetBlock:^(UIButton *button) {
               
                NSString *url = [[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
                [weakSelf openSFDoc:url];
                
              }];
            }
            
          }
        }
        
//        评论明细
        CGFloat commentTop = 0;
        if (![fileArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(fileArr)) {//没有附件
          commentTop  = timelabel.bottom  + 14;
        } else {
          commentTop = timelabel.bottom + 8 + (44 + 10) * fileArr.count - 8 + 14;
        }
        
        CGFloat commentHeight = 0;
        NSArray *commentArr = [itemDic objectForKeyNil:@"details"];
        if (!BCArrayIsEmpty(commentArr)) {
         
          for (int k = 0; k <commentArr.count; k ++) {
            NSDictionary *replyDic = [commentArr objectAtIndexCheck:k];
            
            NSString *replyNameStr = [NSString stringWithFormat:@"%@",[replyDic objectNilForKey:@"create_by"]];
            NSString *replyResult = [NSString stringWithFormat:@"%@ · %@",replyNameStr,[replyDic objectNilForKey:@"create_time"]];
            NSMutableAttributedString *replystring = [[NSMutableAttributedString alloc] initWithString:replyResult];
            [replystring addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:15 weight:UIFontWeightMedium]} range:NSMakeRange(0, replyNameStr.length)];
            
            //  回复人
            NSInteger randomNum = arc4random_uniform(6);
            UIView *replyheadV = [[UIView alloc] initWithFrame:CGRectMake(headImageV.right + 6, commentTop + commentHeight, 24, 24)];
            replyheadV.layer.cornerRadius = 12;
            replyheadV.clipsToBounds = YES;
            [bottomV addSubview:replyheadV];
            
            CAGradientLayer *gl = [CAGradientLayer layer];
            gl.frame = replyheadV.bounds;
            gl.startPoint = CGPointMake(0.0, 0);
            gl.endPoint = CGPointMake(1, 1);
            gl.colors = [colorArr objectAtIndexCheck:randomNum];
            gl.locations = @[@(0), @(1.0f)];
            [replyheadV.layer addSublayer:gl];
            
            UILabel *replyL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
            replyL.textColor = [UIColor whiteColor];
            replyL.textAlignment = NSTextAlignmentCenter;
            replyL.font = [UIFont systemFontOfSize:10];
            replyL.text = replyNameStr.length > 2 ? [replyNameStr substringWithRange:NSMakeRange(replyNameStr.length - 2, 2)] : replyNameStr;
            [replyheadV addSubview:replyL];
            
            UILabel *replyTitleL = [[UILabel alloc] init];
            replyTitleL.frame = CGRectMake(replyheadV.right + 6,replyheadV.top + 1.5,BCWidth - replyheadV.right - 76,20);
            replyTitleL.textColor = ACOLOR(30, 33, 38, 0.45);
            replyTitleL.font = [UIFont systemFontOfSize:MutilFont(13)];
            [bottomV addSubview:replyTitleL];
            replyTitleL.attributedText = replystring;
            
//            评论按钮
            UIButton *replyBtn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - 60, replyTitleL.top, 60, 20)];
            [bottomV addSubview:replyBtn];
            
            UIImageView *replyIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 2, 16, 16)];
            replyIM.image = [UIImage imageNamed:@"detail_comment"];
            [replyBtn addSubview:replyIM];
            
            UILabel *replyRL = [[UILabel alloc] initWithFrame:CGRectMake(replyIM.right + 4,0,30,20)];
            replyRL.text = @"评论";
            replyRL.font = [UIFont systemFontOfSize:MutilFont(12)];
            replyRL.textColor = COLOR(134, 144, 156);
            [replyBtn addSubview:replyRL];
            [replyBtn addtargetBlock:^(UIButton *button) {
              
              MAWriteReplyView *replyVV = [[MAWriteReplyView alloc] initWithName:@"评论" andPla:[NSString stringWithFormat:@"回复%@：",replyNameStr]];
              [replyVV showModal];
              replyVV.okBlock = ^(NSString *dataString) {
                [weakSelf replyString:[replyDic objectNilForKey:@"id"] andMemo:dataString andIndex:1];
              };
            }];
            
//            回复内容
            NSString *anwserStr = [NSString stringWithFormat:@"%@",[replyDic objectNilForKey:@"memo"]];
            NSString *anwserNameStr = [NSString stringWithFormat:@"%@",[replyDic objectNilForKey:@"reply_by"]];
            NSString *anwserResult = [NSString stringWithFormat:@"回复 %@：%@",anwserNameStr,!BCStringIsEmpty(anwserStr) ? anwserStr: @"-"];
            
            NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:anwserResult];
            NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
            [paragraphStyle setLineSpacing:4];//调整行间距
            paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
            [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, anwserResult.length)];
            [opereateResultStr addAttribute:NSForegroundColorAttributeName value:COLOR(26, 106, 255) range:NSMakeRange(2, anwserNameStr.length + 1)];
            
            NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)],NSParagraphStyleAttributeName:paragraphStyle};
            CGFloat height = [anwserResult boundingRectWithSize:CGSizeMake(BCWidth - replyheadV.right - 22, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
            height = ceil(height);
            height = height < 20 ? 20 : height;
            
            UILabel *anwserL = [[UILabel alloc] init];
            anwserL.frame = CGRectMake(replyheadV.right + 6 , replyheadV.bottom + 6,BCWidth - replyheadV.right - 22,height);
            anwserL.textColor = ACOLOR(31, 33, 38, 1);
            anwserL.font = [UIFont systemFontOfSize:MutilFont(15)];
            anwserL.numberOfLines = 0;
            [bottomV addSubview:anwserL];
            anwserL.attributedText = opereateResultStr;
            
            
            commentHeight += 30 + 14 + height;
          
          }
          
        }
        
        //        计算高度
        CGFloat bottomH = commentTop + commentHeight;
        bottomV.height = bottomH + 14;
        itemH += bottomV.height ;
        //      分割线
        if (i < arr.count - 1 ) {
          UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, bottomH, BCWidth - 16, 0.5)];
          lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
          [bottomV addSubview:lineV];
        }
        
        if (i == arr.count - 1) {
          
          bottomV.height = bottomH;
          self.selectPointStateV.height = itemH - 14;
          if ( self.selectPointStateV.bottom  + 114 > self.pointStateScrollView.height) {
            self.pointStateScrollView.contentSize = CGSizeMake(0, self.selectPointStateV.bottom + 40);
          }
        }
        
      } else { //全部动态
        
        NSDictionary *pointDic = [arr objectAtIndexCheck:i];
        UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth, 102)];
        bottomV.backgroundColor = [UIColor whiteColor];
        [self.selectPointStateV addSubview:bottomV];
        
        NSString *typeStr = [pointDic objectNilForKey:@"type"];
        
        if ([typeStr isEqualToString:@"TRANSFER_RECORD"]) {
          
          NSDictionary *itemDic = [pointDic objectForKeyNil:@"data"];
          
          NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
          [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
          [dateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
          NSString *creatTimeStr = [NSString stringWithFormat:@"%@",[pointDic objectNilForKey:@"create_time"]];
          NSDate *creatDate = [dateFormatter dateFromString:creatTimeStr];
          
          NSDateFormatter *newdateFormatter = [[NSDateFormatter alloc] init];
          [newdateFormatter setDateFormat:@"yyyy-MM-dd HH:mm"];
          [newdateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
          NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
          NSString *followResult = [NSString stringWithFormat:@"%@ · 转移 · %@",nameStr,[newdateFormatter stringFromDate:creatDate]];
          NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
          [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
          
          NSInteger randomNum = arc4random_uniform(6);
          UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
          headImageV.layer.cornerRadius = 12;
          headImageV.clipsToBounds = YES;
          [bottomV addSubview:headImageV];
          
          CAGradientLayer *gl = [CAGradientLayer layer];
          gl.frame = headImageV.bounds;
          gl.startPoint = CGPointMake(0.0, 0);
          gl.endPoint = CGPointMake(1, 1);
          gl.colors = [colorArr objectAtIndexCheck:randomNum];
          gl.locations = @[@(0), @(1.0f)];
          [headImageV.layer addSublayer:gl];
          
          UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
          headL.textColor = [UIColor whiteColor];
          headL.textAlignment = NSTextAlignmentCenter;
          headL.font = [UIFont systemFontOfSize:10];
          headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
          [headImageV addSubview:headL];
          
          UILabel *switchlabel = [[UILabel alloc] init];
          switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - headImageV.right - 22,20);
          switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
          switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
          [bottomV addSubview:switchlabel];
          switchlabel.attributedText = string;
          
          //        变更字段
          NSString *beforeStr = @"跟进人";
          NSString *beforeResult = [NSString stringWithFormat:@"变更字段：%@",beforeStr];
          NSMutableAttributedString *beforeString = [[NSMutableAttributedString alloc] initWithString:beforeResult];
          [beforeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, beforeStr.length)];
          
          UILabel *beforelabel = [[UILabel alloc] init];
          beforelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 6,BCWidth - 80,20);
          beforelabel.textColor = ACOLOR(30, 33, 38, 0.45);
          beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:beforelabel];
          beforelabel.attributedText = beforeString;
          
          //        原负责人
          NSString *beforePeopleStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"before_change"]];
          NSString *beforePeopleResult = [NSString stringWithFormat:@"原跟进人：%@",BCStringIsEmpty(beforePeopleStr) ? @"-" : beforePeopleStr];
          NSMutableAttributedString *resultString = [[NSMutableAttributedString alloc] initWithString:beforePeopleResult];
          [resultString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5,BCStringIsEmpty(beforePeopleStr) ? 1 : beforePeopleStr.length)];
          
          UILabel *resultlabel = [[UILabel alloc] init];
          resultlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 80,20);
          resultlabel.textColor = ACOLOR(30, 33, 38, 0.45);
          resultlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:resultlabel];
          resultlabel.attributedText = resultString;
          
  //      新负责人
          NSString *timeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"after_change"]];
          NSString *timeResult = [NSString stringWithFormat:@"新跟进人：%@",timeStr];
          NSMutableAttributedString *timeString = [[NSMutableAttributedString alloc] initWithString:timeResult];
          [timeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, timeStr.length)];
          
          UILabel *timelabel = [[UILabel alloc] init];
          timelabel.frame = CGRectMake(headImageV.right + 6,resultlabel.bottom + 4,BCWidth - 80,20);
          timelabel.textColor = ACOLOR(30, 33, 38, 0.45);
          timelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:timelabel];
          timelabel.attributedText = timeString;
          
  //        转交原因
          NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"change_reason"]];
          NSString *memoResult = [NSString stringWithFormat:@"转交原因：%@",!BCStringIsEmpty(memoStr) ? memoStr : @"-"];
          NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:memoResult];
          NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
          [paragraphStyle setLineSpacing:4];//调整行间距
          paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
          [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, memoResult.length)];
          [opereateResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 5)];
          
          NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:14],NSParagraphStyleAttributeName:paragraphStyle};
          CGFloat height = [memoResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
          height = ceil(height);
          height = height < 20 ? 20 : height;
          
          UILabel *memolabel = [[UILabel alloc] init];
          memolabel.frame = CGRectMake(headImageV.right + 6 , timelabel.bottom + 4,BCWidth - 62,height);
          memolabel.textColor = ACOLOR(31, 33, 38, 1);
          memolabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          memolabel.numberOfLines = 0;
          [bottomV addSubview:memolabel];
          memolabel.attributedText = opereateResultStr;
          
          //      分割线
          if (i < arr.count - 1 ) {
            UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, memolabel.bottom + 14, BCWidth - 16, 0.5)];
            lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
            [bottomV addSubview:lineV];
          }
          bottomV.height = memolabel.bottom + 14 + 14;
          
          if (i == arr.count - 1) {
            bottomV.height = memolabel.bottom + 14 ;
          }
          
          itemH += bottomV.height ;
          
        } else if ([typeStr isEqualToString:@"FOLLOW_RECORD"]) {//跟进
          
          NSDictionary *itemDic = [pointDic objectForKeyNil:@"data"];
          NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
          NSString *followResult = [NSString stringWithFormat:@"%@ · 跟进 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
          NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
          [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:15 weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
          
          NSInteger randomNum = arc4random_uniform(6);
          UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
          headImageV.layer.cornerRadius = 12;
          headImageV.clipsToBounds = YES;
          [bottomV addSubview:headImageV];
          
          CAGradientLayer *gl = [CAGradientLayer layer];
          gl.frame = headImageV.bounds;
          gl.startPoint = CGPointMake(0.0, 0);
          gl.endPoint = CGPointMake(1, 1);
          gl.colors = [colorArr objectAtIndexCheck:randomNum];
          gl.locations = @[@(0), @(1.0f)];
          [headImageV.layer addSublayer:gl];
          
          UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
          headL.textColor = [UIColor whiteColor];
          headL.textAlignment = NSTextAlignmentCenter;
          headL.font = [UIFont systemFontOfSize:10];
          headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
          [headImageV addSubview:headL];
          
          UILabel *switchlabel = [[UILabel alloc] init];
          switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 116,20);
          switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
          switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
          [bottomV addSubview:switchlabel];
          switchlabel.attributedText = string;
          
  //        评论
          UIButton *commentBtn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - 60, switchlabel.top, 60, 20)];
          [bottomV addSubview:commentBtn];
          [commentBtn addtargetBlock:^(UIButton *button) {
            
            MAWriteReplyView *replyVV = [[MAWriteReplyView alloc] initWithName:@"评论" andPla:[NSString stringWithFormat:@"回复%@：",nameStr]];
            [replyVV showModal];
            replyVV.okBlock = ^(NSString *dataString) {
              [weakSelf replyString:[itemDic objectNilForKey:@"id"] andMemo:dataString andIndex:1];
            };
          }];
          
          UIImageView *commentIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 2, 16, 16)];
          commentIM.image = [UIImage imageNamed:@"detail_comment"];
          [commentBtn addSubview:commentIM];
          
          UILabel *commentL = [[UILabel alloc] initWithFrame:CGRectMake(commentIM.right + 4,0,30,20)];
          commentL.text = @"评论";
          commentL.font = [UIFont systemFontOfSize:MutilFont(12)];
          commentL.textColor = COLOR(134, 144, 156);
          [commentBtn addSubview:commentL];
          
          //        备注
          NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
          NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
          CGFloat height = [memoStr boundingRectWithSize:CGSizeMake(BCWidth - 92, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
          height = ceil(height);
          UILabel *memoLabel= [[UILabel alloc] initWithFrame:CGRectMake(headImageV.right + 6, headImageV.bottom + 6, BCWidth - 92,BCStringIsEmpty(memoStr) ? 0 : height)];
          memoLabel.textColor = ACOLOR(29, 33, 41, 1);
          memoLabel.font = [UIFont systemFontOfSize:MutilFont(15)];
          memoLabel.text = memoStr;
          memoLabel.numberOfLines = 0;
          [bottomV addSubview:memoLabel];
          
          //        跟进状态
          NSString *beforeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"state"]];
          NSString *followStr = BCStringIsEmpty(beforeStr) ? @"-" : [beforeStr isEqualToString:@"FOLLOW"] ? @"跟进中":[beforeStr isEqualToString:@"FINISH"] ? @"已完成" : @"关闭";
          NSString *beforeResult = [NSString stringWithFormat:@"跟进状态：%@",followStr];
          NSMutableAttributedString *beforeString = [[NSMutableAttributedString alloc] initWithString:beforeResult];
          [beforeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, followStr.length)];
          
          UILabel *beforelabel = [[UILabel alloc] init];
          beforelabel.frame = CGRectMake(headImageV.right + 6,memoLabel.bottom + 6,BCWidth - 80,20);
          beforelabel.textColor = ACOLOR(30, 33, 38, 0.45);
          beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:beforelabel];
          beforelabel.attributedText = beforeString;
          
         
          
          //        跟进结果
          NSString *resultStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"result"]];
          NSString *resultResult = [NSString stringWithFormat:@"跟进结果：%@",BCStringIsEmpty(resultStr) ? @"-" : resultStr];
          NSMutableAttributedString *resultString = [[NSMutableAttributedString alloc] initWithString:resultResult];
          [resultString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5,BCStringIsEmpty(resultStr) ? 1 : resultStr.length)];
          
          UILabel *resultlabel = [[UILabel alloc] init];
          resultlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 80,20);
          resultlabel.textColor = ACOLOR(30, 33, 38, 0.45);
          resultlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:resultlabel];
          resultlabel.attributedText = resultString;
          
          
          //        跟进时间
          NSString *timeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"follow_time"]];
          timeStr = [timeStr substringWithRange:NSMakeRange(0, timeStr.length - 3)];
          NSString *timeResult = [NSString stringWithFormat:@"跟进时间：%@",timeStr];
          NSMutableAttributedString *timeString = [[NSMutableAttributedString alloc] initWithString:timeResult];
          [timeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, timeStr.length)];
          
          UILabel *timelabel = [[UILabel alloc] init];
          timelabel.frame = CGRectMake(headImageV.right + 6,resultlabel.bottom + 4,BCWidth - 80,20);
          timelabel.textColor = ACOLOR(30, 33, 38, 0.45);
          timelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:timelabel];
          timelabel.attributedText = timeString;
          //        附件信息
          NSArray *fileArr = [itemDic objectForKeyNil:@"files"];
          if ([fileArr isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(fileArr)) {
            
            
            for (int j = 0; j <fileArr.count; j ++) {
              
              NSDictionary *deedDic = [fileArr objectAtIndexCheck:j];
              UIButton *leaseImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(headImageV.right + 6, timelabel.bottom + 8 + (44 + 10) * j, BCWidth - 62, 44)];
              leaseImageBtn.layer.cornerRadius = 4;
              leaseImageBtn.clipsToBounds = YES;
              leaseImageBtn.backgroundColor = COLOR(244, 245, 247);
              [bottomV addSubview:leaseImageBtn];
              
              UIImageView *leaseIM = [[UIImageView alloc] initWithFrame:CGRectMake(16, 10 ,24, 24)];
              [leaseImageBtn addSubview:leaseIM];
              
              UILabel *leaseL = [[UILabel alloc] initWithFrame:CGRectMake(leaseIM.right + 10,0, leaseImageBtn.width - leaseIM.right - 26, 44)];
              leaseL.text = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"name"]];
              leaseL.font = [UIFont systemFontOfSize:MutilFont(14)];
              leaseL.textColor = ACOLOR(31, 33, 38,1);
              leaseL.lineBreakMode = NSLineBreakByTruncatingMiddle;
              [leaseImageBtn addSubview:leaseL];
              
              if ([temArr containsObject:[deedDic objectNilForKey:@"suffix_type"]]) {//如果是图片
                
                leaseIM.image = [UIImage imageNamed:@"file_img"];
                [leaseImageBtn addtargetBlock:^(UIButton *button) {
                  [ZKPhotoBrowser showWithImageUrls:@[[[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] currentPhotoIndex:0 sourceSuperView:button];
                }];
              } else {//是其他文本 pdf  word
                
                NSString *fileStr = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"suffix_type"] ];
                if ([fileStr containsString:@"pdf"]) {
                  leaseIM.image = [UIImage imageNamed:@"file_pdf"];
                } else  if ([fileStr containsString:@"doc"] || [fileStr containsString:@"docx"]) {
                  leaseIM.image = [UIImage imageNamed:@"file_word"];
                } else  if ([fileStr containsString:@"xls"] || [fileStr containsString:@"xls"]) {
                  leaseIM.image = [UIImage imageNamed:@"file_excel"];
                }else  if ([fileStr containsString:@"ppt"] || [fileStr containsString:@"pptx"]) {
                  leaseIM.image = [UIImage imageNamed:@"file_ppt"];
                }else {
                  leaseIM.image = [UIImage imageNamed:@"file_no"];
                }
                
                [leaseImageBtn addtargetBlock:^(UIButton *button) {
                 
                  NSString *url = [[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
                  [weakSelf openSFDoc:url];
                 
                }];
              }
              
            }
          }
          
          //                 评论明细
          CGFloat commentTop = 0;
          if (![fileArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(fileArr)) {//没有附件
            commentTop  = timelabel.bottom  + 14;
          } else {
            commentTop = timelabel.bottom + 8 + (44 + 10) * fileArr.count - 8 + 14;
          }
          
          CGFloat commentHeight = 0;
          NSArray *commentArr = [itemDic objectForKeyNil:@"details"];
          if (!BCArrayIsEmpty(commentArr)) {
            
            for (int k = 0; k <commentArr.count; k ++) {
              NSDictionary *replyDic = [commentArr objectAtIndexCheck:k];
              
              NSString *replyNameStr = [NSString stringWithFormat:@"%@",[replyDic objectNilForKey:@"create_by"]];
              NSString *replyResult = [NSString stringWithFormat:@"%@ · %@",replyNameStr,[replyDic objectNilForKey:@"create_time"]];
              NSMutableAttributedString *replystring = [[NSMutableAttributedString alloc] initWithString:replyResult];
              [replystring addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:15 weight:UIFontWeightMedium]} range:NSMakeRange(0, replyNameStr.length)];
              
              //  回复人
              NSInteger randomNum = arc4random_uniform(6);
              UIView *replyheadV = [[UIView alloc] initWithFrame:CGRectMake(headImageV.right + 6, commentTop + commentHeight, 24, 24)];
              replyheadV.layer.cornerRadius = 12;
              replyheadV.clipsToBounds = YES;
              [bottomV addSubview:replyheadV];
              
              CAGradientLayer *gl = [CAGradientLayer layer];
              gl.frame = replyheadV.bounds;
              gl.startPoint = CGPointMake(0.0, 0);
              gl.endPoint = CGPointMake(1, 1);
              gl.colors = [colorArr objectAtIndexCheck:randomNum];
              gl.locations = @[@(0), @(1.0f)];
              [replyheadV.layer addSublayer:gl];
              
              UILabel *replyL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
              replyL.textColor = [UIColor whiteColor];
              replyL.textAlignment = NSTextAlignmentCenter;
              replyL.font = [UIFont systemFontOfSize:10];
              replyL.text = replyNameStr.length > 2 ? [replyNameStr substringWithRange:NSMakeRange(replyNameStr.length - 2, 2)] : replyNameStr;
              [replyheadV addSubview:replyL];
              
              UILabel *replyTitleL = [[UILabel alloc] init];
              replyTitleL.frame = CGRectMake(replyheadV.right + 6,replyheadV.top + 1.5,BCWidth - replyheadV.right - 76,20);
              replyTitleL.textColor = ACOLOR(30, 33, 38, 0.45);
              replyTitleL.font = [UIFont systemFontOfSize:MutilFont(13)];
              [bottomV addSubview:replyTitleL];
              replyTitleL.attributedText = replystring;
              
              //            评论按钮
              UIButton *replyBtn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - 60, replyTitleL.top, 60, 20)];
              [bottomV addSubview:replyBtn];
              
              UIImageView *replyIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 2, 16, 16)];
              replyIM.image = [UIImage imageNamed:@"detail_comment"];
              [replyBtn addSubview:replyIM];
              
              UILabel *replyRL = [[UILabel alloc] initWithFrame:CGRectMake(replyIM.right + 4,0,30,20)];
              replyRL.text = @"评论";
              replyRL.font = [UIFont systemFontOfSize:MutilFont(12)];
              replyRL.textColor = COLOR(134, 144, 156);
              [replyBtn addSubview:replyRL];
              [replyBtn addtargetBlock:^(UIButton *button) {
                
                MAWriteReplyView *replyVV = [[MAWriteReplyView alloc] initWithName:@"评论" andPla:[NSString stringWithFormat:@"回复%@：",replyNameStr]];
                [replyVV showModal];
                replyVV.okBlock = ^(NSString *dataString) {
                  [weakSelf replyString:[replyDic objectNilForKey:@"id"] andMemo:dataString andIndex:1];
                };
              }];
              
              //            回复内容
              NSString *anwserStr = [NSString stringWithFormat:@"%@",[replyDic objectNilForKey:@"memo"]];
              NSString *anwserNameStr = [NSString stringWithFormat:@"%@",[replyDic objectNilForKey:@"reply_by"]];
              NSString *anwserResult = [NSString stringWithFormat:@"回复 %@：%@",anwserNameStr,!BCStringIsEmpty(anwserStr) ? anwserStr: @"-"];
              
              NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:anwserResult];
              NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
              [paragraphStyle setLineSpacing:4];//调整行间距
              paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
              [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, anwserResult.length)];
              [opereateResultStr addAttribute:NSForegroundColorAttributeName value:COLOR(26, 106, 255) range:NSMakeRange(2, anwserNameStr.length + 1)];
              
              NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)],NSParagraphStyleAttributeName:paragraphStyle};
              CGFloat height = [anwserResult boundingRectWithSize:CGSizeMake(BCWidth - replyheadV.right - 22, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
              height = ceil(height);
              height = height < 20 ? 20 : height;
              
              UILabel *anwserL = [[UILabel alloc] init];
              anwserL.frame = CGRectMake(replyheadV.right + 6 , replyheadV.bottom + 6,BCWidth - replyheadV.right - 22,height);
              anwserL.textColor = ACOLOR(31, 33, 38, 1);
              anwserL.font = [UIFont systemFontOfSize:MutilFont(15)];
              anwserL.numberOfLines = 0;
              [bottomV addSubview:anwserL];
              anwserL.attributedText = opereateResultStr;
              
              
              commentHeight += 30 + 14 + height;
              
            }
            
          }
          
          //        计算高度
          CGFloat bottomH = commentTop + commentHeight;
          
          bottomV.height = bottomH + 14;
          
          //      分割线
          if (i < arr.count - 1 ) {
            UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, bottomH, BCWidth - 16, 0.5)];
            lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
            [bottomV addSubview:lineV];
          }
          
          if (i == arr.count - 1) {
            bottomV.height = bottomH;
          }
          itemH += bottomV.height;
          
        } else if ([typeStr isEqualToString:@"CHANGE_RECORD"]) {//变更
          
          NSDictionary *itemDic = [pointDic objectForKeyNil:@"data"];
          NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
          NSString *followResult = [NSString stringWithFormat:@"%@ · 变更 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
          NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
          [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
          
          NSInteger randomNum = arc4random_uniform(6);
          UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
          headImageV.layer.cornerRadius = 12;
          headImageV.clipsToBounds = YES;
          [bottomV addSubview:headImageV];
          
          CAGradientLayer *gl = [CAGradientLayer layer];
          gl.frame = headImageV.bounds;
          gl.startPoint = CGPointMake(0.0, 0);
          gl.endPoint = CGPointMake(1, 1);
          gl.colors = [colorArr objectAtIndexCheck:randomNum];
          gl.locations = @[@(0), @(1.0f)];
          [headImageV.layer addSublayer:gl];
          
          UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
          headL.textColor = [UIColor whiteColor];
          headL.textAlignment = NSTextAlignmentCenter;
          headL.font = [UIFont systemFontOfSize:10];
          headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
          [headImageV addSubview:headL];
          
          UILabel *switchlabel = [[UILabel alloc] init];
          switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
          switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
          switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
          [bottomV addSubview:switchlabel];
          switchlabel.attributedText = string;
          
          NSString *operateStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"field_name"]];
          NSString *operateResult = [NSString stringWithFormat:@"变更字段：%@",operateStr];
          NSMutableAttributedString *operateString = [[NSMutableAttributedString alloc] initWithString:operateResult];
          [operateString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, operateStr.length)];
          
          UILabel *operatelabel = [[UILabel alloc] init];
          operatelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 8,BCWidth - 80,20);
          operatelabel.textColor = ACOLOR(30, 33, 38, 0.45);
          operatelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:operatelabel];
          operatelabel.attributedText = operateString;
          
          //       原值
          NSString *beforeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"before_change"]];
          if ([beforeStr containsString:@"https"] || [beforeStr containsString:@"http"]  || BCStringIsEmpty(beforeStr)) {
            beforeStr = @"-";
          }
          
          NSString *beforeResult = [NSString stringWithFormat:@"原值：%@", beforeStr];
          NSMutableAttributedString *beforeResultStr = [[NSMutableAttributedString alloc] initWithString:beforeResult];
          NSMutableParagraphStyle *beforeParagraphStyle = [[NSMutableParagraphStyle alloc] init];
          [beforeParagraphStyle setLineSpacing:4];//调整行间距
          beforeParagraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
          [beforeResultStr addAttribute:NSParagraphStyleAttributeName value:beforeParagraphStyle range:NSMakeRange(0, beforeResult.length)];
          [beforeResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 3)];
          
          NSDictionary *beforeAttributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:beforeParagraphStyle};
          CGFloat beforeHeight = [beforeResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:beforeAttributes context:nil].size.height;
          beforeHeight= ceil(beforeHeight);
          beforeHeight = beforeHeight < 20 ? 20 : beforeHeight;
          
          UILabel *beforelabel = [[UILabel alloc] init];
          beforelabel.frame = CGRectMake(headImageV.right + 6 , operatelabel.bottom + 4,BCWidth - 62,beforeHeight);
          beforelabel.textColor = COLOR(31, 33, 38);
          beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          beforelabel.numberOfLines = 0;
          [bottomV addSubview:beforelabel];
          beforelabel.attributedText = beforeResultStr;
          
          //        新值
          NSString *afterStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"after_change"]];
          if ([afterStr containsString:@"https"] || [afterStr containsString:@"http"] || BCStringIsEmpty(beforeStr)) {
            afterStr = @"-";
          }
          
          NSString *afterResult = [NSString stringWithFormat:@"新值：%@", afterStr];
          NSMutableAttributedString *afterResultStr = [[NSMutableAttributedString alloc] initWithString:afterResult];
          NSMutableParagraphStyle *afterParagraphStyle = [[NSMutableParagraphStyle alloc] init];
          [afterParagraphStyle setLineSpacing:4];//调整行间距
          afterParagraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
          [afterResultStr addAttribute:NSParagraphStyleAttributeName value:afterParagraphStyle range:NSMakeRange(0, afterResult.length)];
          [afterResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 3)];
          
          NSDictionary *afterAttributes = @{NSFontAttributeName:[UIFont systemFontOfSize:14],NSParagraphStyleAttributeName:afterParagraphStyle};
          CGFloat afterHeight = [afterResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:afterAttributes context:nil].size.height;
          afterHeight = ceil(afterHeight);
          afterHeight = afterHeight < 20 ? 20 : afterHeight;
          
          UILabel *afterlabel = [[UILabel alloc] init];
          afterlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 62,afterHeight);
          afterlabel.textColor = COLOR(31, 33, 38);
          afterlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          afterlabel.numberOfLines = 0;
          [bottomV addSubview:afterlabel];
          afterlabel.attributedText = afterResultStr;
          
          
          //      分割线
          if (i < arr.count - 1 ) {
            UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,  afterlabel.bottom + 14, BCWidth - 16, 0.5)];
            lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
            [bottomV addSubview:lineV];
          }
          bottomV.height = afterlabel.bottom + 14 + 14;
          
          if (i == arr.count - 1) {
            bottomV.height = afterlabel.bottom + 14 ;
          }
          
          itemH += bottomV.height ;
          
        } else {//操作
          
          NSDictionary *itemDic = [pointDic objectForKeyNil:@"data"];
          NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
          NSString *followResult = [NSString stringWithFormat:@"%@ · 操作 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
          NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
          [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
          
          NSInteger randomNum = arc4random_uniform(6);
          UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
          headImageV.layer.cornerRadius = 12;
          headImageV.clipsToBounds = YES;
          [bottomV addSubview:headImageV];
          
          CAGradientLayer *gl = [CAGradientLayer layer];
          gl.frame = headImageV.bounds;
          gl.startPoint = CGPointMake(0.0, 0);
          gl.endPoint = CGPointMake(1, 1);
          gl.colors = [colorArr objectAtIndexCheck:randomNum];
          gl.locations = @[@(0), @(1.0f)];
          [headImageV.layer addSublayer:gl];
          
          UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
          headL.textColor = [UIColor whiteColor];
          headL.textAlignment = NSTextAlignmentCenter;
          headL.font = [UIFont systemFontOfSize:10];
          headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
          [headImageV addSubview:headL];
          
          UILabel *switchlabel = [[UILabel alloc] init];
          switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
          switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
          switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
          [bottomV addSubview:switchlabel];
          switchlabel.attributedText = string;
          
          NSString *operateStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"model_state"]];
          NSString *operateResult = [NSString stringWithFormat:@"操作：%@",operateStr];
          NSMutableAttributedString *operateString = [[NSMutableAttributedString alloc] initWithString:operateResult];
          [operateString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(3, operateStr.length)];
          
          UILabel *operatelabel = [[UILabel alloc] init];
          operatelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 8,BCWidth - 80,20);
          operatelabel.textColor = ACOLOR(30, 33, 38, 0.45);
          operatelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:operatelabel];
          operatelabel.attributedText = operateString;
          
          NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
          NSString *memoResult = [NSString stringWithFormat:@"审批意见：%@",!BCStringIsEmpty(memoStr) ? memoStr : @"-"];
          NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:memoResult];
          NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
          [paragraphStyle setLineSpacing:4];//调整行间距
          paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
          [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, memoResult.length)];
          [opereateResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 5)];
          
          NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:paragraphStyle};
          CGFloat height = [memoResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
          height = ceil(height);
          height = height < 20 ? 20 : height;
          
          UILabel *memolabel = [[UILabel alloc] init];
          memolabel.frame = CGRectMake(headImageV.right + 6 , operatelabel.bottom + 4,BCWidth - 62,height);
          memolabel.textColor = ACOLOR(31, 33, 38, 1);
          memolabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          memolabel.numberOfLines = 0;
          [bottomV addSubview:memolabel];
          memolabel.attributedText = opereateResultStr;
          
          //      分割线
          if (i < arr.count - 1 ) {
            UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, memolabel.bottom + 14, BCWidth - 16, 0.5)];
            lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
            [bottomV addSubview:lineV];
          }
          
          bottomV.height = memolabel.bottom + 14 + 14;
          
          if (i == arr.count - 1) {
            bottomV.height = memolabel.bottom + 14 ;
          }
          itemH += bottomV.height ;
        }
        
        if (i == arr.count - 1) {
          self.selectPointStateV.height = itemH;
          if ( self.selectPointStateV.bottom  + 114 > self.pointStateScrollView.height) {
            self.pointStateScrollView.contentSize = CGSizeMake(0, self.selectPointStateV.bottom + 40);
          }
        }
        
      }
      
      
    }
    
    
    
  }
}


#pragma mark 回复评论功能
- (void)replyString:(NSString *)replyId andMemo:(NSString *)memo andIndex:(NSInteger)index{
  
  
  NSDictionary *params = @{@"store_plan_id":self.pointId,@"reply_id":replyId,@"memo":memo};
  
  [self.loadingView showModal];
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storefollowrecord.save" Params:params success:^(NSDictionary *successResult) {
   
    if (index == 0) {//全部
      
      [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storefollowrecord.find" Params:@{@"store_plan_id":self.pointId} success:^(NSDictionary *successResult) {
       
        NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
        
        if (![receiveData isKindOfClass:[NSArray class]]) {
          return;
        }
        
        if (BCArrayIsEmpty(receiveData)) {
          return;
        }
        
        self.followPointArr = receiveData;
        
      } failure:^(NSString *errorResult) {
        
        [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
      }];
      
      
      [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.flag.storeplan.find" Params:@{@"store_plan_id":self.pointId} success:^(NSDictionary *successResult) {
        [self.loadingView hideModal];
        NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
        
        if (![receiveData isKindOfClass:[NSArray class]]) {
          return;
        }
        
        if (BCArrayIsEmpty(receiveData)) {
          return;
        }
        
        self.allPointArr = receiveData;
        
       
        [self.statusView showModal:ToastSuccess andTitle:@"评论成功"];
        
        [self reloadSelectSegment:index];
        
      } failure:^(NSString *errorResult) {
        [self.loadingView hideModal];
        [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
      }];
      
    } else {//跟进
      
      
      [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storefollowrecord.find" Params:@{@"store_plan_id":self.pointId} success:^(NSDictionary *successResult) {
       
        [self.loadingView hideModal];
        NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
        
        if (![receiveData isKindOfClass:[NSArray class]]) {
          return;
        }
        
        if (BCArrayIsEmpty(receiveData)) {
          return;
        }
        
        self.followPointArr = receiveData;
        
        [self.statusView showModal:ToastSuccess andTitle:@"评论成功"];
        
        
        [self reloadSelectSegment:index];
        
        
      } failure:^(NSString *errorResult) {
        
        [self.loadingView hideModal];
        [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
      }];
      
      
      [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.flag.storeplan.find" Params:@{@"store_plan_id":self.pointId} success:^(NSDictionary *successResult) {
       
        NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
        
        if (![receiveData isKindOfClass:[NSArray class]]) {
          return;
        }
        
        if (BCArrayIsEmpty(receiveData)) {
          return;
        }
        
        self.allPointArr = receiveData;
        
        
      } failure:^(NSString *errorResult) {
        
        [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
      }];
    
    }
      
     
    
    
    
  } failure:^(NSString *errorResult) {
    
    [self.loadingView hideModal];
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
}


#pragma mark 所有网络请求回来加载tab下的带看记录界面
- (void)loadLookRecord{
  
  if (![self.lookArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.lookArr)) {//没数据
    
    UIImageView *emptyView = [[UIImageView alloc] initWithFrame:CGRectMake((BCWidth - 160)/2, self.heightTop + 144, 160, 180)];
    emptyView.image = [UIImage imageNamed:@"tab_nodata"];
    [self.lookScrollView addSubview:emptyView];
    
    UILabel *emptyL = [[UILabel alloc] initWithFrame:CGRectMake(0, emptyView.bottom - 40, BCWidth, 20)];
    emptyL.textColor = ACOLOR(30, 33, 38, 0.45);
    emptyL.font = [UIFont systemFontOfSize:14];
    emptyL.text = @"暂无带看记录";
    emptyL.textAlignment = NSTextAlignmentCenter;
    [self.lookScrollView addSubview:emptyL];
    
  } else {
    
    __weak typeof(self)weakSelf = self;
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 12,BCWidth,50)];
    contentView.backgroundColor = [UIColor whiteColor];
    [self.lookScrollView addSubview:contentView];
    
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.frame = CGRectMake(16,0,200,50);
    nameLabel.textColor = COLOR(31, 33, 38);
    nameLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    nameLabel.text = [NSString stringWithFormat:@"带看记录（%lu）",(unsigned long)((![self.lookArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.lookArr)) ? 0:self.lookArr.count)];
    [contentView addSubview:nameLabel];
    
    CGFloat itemH = 50;
    for (int i = 0; i < self.lookArr.count ; i ++) {
      NSDictionary *itemDic = [self.lookArr objectAtIndexCheck:i];
      
      UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(16, itemH, BCWidth - 32, 50)];
      [contentView addSubview:bottomV];
      
      //    分割线
      UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,itemH, BCWidth - 16, 0.5)];
      lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
      [contentView addSubview:lineV];
      
      //    带看客户
      UILabel *leftL = [[UILabel alloc] initWithFrame:CGRectMake(0, 14, 200, 20)];
      leftL.text = [NSString stringWithFormat:@"带看客户  %@",[itemDic objectNilForKey:@"client_name"]];
      leftL.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
      leftL.textColor = ACOLOR(31, 33, 38, 1);
      [bottomV addSubview:leftL];
      
      //    状态
      UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(0,14, BCWidth - 32, 20)];
      rightL.font = [UIFont systemFontOfSize:14];
      rightL.textAlignment = NSTextAlignmentRight;
      rightL.text = [[itemDic objectNilForKey:@"result"] isEqualToString:@"YES"] ? @"可签约" : [[itemDic objectNilForKey:@"result"] isEqualToString:@"NO"] ? @"无意向" :@"持续关注";
      rightL.textColor = [[itemDic objectNilForKey:@"result"] isEqualToString:@"YES"] ? COLOR(0, 180, 43) : [[itemDic objectNilForKey:@"result"] isEqualToString:@"NO"] ?  COLOR(245, 63, 63) : COLOR(255, 125, 1);
      [bottomV addSubview:rightL];
      
      //    备注
      NSString *memoNewStr =  [NSString stringWithFormat:@"%@", [itemDic objectNilForKey:@"look_memo"]];
      NSString *str = [NSString stringWithFormat:@"备注：%@", [itemDic objectNilForKey:@"look_memo"]];
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:14]};
      CGFloat height = [str boundingRectWithSize:CGSizeMake(BCWidth - 32, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      UILabel *memoL = [[UILabel alloc] initWithFrame:CGRectMake(0, leftL.bottom + 10, BCWidth - 32,BCStringIsEmpty(memoNewStr) ? 0 : height)];
      memoL.font = [UIFont systemFontOfSize:14];
      memoL.textColor = ACOLOR(30, 33, 38, 0.70);
      memoL.text = str;
      memoL.numberOfLines = 0;
      [bottomV addSubview:memoL];
      
      //    带看人
      UILabel *lookL = [[UILabel alloc] initWithFrame:CGRectMake(0,BCStringIsEmpty(memoNewStr) ? leftL.bottom + 12 :memoL.bottom + 12, BCWidth - 16, 18)];
      lookL.font = [UIFont systemFontOfSize:13];
      lookL.textColor = ACOLOR(30, 33, 38, 0.45);
      NSString *timeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"actual_date"]];
      timeStr = [timeStr substringWithRange:NSMakeRange(0, timeStr.length - 9)];
      lookL.text = [NSString stringWithFormat:@"%@ · %@",[itemDic objectNilForKey:@"user_name"],timeStr];
      [bottomV addSubview:lookL];
      itemH += lookL.bottom + 14;
    }
    
    contentView.height = itemH;
    if (contentView.bottom > self.lookScrollView.height) {
      self.lookScrollView.contentSize = CGSizeMake(0, contentView.bottom + 40);
    }
  }
  
}

#pragma mark 所有网络请求回来加载tab下的分享记录界面
- (void)loadShareRecord{
  
  if (![self.shareArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.shareArr)) {//没数据
    UIImageView *emptyView = [[UIImageView alloc] initWithFrame:CGRectMake((BCWidth - 160)/2, self.heightTop + 144, 160, 180)];
    emptyView.image = [UIImage imageNamed:@"tab_nodata"];
    [self.shareScrollView addSubview:emptyView];
    
    UILabel *emptyL = [[UILabel alloc] initWithFrame:CGRectMake(0, emptyView.bottom - 40, BCWidth, 20)];
    emptyL.textColor = ACOLOR(30, 33, 38, 0.45);
    emptyL.font = [UIFont systemFontOfSize:14];
    emptyL.text = @"暂无分享记录";
    emptyL.textAlignment = NSTextAlignmentCenter;
    [self.shareScrollView addSubview:emptyL];
    
  } else {
    
    __weak typeof(self)weakSelf = self;
    CGFloat itemH =  12;
    for (int i = 0; i < self.shareArr.count ; i ++) {
      
      NSDictionary *itemDic = [self.shareArr objectAtIndexCheck:i];
      
      UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth, 182)];
      bottomV.backgroundColor = [UIColor whiteColor];
      [self.shareScrollView addSubview:bottomV];
      
//      带看客户
      NSString *nameC = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"client_name"]];
      UILabel *leftNameL = [[UILabel alloc] initWithFrame:CGRectMake(16, 14, 200, 22)];
      leftNameL.text = BCStringIsEmpty(nameC) ? @"-" : nameC;
      leftNameL.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
      leftNameL.textColor = COLOR(31, 33, 38);
      [bottomV addSubview:leftNameL];
      
      //    状态
      UILabel *rightStateL = [[UILabel alloc] initWithFrame:CGRectMake(bottomV.width - 44 - 16,17, 44, 20)];
      rightStateL.font = [UIFont systemFontOfSize:12];
      rightStateL.layer.cornerRadius = 4;
      rightStateL.clipsToBounds = YES;
      rightStateL.textAlignment = NSTextAlignmentCenter;
      rightStateL.text = [[itemDic objectForKeyNil:@"valid"] isEqual:@(1)] ? @"生效中" :@"已失效";
      rightStateL.backgroundColor = [[itemDic objectForKeyNil:@"valid"] isEqual:@(1)] ? COLOR(26, 106, 255)  : ACOLOR(30, 33, 38, 0.45);
      rightStateL.textColor = [UIColor whiteColor];
      [bottomV addSubview:rightStateL];
      
//      查看次数
      UILabel *leftTimeL = [[UILabel alloc] initWithFrame:CGRectMake(16, leftNameL.bottom + 12, 100, 20)];
      leftTimeL.text = @"查看次数";
      leftTimeL.font = [UIFont systemFontOfSize:14];
      leftTimeL.textColor = ACOLOR(30, 33, 38, 0.7);
      [bottomV addSubview:leftTimeL];
      
      UILabel *rightTimeL = [[UILabel alloc] initWithFrame:CGRectMake(leftTimeL.right + 6, leftNameL.bottom + 12, bottomV.width - leftTimeL.right - 6, 20)];
      rightTimeL.text =  [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"times"]];
      rightTimeL.font = [UIFont systemFontOfSize:14];
      rightTimeL.textColor = COLOR(31, 33, 38);
      [bottomV addSubview:rightTimeL];
      
//    有效时长
      UILabel *leftPeriodL = [[UILabel alloc] initWithFrame:CGRectMake(16, leftTimeL.bottom + 5, 100, 20)];
      leftPeriodL.text = @"有效期限";
      leftPeriodL.font = [UIFont systemFontOfSize:14];
      leftPeriodL.textColor = ACOLOR(30, 33, 38, 0.7);
      [bottomV addSubview:leftPeriodL];
      
      UILabel *rightPeriodL = [[UILabel alloc] initWithFrame:CGRectMake(leftPeriodL.right + 6, leftTimeL.bottom + 5, bottomV.width - leftPeriodL.right - 6, 20)];
      rightPeriodL.text =  [NSString stringWithFormat:@"%0.0f小时",[[itemDic objectNilForKey:@"valid_period"] floatValue]/60];
      rightPeriodL.font = [UIFont systemFontOfSize:14];
      rightPeriodL.textColor = COLOR(31, 33, 38);
      [bottomV addSubview:rightPeriodL];
      
//      分享人
      UILabel *leftShareL = [[UILabel alloc] initWithFrame:CGRectMake(16, leftPeriodL.bottom + 5, 100, 20)];
      leftShareL.text = @"分享人";
      leftShareL.font = [UIFont systemFontOfSize:14];
      leftShareL.textColor = ACOLOR(30, 33, 38, 0.7);
      [bottomV addSubview:leftShareL];
      
      UILabel *rightShareL = [[UILabel alloc] initWithFrame:CGRectMake(leftShareL.right + 6, leftPeriodL.bottom + 5, bottomV.width - leftShareL.right - 6, 20)];
      rightShareL.text =  [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
      rightShareL.font = [UIFont systemFontOfSize:14];
      rightShareL.textColor = COLOR(31, 33, 38);
      [bottomV addSubview:rightShareL];
      
//      分享时间
      UILabel *leftShareTL = [[UILabel alloc] initWithFrame:CGRectMake(16, leftShareL.bottom + 5, 100, 20)];
      leftShareTL.text = @"分享时间";
      leftShareTL.font = [UIFont systemFontOfSize:14];
      leftShareTL.textColor = ACOLOR(30, 33, 38, 0.7);
      [bottomV addSubview:leftShareTL];
      
      UILabel *rightShareTL = [[UILabel alloc] initWithFrame:CGRectMake(leftShareL.right + 6, leftShareL.bottom + 5, bottomV.width - leftShareL.right - 6, 20)];
      rightShareTL.text =  [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_time"]];
      rightShareTL.font = [UIFont systemFontOfSize:14];
      rightShareTL.textColor = COLOR(31, 33, 38);
      [bottomV addSubview:rightShareTL];
      
      CGFloat heightB = rightShareTL.bottom;
      if (!BCStringIsEmpty([itemDic objectForKeyNil:@"look_time"])) {
        
        UILabel *leftLookTL = [[UILabel alloc] initWithFrame:CGRectMake(16, leftShareTL.bottom + 5, 100, 20)];
        leftLookTL.text = @"勘察打卡时间";
        leftLookTL.font = [UIFont systemFontOfSize:14];
        leftLookTL.textColor = ACOLOR(30, 33, 38, 0.7);
        [bottomV addSubview:leftLookTL];
        
        UILabel *rightLookTL = [[UILabel alloc] initWithFrame:CGRectMake(leftShareL.right + 6, leftLookTL.top, bottomV.width - leftShareL.right - 6, 20)];
        rightLookTL.text =  [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"look_time"]];
        rightLookTL.font = [UIFont systemFontOfSize:14];
        rightLookTL.textColor = COLOR(31, 33, 38);
        [bottomV addSubview:rightLookTL];
        
        UILabel *leftLookRL = [[UILabel alloc] initWithFrame:CGRectMake(16, leftLookTL.bottom + 5, 100, 20)];
        leftLookRL.text = @"勘察结果";
        leftLookRL.font = [UIFont systemFontOfSize:14];
        leftLookRL.textColor = ACOLOR(30, 33, 38, 0.7);
        [bottomV addSubview:leftLookRL];
        
        NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:14]};
        NSString *titleNameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"look_result"]];
        CGFloat height = [titleNameStr boundingRectWithSize:CGSizeMake(bottomV.width - leftShareL.right - 22, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        height = ceil(height);
        height = height < 20 ? 20 : height;
        
        UILabel *rightLookRL = [[UILabel alloc] initWithFrame:CGRectMake(leftLookRL.right + 6, leftLookRL.top, bottomV.width - leftLookRL.right - 22, height)];
        rightLookRL.text =  titleNameStr;
        rightLookRL.numberOfLines = 0;
        rightLookRL.font = [UIFont systemFontOfSize:14];
        rightLookRL.textColor = COLOR(31, 33, 38);
        [bottomV addSubview:rightLookRL];
        
        UILabel *leftLookVL = [[UILabel alloc] initWithFrame:CGRectMake(16, rightLookRL.bottom + 5, 100, 20)];
        leftLookVL.text = @"现场勘察视频";
        leftLookVL.font = [UIFont systemFontOfSize:14];
        leftLookVL.textColor = ACOLOR(30, 33, 38, 0.7);
        [bottomV addSubview:leftLookVL];
        
        CGFloat imageH = (BCWidth - 32 - 16)/3;
        CGFloat paddingL = 8.0; //button 间距
        CGFloat paddingT = 8.0; //button 间距
        CGFloat pointX = 16; //button X坐标
        CGFloat pointY = leftLookVL.bottom + 8; //button Y坐标
        NSArray *videoArr = [itemDic objectForKeyNil:@"look_files"];
        if (BCArrayIsEmpty(videoArr)) {
          continue;
        }
//        视频
        for (int k = 0; k < videoArr.count; k ++) {
          NSDictionary *imageDic = [videoArr objectAtIndexCheck:k];
          if (pointX + imageH > (BCWidth - 16)) {//换行
            pointX = 16;//X从新开始
            pointY += (imageH + paddingT);//换行后Y+
          }
          UIButton *imageBtn = [[UIButton alloc] initWithFrame:CGRectMake(pointX, pointY, imageH, imageH)];
          imageBtn.layer.cornerRadius = 4;
          imageBtn.clipsToBounds = YES;
          NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[imageDic objectNilForKey:@"url"]];
          UIImageView *imageI = [[UIImageView alloc] initWithFrame:CGRectMake(0,0, imageH, imageH)];
          [imageI sd_setImageWithURL:[NSURL URLWithString:[videoUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
          [imageBtn addSubview:imageI];
          
          UIImageView *playIM = [[UIImageView alloc] initWithFrame:CGRectMake((imageH - 25)/2, (imageH - 25)/2, 25, 25)];
          playIM.image = [UIImage imageNamed:@"play_video"];
          [imageBtn addSubview:playIM];
          
          [bottomV addSubview:imageBtn];
          [imageBtn addtargetBlock:^(UIButton *button) {
            NSURL * url = [NSURL URLWithString:[[imageDic objectForKeyNil:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
            AVPlayerViewController * pVC = [[AVPlayerViewController alloc] init];
            pVC.player = [AVPlayer playerWithURL:url];
            [weakSelf presentViewController:pVC animated:YES completion:nil];
            [pVC.player play];
          }];
          
          pointX += (imageH + paddingL);
          
        }
        
        heightB = pointY + imageH;
      }
      
      
      if ([[itemDic objectForKeyNil:@"valid"] isEqual:@(1)]) {//生效
        NSString *userName = [[NSUserDefaults standardUserDefaults] objectForKey:@"MAP_USERNAME"];
//        2个按钮
        UIButton *leftBtn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - 110 - 84, heightB + 14, 84, 32)];
        leftBtn.layer.cornerRadius = 4;
        leftBtn.layer.borderWidth = 1;
        leftBtn.tag = 66666 + i;
        [leftBtn addTarget:self action:@selector(clickStopShare:) forControlEvents:UIControlEventTouchUpInside];
        leftBtn.layer.borderColor = ACOLOR(30, 33, 38, 0.15).CGColor;
        [leftBtn setTitle:@"终止分享" forState:UIControlStateNormal];
        [leftBtn setTitleColor:COLOR(31, 33, 38) forState:UIControlStateNormal];
        leftBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        if ([userName isEqualToString:[itemDic objectForKeyNil:@"create_by"]]) {
          [bottomV addSubview:leftBtn];
        }
       
        
        UIButton *rightBtn = [[UIButton alloc] initWithFrame:CGRectMake(leftBtn.right + 10, heightB + 14, 84, 32)];
        rightBtn.tag = 77777 + i;
        rightBtn.layer.cornerRadius = 4;
        rightBtn.layer.borderWidth = 1;
        rightBtn.layer.borderColor = ACOLOR(26, 106, 255, 1).CGColor;
        [rightBtn addTarget:self action:@selector(clickTakeLook:) forControlEvents:UIControlEventTouchUpInside];
        if (!BCStringIsEmpty([itemDic objectForKeyNil:@"look_time"])) {//如果已经有勘查打卡了
          [rightBtn setTitle:@"" forState:UIControlStateNormal];
          rightBtn.hidden = YES;
          leftBtn.left = BCWidth - 84 - 16;
        } else {
        
          if (self.isTakeLook && [userName isEqualToString:[itemDic objectForKeyNil:@"create_by"]]) {
            [rightBtn setTitle:@"勘查打卡" forState:UIControlStateNormal];
          } else {
            [rightBtn setTitle:@"复制链接" forState:UIControlStateNormal];
          }
          
        }
        [rightBtn setTitleColor:ACOLOR(26, 106, 255, 1) forState:UIControlStateNormal];
        rightBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        [bottomV addSubview:rightBtn];
        
        if ([userName isEqualToString:[itemDic objectForKeyNil:@"create_by"]] || !rightBtn.hidden) {
          bottomV.height = leftBtn.bottom + 16;
        } else {
          bottomV.height = heightB + 16;
        }
       
        itemH += bottomV.height + 12;
        
      } else {//不生效
        bottomV.height = heightB + 16;
        itemH += bottomV.height + 12;
      }
     
      
    }
    
   
    if ( itemH > self.shareScrollView.height) {
      self.shareScrollView.contentSize = CGSizeMake(0, itemH + 40);
    }
  }
  
}

#pragma mark 点击终止分享
- (void)clickStopShare:(UIButton *)sender{
  __weak typeof(self)weakSelf = self;
  
  NSDictionary *dic = [self.shareArr objectAtIndexCheck:sender.tag - 66666];
  
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定终止该分享？" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf getStopShareReques:dic];
  };
}
- (void)getStopShareReques:(NSDictionary *)dic{
  
  [self.loadingView showModal];
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storeplan.sharerecord.stop" Params:@{@"id":[dic objectForKeyNil:@"id"]} success:^(NSDictionary *successResult) {
    [self.loadingView hideModal];
    
    [self resetView];
   
    
    [self.statusView showModal:ToastSuccess andTitle:@"终止分享成功"];
    
    
  } failure:^(NSString *errorResult) {
    
    [self.loadingView hideModal];
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}
#pragma mark 点击勘察打卡
- (void)clickTakeLook:(UIButton *)sender{
  
  
  NSDictionary *dic = [self.shareArr objectAtIndexCheck:sender.tag - 77777];
  if ([sender.currentTitle isEqualToString:@"勘查打卡"]) {//打卡
   
    MATakeLookViewController *lookVC = [[MATakeLookViewController alloc] init];
    lookVC.shareId = [NSString stringWithFormat:@"%@",[dic objectForKeyNil:@"id"]];
    lookVC.pointLatitude = [NSString stringWithFormat:@"%@",[[self.detailDic objectNilForKey:@"info"] objectNilForKey:@"latitude"]];
    lookVC.pointLongitude = [NSString stringWithFormat:@"%@",[[self.detailDic objectNilForKey:@"info"] objectNilForKey:@"longitude"]];
    [self.navigationController pushViewController:lookVC animated:YES];
    
  } else {//复制
    
    UIPasteboard *pp = [UIPasteboard generalPasteboard];
    pp.string = [NSString stringWithFormat:@"%@",[dic objectForKeyNil:@"url"]];
    [self.notiflyView showModal:NotiflySuccess andTitle:@"复制成功"];
  }
  
}

#pragma mark 所有网络请求回来加载tab下的审核记录界面
- (void)loadAuditRecord{
  
  
  if (BCArrayIsEmpty(self.auditArr)) {//没数据
    
    UIImageView *emptyView = [[UIImageView alloc] initWithFrame:CGRectMake((BCWidth - 160)/2, self.heightTop + 144, 160, 180)];
    emptyView.image = [UIImage imageNamed:@"tab_nodata"];
    [self.auditScrollView addSubview:emptyView];
    
    UILabel *emptyL = [[UILabel alloc] initWithFrame:CGRectMake(0, emptyView.bottom - 40, BCWidth, 20)];
    emptyL.textColor = ACOLOR(30, 33, 38, 0.45);
    emptyL.font = [UIFont systemFontOfSize:14];
    emptyL.text = @"暂无审批记录";
    emptyL.textAlignment = NSTextAlignmentCenter;
    [self.auditScrollView addSubview:emptyL];
    
  } else {
    
    __weak typeof(self)weakSelf = self;
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,12,BCWidth,50)];
    contentView.backgroundColor = [UIColor whiteColor];
    
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.frame = CGRectMake(16,0,100,50);
    nameLabel.textColor = COLOR(31, 33, 38);
    nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
    nameLabel.text = @"审批记录";
    [contentView addSubview:nameLabel];
    
    //    分割线
    UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,49, BCWidth - 16, 1)];
    lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
    [contentView addSubview:lineV];
    
    [self.auditScrollView addSubview:contentView];
    
    UIView *mainV = [[UIView alloc] initWithFrame:CGRectMake(0, contentView.bottom, BCWidth, 400)];
    mainV.backgroundColor = COLOR(242, 243, 245);
    [self.auditScrollView addSubview:mainV];
    
  
    CGFloat itemH = 0;
    for (int i = 0; i < self.auditArr.count; i ++) {
     
      NSArray *itemArr = [self.auditArr objectAtIndexCheck:i];
  
      MAApprovalRecordView *appV = [[MAApprovalRecordView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth, 200) andArray:itemArr];
      [mainV addSubview:appV];
      itemH += appV.height + 12;

    }
    
    mainV.height = itemH;
   
    if (itemH + 40 > self.auditScrollView.height) {
      self.auditScrollView.contentSize = CGSizeMake(0, itemH + 60);
    }
    
    
  }
 
}

// 点击返回上一页
- (void)clickBack{
  
  [self.bannerView stopAutoScrollPage];
  [[NSNotificationCenter defaultCenter] postNotificationName:@"GETPOINTDETAILINDEX" object:self userInfo:@{@"currentIndex":[NSString stringWithFormat:@"%ld",(long)self.bannerView.currentPageIndex]}];
  [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark 点击新增房东
- (void)addLandlord{
 
  MAAddLandlordViewController *vc = [[MAAddLandlordViewController alloc] init];
  vc.pointId = self.pointId;
  vc.isFromDetail = YES;
  vc.orgId = self.orgId;
  [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark 点击房东信息去编辑
- (void)clickEditLandlord:(UIButton *)sender{
 
  NSArray *arr = [self.detailDic objectForKeyNil:@"landlords"];
  NSDictionary *dic = [arr objectAtIndexCheck:sender.tag - 999];
  MAAddLandlordViewController *vc = [[MAAddLandlordViewController alloc] init];
  vc.pointId = self.pointId;
  vc.isEdit = YES;
  vc.dataDic = dic;
  vc.isFromDetail = YES;
  vc.orgId = self.orgId;
  [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark 点击弹出底部选择框
- (void)showBottomSheet{
  
  NSString *pointState = [NSString stringWithFormat:@"%@",[self changeState:[self.detailDic objectNilForKey:@"state"]]];
  
  if (BCStringIsEmpty(pointState)) {
    [self.notiflyView showModal:NotiflyFail andTitle:@"单据状态错误"];
    return;
  }
  
  NSArray *arr = @[];
  if ([pointState isEqualToString:@"制单"] || [pointState isEqualToString:@"审批通过"]) {
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"TRANS_POINT"];
    if ([editPoint isEqualToString:@"1"]) {
      arr = @[@"变更跟进人"];
    }
    
  } else if ([pointState isEqualToString:@"待审批"]) {
    NSMutableArray *tmpArr = [NSMutableArray array];
    
    NSString *userName = [[NSUserDefaults standardUserDefaults] objectForKey:@"MAP_USERNAME"];
    if ([userName isEqualToString:[self.detailDic objectForKeyNil:@"update_by"]]) {
      [tmpArr addObject:@"撤销审批"];
    }
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"TRANS_POINT"];
    if ([editPoint isEqualToString:@"1"]) {
      [tmpArr addObject:@"变更跟进人"];
    }
    arr = [tmpArr mutableCopy];
    
  } else if ([pointState isEqualToString:@"否决"]) {
    NSMutableArray *tmpArr = [NSMutableArray array];
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"EDIT_POINT"];
    if ([editPoint isEqualToString:@"1"]) {
      [tmpArr addObject:@"重新打开"];
    }
    
    NSString *transPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"TRANS_POINT"];
    if ([transPoint isEqualToString:@"1"]) {
      [tmpArr addObject:@"变更跟进人"];
    }
    
    arr = [tmpArr mutableCopy];
  }
  
  if (BCArrayIsEmpty(arr)) {
    [self.view makeToast:@"您没有点位操作权限" duration:1 position:CSToastPositionCenter];
    return;
  }
  
  __weak typeof(self)weakSelf = self;
  [self showSystemSheetTitle:@"选择操作" message:nil buttonTitle:arr handler:^(NSString *title) {
    if ([title isEqualToString:@"变更跟进人"]) {
      [weakSelf showFollowView];
    } else if ([title isEqualToString:@"撤销审批"]) {
      [self clickRevoke];
    } else if ([title isEqualToString:@"重新打开"]) {
      [self clickReopen:@""];
    }
  }];
}

- (void)showFollowView{
  
  __weak typeof(self)weakSelf = self;
  
  
  if (self.followAlert) {
    [self.followAlert showModal];
     self.followAlert.chooseBlock = ^(NSDictionary *chooseDic) {
     
      MAWriteMeassageView *meassgaeV = [[MAWriteMeassageView alloc] initWithName:@"转交原因"];
      meassgaeV.okBlock = ^(NSString *dataString) {
        NSMutableDictionary *reasonDic = [NSMutableDictionary dictionaryWithDictionary:chooseDic];
        [reasonDic setObject:dataString forKey:@"reason"];
        [weakSelf changeFollow:reasonDic];

      };
      dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [meassgaeV showModal];
      });
      
    };
    
  } else {
    
    [self.view makeToast:@"获取跟进人失败" duration:1 position:CSToastPositionCenter];
  }
    
   
    
 
}

//修改跟进人
- (void)changeFollow:(NSDictionary *)params{
  
  NSDictionary *dic = @{@"change_reason":[params objectNilForKey:@"reason"],@"type":@"STORE_PLAN",@"ids":@[self.pointId],@"after_change_link":[NSString stringWithFormat:@"%@",[params objectForKeyNil:@"phone"]],@"user_id":[params objectForKeyNil:@"id"]};
  
  [self.loadingView showModal];
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.user.business.transfer" Params:dic success:^(NSDictionary * _Nonnull successResult) {
    [self.loadingView hideModal];
    
    //  跟进人
    NSString *followStr = [NSString stringWithFormat:@"%@",[params objectForKeyNil:@"name"]];
    NSString *followState = [[self.detailDic objectNilForKey:@"follow_state"] isEqualToString:@"CLOSE"] ? @"关闭" : [[self.detailDic objectNilForKey:@"follow_state"] isEqualToString:@"FINISH"] ? @"完成":@"跟进中";
    
    NSString *followResult = [NSString stringWithFormat:@"跟进人：%@ %@",followStr,followState];
    NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
    [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:134/255.0 green:144/255.0 blue:156/255.0 alpha:1.000000]} range:NSMakeRange(0, 4 + followStr.length)];
    
    if ([followState isEqualToString:@"跟进中"]) {
      [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:255/255.0 green:125/255.0 blue:1/255.0 alpha:1.000000]} range:NSMakeRange(5 + followStr.length, followState.length)];
      
    } else if ([followState isEqualToString:@"完成"]) {
      [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:0/255.0 green:180/255.0 blue:43/255.0 alpha:1.000000]} range:NSMakeRange(5 + followStr.length, followState.length)];
      
    } else {
      [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:245/255.0 green:63/255.0 blue:63/255.0 alpha:1.000000]} range:NSMakeRange(5 + followStr.length, followState.length)];
    }
    
    self.followLabel.attributedText = string;
   
    [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storeplan.read" Params:@{@"id":self.pointId} success:^(NSDictionary *successResult) {
      NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (![receiveData isKindOfClass:[NSDictionary class]]) {
        return;
      }
      
      if (BCDictIsEmpty(receiveData)) {
        return;
      }
      [self.statusView showModal:ToastSuccess andTitle:@"变更成功"];
      self.detailDic = receiveData;
      self.orgId = [NSString stringWithFormat:@"%@",[self.detailDic objectForKeyNil:@"org_id"]];
      
    } failure:^(NSString *errorResult) {
      
      [self.statusView showModal:ToastFail andTitle:@"变更失败"];
      
    }];
    
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHLISTDATA" object:nil];
    
  } failure:^(NSString * _Nonnull errorResult) {
    [self.loadingView hideModal];
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
}

//撤销审批
- (void)clickRevoke{
  __weak typeof(self)weakSelf = self;
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定要撤销审批?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf getRevoke];
  };
  
}

- (void)getRevoke{
  
  [self.loadingView showModal];
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.approvatask.cancel" Params:@{@"process_definition_key":@"STORE_PLAN",@"business_key":self.pointId,@"id":self.pointId} success:^(NSDictionary * _Nonnull successResult) {
    [self.loadingView hideModal];
    
  
    [self.statusView showModal:ToastSuccess andTitle:@"撤销审批成功"];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHMAPDATA" object:nil];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self resetView];
    });
    
  } failure:^(NSString * _Nonnull errorResult) {
    [self.loadingView hideModal];
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}

//重新打开
- (void)clickReopen:(NSString *)reson{
  __weak typeof(self)weakSelf = self;
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定要重新打开?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf getReopen:reson];
  };
}
- (void)getReopen:(NSString *)reson{
  [self.loadingView showModal];
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storeplan.reopen" Params:@{@"id":self.pointId,@"memo":reson} success:^(NSDictionary * _Nonnull successResult) {
    [self.loadingView hideModal];
    
   
    [self.statusView showModal:ToastSuccess andTitle:@"重新打开成功"];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHMAPDATA" object:nil];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self resetView];
    });
    
  } failure:^(NSString * _Nonnull errorResult) {
    [self.loadingView hideModal];
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
}

- (void)resetView{
  
  self.alphaV.alpha = 0;
  [self.auditScrollView removeFromSuperview];
  self.auditScrollView = nil;
  [self.pointStateScrollView removeFromSuperview];
  self.pointStateScrollView = nil;
  [self.landlordScrollView removeFromSuperview];
  self.landlordScrollView = nil;
  [self.lookScrollView removeFromSuperview];
  self.lookScrollView = nil;
  [self.shareScrollView removeFromSuperview];
  self.shareScrollView = nil;
  [self.homeScrollView removeFromSuperview];
  self.homeScrollView = nil;
  [self.mainTableView setContentOffset:CGPointMake(0, 0) animated:NO];
  [self.horizontalScrollView setContentOffset:CGPointMake(0, 0) animated:NO];
  if ([self haveObserverKeyPath:self.horizontalScrollView andKey:@"contentOffset"]) {
    [self.horizontalScrollView removeObserver:self forKeyPath:@"contentOffset"];
  }
  [self.topTabV removeFromSuperview];
  self.topTabV = nil;
  [self.indicateLayer removeFromSuperview];
  self.indicateLayer = nil;
  self.bannerView.imagesArray = @[];
  self.bannerView.delegate = nil;
  [self.bannerView removeFromSuperview];
  self.bannerView = nil;
  [self.horizontalScrollView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  [self.horizontalScrollView removeFromSuperview];
  self.horizontalScrollView = nil;
  [self.mainTableView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  [self.mainTableView.tableHeaderView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  self.mainTableView.tableHeaderView = nil;
  [self.mainTableView.tableFooterView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  self.mainTableView.tableFooterView = nil;
  self.mainTableView.delegate = nil;
  self.mainTableView.dataSource = nil;
  [self.mainTableView removeFromSuperview];
  self.mainTableView = nil;
  
  [self.bottomOpereateV.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  [self.bottomOpereateV removeFromSuperview];
  self.bottomOpereateV = nil;
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storeplan.read" Params:@{@"id":self.pointId} success:^(NSDictionary *successResult) {
    NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
    if (![receiveData isKindOfClass:[NSDictionary class]]) {
      return;
    }
    
    if (BCDictIsEmpty(receiveData)) {
      return;
    }
    self.detailDic = receiveData;
    self.orgId = [NSString stringWithFormat:@"%@",[self.detailDic objectForKeyNil:@"org_id"]];
    [self.loadingView showModal];
    [self loadMoreRequest];
    
  } failure:^(NSString *errorResult) {
   
      [self.view makeToast:errorResult duration:1 position:CSToastPositionCenter];
    
  }];
}

// 点击顶部关注按钮
- (void)clickFollow:(UIButton *)sender{
  sender.selected = !sender.selected;
  sender.userInteractionEnabled = NO;
  
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    sender.userInteractionEnabled = YES;
  });
  [sender.layer removeAllAnimations];
  
  if (sender.selected) {//去关注
    
    CAKeyframeAnimation * animation = [CAKeyframeAnimation animationWithKeyPath:@"transform"];
    animation.duration = 0.5;
    animation.removedOnCompletion = YES;
    animation.fillMode = kCAFillModeForwards;
    NSMutableArray *values = [NSMutableArray array];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(0.1, 0.1, 1.0)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(1.1, 1.1, 1.0)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(0.9, 0.9, 0.9)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(1.0, 1.0, 1.0)]];
    animation.values = values;
    
    [sender.layer addAnimation:animation forKey:nil];
    
    [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.interestuser.save" Params:@{@"business_id":self.pointId,@"module":@"STORE_PLAN"} success:^(NSDictionary * _Nonnull successResult) {
    
      
      [self.statusView showModal:ToastSuccess andTitle:@"关注成功"];
    } failure:^(NSString * _Nonnull errorResult) {
      sender.selected = YES;
      [self.statusView showModal:ToastFail andTitle:@"关注失败"];
    }];
    
  } else {//取消关注
    
    CAKeyframeAnimation * scaleAnimation = [CAKeyframeAnimation animationWithKeyPath:@"transform.scale"];
    scaleAnimation.duration = 0.5;
    scaleAnimation.values = @[@0.6, @1, @0.8, @1];
    [sender.layer addAnimation:scaleAnimation forKey:@"transform.scale"];
    
    [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.interestuser.delete" Params:@{@"business_id":self.pointId,@"module":@"STORE_PLAN"} success:^(NSDictionary * _Nonnull successResult) {
     
      [self.statusView showModal:ToastSuccess andTitle:@"取消关注成功"];
    } failure:^(NSString * _Nonnull errorResult) {
      sender.selected = YES;
      [self.statusView showModal:ToastFail andTitle:@"取消关注失败"];
    }];
  }
}

// 点击顶部选项卡
- (void)titleClick:(UIButton *)sender{
  
  if (sender != _selectTabButton) {
    
    UIImageView *iconIm = [_selectTabButton viewWithTag:100];
    if (iconIm) {
      iconIm.tintColor =  COLOR(31, 33, 38);
    }
    
    
    UILabel *titleL = [_selectTabButton viewWithTag:200];
    titleL.font = [UIFont systemFontOfSize:MutilFont(14)];
    titleL.textColor = COLOR(78, 89, 105);
    
    UILabel *numL = [_selectTabButton viewWithTag:300];
    if (numL) {
      numL.textColor = COLOR(31, 33, 38);
    }
    
    _selectTabButton.selected = NO;
    sender.selected = YES;
    
    UIImageView *iconIms = [sender viewWithTag:100];
    if (iconIms) {
      iconIms.tintColor =  COLOR(26, 106, 255);
    }
    
    UILabel *titleLs = [sender viewWithTag:200];
    titleLs.font = [UIFont systemFontOfSize:MutilFont(14) weight:UIFontWeightMedium];
    titleLs.textColor = COLOR(26, 106, 255);
    
    UILabel *numLs = [sender viewWithTag:300];
    if (numLs) {
      numLs.textColor = COLOR(26, 106, 255);
    }
    
    _selectTabButton = sender;
  
    
    //    指示条动画
    [UIView animateWithDuration:0.2 animations:^{
      self.indicateLayer.frame = CGRectMake(sender.left + (sender.width - 20)/2, sender.bottom - 3, 20, 3);
    }];
    
    [UIView animateWithDuration:0.3 animations:^{
      sender.transform = CGAffineTransformMakeScale(1.05, 1.05);
    } completion:^(BOOL finished) {
      [UIView animateWithDuration:0.3 animations:^{
        sender.transform = CGAffineTransformIdentity;
      }];
    }];
    
    //   计算选项卡滚动位置
    [self scrollSelectedViewToCenter:sender];
    
    //    计算横向滚动scrollview滚动位置
    [self.horizontalScrollView setContentOffset:CGPointMake(BCWidth * (sender.tag - 1014), 0) animated:NO];
  }
  
}

// 设置标题滚动区域的偏移量
- (void)scrollSelectedViewToCenter:(UIButton *)subView {
  
  
  CGFloat offsetX = subView.center.x - BCWidth * 0.5;
  
  if (offsetX < 0) {
    offsetX = -16;
  }
  
  CGFloat maxOffsetX = self.topTabV.contentSize.width - BCWidth;
  
  if (maxOffsetX < 0) {
    maxOffsetX = -16;
  }
  
  if (offsetX > maxOffsetX) {
    offsetX = maxOffsetX;
  }
  
  [self.topTabV setContentOffset:CGPointMake(offsetX, 0) animated:YES];
}

#pragma mark 加载轮播图
- (UIView *)BannerHeader{
  
  UIView *headV = [[UIView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, 460)];
  headV.backgroundColor = [UIColor whiteColor];
  self.bannerView = [BHInfiniteScrollView infiniteScrollViewWithFrame:CGRectMake(0, 0, self.view.bounds.size.width ,307) Delegate:self ImagesArray:@[] PlageHolderImage:[UIImage imageNamed:@"icon_pla"]];
  self.bannerView.dotSize = 6;
  self.bannerView.showPlay = YES;
  self.bannerView.dotSpacing = 5;
  self.bannerView.scrollTimeInterval = 5;
  self.bannerView.pageControlHidden = YES;
  [self.bannerView stopAutoScrollPage];
  [headV addSubview:self.bannerView];
  
  
  UIView *countV = [[UIView alloc] initWithFrame:CGRectMake(BCWidth - RealSize(42) - 8, 307 - 24 - 7, RealSize(42), 24)];
  countV.backgroundColor = ACOLOR(0, 0, 0, 0.5);
  countV.layer.cornerRadius = 12;
  [self.bannerView addSubview:countV];
  
  _indexLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, RealSize(42), 24)];
  _indexLabel.textColor = [UIColor whiteColor];
  _indexLabel.textAlignment = NSTextAlignmentCenter;
  _indexLabel.font = [UIFont systemFontOfSize:MutilFont(12) weight:UIFontWeightMedium];
  [countV addSubview:_indexLabel];
  
  
  if (![self.detailDic isKindOfClass:[NSDictionary class]] || BCDictIsEmpty(self.detailDic)) {
    return headV;
  }

  __weak typeof(self)weakSelf = self;
  //  点位名称
  NSString *name = [NSString stringWithFormat:@"%@",[self.detailDic objectNilForKey:@"name"]];
  NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
  [paragraphStyle setLineSpacing:4];
  paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
  NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:18 weight:UIFontWeightMedium],NSParagraphStyleAttributeName:paragraphStyle};
  CGFloat height = [name boundingRectWithSize:CGSizeMake(BCWidth - 32, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
  height = ceil(height);
  
  NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:name];
  [attributedString addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, [name length])];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,307 + 12,BCWidth - 32 - 10 - 44 ,height);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:18 weight:UIFontWeightMedium];
  nameLabel.numberOfLines = 0;
  nameLabel.attributedText = attributedString;
  [headV addSubview:nameLabel];
  [nameLabel addTapGestureWithBlock:^{
    UIPasteboard *pp = [UIPasteboard generalPasteboard];
    pp.string = name;
    [weakSelf.notiflyView showModal:NotiflySuccess andTitle:@"复制成功"];
  }];
  
//  复制按钮
  UIButton *copyBtn = [[UIButton alloc] initWithFrame:CGRectMake(nameLabel.right + 10, nameLabel.top,44, 24)];
  UIImageView *copyIM = [[UIImageView alloc] initWithFrame:CGRectMake(20, 2, 24, 24)];
  copyIM.image = [UIImage imageNamed:@"icon_copyname"];
  [copyBtn addSubview:copyIM];
  [headV addSubview:copyBtn];
  [copyBtn addtargetBlock:^(UIButton *button) {
    UIPasteboard *pp = [UIPasteboard generalPasteboard];
    pp.string = name;
    [weakSelf.notiflyView showModal:NotiflySuccess andTitle:@"复制成功"];
  }];
  
  
  //  多个标签
  NSMutableArray *arr = [NSMutableArray arrayWithCapacity:10];
  if (![[self.detailDic objectNilForKey:@"state"] isEqualToString:@""]) {//点位状态
    [arr addObject:[self changeState:[self.detailDic objectNilForKey:@"state"]]];
  }
  
  if (![[self.detailDic objectNilForKey:@"store_valuation"] isEqualToString:@""]) {//点位评估
    [arr addObject:[self.detailDic objectNilForKey:@"store_valuation"]];
  }
  
  if ([[self.detailDic objectNilForKey:@"allow_look"] isEqual:@(1)]) { //允许带看
    [arr addObject:@"允许带看"];
  }
  
  NSString *mainL = [NSString stringWithFormat:@"%@",[self.detailDic objectForKeyNil:@"store_type_name"]];
  if (!BCStringIsEmpty(mainL)) {
    [arr addObject:mainL];
  }
  NSString *subL = [NSString stringWithFormat:@"%@",[self.detailDic objectForKeyNil:@"sub_store_type_name"]];
  if (!BCStringIsEmpty(subL)) {
    [arr addObject:subL];
  }
  
  CGFloat w = 16;
  for (int i = 0; i < arr.count; i++) {
    
    NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:11]};
    CGFloat length = [arr[i] boundingRectWithSize:CGSizeMake(MAXFLOAT, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.width;
    length = ceil(length);
    UILabel *label = [[UILabel alloc] init];
    label.layer.cornerRadius = 3;
    label.clipsToBounds = YES;
    label.textAlignment = NSTextAlignmentCenter;
    label.frame = CGRectMake(w , nameLabel.bottom + 6, length + 6, 17);
    label.font = [UIFont systemFontOfSize:11];
    label.text = [arr objectAtIndex:i];
    [headV addSubview:label];
    
    if (i == 0) {//点位状态
      label.textColor = [UIColor whiteColor];
      if ([label.text isEqualToString:@"制单"]) {
        label.backgroundColor =  [UIColor colorWithRed:26/255.0 green:106/255.0 blue:255/255.0 alpha:1];
      } else if ([label.text isEqualToString:@"审批通过"] || [label.text isEqualToString:@"已单签"]) {
        label.backgroundColor = [UIColor colorWithRed:0/255.0 green:180/255.0 blue:43/255.0 alpha:1];
      } else if ([label.text isEqualToString:@"否决"] || [label.text isEqualToString:@"无效"]) {
        label.backgroundColor = [UIColor colorWithRed:134/255.0 green:144/255.0 blue:156/255.0 alpha:1];
      } else if ([label.text isEqualToString:@"待审批"]) {
        label.backgroundColor =  [UIColor colorWithRed:255/255.0 green:125/255.0 blue:1/255.0 alpha:1];
      } else {//双签
        label.backgroundColor =  [UIColor colorWithRed:245/255.0 green:63/255.0 blue:63/255.0 alpha:1];
      }
      
    } else if ([label.text isEqualToString:@"允许带看"]){///是否允许带看
      label.textColor = [UIColor colorWithRed:78/255.0 green:89/255.0 blue:105/255.0 alpha:1];
      label.backgroundColor = [UIColor colorWithRed:229/255.0 green:230/255.0 blue:234/255.0 alpha:1];
      
    } else if(!BCStringIsEmpty([self.detailDic objectNilForKey:@"store_valuation"]) && i == 1){//等级
      label.textColor = [UIColor whiteColor];
      label.backgroundColor = [UIColor colorWithRed:245/255.0 green:63/255.0 blue:63/255.0 alpha:1];
    } else {//主副标签
      label.textColor = [UIColor colorWithRed:78/255.0 green:89/255.0 blue:105/255.0 alpha:1];
      label.backgroundColor = [UIColor colorWithRed:229/255.0 green:230/255.0 blue:234/255.0 alpha:1];
    }
    
    w = label.frame.size.width + label.frame.origin.x + 4;
  }
  
  //  跟进人
  NSString *followStr = [self.detailDic objectNilForKey:@"follow_by"];
  NSString *followState = [[self.detailDic objectNilForKey:@"follow_state"] isEqualToString:@"CLOSE"] ? @"关闭" : [[self.detailDic objectNilForKey:@"follow_state"] isEqualToString:@"FINISH"] ? @"完成":@"跟进中";
  
  NSString *followResult = [NSString stringWithFormat:@"跟进人：%@ %@",followStr,followState];
  NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
  [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:134/255.0 green:144/255.0 blue:156/255.0 alpha:1.000000]} range:NSMakeRange(0, 4 + followStr.length)];
  
  if ([followState isEqualToString:@"跟进中"]) {
    [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:255/255.0 green:125/255.0 blue:1/255.0 alpha:1.000000]} range:NSMakeRange(5 + followStr.length, followState.length)];
    
  } else if ([followState isEqualToString:@"完成"]) {
    [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:0/255.0 green:180/255.0 blue:43/255.0 alpha:1.000000]} range:NSMakeRange(5 + followStr.length, followState.length)];
    
  } else {
    [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:245/255.0 green:63/255.0 blue:63/255.0 alpha:1.000000]} range:NSMakeRange(5 + followStr.length, followState.length)];
  }
  
  _followLabel = [[UILabel alloc] init];
  _followLabel.frame = CGRectMake(16 ,nameLabel.bottom + 27, BCWidth, 18.5);
  _followLabel.font = [UIFont systemFontOfSize:13];
  _followLabel.attributedText = string;
  [headV addSubview:_followLabel];
  
  //  点位位置
  UIButton *pointBtn = [[UIButton alloc] initWithFrame:CGRectMake(16, _followLabel.bottom + 8, BCWidth - 32, 53)];
  [pointBtn setBackgroundImage:[UIImage imageNamed:@"blue_newmap"] forState:UIControlStateNormal];
  [pointBtn addTarget:self action:@selector(lookPoint) forControlEvents:UIControlEventTouchUpInside];
  [headV addSubview:pointBtn];
  
  
  NSString *labelText = [self.detailDic objectNilForKey:@"address"];
  NSMutableAttributedString *attributedString1 = [[NSMutableAttributedString alloc] initWithString:labelText];
  NSMutableParagraphStyle *paragraphStyle1 = [[NSMutableParagraphStyle alloc] init];
  [paragraphStyle1 setLineSpacing:5];
  paragraphStyle1.lineBreakMode = NSLineBreakByCharWrapping;
  [attributedString1 addAttribute:NSParagraphStyleAttributeName value:paragraphStyle1 range:NSMakeRange(0, [labelText length])];
  
  UILabel *pointLabel = [[UILabel alloc] init];
  pointLabel.frame = CGRectMake(10 ,0, BCWidth - 88, 53);
  pointLabel.font = [UIFont systemFontOfSize:MutilFont(13)];
  pointLabel.numberOfLines = 2;
  pointLabel.attributedText = attributedString1;
  pointLabel.textColor = [UIColor colorWithRed:78/255.0 green:89/255.0 blue:105/255.0 alpha:1];
  [pointBtn addSubview: pointLabel];
  
  UIView *rightV = [[UIView alloc] initWithFrame:CGRectMake(BCWidth - 62, 0, 24, 53)];
  rightV.userInteractionEnabled = NO;
  [pointBtn addSubview:rightV];
  
  UIImageView *topIM = [[UIImageView alloc] initWithFrame:CGRectMake(2 , 9.5, 18, 18)];
  topIM.image = [UIImage imageNamed:@"blue_map"];
  [rightV addSubview:topIM];
  
  UILabel *bottomL = [[UILabel alloc] init];
  bottomL.frame = CGRectMake(0 ,topIM.bottom + 2, 24, 15);
  bottomL.font = [UIFont systemFontOfSize:11];
  bottomL.textColor = [UIColor colorWithRed:26/255.0 green:106/255.0 blue:255/255.0 alpha:1];
  bottomL.text = @"地图";
  [rightV addSubview: bottomL];
  
  headV.height = pointBtn.bottom;
  
  return headV;
}

- (NSString *)changeState:(NSString *)state{
  if ([state isEqualToString:@"INIT_PRE"]) {
    return @"制单";
  } else if ([state isEqualToString:@"INIT"]) {
    return @"待审批";
  } else if ([state isEqualToString:@"SECOND_AUDIT"]) {
    return @"审批通过";
  } else if ([state isEqualToString:@"REJECT"]) {
    return @"否决";
  } else if ([state isEqualToString:@"INVALID"]) {
    return @"无效";
  } else if ([state isEqualToString:@"STORE_BUILD"]) {
    return @"已单签";
  } else if ([state isEqualToString:@"STORE_SIGN"]) {
    return @"已双签";
  }
  
  return @"";
}

#pragma mark 点击查看地图
- (void)lookPoint{
  
  [self.bannerView stopAutoScrollPage];
  MALookPointViewController *lookVC = [[MALookPointViewController alloc] init];
  lookVC.pointName = [self.detailDic objectNilForKey:@"name"];
  lookVC.pointAddress = [self.detailDic objectNilForKey:@"address"];
  lookVC.pointLatitude = [NSString stringWithFormat:@"%@",[[self.detailDic objectNilForKey:@"info"] objectNilForKey:@"latitude"]];
  lookVC.pointLongitude = [NSString stringWithFormat:@"%@",[[self.detailDic objectNilForKey:@"info"] objectNilForKey:@"longitude"]];
  [self.navigationController pushViewController:lookVC animated:YES];
}

#pragma mark 跳转动画
- (id<UIViewControllerAnimatedTransitioning>)navigationController:(UINavigationController *)navigationController animationControllerForOperation:(UINavigationControllerOperation)operation fromViewController:(UIViewController *)fromVC toViewController:(UIViewController *)toVC
{
  if (operation == UINavigationControllerOperationNone ) {
    return nil;
  }
  
//  去查看点位地图时不执行动画
  if ([toVC isKindOfClass:[MALookPointViewController class]] || [fromVC isKindOfClass:[MALookPointViewController class]]) {
    return nil;
  }
  if ([toVC isKindOfClass:[MABuniessDetailViewController class]] || [fromVC isKindOfClass:[MABuniessDetailViewController class]]) {
    return nil;
  }
  if ([toVC isKindOfClass:[MAAddLandlordViewController class]] || [fromVC isKindOfClass:[MAAddLandlordViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MATakeLookViewController class]] || [fromVC isKindOfClass:[MATakeLookViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MAWriteFollowUpViewController class]] || [fromVC isKindOfClass:[MAWriteFollowUpViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MAEditPointViewController class]] || [fromVC isKindOfClass:[MAEditPointViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MASearchPoiViewController class]] || [fromVC isKindOfClass:[MASearchPoiViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MAChooseLocationViewController class]] || [fromVC isKindOfClass:[MAChooseLocationViewController class]]) {
    return nil;
  }
  
 
  if (self.pushType == PushAnimationTypeModal) {
    
    return [[PushZoomScaleTranstion alloc] initWithTransitionType:operation == UINavigationControllerOperationPush ?  MoveTransitionTypePush: MoveTransitionTypePop];
    
  } else if (self.pushType == PushAnimationTypeCell) {
    
    return [[PushCellScaleTranstion alloc] initWithTransitionType:operation == UINavigationControllerOperationPush ?  CellTransitionTypePush: CellTransitionTypePop];
    
  } else {
    
    return nil;
  }
}




#pragma mark *** UITableViewDataSource ***
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
  
  return 0;
  
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
  return 0.01;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
  return 70;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
  
  _indicateLayer = [[UIView alloc] init];
  _indicateLayer.layer.cornerRadius = 2;
  _indicateLayer.backgroundColor = COLOR(26, 106, 255);
  [self.topTabV addSubview:_indicateLayer];
  
  NSArray *titleArr = @[@"首页",@"房东信息",@"点位动态",@"带看记录",@"分享记录",@"审批记录"];
  NSArray *iconArr = @[@"point_home",@"point_landlord",@"point_state",@"audit_record"];
  NSArray *numArr = @[[NSString stringWithFormat:@"%lu",(unsigned long)((![self.lookArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.lookArr)) ? 0:self.lookArr.count)],[NSString stringWithFormat:@"%lu",(unsigned long)((![self.shareArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.shareArr)) ? 0:self.shareArr.count)],@"0"];
  CGFloat w = 0;
  for (int i = 0; i < titleArr.count; i ++) {
    
    NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14) weight:UIFontWeightMedium]};
    CGFloat length = [titleArr[i] boundingRectWithSize:CGSizeMake(MAXFLOAT, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.width;
    length = ceil(length);
    UIButton *titleButton = [UIButton buttonWithType:UIButtonTypeCustom];
    titleButton.frame = CGRectMake(w, 10, length , 52);
    titleButton.tag = 1014 + i;
    
    if (i < 3 || i == 5) {//图标
      UIImageView *iconIm = [[UIImageView alloc] initWithFrame:CGRectMake((length - 20)/2, 3, 20, 20)];
      if (i == 5) {
        iconIm.image = [[UIImage imageNamed:iconArr[3]] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
      } else {
        iconIm.image = [[UIImage imageNamed:iconArr[i]] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
      }
      iconIm.tag = 100;
      iconIm.tintColor = COLOR(31, 33, 38);
      [titleButton addSubview:iconIm];
      
      UILabel *titleL = [[UILabel alloc] initWithFrame:CGRectMake(0, iconIm.bottom + 2, length, 20)];
      titleL.textColor  = COLOR(78, 89, 105);
      titleL.text = titleArr[i];
      titleL.tag = 200;
      titleL.font = [UIFont systemFontOfSize:MutilFont(14)];
      [titleButton addSubview:titleL];
      
    } else {//数字
      
      UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, length, 25)];
      label.textColor  = COLOR(31, 33, 38);
      label.font = [UIFont systemFontOfSize:18];
      label.text = numArr[i - 3];
      label.tag = 300;
      label.textAlignment = NSTextAlignmentCenter;
      [titleButton addSubview:label];
      
      UILabel *titleL = [[UILabel alloc] initWithFrame:CGRectMake(0, label.bottom, length, 20)];
      titleL.textColor  = COLOR(78, 89, 105);
      titleL.font = [UIFont systemFontOfSize:MutilFont(14)];
      titleL.text = titleArr[i];
      titleL.tag = 200;
      [titleButton addSubview:titleL];
    }
    
    if (i == 0) {
      titleButton.selected = YES;
      UIImageView *iconIm = [titleButton viewWithTag:100];
      iconIm.tintColor = COLOR(26, 106, 255);
      
      UILabel *titleL = [titleButton viewWithTag:200];
      titleL.font = [UIFont systemFontOfSize:MutilFont(14) weight:UIFontWeightMedium];
      titleL.textColor = COLOR(26, 106, 255);
      _selectTabButton = titleButton;
      _indicateLayer.frame = CGRectMake(titleButton.left + (titleButton.width - 20)/2, titleButton.bottom - 3, 20, 3);
    }
    [titleButton addTarget:self action:@selector(titleClick:) forControlEvents:UIControlEventTouchUpInside];
    [self.topTabV addSubview:titleButton];
    
    w  =  titleButton.frame.size.width +  titleButton.frame.origin.x + 24;
  }
  self.topTabV.contentSize = CGSizeMake(w - 8 , 70);
 
  return self.topTabV;
}


- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  
  
  UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"nullCell1"];
  if (!cell) {
    cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:@"nullCell1"];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.backgroundColor = [UIColor greenColor];
    
  }
  
  return cell;
  
}

#pragma mark 滑动手势代理
- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
  
  
  
  if (scrollView == self.mainTableView) {//如果是竖向滑动的主scrollview滑动，则导航栏渐变
    
    
    CGFloat tmpHeight = 307  ;
    CGFloat alphaValue = MIN(1, scrollView.contentOffset.y/tmpHeight);
    
   
    self.alphaV.alpha = alphaValue;
    if (scrollView.contentOffset.y>=307) {
      scrollView.contentInset = UIEdgeInsetsMake(self.heightTop + 44, 0, 0, 0);
      [self.bannerView stopAutoScrollPage];
    } else {
      scrollView.contentInset = UIEdgeInsetsMake(0, 0, 0, 0);
      [self.bannerView startAutoScrollPage];
    }
    
    
    if (scrollView.contentOffset.y >= (scrollView.contentSize.height-scrollView.height)) {
      self.offsetType = OffsetTypeMax;
    } else if (scrollView.contentOffset.y <= 0) {
      self.offsetType = OffsetTypeMin;
    } else {
      self.offsetType = OffsetTypeCenter;
    }
    
    NSInteger selectedIndex = self.selectTabButton.tag - 1014;
    
    if (selectedIndex == 0) {
      
      if (self.homeScrollView.offsetType == OffsetTypeCenter) {
        scrollView.contentOffset = CGPointMake(scrollView.contentOffset.x, scrollView.contentSize.height-scrollView.height);
      } else {
        self.auditScrollView.contentOffset = CGPointZero;
        self.pointStateScrollView.contentOffset = CGPointZero;
        self.landlordScrollView.contentOffset = CGPointZero;
        self.lookScrollView.contentOffset = CGPointZero;
        self.shareScrollView.contentOffset = CGPointZero;
      }
      
    } else  if (selectedIndex == 5) {
      
      if (self.auditScrollView.offsetType == OffsetTypeCenter) {
        scrollView.contentOffset = CGPointMake(scrollView.contentOffset.x, scrollView.contentSize.height-scrollView.height);
      } else {
        self.homeScrollView.contentOffset = CGPointZero;
        self.pointStateScrollView.contentOffset = CGPointZero;
        self.landlordScrollView.contentOffset = CGPointZero;
        self.lookScrollView.contentOffset = CGPointZero;
        self.shareScrollView.contentOffset = CGPointZero;
      }
      
    }else  if (selectedIndex == 1) {
      
      if (self.landlordScrollView.offsetType == OffsetTypeCenter) {
        scrollView.contentOffset = CGPointMake(scrollView.contentOffset.x, scrollView.contentSize.height-scrollView.height);
      } else {
        self.auditScrollView.contentOffset = CGPointZero;
        self.pointStateScrollView.contentOffset = CGPointZero;
        self.homeScrollView.contentOffset = CGPointZero;
        self.lookScrollView.contentOffset = CGPointZero;
        self.shareScrollView.contentOffset = CGPointZero;
      }
      
    }else  if (selectedIndex == 2) {
      
      if (self.pointStateScrollView.offsetType == OffsetTypeCenter) {
        scrollView.contentOffset = CGPointMake(scrollView.contentOffset.x, scrollView.contentSize.height-scrollView.height);
      } else {
        self.homeScrollView.contentOffset = CGPointZero;
        self.auditScrollView.contentOffset = CGPointZero;
        self.homeScrollView.contentOffset = CGPointZero;
        self.lookScrollView.contentOffset = CGPointZero;
        self.shareScrollView.contentOffset = CGPointZero;
      }
      
    }else  if (selectedIndex == 3) {
      
      if (self.lookScrollView.offsetType == OffsetTypeCenter) {
        scrollView.contentOffset = CGPointMake(scrollView.contentOffset.x, scrollView.contentSize.height-scrollView.height);
      } else {
        self.homeScrollView.contentOffset = CGPointZero;
        self.auditScrollView.contentOffset = CGPointZero;
        self.pointStateScrollView.contentOffset = CGPointZero;
        self.lookScrollView.contentOffset = CGPointZero;
        self.shareScrollView.contentOffset = CGPointZero;
      }
      
    }else  if (selectedIndex == 4) {
      
      if (self.shareScrollView.offsetType == OffsetTypeCenter) {
        scrollView.contentOffset = CGPointMake(scrollView.contentOffset.x, scrollView.contentSize.height-scrollView.height);
      } else {
        self.homeScrollView.contentOffset = CGPointZero;
        self.auditScrollView.contentOffset = CGPointZero;
        self.pointStateScrollView.contentOffset = CGPointZero;
        self.lookScrollView.contentOffset = CGPointZero;
        self.landlordScrollView.contentOffset = CGPointZero;
      }
      
    }
    
  }
  
  
  
}
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
  if ([scrollView isEqual:self.horizontalScrollView]) {
    self.homeScrollView.scrollEnabled = NO;
    self.auditScrollView.scrollEnabled = NO;
    self.landlordScrollView.scrollEnabled = NO;
    self.pointStateScrollView.scrollEnabled = NO;
    self.lookScrollView.scrollEnabled = NO;
    self.shareScrollView.scrollEnabled = NO;
    
  }
}
- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
  if ([scrollView isEqual:self.horizontalScrollView]) {
    self.homeScrollView.scrollEnabled = YES;
    self.auditScrollView.scrollEnabled = YES;
    self.landlordScrollView.scrollEnabled =YES;
    self.pointStateScrollView.scrollEnabled = YES;
    self.lookScrollView.scrollEnabled = YES;
    self.shareScrollView.scrollEnabled = YES;
    
    
  }
  
}

- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView withVelocity:(CGPoint)velocity targetContentOffset:(inout CGPoint *)targetContentOffset
{
  
  
  if (scrollView == self.horizontalScrollView) {//如果是横向左右滚动的scrollview,控制选项卡按钮位置
    
    NSInteger selectIndex = targetContentOffset->x/BCWidth;
    
    UIButton *sender = [self.topTabV viewWithTag:1014 + selectIndex];
    if (sender != _selectTabButton) {
      
      
      UIImageView *iconIm = [_selectTabButton viewWithTag:100];
      if (iconIm) {
        iconIm.tintColor =  COLOR(31, 33, 38);
      }
      
      
      UILabel *titleL = [_selectTabButton viewWithTag:200];
      titleL.font = [UIFont systemFontOfSize:MutilFont(14)];
      titleL.textColor = COLOR(78, 89, 105);
      
      UILabel *numL = [_selectTabButton viewWithTag:300];
      if (numL) {
        numL.textColor = COLOR(31, 33, 38);
      }
      
      _selectTabButton.selected = NO;
      sender.selected = YES;
     
      UIImageView *iconIms = [sender viewWithTag:100];
      if (iconIms) {
        iconIms.tintColor =  COLOR(26, 106, 255);
      }
      
      UILabel *titleLs = [sender viewWithTag:200];
      titleLs.font = [UIFont systemFontOfSize:MutilFont(14) weight:UIFontWeightMedium];
      titleLs.textColor = COLOR(26, 106, 255);
      
      UILabel *numLs = [sender viewWithTag:300];
      if (numLs) {
        numLs.textColor = COLOR(26, 106, 255);
      }
      
      _selectTabButton = sender;
      
      [UIView animateWithDuration:0.3 animations:^{
        sender.transform = CGAffineTransformMakeScale(1.05, 1.05);
      } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.3 animations:^{
          sender.transform = CGAffineTransformIdentity;
        }];
      }];
      
      //   计算选项卡滚动位置
      [self scrollSelectedViewToCenter:sender];
      
    }
    
  }
}
// 进行检索获取Key
- (BOOL)haveObserverKeyPath:(NSObject*)object andKey:(NSString *)key
{
    //判断keyPath有或者无来实现防止多次重复添加和删除KVO监听
    id info = object.observationInfo;
    NSArray *array = [info valueForKey:@"_observances"];
    for (id objc in array) {
        id properties = [objc valueForKeyPath:@"_property"];
        NSString *keyPath = [properties valueForKeyPath:@"_keyPath"];
        if ([key isEqualToString:keyPath]) {
            return YES;
        }
    }
    return NO;
}
- (void)dealloc{
  NSLog(@"点位详情释放了");
  if ([self haveObserverKeyPath:self.horizontalScrollView andKey:@"contentOffset"]) {
    [self.horizontalScrollView removeObserver:self forKeyPath:@"contentOffset"];
  }
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}
- (UIStatusBarStyle)preferredStatusBarStyle{
  if (@available(iOS 13.0, *)) {
    return  UIStatusBarStyleDarkContent;
  } else {
    return UIStatusBarStyleDefault;
  }
}

#pragma mark 懒加载
- (MALoadWaveView *)loadingView{
  if (!_loadingView) {
    _loadingView = [[MALoadWaveView alloc] initWithFrame:[UIScreen mainScreen].bounds];
  }
  return _loadingView;
}


- (UITableView *)mainTableView{
  if (!_mainTableView) {
    _mainTableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, BCHeight) style:UITableViewStylePlain];
    _mainTableView.showsVerticalScrollIndicator = NO;
    _mainTableView.backgroundColor = [UIColor whiteColor];
    _mainTableView.delegate = self;
    _mainTableView.dataSource = self;
    _mainTableView.bounces = NO;
    _mainTableView.alpha = 0;
    _mainTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    if (@available(iOS 15.0, *)) {
      _mainTableView.sectionHeaderTopPadding = 0;
    }
    _mainTableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    
  }
  
  return _mainTableView;
}

- (UIScrollView*)horizontalScrollView{
  if (!_horizontalScrollView) {
    _horizontalScrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, BCHeight - (self.heightTop + 44 + 70))];
    _horizontalScrollView.contentSize = CGSizeMake(BCWidth * 6,  0);
    _horizontalScrollView.showsHorizontalScrollIndicator = NO;
    _horizontalScrollView.backgroundColor = COLOR(242, 243, 245);
    _horizontalScrollView.delegate = self;
    _horizontalScrollView.bounces = NO;
    _horizontalScrollView.pagingEnabled = YES;
    
  }
  
  return _horizontalScrollView;
}
- (UIScrollView*)topTabV{
  if (!_topTabV) {
    _topTabV = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, 70)];
    _topTabV.contentInset = UIEdgeInsetsMake(0, 16, 0, 0);
    _topTabV.backgroundColor = [UIColor whiteColor];
    _topTabV.showsHorizontalScrollIndicator=NO;
    _topTabV.showsVerticalScrollIndicator=NO;
    
  }
  
  return _topTabV;
}


- (MAUIScrollView *)homeScrollView{
  if (!_homeScrollView) {
    _homeScrollView = [[MAUIScrollView alloc] initWithFrame: CGRectMake(0, 0, BCWidth, BCHeight - (self.heightTop + 44 + 70 + 90))];
    _homeScrollView.showsVerticalScrollIndicator = NO;
    _homeScrollView.backgroundColor = COLOR(242, 243, 245);
    _homeScrollView.mainVC = self;
    _homeScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
  }
  
  return _homeScrollView;
}

- (MAUIScrollView *)landlordScrollView{
  if (!_landlordScrollView) {
    _landlordScrollView = [[MAUIScrollView alloc] initWithFrame: CGRectMake(BCWidth , 0, BCWidth, BCHeight - (self.heightTop + 44 + 70 + 90))];
    _landlordScrollView.showsVerticalScrollIndicator = NO;
    _landlordScrollView.backgroundColor =  COLOR(242, 243, 245);
    _landlordScrollView.mainVC = self;
    _landlordScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
  }
  
  return _landlordScrollView;
}

- (MAUIScrollView *)pointStateScrollView{
  if (!_pointStateScrollView) {
    _pointStateScrollView = [[MAUIScrollView alloc] initWithFrame: CGRectMake(BCWidth * 2, 0, BCWidth, BCHeight - (self.heightTop + 44 + 70 + 90))];
    _pointStateScrollView.showsVerticalScrollIndicator = NO;
    _pointStateScrollView.backgroundColor = COLOR(242, 243, 245);
    _pointStateScrollView.mainVC = self;
    _pointStateScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
  }
  
  return _pointStateScrollView;
}


- (MAUIScrollView *)lookScrollView{
  if (!_lookScrollView) {
    _lookScrollView = [[MAUIScrollView alloc] initWithFrame: CGRectMake(BCWidth * 3, 0, BCWidth,BCHeight - (self.heightTop + 44 + 70 + 90))];
    _lookScrollView.showsVerticalScrollIndicator = NO;
    _lookScrollView.backgroundColor = COLOR(242, 243, 245);
    _lookScrollView.mainVC = self;
    _lookScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
  }
  
  return _lookScrollView;
}
- (MAUIScrollView *)shareScrollView{
  if (!_shareScrollView) {
    _shareScrollView = [[MAUIScrollView alloc] initWithFrame: CGRectMake(BCWidth * 4, 0, BCWidth, BCHeight - (self.heightTop + 44 + 70 + 90))];
    _shareScrollView.showsVerticalScrollIndicator = NO;
    _shareScrollView.backgroundColor = COLOR(242, 243, 245);
    _shareScrollView.mainVC = self;
    _shareScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
  }
  
  return _shareScrollView;
}
- (MAUIScrollView *)auditScrollView{
  if (!_auditScrollView) {
    _auditScrollView = [[MAUIScrollView alloc] initWithFrame: CGRectMake(BCWidth * 5, 0, BCWidth, BCHeight - (self.heightTop + 44 + 70 + 90))];
    _auditScrollView.showsVerticalScrollIndicator = NO;
    _auditScrollView.backgroundColor = COLOR(242, 243, 245);
    _auditScrollView.mainVC = self;
    _auditScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
  }
  
  return _auditScrollView;
}

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context {
  
  if (object == self.horizontalScrollView) {
    if ([keyPath isEqualToString:@"contentOffset"]) {
      // 当scrolllView滚动时,让跟踪器跟随scrollView滑动
      [self prepareMoveTrackerFollowScrollView:self.horizontalScrollView];
    }
  } else {
    [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
  }
}

- (void)prepareMoveTrackerFollowScrollView:(UIScrollView *)scrollView {
  // 这个if条件的意思是scrollView的滑动不是由手指拖拽产生
  if (!scrollView.isDragging && !scrollView.isDecelerating) {return;}
  
  // 当滑到边界时，继续通过scrollView的bouces效果滑动时，直接return
  if (scrollView.contentOffset.x < 0 || scrollView.contentOffset.x > scrollView.contentSize.width-scrollView.bounds.size.width) {
    return;
  }
  
  // 当前偏移量
  CGFloat currentOffSetX = scrollView.contentOffset.x;
  // 偏移进度
  CGFloat offsetProgress = currentOffSetX / scrollView.bounds.size.width;
  CGFloat progress = offsetProgress - floor(offsetProgress);
  
  NSInteger fromIndex = 0;
  NSInteger toIndex = 0;
  // 初始值不要等于scrollView.contentOffset.x,因为第一次进入此方法时，scrollView.contentOffset.x的值已经有一点点偏移了，不是很准确
  _beginOffsetX = scrollView.bounds.size.width * (self.selectTabButton.tag - 1014);
  
  if (currentOffSetX - _beginOffsetX > 0) { // 向左拖拽了
    // 求商,获取上一个item的下标
    fromIndex = currentOffSetX / scrollView.bounds.size.width;
    // 当前item的下标等于上一个item的下标加1
    toIndex = fromIndex + 1;
    if (toIndex >= 6) {
      toIndex = fromIndex;
    }
  } else if (currentOffSetX - _beginOffsetX < 0) {  // 向右拖拽了
    toIndex = currentOffSetX / scrollView.bounds.size.width;
    fromIndex = toIndex + 1;
    progress = 1.0 - progress;
    
  } else {
    progress = 1.0;
    fromIndex = (self.selectTabButton.tag - 1014);
    toIndex = fromIndex;
  }
  
  if (currentOffSetX == scrollView.bounds.size.width * fromIndex) {// 滚动停止了
    progress = 1.0;
    toIndex = fromIndex;
  }
  
  [self moveTrackerWithProgress:progress fromIndex:fromIndex toIndex:toIndex currentOffsetX:currentOffSetX beginOffsetX:_beginOffsetX];
}
// 这个方法才开始真正滑动跟踪器，上面都是做铺垫
- (void)moveTrackerWithProgress:(CGFloat)progress fromIndex:(NSInteger)fromIndex toIndex:(NSInteger)toIndex currentOffsetX:(CGFloat)currentOffsetX beginOffsetX:(CGFloat)beginOffsetX {
  
  UIButton *fromButton = [self.topTabV viewWithTag:1014 + fromIndex];
  UIButton *toButton = [self.topTabV viewWithTag:1014 + toIndex];
  
  // 2个按钮之间的距离
  CGFloat xDistance = toButton.center.x - fromButton.center.x;
  // 2个按钮宽度的差值
  CGFloat wDistance = toButton.frame.size.width - fromButton.frame.size.width;
  
  CGRect newFrame = self.indicateLayer.frame;
  CGPoint newCenter = self.indicateLayer.center;
  
  CGFloat _trackerWidth = 20;
  
  CGFloat originX = fromButton.frame.origin.x+(fromButton.frame.size.width-(_trackerWidth ? _trackerWidth : fromButton.titleLabel.font.pointSize))*0.5;
  // 原先的宽度
  CGFloat originW = _trackerWidth ? _trackerWidth : fromButton.titleLabel.font.pointSize;
  if (currentOffsetX - _beginOffsetX >= 0) { // 向左拖拽了
    if (progress < 0.5) {
      newFrame.origin.x = originX; // x值保持不变
      newFrame.size.width = originW + xDistance * progress * 2;
    } else {
      newFrame.origin.x = originX + xDistance * (progress-0.5) * 2;
      newFrame.size.width = originW + xDistance - xDistance * (progress-0.5) * 2;
    }
  } else { // 向右拖拽了
    // 此时xDistance为负
    if (progress < 0.5) {
      newFrame.origin.x = originX + xDistance * progress * 2;
      newFrame.size.width = originW - xDistance * progress * 2;
    } else {
      newFrame.origin.x = originX + xDistance;
      newFrame.size.width = originW - xDistance + xDistance * (progress-0.5) * 2;
    }
  }
  
  self.indicateLayer.frame = newFrame;
}
#pragma mark 轮播图代理
- (void)infiniteScrollView:(BHInfiniteScrollView *)infiniteScrollView didScrollToIndex:(NSInteger)index{
  _indexLabel.text = [NSString stringWithFormat:@"%ld/%lu",(long)(index + 1),(unsigned long)self.bannerImages.count];
  
  //  处理标签滚动
  UIButton *sender;
  for (NSDictionary *dic in self.bannerLabels) {
    if (index >= [[dic objectNilForKey:@"minIndex"] integerValue] && index < [[dic objectNilForKey:@"maxIndex"] integerValue]) {
      sender = [ _bannerLabelV viewWithTag:600 + [self.bannerLabels indexOfObject:dic]];
      
      break;
    }
  }
  
  if (sender && sender != _selectTabButton) {
    self.selectBannerButton.selected = NO;
    sender.selected = YES;
    self.selectBannerButton = sender;
    
    //    滑块动画
    [UIView animateWithDuration:0.25 animations:^{
      self.swipeLayer.frame = CGRectMake(sender.frame.origin.x + 2, 2,sender.frame.size.width - 4, 20);
    }];
    
  }
  
  //  处理图片
  if (self.pushType == PushAnimationTypeModal) {
    if (infiniteScrollView.cellShowImage) {
      self.showImage = infiniteScrollView.cellShowImage;
    } else {
      if (index == self.scrollIndex) {
        self.showImage = self.tmpIM.image;
      } else {
        NSString *imageUrl = [self.bannerImages objectAtIndexCheck:index];
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
          NSData *data = [NSData dataWithContentsOfURL: [NSURL URLWithString:[imageUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
          self.showImage = [UIImage imageWithData:data];
        });
      }
      
    }
  }
  
}

#pragma mark 点击轮播图
- (void)infiniteScrollView:(BHInfiniteScrollView *)infiniteScrollView didSelectItemAtIndex:(NSInteger)index{
  
  //   视频播放
  
  NSString *fileUrl = [self.bannerImages objectAtIndexCheck:index];
  NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
  if ([temArr containsObject:[fileUrl pathExtension]]) {
    NSString *imageUrl = [[self.bannerImages objectAtIndexCheck:index] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    NSInteger imageIndex  = [self.previewUrls indexOfObject:imageUrl];
    
    if (imageIndex != NSNotFound) {
      [ZKPhotoBrowser showWithImageUrls:self.previewUrls currentPhotoIndex:imageIndex sourceSuperView:infiniteScrollView];
    }
  } else {
    NSString *videoUrl =  [fileUrl substringToIndex:[fileUrl rangeOfString:@"?spm"].location];
    if (BCStringIsEmpty(videoUrl)) {
      return;
    }
    NSArray *temArr = @[@"mp4",@"mov",@"MP4",@"MOV",@"WMV",@"AVI",@"MKV",@"wmv",@"avi",@"mkv"];
    NSURL * url = [NSURL URLWithString:[videoUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
    AVPlayerViewController * pVC = [[AVPlayerViewController alloc] init];
    pVC.player = [AVPlayer playerWithURL:url];
//    pVC.allowsPictureInPicturePlayback = NO;
    [self presentViewController:pVC animated:YES completion:nil];
    [pVC.player play];
  }
  
}

// 底部sheet弹窗
-(void)showSystemSheetTitle:(NSString *)title message:(NSString *)message buttonTitle:(NSArray *)buttonArray handler:(void (^)(NSString *)) handler{
  
  
  
  UIAlertController *sheetController = [UIAlertController alertControllerWithTitle:title message:message preferredStyle:UIAlertControllerStyleActionSheet];
  for (int i = 0; i < buttonArray.count; i ++) {
    
    UIAlertAction *confimAction = [UIAlertAction actionWithTitle:buttonArray[i] style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
      if (handler) {
        handler((buttonArray[i]));
      }
      
    }];
    [sheetController addAction:confimAction];
  }
  
  UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];
  [sheetController addAction:cancelAction];
  [self presentViewController:sheetController animated:YES completion:nil];
}

- (NSString *)getAmountInWords:(NSString *)money{
  if (money.length == 0) {
    return @"";
  }
  if (money.floatValue == 0) {
    return @"零圆整";
  }
  //大写数字
  NSArray *upperArray = @[ @"零",@"壹",@"贰",@"叁",@"肆",@"伍",@"陆",@"柒",@"捌",@"玖" ];
  /** 整数部分的单位 */
  NSArray *measureArray = @[ @"", @"拾", @"佰", @"仟"];
  /** 整数部分的单位 */
  NSArray *intUnit = @[@"圆", @"万", @"亿"];
  /** 小数部分的单位 */
  NSArray *floatUnitArray = @[ @"角", @"分" ];
  
  NSString *upIntNum = [NSString string];
  NSString *upFloatNum = [NSString string];
  NSArray *numArray = [money componentsSeparatedByString:@"."];
  
  NSString *str1 = [numArray objectAtIndex:0];
  NSInteger num1 = str1.integerValue;
  for (int i = 0; i < intUnit.count && num1 > 0; i++) {//这一部分就是单纯的转化
    NSString *temp = @"";
    int tempNum = num1%10000;
    if (tempNum != 0 || i == 0) {
      for (int j = 0; j < measureArray.count && num1 > 0; j++) {
        temp = [NSString stringWithFormat:@"%@%@%@", [upperArray objectAtIndex:num1%10], [measureArray objectAtIndex:j],temp];//每次转化最后一位数
        num1 = num1/10;//数字除以10
      }
      upIntNum = [[temp stringByAppendingString:[intUnit objectAtIndex:i]] stringByAppendingString:upIntNum];
    } else {
      num1 /= 10000;
      temp = @"零";
      upIntNum = [temp stringByAppendingString:upIntNum];
    }
    
  }
  
  for (int m = 1; m < measureArray.count; m++) { //把零佰零仟这种情况转为零
    NSString *lingUnit = [@"零" stringByAppendingString:[measureArray objectAtIndex:m]];
    upIntNum = [upIntNum stringByReplacingOccurrencesOfString:lingUnit withString:@"零"];
  }
  
  while ([upIntNum rangeOfString:@"零零"].location != NSNotFound) {//多个零相邻的保留一个零
    upIntNum = [upIntNum stringByReplacingOccurrencesOfString:@"零零" withString:@"零"];
  }
  for (int k = 0; k < intUnit.count * 2; k++) { //零万、零亿这种情况转化为万零
    NSString *unit = [intUnit objectAtIndex:k%intUnit.count];
    NSString *lingUnit = [@"零" stringByAppendingString:unit];
    upIntNum = [upIntNum stringByReplacingOccurrencesOfString:lingUnit withString:[unit stringByAppendingString:@"零"]];
  }
  
  if (numArray.count == 2) {//小数部分转化
    NSString *floatStr = [numArray objectAtIndex:1];
    for (NSInteger i = floatStr.length; i > 0; i--) {
      NSString *temp = [floatStr substringWithRange:NSMakeRange(floatStr.length - i, 1)];
      NSInteger tempNum = temp.integerValue;
      if (tempNum == 0) continue;
      NSString *upNum = [upperArray objectAtIndex:tempNum];
      NSString *unit = [floatUnitArray objectAtIndex:floatStr.length - i];
      if (i < floatStr.length && upFloatNum.length == 0 && upIntNum.length > 0) {
        upFloatNum = @"零";
      }
      upFloatNum = [NSString stringWithFormat:@"%@%@%@", upFloatNum, upNum, unit];
    }
  }
  if (upFloatNum.length == 0) {
    upFloatNum = @"整";
  }
  
  NSString *amountInWords = [NSString stringWithFormat:@"%@%@", upIntNum, upFloatNum];
  
  while ([amountInWords rangeOfString:@"零零"].location != NSNotFound) {//再次除去多余的零
    amountInWords = [amountInWords stringByReplacingOccurrencesOfString:@"零零" withString:@"零"];
  }
  
  if ([amountInWords rangeOfString:@"零整"].location != NSNotFound) {
    amountInWords = [amountInWords stringByReplacingOccurrencesOfString:@"零整" withString:@"整"];
  }
  
  return amountInWords;
  
}
- (NSString *)numberToChinese:(NSString *)number{
  

  NSArray *nums = @[@"零",@"一", @"二", @"三", @"四", @"五", @"六", @"七", @"八", @"九"];
  NSString *inStr = [NSString stringWithFormat:@"%@",number];
  return [nums objectAtIndexCheck:[inStr integerValue]];
}

- (MABottomOpereateView *)operatV{
  if (!_operatV) {
    _operatV = [[MABottomOpereateView alloc]initWithArray:@[@"删除",@"复制"]];
  }
  return _operatV;
}

- (NSString *)removeSpaceAndNewline:(NSString *)str{
   
    NSString *temp = [str stringByReplacingOccurrencesOfString:@" " withString:@""];
    temp = [temp stringByReplacingOccurrencesOfString:@"\r" withString:@""];
    temp = [temp stringByReplacingOccurrencesOfString:@"\n" withString:@""];
    return temp;
}

- (MATopToastView *)statusView{
  if (!_statusView) {
    _statusView = [[MATopToastView alloc] initCustomViewWithDuration:1];
  }
  
  return _statusView;
}

- (MATopNotiflyView *)notiflyView{
  if (!_notiflyView) {
    _notiflyView = [[MATopNotiflyView alloc] initCustomViewWithDuration:1];
  }
  return _notiflyView;
}
@end


