// 工作台页面
import React, {useEffect, useMemo} from 'react';
import {NativeModules, Platform, StyleSheet} from 'react-native';
import {getSwipers, getAccessApps} from './server';
import PageWarpper from '@xlb/business-base/src/components/pageWarpper';
import useStore from './store';
import {authModel} from '@xlb/business-base/src/models/auth';
import {systemData, SystemDataProp} from '@xlb/common/src/utils/dataSystem';
import Tabs from './components/xlb-tab-temporarily';
import {
  applicationFields,
  AppModule,
  tabBarFields,
} from '@xlb/common/src/config/fields';
import useHasAuth from '@xlb/common/src/hooks/useHasAuth';
import Card from '../staging/components/SystemMode/Card';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttp';
import Toast from 'react-native-root-toast';
import {$modalAlert} from '@xlb/common/src/components/SecondModal';
import AnalyticsUtil from '@xlb/common/src/utils/AnalyticsUtil';
import PermissionHandler from '@xlb/common/src/utils/PermissionHandler';
import {useNavigation} from '@react-navigation/native';
import Loading from '@xlb/common/src/components/RootView/Loading';
import useSelectStore from '@xlb/business-base/src/screens/home/<USER>/ErpHome/store';
import union from 'lodash/union';
import {
  handlePreloadPage,
  preWebView,
} from '@xlb/common/src/navigation/preLoadRouter';
import XLBStorage from 'src/utils/storage';
const ScanService = NativeModules.ScanStr;
const Staging: React.FC = () => {
  const {setSwiperPics, setQuickAccessApps} = useStore((state: any) => state);
  const position = useSelectStore((state: any) => state.position);
  const store_id = authModel?.state?.userInfos?.store_id;

  // 获取应用
  const getApps = async () => {
    try {
      // 先从本地存储读取数据
      const localApps = await XLBStorage.getItem('quickAccessApps');
      if (localApps && localApps.length > 0) {
        // 如果本地有数据，先设置本地数据
        setQuickAccessApps(localApps);
      }

      // 然后请求接口获取最新数据
      const res = await getAccessApps({});
      if (res?.code === 0) {
        const serverApps = res?.data || [];

        // 比较本地数据和服务器数据是否一致
        const isDataChanged =
          JSON.stringify(localApps) !== JSON.stringify(serverApps);

        if (isDataChanged) {
          // 如果数据不一致，更新本地存储和状态
          XLBStorage.setItem('quickAccessApps', serverApps);
          setQuickAccessApps(serverApps);
        }
      }
    } catch (error) {
      console.error('获取应用数据失败:', error);
      // 如果接口请求失败，尝试使用本地数据
      const localApps = await XLBStorage.getItem('quickAccessApps');
      if (localApps && localApps.length > 0) {
        setQuickAccessApps(localApps);
      }
    }
  };

  useEffect(() => {
    getApps();
  }, []);

  if (Platform.OS === 'android') {
    // ScanService.startBoardcast()
  }

  const kmsSplitedModule = ['CRM', 'SDS'];
  const authorities = authModel?.state?.userInfos?.authorities ?? [];
  const authoritiesKey = union(authorities.map(item => item.app_type));
  console.log('authorities', authoritiesKey);
  console.log(systemData, 'systemData');

  const systemTabs: SystemDataProp[] = authModel?.state?.userInfos.supplier
    ? systemData.filter(item => authoritiesKey.includes(item.value))
    : systemData.filter(item => {
        const module = kmsSplitedModule.includes(item.value)
          ? 'KMS'
          : item?.value === 'BPM'
            ? 'OA'
            : item.value;

        console.log(module, 'module');

        if (module === 'OA') {
          return authorities.some(
            v =>
              v.app_type === module && v.name === '审批' && v.action === '编辑',
          );
        }
        return authorities.some(v => v.app_type === module);
      });

  console.log('systemTabs', systemTabs);

  const getList = system => {
    const applicationTitles = tabBarFields[system]?.application;
    const apps = applicationFields[system];

    const showList = applicationTitles /* 过滤没有应用的模块区域 */
      ?.filter(
        /* applicationTitle = { key: 'purchase', title: '采购管理' }, */
        (applicationTitle: any) =>
          apps?.find(
            (app: any) =>
              useHasAuth(app.isHasAuth, false, app?.appType) &&
              app.moduleName === applicationTitle.title,
          ),
      );
    return {showList, apps};
  };

  const list = useMemo(() => {
    return systemTabs
      .map(item => {
        const {showList, apps} = getList(item.value);
        return {showList, apps, ...item};
      })
      .filter(item => item.showList.length > 0);
    // 过滤一下，只有底下有菜单的才展示
  }, [systemTabs]);

  const navigation = useNavigation<any>();
  const goTo = async ({route, params, apps, system}) => {
    let obj = {};

    console.log('apps11111111111111', apps);
    if (apps) {
      obj = apps.find((item: any) => item.route === route);
      AnalyticsUtil[Platform.OS === 'ios' ? 'onPageBegin' : 'onPageStart'](
        `${system}-${route}-${obj?.moduleName || ''}-${obj?.name || ''}`,
      );
    }
    if (
      route == 'LoadManager' ||
      route == 'DistributionManager' ||
      route == 'SignManager'
    ) {
      // Loading.show()
      const checkResult = await PermissionHandler.checkLocationPermission();
      if (!checkResult) {
        //打开定位设置
        await PermissionHandler.requestLocationPermission();
        Loading.hide();
        return;
      }
      if (!(position?.latitude && position?.longitude)) {
        Toast.show('系统定位服务没有开启，请打开定位服务开关，重启app尝试');
        return;
      }
      if (route == 'LoadManager') {
        const params_res = await ErpHttp.post<CommonResponse>(
          '/tms/hxl.tms.param.find',
          {
            store_id,
            strategy_param: 'BASE_PARAM',
          },
        );
        if (params_res.code == 0) {
          const find = (params_res.data.loaded_check_stores ?? []).find(
            (item: any) => item.store_ids.includes(store_id),
          );
          if (find) {
            const check_res = await ErpHttp.post<CommonResponse>(
              '/tms/hxl.tms.loading.check',
              {
                store_id: authModel?.state?.userInfos?.store_id,
              },
            );
            if (!check_res.data && find.type == 'REMIND') {
              Toast.show('存在未送达单据，请尽快处理！');
              $modalAlert(
                '提示',
                `存在未送达单据，请尽快处理！`,
                () => {
                  navigation.navigate(route, {
                    ...(params || {}),
                    system: system,
                    pageName: obj?.name || '',
                    moduleName: obj?.moduleName || '',
                  });
                },
                () => {
                  // resovle(false);
                },
              );
              return;
            } else if (!check_res.data && find.type == 'FORBIDDEN') {
              Toast.show('存在未送达门店，禁止操作！');
              return;
            } else {
              navigation.navigate(route, {
                ...(params || {}),
                system: system,
                pageName: obj?.name || '',
                moduleName: obj?.moduleName || '',
              });
            }
          } else {
            navigation.navigate(route, {
              ...(params || {}),
              system: system,
              pageName: obj?.name || '',
              moduleName: obj?.moduleName || '',
            });
          }
        }
      } else {
        console.log('apps2222222222', apps);
        navigation.navigate(route, {
          ...(params || {}),
          system: system,
          pageName: obj?.name || '',
          moduleName: obj?.moduleName || '',
        });
      }
    } else {
      console.log('apps333333333', apps);
      console.log('route', system, route, obj?.name, obj?.moduleName);
      if (preWebView[route]) {
        console.log('进入预加载模块');
        handlePreloadPage(navigation, {
          path: preWebView[route]?.url,
          appType: system,
        });
      } else {
        //在这里跳转
        console.log('进入正常跳转模块', route);

        navigation.navigate(route, {
          ...(params || {}),
          system: system,
          pageName: obj?.name || '',
          moduleName: obj?.moduleName || '',
        });
      }
    }
  };
  const onRoute = route => goTo(route);

  return (
    <PageWarpper isSearch>
      <Tabs
        isSync={false}
        cacheTab={true}
        data={list.map(item => ({...item, label: item.name}))}
        goTo={onRoute}>
        {list.map((element, index) => {
          return (
            <Tabs.Item key={element.value} id={element.value}>
              {element.showList.map((item, i) => {
                return (
                  <Card
                    key={`module-${i}`}
                    title={item.title}
                    data={element.apps.filter(
                      (app: AppModule) =>
                        useHasAuth(app.isHasAuth, false, app?.appType) &&
                        !app.hiddenMenu,
                    )}
                    onRoute={route =>
                      onRoute({route, app: element.apps, system: element.value})
                    }
                  />
                );
              })}
            </Tabs.Item>
          );
        })}
      </Tabs>
    </PageWarpper>
  );
};

export default Staging;

const styles = StyleSheet.create({
  container: {
    height: '100%',
    backgroundColor: '#F5F6FA',
  },
  content_wrap: {
    // paddingVertical: 8,
  },
});
