//
//  MACooperateTableViewCell.m
//  xlb
//
//  Created by 莱昂纳多·迪卡普里奥 on 2024/11/25.
//

#import "MACooperateTableViewCell.h"
#import "UIView+Common.h"

#define BCWidth   [UIScreen mainScreen].bounds.size.width
#define BCHeight  [UIScreen mainScreen].bounds.size.height
#define COLOR(R, G, B) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:1]
#define ACOLOR(R, G, B,A) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:A]
#define BCStringIsEmpty(str) ([str isKindOfClass:[NSNull class]] || str == nil || [str length]< 1 ? YES : NO || [str isEqualToString:@"null"] || [str isEqualToString:@"<null>"])

#define RealSize(value)  MAX(round(value * [UIScreen mainScreen].bounds.size.width / 400.0), value)
#define MutilFont(value)  [UIScreen mainScreen].bounds.size.width > 420 ? (value + 2) : value

@implementation MACooperateTableViewCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

  if (selected) {
    [self.backV.layer removeAllAnimations];
    CAKeyframeAnimation *keyframeAni = [CAKeyframeAnimation animationWithKeyPath:@"transform.translation.x"];
    keyframeAni.duration = 0.25;
    keyframeAni.values = @[@0, @(-12), @0, @(12), @0];
    keyframeAni.repeatCount = 2;
    [self.backV.layer addAnimation:keyframeAni forKey:nil];
    self.userInteractionEnabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      self.userInteractionEnabled = YES;
    });
  }
}



- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
  
  if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
    self.contentView.backgroundColor = COLOR(242, 243, 245);
    [self initView];
  }
  
  return self;
}

- (void)initView {
  
  _backV = [[UIView alloc] initWithFrame:CGRectMake(RealSize(12), RealSize(12),BCWidth - RealSize(24) , RealSize(100))];
  _backV.backgroundColor = [UIColor whiteColor];
  _backV.layer.cornerRadius = 12;
  if (@available(iOS 13.0, *)) {
    self.backV.layer.cornerCurve = kCACornerCurveContinuous;
  }
  [self.contentView addSubview:_backV];
  
  _pointIM = [[UIImageView alloc] initWithFrame:CGRectMake(RealSize(10), RealSize(10), RealSize(102), RealSize(80))];
  _pointIM.layer.cornerRadius = 6;
  _pointIM.clipsToBounds = YES;
  _pointIM.backgroundColor = [UIColor whiteColor];
  _pointIM.contentMode = UIViewContentModeScaleAspectFill;
  [_backV addSubview:_pointIM];
  
  _pointName = [[UILabel alloc] initWithFrame:CGRectMake(_pointIM.right + RealSize(10), RealSize(10), _backV.width - RealSize(132), RealSize(22))];
  _pointName.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  _pointName.textColor = COLOR(31, 33, 38);
  [_backV addSubview:_pointName];
  
  _pointAdress = [[UILabel alloc] initWithFrame:CGRectMake(_pointIM.right + RealSize(10), _pointName.bottom + 4, _backV.width - RealSize(132), RealSize(19))];
  _pointAdress.font = [UIFont systemFontOfSize:MutilFont(14)];
  _pointAdress.textColor = COLOR(31, 33, 38);
  _pointAdress.numberOfLines = 2;
  [_backV addSubview: _pointAdress];
  
  _startTime = [[UILabel alloc] initWithFrame:CGRectMake(_pointIM.right + RealSize(10), RealSize(73.5), _backV.width - RealSize(132), RealSize(16.5))];
  _startTime.font = [UIFont systemFontOfSize:MutilFont(12)];
  _startTime.textColor = ACOLOR(30, 33, 38, 0.45);
  [_backV addSubview: _startTime];
}

- (void)setDataForAddress:(NSString *)address{
  
  if (![address isKindOfClass:[NSString class]] || BCStringIsEmpty(address)) {
    return;
  }
  
  NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)]};
  CGFloat height = [address boundingRectWithSize:CGSizeMake(_backV.width - RealSize(142), MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
  height = ceil(height);
  _pointAdress.text = address;
  _pointAdress.height = height > RealSize(38) ? RealSize(38) : height < RealSize(19) ? RealSize(19) : height;
}
@end
