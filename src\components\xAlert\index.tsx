import { Toast } from 'native-base'
import XAlert from './XAlert'
import React from 'react'

export const $modalAlert = (title = '', content: any = '', onConfirm: any, onCancel = null as any, cancelText = '取消', confirmText = '确认',showButton=true,contanerStyle={}) => {  
  const id = 'toast-alert'
  if (!Toast.isActive(id)) {
    Toast.show({
      id,
      duration: null,
      render: (props) => {
        return typeof content === 'string' ? (
          <XAlert
            show
            animationPreset={'slide'}
            title={title}
            content={content}
            cancelText={cancelText}
            confirmText={confirmText}
            onCancel={() => {
              Toast.close(props.id)
              onCancel && onCancel()
            }}
            onConfirm={() => {
              Toast.close(props.id)
              onConfirm && onConfirm()
            }}
          />
        ) : (
          <XAlert
            show
            animationPreset={'slide'}
            title={title}
            showButton={showButton}
            cancelText={cancelText}
            confirmText={confirmText}
            onCancel={() => {
              Toast.close(props.id)
              onCancel && onCancel()
            }}
            contanerStyle={contanerStyle}
            onConfirm={() => {
              Toast.close(props.id)
              onConfirm && onConfirm()
            }}
          >
            {content}
          </XAlert>
        )
      },
    })
  }
}
