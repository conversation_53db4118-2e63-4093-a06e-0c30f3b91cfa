// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		07598A9F619F456AA4872AA7 /* HarmonyOS_Sans_Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5A8570CD27644EDD9F1299A1 /* HarmonyOS_Sans_Bold.ttf */; };
		0D8739E92E260226005B375C /* icon_showmap.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87385A2E260226005B375C /* icon_showmap.png */; };
		0D8739EA2E260226005B375C /* bottom_copy.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87388B2E260226005B375C /* bottom_copy.png */; };
		0D8739EB2E260226005B375C /* audit_reject.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738802E260226005B375C /* audit_reject.png */; };
		0D8739EC2E260226005B375C /* bottom_edit.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87388A2E260226005B375C /* bottom_edit.png */; };
		0D8739ED2E260226005B375C /* map_oregen.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738712E260226005B375C /* map_oregen.png */; };
		0D8739EE2E260226005B375C /* icon_red.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738DF2E260226005B375C /* icon_red.png */; };
		0D8739EF2E260226005B375C /* add.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738982E260226005B375C /* add.png */; };
		0D8739F02E260226005B375C /* icon_newsearch.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738DA2E260226005B375C /* icon_newsearch.png */; };
		0D8739F12E260226005B375C /* move_figer.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738F32E260226005B375C /* move_figer.png */; };
		0D8739F22E260226005B375C /* draw_point.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738642E260226005B375C /* draw_point.png */; };
		0D8739F32E260226005B375C /* icon_liscen.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738D42E260226005B375C /* icon_liscen.png */; };
		0D8739F42E260226005B375C /* file_music.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738B12E260226005B375C /* file_music.png */; };
		0D8739F52E260226005B375C /* buniess_minigreen.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738A62E260226005B375C /* buniess_minigreen.png */; };
		0D8739F62E260226005B375C /* map_blue.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87386E2E260226005B375C /* map_blue.png */; };
		0D8739F72E260226005B375C /* audit_withdraw.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738822E260226005B375C /* audit_withdraw.png */; };
		0D8739F82E260226005B375C /* look_eye.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738EB2E260226005B375C /* look_eye.png */; };
		0D8739F92E260226005B375C /* infonomal.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87386A2E260226005B375C /* infonomal.png */; };
		0D8739FA2E260226005B375C /* file_pdf.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738B32E260226005B375C /* file_pdf.png */; };
		0D8739FB2E260226005B375C /* icon_head.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738D12E260226005B375C /* icon_head.png */; };
		0D8739FC2E260226005B375C /* icon_deleL.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738C42E260226005B375C /* icon_deleL.png */; };
		0D8739FD2E260226005B375C /* locationrefresh.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738692E260226005B375C /* locationrefresh.png */; };
		0D8739FE2E260226005B375C /* bottom_close.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738A02E260226005B375C /* bottom_close.png */; };
		0D8739FF2E260226005B375C /* icon_closeAlert.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738C12E260226005B375C /* icon_closeAlert.png */; };
		0D873A002E260226005B375C /* file_excel.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738AF2E260226005B375C /* file_excel.png */; };
		0D873A012E260226005B375C /* mini_grenn.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738F02E260226005B375C /* mini_grenn.png */; };
		0D873A022E260226005B375C /* icon_searchnone.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738E22E260226005B375C /* icon_searchnone.png */; };
		0D873A032E260226005B375C /* mini_blue.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738EE2E260226005B375C /* mini_blue.png */; };
		0D873A042E260226005B375C /* map_gray.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87386F2E260226005B375C /* map_gray.png */; };
		0D873A052E260226005B375C /* follow_noshare.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738912E260226005B375C /* follow_noshare.png */; };
		0D873A062E260226005B375C /* openstoreline_bottom.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87385E2E260226005B375C /* openstoreline_bottom.png */; };
		0D873A072E260226005B375C /* icon_direct.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738C82E260226005B375C /* icon_direct.png */; };
		0D873A082E260226005B375C /* share_lookhead.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87387C2E260226005B375C /* share_lookhead.png */; };
		0D873A092E260226005B375C /* changeitemdelete.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738662E260226005B375C /* changeitemdelete.png */; };
		0D873A0A2E260226005B375C /* map_green.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738702E260226005B375C /* map_green.png */; };
		0D873A0B2E260226005B375C /* point_home.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738FE2E260226005B375C /* point_home.png */; };
		0D873A0C2E260226005B375C /* buniess_minioragon.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738A82E260226005B375C /* buniess_minioragon.png */; };
		0D873A0D2E260226005B375C /* icoon_delete.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738E52E260226005B375C /* icoon_delete.png */; };
		0D873A0E2E260226005B375C /* icon_searchclear.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738E12E260226005B375C /* icon_searchclear.png */; };
		0D873A0F2E260226005B375C /* logo_back.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738EA2E260226005B375C /* logo_back.png */; };
		0D873A102E260226005B375C /* back_userS.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87389D2E260226005B375C /* back_userS.png */; };
		0D873A112E260226005B375C /* creat_buniess.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738AE2E260226005B375C /* creat_buniess.png */; };
		0D873A122E260226005B375C /* bottom_approve.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738872E260226005B375C /* bottom_approve.png */; };
		0D873A132E260226005B375C /* icon_shangquan.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738E32E260226005B375C /* icon_shangquan.png */; };
		0D873A142E260226005B375C /* map_red.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738722E260226005B375C /* map_red.png */; };
		0D873A152E260226005B375C /* move_view.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738F42E260226005B375C /* move_view.png */; };
		0D873A162E260226005B375C /* complete_drawNo.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738AD2E260226005B375C /* complete_drawNo.png */; };
		0D873A172E260226005B375C /* complete_draw.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738AC2E260226005B375C /* complete_draw.png */; };
		0D873A182E260226005B375C /* bottom_more.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87388D2E260226005B375C /* bottom_more.png */; };
		0D873A192E260226005B375C /* bottom_agree.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738862E260226005B375C /* bottom_agree.png */; };
		0D873A1A2E260226005B375C /* stand_map.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8739032E260226005B375C /* stand_map.png */; };
		0D873A1B2E260226005B375C /* point_landlord.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738FF2E260226005B375C /* point_landlord.png */; };
		0D873A1C2E260226005B375C /* bottom_follow.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87388C2E260226005B375C /* bottom_follow.png */; };
		0D873A1D2E260226005B375C /* file_word.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738B52E260226005B375C /* file_word.png */; };
		0D873A1E2E260226005B375C /* blue_map.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87389E2E260226005B375C /* blue_map.png */; };
		0D873A1F2E260226005B375C /* icon_empty.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738CD2E260226005B375C /* icon_empty.png */; };
		0D873A202E260226005B375C /* notifisuccess.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87386C2E260226005B375C /* notifisuccess.png */; };
		0D873A212E260226005B375C /* heat_mapS.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738B82E260226005B375C /* heat_mapS.png */; };
		0D873A222E260226005B375C /* point_detail.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738FB2E260226005B375C /* point_detail.png */; };
		0D873A232E260226005B375C /* icon_cardb.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738BD2E260226005B375C /* icon_cardb.png */; };
		0D873A242E260226005B375C /* TZImagePickerController.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 0D8739812E260226005B375C /* TZImagePickerController.bundle */; };
		0D873A252E260226005B375C /* buniess_yellow.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738A92E260226005B375C /* buniess_yellow.png */; };
		0D873A272E260226005B375C /* poi_loc.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738F92E260226005B375C /* poi_loc.png */; };
		0D873A282E260226005B375C /* item_rotate.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738E72E260226005B375C /* item_rotate.png */; };
		0D873A292E260226005B375C /* openstoreline_mid.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87385F2E260226005B375C /* openstoreline_mid.png */; };
		0D873A2A2E260226005B375C /* weixing_map.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8739082E260226005B375C /* weixing_map.png */; };
		0D873A2B2E260226005B375C /* userArrow.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8739072E260226005B375C /* userArrow.png */; };
		0D873A2C2E260226005B375C /* apple_navi.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738742E260226005B375C /* apple_navi.png */; };
		0D873A2D2E260226005B375C /* openstorearea_bottom.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87385B2E260226005B375C /* openstorearea_bottom.png */; };
		0D873A2E2E260226005B375C /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = 0D87393F2E260226005B375C /* README.md */; };
		0D873A2F2E260226005B375C /* audit_notread.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87387D2E260226005B375C /* audit_notread.png */; };
		0D873A302E260226005B375C /* mark_name.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738ED2E260226005B375C /* mark_name.png */; };
		0D873A312E260226005B375C /* icon_downarrow.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738C92E260226005B375C /* icon_downarrow.png */; };
		0D873A322E260226005B375C /* audit_system.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87387F2E260226005B375C /* audit_system.png */; };
		0D873A332E260226005B375C /* openstore_mid.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738622E260226005B375C /* openstore_mid.png */; };
		0D873A342E260226005B375C /* back_drawNo.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87389B2E260226005B375C /* back_drawNo.png */; };
		0D873A352E260226005B375C /* point_state.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8739012E260226005B375C /* point_state.png */; };
		0D873A362E260226005B375C /* city_allchoose.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87387A2E260226005B375C /* city_allchoose.png */; };
		0D873A372E260226005B375C /* openstorearea_top.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87385D2E260226005B375C /* openstorearea_top.png */; };
		0D873A382E260226005B375C /* nav_man.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738F62E260226005B375C /* nav_man.png */; };
		0D873A392E260226005B375C /* mini_oragan.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738F12E260226005B375C /* mini_oragan.png */; };
		0D873A3A2E260226005B375C /* bottom_reject.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87388E2E260226005B375C /* bottom_reject.png */; };
		0D873A3B2E260226005B375C /* icon_orange.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738DC2E260226005B375C /* icon_orange.png */; };
		0D873A3C2E260226005B375C /* apple_loc.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738752E260226005B375C /* apple_loc.png */; };
		0D873A3D2E260226005B375C /* heat_map.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738B72E260226005B375C /* heat_map.png */; };
		0D873A3E2E260226005B375C /* icon_creatp.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738C32E260226005B375C /* icon_creatp.png */; };
		0D873A3F2E260226005B375C /* icon_camer.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738BC2E260226005B375C /* icon_camer.png */; };
		0D873A402E260226005B375C /* audit_wait.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738812E260226005B375C /* audit_wait.png */; };
		0D873A412E260226005B375C /* icon_leftse.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738D22E260226005B375C /* icon_leftse.png */; };
		0D873A422E260226005B375C /* file_ppt.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738B42E260226005B375C /* file_ppt.png */; };
		0D873A432E260226005B375C /* back_user.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87389C2E260226005B375C /* back_user.png */; };
		0D873A442E260226005B375C /* back_draw.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87389A2E260226005B375C /* back_draw.png */; };
		0D873A452E260226005B375C /* MJRefresh.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 0D8739632E260226005B375C /* MJRefresh.bundle */; };
		0D873A462E260226005B375C /* LICENSE in Resources */ = {isa = PBXBuildFile; fileRef = 0D87393E2E260226005B375C /* LICENSE */; };
		0D873A472E260226005B375C /* point_follow.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738FC2E260226005B375C /* point_follow.png */; };
		0D873A482E260226005B375C /* icon_play.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738DE2E260226005B375C /* icon_play.png */; };
		0D873A492E260226005B375C /* point_add.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738FA2E260226005B375C /* point_add.png */; };
		0D873A4A2E260226005B375C /* icon_cbuniess.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738BF2E260226005B375C /* icon_cbuniess.png */; };
		0D873A4B2E260226005B375C /* search_delete.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8739022E260226005B375C /* search_delete.png */; };
		0D873A4C2E260226005B375C /* nav_back.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738F52E260226005B375C /* nav_back.png */; };
		0D873A4D2E260226005B375C /* icon_deleteL.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738C62E260226005B375C /* icon_deleteL.png */; };
		0D873A4E2E260226005B375C /* mini_red.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738F22E260226005B375C /* mini_red.png */; };
		0D873A4F2E260226005B375C /* close_btn.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738AB2E260226005B375C /* close_btn.png */; };
		0D873A502E260226005B375C /* add_landlord.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738972E260226005B375C /* add_landlord.png */; };
		0D873A512E260226005B375C /* mini_gray.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738EF2E260226005B375C /* mini_gray.png */; };
		0D873A522E260226005B375C /* tab_nodata.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8739062E260226005B375C /* tab_nodata.png */; };
		0D873A532E260226005B375C /* icon_leftstore.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738D32E260226005B375C /* icon_leftstore.png */; };
		0D873A542E260226005B375C /* icon_halfselect.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738D02E260226005B375C /* icon_halfselect.png */; };
		0D873A552E260226005B375C /* icon_mylo.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738942E260226005B375C /* icon_mylo.png */; };
		0D873A562E260226005B375C /* buiness_miniblue.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738A22E260226005B375C /* buiness_miniblue.png */; };
		0D873A572E260226005B375C /* ZKPhotoBrowser.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 0D8737D62E260226005B375C /* ZKPhotoBrowser.bundle */; };
		0D873A582E260226005B375C /* audit_read.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87387E2E260226005B375C /* audit_read.png */; };
		0D873A592E260226005B375C /* icon_cardf.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738BE2E260226005B375C /* icon_cardf.png */; };
		0D873A5A2E260226005B375C /* icon_deleteS.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738C72E260226005B375C /* icon_deleteS.png */; };
		0D873A5B2E260226005B375C /* icon_edit.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738CC2E260226005B375C /* icon_edit.png */; };
		0D873A5C2E260226005B375C /* icon_share.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738962E260226005B375C /* icon_share.png */; };
		0D873A5D2E260226005B375C /* icon_choose.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738C02E260226005B375C /* icon_choose.png */; };
		0D873A5E2E260226005B375C /* style.data in Resources */ = {isa = PBXBuildFile; fileRef = 0D87390B2E260226005B375C /* style.data */; };
		0D873A5F2E260226005B375C /* icon_blue.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738BB2E260226005B375C /* icon_blue.png */; };
		0D873A602E260226005B375C /* style_extra.data in Resources */ = {isa = PBXBuildFile; fileRef = 0D87390C2E260226005B375C /* style_extra.data */; };
		0D873A612E260226005B375C /* list_maps.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738E92E260226005B375C /* list_maps.png */; };
		0D873A622E260226005B375C /* icon_draft.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738CA2E260226005B375C /* icon_draft.png */; };
		0D873A632E260226005B375C /* point_location.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8739002E260226005B375C /* point_location.png */; };
		0D873A642E260226005B375C /* clear.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738AA2E260226005B375C /* clear.png */; };
		0D873A652E260226005B375C /* icon_cpoint.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738C22E260226005B375C /* icon_cpoint.png */; };
		0D873A662E260226005B375C /* switch_map.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8739042E260226005B375C /* switch_map.png */; };
		0D873A672E260226005B375C /* city_nochose.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738782E260226005B375C /* city_nochose.png */; };
		0D873A682E260226005B375C /* icon_mutilno.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738D62E260226005B375C /* icon_mutilno.png */; };
		0D873A692E260226005B375C /* play_video.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738F82E260226005B375C /* play_video.png */; };
		0D873A6A2E260226005B375C /* help_icon.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738B92E260226005B375C /* help_icon.png */; };
		0D873A6B2E260226005B375C /* buniess_green.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738A72E260226005B375C /* buniess_green.png */; };
		0D873A6C2E260226005B375C /* icon_mutilselect.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738D72E260226005B375C /* icon_mutilselect.png */; };
		0D873A6D2E260226005B375C /* file_no.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738B22E260226005B375C /* file_no.png */; };
		0D873A6E2E260226005B375C /* icon_rightstore.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738E02E260226005B375C /* icon_rightstore.png */; };
		0D873A6F2E260226005B375C /* icon_addp.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738BA2E260226005B375C /* icon_addp.png */; };
		0D873A702E260226005B375C /* buniess_gray.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738A52E260226005B375C /* buniess_gray.png */; };
		0D873A712E260226005B375C /* buniess_time.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87387B2E260226005B375C /* buniess_time.png */; };
		0D873A722E260226005B375C /* icon_copyname.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738762E260226005B375C /* icon_copyname.png */; };
		0D873A732E260226005B375C /* nav_serach.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738F72E260226005B375C /* nav_serach.png */; };
		0D873A742E260226005B375C /* list_map.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738E82E260226005B375C /* list_map.png */; };
		0D873A752E260226005B375C /* openstorearea_mid.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87385C2E260226005B375C /* openstorearea_mid.png */; };
		0D873A762E260226005B375C /* navtargeticon.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738672E260226005B375C /* navtargeticon.png */; };
		0D873A772E260226005B375C /* item_newdele.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738E62E260226005B375C /* item_newdele.png */; };
		0D873A782E260226005B375C /* follow_noshareright.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738902E260226005B375C /* follow_noshareright.png */; };
		0D873A792E260226005B375C /* icon_tipfollow.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87386D2E260226005B375C /* icon_tipfollow.png */; };
		0D873A7A2E260226005B375C /* bottom_change.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738882E260226005B375C /* bottom_change.png */; };
		0D873A7B2E260226005B375C /* file_img.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738B02E260226005B375C /* file_img.png */; };
		0D873A7C2E260226005B375C /* openstore_top.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738612E260226005B375C /* openstore_top.png */; };
		0D873A7D2E260226005B375C /* switch_mapS.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8739052E260226005B375C /* switch_mapS.png */; };
		0D873A7E2E260226005B375C /* icon_newback.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738D82E260226005B375C /* icon_newback.png */; };
		0D873A7F2E260226005B375C /* notifierror.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87386B2E260226005B375C /* notifierror.png */; };
		0D873A802E260226005B375C /* icon_green.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738CF2E260226005B375C /* icon_green.png */; };
		0D873A812E260226005B375C /* buiness_minigray.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738A32E260226005B375C /* buiness_minigray.png */; };
		0D873A822E260226005B375C /* search_place.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738732E260226005B375C /* search_place.png */; };
		0D873A832E260226005B375C /* icon_closeshare.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738952E260226005B375C /* icon_closeshare.png */; };
		0D873A842E260226005B375C /* look_landlord.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738EC2E260226005B375C /* look_landlord.png */; };
		0D873A852E260226005B375C /* open_creatpoint.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738652E260226005B375C /* open_creatpoint.png */; };
		0D873A862E260226005B375C /* buniess_blue.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738A42E260226005B375C /* buniess_blue.png */; };
		0D873A872E260226005B375C /* apple_back.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738772E260226005B375C /* apple_back.png */; };
		0D873A882E260226005B375C /* city_halfchoose.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738792E260226005B375C /* city_halfchoose.png */; };
		0D873A892E260226005B375C /* locationchangeitem.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738682E260226005B375C /* locationchangeitem.png */; };
		0D873A8A2E260226005B375C /* bottom_delete.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738892E260226005B375C /* bottom_delete.png */; };
		0D873A8B2E260226005B375C /* point_follows.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738FD2E260226005B375C /* point_follows.png */; };
		0D873A8C2E260226005B375C /* gonggong_map.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738B62E260226005B375C /* gonggong_map.png */; };
		0D873A8D2E260226005B375C /* icon_nochoose.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738DB2E260226005B375C /* icon_nochoose.png */; };
		0D873A8E2E260226005B375C /* openstoreline_top.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738602E260226005B375C /* openstoreline_top.png */; };
		0D873A8F2E260226005B375C /* icon_deletefile.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738C52E260226005B375C /* icon_deletefile.png */; };
		0D873A902E260226005B375C /* share_close.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738922E260226005B375C /* share_close.png */; };
		0D873A912E260226005B375C /* detail_comment.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738842E260226005B375C /* detail_comment.png */; };
		0D873A922E260226005B375C /* build_num.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738A12E260226005B375C /* build_num.png */; };
		0D873A932E260226005B375C /* icon_more.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738D52E260226005B375C /* icon_more.png */; };
		0D873A942E260226005B375C /* icon_drag.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738CB2E260226005B375C /* icon_drag.png */; };
		0D873A952E260226005B375C /* icon_gray.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738CE2E260226005B375C /* icon_gray.png */; };
		0D873A962E260226005B375C /* audit_record.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738992E260226005B375C /* audit_record.png */; };
		0D873A972E260226005B375C /* bottom_revoke.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87388F2E260226005B375C /* bottom_revoke.png */; };
		0D873A982E260226005B375C /* icon_tip.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738932E260226005B375C /* icon_tip.png */; };
		0D873A992E260226005B375C /* blue_newmap.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D87389F2E260226005B375C /* blue_newmap.png */; };
		0D873A9A2E260226005B375C /* openstore_bottom.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738632E260226005B375C /* openstore_bottom.png */; };
		0D873A9B2E260226005B375C /* audit_approve.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738832E260226005B375C /* audit_approve.png */; };
		0D873A9C2E260226005B375C /* bottom_reopen.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738852E260226005B375C /* bottom_reopen.png */; };
		0D873A9D2E260226005B375C /* icon_newpla.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738D92E260226005B375C /* icon_newpla.png */; };
		0D873A9E2E260226005B375C /* icon_pla.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738DD2E260226005B375C /* icon_pla.png */; };
		0D873A9F2E260226005B375C /* icon_single.png in Resources */ = {isa = PBXBuildFile; fileRef = 0D8738E42E260226005B375C /* icon_single.png */; };
		0D873AA02E260226005B375C /* MASViewAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739302E260226005B375C /* MASViewAttribute.m */; };
		0D873AA12E260226005B375C /* TZVideoEditedPreviewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739912E260226005B375C /* TZVideoEditedPreviewController.m */; };
		0D873AA22E260226005B375C /* MACustomBuniessTableView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87383E2E260226005B375C /* MACustomBuniessTableView.m */; };
		0D873AA32E260226005B375C /* UILabel+STFakeAnimation.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739A82E260226005B375C /* UILabel+STFakeAnimation.m */; };
		0D873AA42E260226005B375C /* MAReadListView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738522E260226005B375C /* MAReadListView.m */; };
		0D873AA52E260226005B375C /* MJRefreshHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87394A2E260226005B375C /* MJRefreshHeader.m */; };
		0D873AA62E260226005B375C /* MABottomSingleSelectAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737F42E260226005B375C /* MABottomSingleSelectAlert.m */; };
		0D873AA72E260226005B375C /* PushCellScaleTranstion.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739A02E260226005B375C /* PushCellScaleTranstion.m */; };
		0D873AA82E260226005B375C /* MASCompositeConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739252E260226005B375C /* MASCompositeConstraint.m */; };
		0D873AA92E260226005B375C /* MACreatBuniessPolyline.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737B72E260226005B375C /* MACreatBuniessPolyline.m */; };
		0D873AAA2E260226005B375C /* MJRefreshFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739482E260226005B375C /* MJRefreshFooter.m */; };
		0D873AAB2E260226005B375C /* TZPhotoPreviewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87398B2E260226005B375C /* TZPhotoPreviewController.m */; };
		0D873AAC2E260226005B375C /* TZImageCropManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87397E2E260226005B375C /* TZImageCropManager.m */; };
		0D873AAD2E260226005B375C /* MASearchPoiViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739C52E260226005B375C /* MASearchPoiViewController.m */; };
		0D873AAE2E260226005B375C /* MAFilterItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738022E260226005B375C /* MAFilterItemView.m */; };
		0D873AAF2E260226005B375C /* TZImageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739802E260226005B375C /* TZImageManager.m */; };
		0D873AB02E260226005B375C /* TZGifPhotoPreviewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87397C2E260226005B375C /* TZGifPhotoPreviewController.m */; };
		0D873AB12E260226005B375C /* MAWriteReplyView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87384A2E260226005B375C /* MAWriteReplyView.m */; };
		0D873AB22E260226005B375C /* MASLayoutConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87392C2E260226005B375C /* MASLayoutConstraint.m */; };
		0D873AB32E260226005B375C /* MACenterBuniessAnnotation.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737BF2E260226005B375C /* MACenterBuniessAnnotation.m */; };
		0D873AB42E260226005B375C /* JAMSVGImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87391E2E260226005B375C /* JAMSVGImageView.m */; };
		0D873AB52E260226005B375C /* ZKPhotoProgressView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737DA2E260226005B375C /* ZKPhotoProgressView.m */; };
		0D873AB62E260226005B375C /* MALocalAnnotation.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737B92E260226005B375C /* MALocalAnnotation.m */; };
		0D873AB72E260226005B375C /* MACooperateTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737FC2E260226005B375C /* MACooperateTableViewCell.m */; };
		0D873AB82E260226005B375C /* MALoadWaveView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738162E260226005B375C /* MALoadWaveView.m */; };
		0D873AB92E260226005B375C /* TZImageRequestOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739852E260226005B375C /* TZImageRequestOperation.m */; };
		0D873ABA2E260226005B375C /* JAMSVGButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87391C2E260226005B375C /* JAMSVGButton.m */; };
		0D873ABB2E260226005B375C /* UIButton+Common.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739A62E260226005B375C /* UIButton+Common.m */; };
		0D873ABC2E260226005B375C /* MABottomOpereateView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87384C2E260226005B375C /* MABottomOpereateView.m */; };
		0D873ABD2E260226005B375C /* MASingleDateView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87382C2E260226005B375C /* MASingleDateView.m */; };
		0D873ABE2E260226005B375C /* JAMStyledBezierPathFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739102E260226005B375C /* JAMStyledBezierPathFactory.m */; };
		0D873ABF2E260226005B375C /* MAHttpTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739982E260226005B375C /* MAHttpTool.m */; };
		0D873AC02E260226005B375C /* MASearchPointViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739DD2E260226005B375C /* MASearchPointViewController.m */; };
		0D873AC12E260226005B375C /* UITextField+Common.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739AC2E260226005B375C /* UITextField+Common.m */; };
		0D873AC22E260226005B375C /* UIView+Toast.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737D42E260226005B375C /* UIView+Toast.m */; };
		0D873AC32E260226005B375C /* BHInfiniteScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737D02E260226005B375C /* BHInfiniteScrollView.m */; };
		0D873AC42E260226005B375C /* MAUIScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738322E260226005B375C /* MAUIScrollView.m */; };
		0D873AC52E260226005B375C /* MAUploadFileViewTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738362E260226005B375C /* MAUploadFileViewTableViewCell.m */; };
		0D873AC62E260226005B375C /* MJRefreshStateHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739602E260226005B375C /* MJRefreshStateHeader.m */; };
		0D873AC72E260226005B375C /* MATakeLookViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739DB2E260226005B375C /* MATakeLookViewController.m */; };
		0D873AC82E260226005B375C /* ZKPhotoBrowser.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737DF2E260226005B375C /* ZKPhotoBrowser.m */; };
		0D873AC92E260226005B375C /* MATopNotiflyView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737EA2E260226005B375C /* MATopNotiflyView.m */; };
		0D873ACA2E260226005B375C /* MAStoreTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738302E260226005B375C /* MAStoreTableViewCell.m */; };
		0D873ACB2E260226005B375C /* MJRefreshGifHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87395C2E260226005B375C /* MJRefreshGifHeader.m */; };
		0D873ACC2E260226005B375C /* TZVideoPlayerController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739932E260226005B375C /* TZVideoPlayerController.m */; };
		0D873ACE2E260226005B375C /* MAShareChooseView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738462E260226005B375C /* MAShareChooseView.m */; };
		0D873ACF2E260226005B375C /* MAH5ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739D32E260226005B375C /* MAH5ViewController.m */; };
		0D873AD02E260226005B375C /* MAOpenStorePolygon.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737C32E260226005B375C /* MAOpenStorePolygon.m */; };
		0D873AD12E260226005B375C /* TZAuthLimitedFooterTipView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87397A2E260226005B375C /* TZAuthLimitedFooterTipView.m */; };
		0D873AD22E260226005B375C /* MASViewConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739322E260226005B375C /* MASViewConstraint.m */; };
		0D873AD32E260226005B375C /* View+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739392E260226005B375C /* View+MASAdditions.m */; };
		0D873AD42E260226005B375C /* MJRefreshBackFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739442E260226005B375C /* MJRefreshBackFooter.m */; };
		0D873AD52E260226005B375C /* MAAllFilterItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737E62E260226005B375C /* MAAllFilterItemView.m */; };
		0D873AD62E260226005B375C /* UIView+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87396E2E260226005B375C /* UIView+MJExtension.m */; };
		0D873AD72E260226005B375C /* MATopToastView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737E82E260226005B375C /* MATopToastView.m */; };
		0D873AD82E260226005B375C /* ZKPhotoToolbar.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737E12E260226005B375C /* ZKPhotoToolbar.m */; };
		0D873AD92E260226005B375C /* UIView+Common.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739B02E260226005B375C /* UIView+Common.m */; };
		0D873ADA2E260226005B375C /* MAUploadMutilImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87383A2E260226005B375C /* MAUploadMutilImageView.m */; };
		0D873ADB2E260226005B375C /* MABuniessScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737F62E260226005B375C /* MABuniessScrollView.m */; };
		0D873ADC2E260226005B375C /* ZKPhotoLoadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737D82E260226005B375C /* ZKPhotoLoadingView.m */; };
		0D873ADD2E260226005B375C /* MAFilterChooseCityItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738082E260226005B375C /* MAFilterChooseCityItem.m */; };
		0D873ADE2E260226005B375C /* MJRefreshBackGifFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739542E260226005B375C /* MJRefreshBackGifFooter.m */; };
		0D873ADF2E260226005B375C /* MABottomMutiChooseCityView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87380C2E260226005B375C /* MABottomMutiChooseCityView.m */; };
		0D873AE02E260226005B375C /* MASConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739272E260226005B375C /* MASConstraint.m */; };
		0D873AE12E260226005B375C /* MAChooseCityView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737FA2E260226005B375C /* MAChooseCityView.m */; };
		0D873AE22E260226005B375C /* FXPageControl.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737C82E260226005B375C /* FXPageControl.m */; };
		0D873AE32E260226005B375C /* MACustomTableView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87383C2E260226005B375C /* MACustomTableView.m */; };
		0D873AE42E260226005B375C /* JAMSVGUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739212E260226005B375C /* JAMSVGUtilities.m */; };
		0D873AE52E260226005B375C /* MAManagerFilterTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738182E260226005B375C /* MAManagerFilterTableViewCell.m */; };
		0D873AE62E260226005B375C /* NSBundle+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739682E260226005B375C /* NSBundle+MJRefresh.m */; };
		0D873AE72E260226005B375C /* MACreatBuniessPointAnnView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737B12E260226005B375C /* MACreatBuniessPointAnnView.m */; };
		0D873AE82E260226005B375C /* MAManagerFilterViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739D72E260226005B375C /* MAManagerFilterViewController.m */; };
		0D873AE92E260226005B375C /* TZPhotoPreviewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739892E260226005B375C /* TZPhotoPreviewCell.m */; };
		0D873AEA2E260226005B375C /* MAShareView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738442E260226005B375C /* MAShareView.m */; };
		0D873AEB2E260226005B375C /* AmapNativeMapViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739B72E260226005B375C /* AmapNativeMapViewController.m */; };
		0D873AEC2E260226005B375C /* MACreatBuniessView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738002E260226005B375C /* MACreatBuniessView.m */; };
		0D873AED2E260226005B375C /* MJRefreshAutoNormalFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87394F2E260226005B375C /* MJRefreshAutoNormalFooter.m */; };
		0D873AEE2E260226005B375C /* MAOpenStoreView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738542E260226005B375C /* MAOpenStoreView.m */; };
		0D873AEF2E260226005B375C /* MAApprovalRecordView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87384E2E260226005B375C /* MAApprovalRecordView.m */; };
		0D873AF02E260226005B375C /* MAShareUITableView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87382A2E260226005B375C /* MAShareUITableView.m */; };
		0D873AF12E260226005B375C /* MAOpenStoreViewTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738562E260226005B375C /* MAOpenStoreViewTableViewCell.m */; };
		0D873AF22E260226005B375C /* NSDictionary+Common.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87399C2E260226005B375C /* NSDictionary+Common.m */; };
		0D873AF32E260226005B375C /* MALocalAnnotationView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737BB2E260226005B375C /* MALocalAnnotationView.m */; };
		0D873AF42E260226005B375C /* MAUploadMutilCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738382E260226005B375C /* MAUploadMutilCell.m */; };
		0D873AF52E260226005B375C /* UITapGestureRecognizer+Common.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739AA2E260226005B375C /* UITapGestureRecognizer+Common.m */; };
		0D873AF62E260226005B375C /* MAWriteMeassageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738482E260226005B375C /* MAWriteMeassageView.m */; };
		0D873AF72E260226005B375C /* TZImagePickerController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739832E260226005B375C /* TZImagePickerController.m */; };
		0D873AF82E260226005B375C /* MABottomPointAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737F22E260226005B375C /* MABottomPointAlert.m */; };
		0D873AF92E260226005B375C /* MAPointTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738202E260226005B375C /* MAPointTableViewCell.m */; };
		0D873AFA2E260226005B375C /* ViewController+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87393C2E260226005B375C /* ViewController+MASAdditions.m */; };
		0D873AFB2E260226005B375C /* MAUploadSingleImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738402E260226005B375C /* MAUploadSingleImageView.m */; };
		0D873AFC2E260226005B375C /* JAMStyledBezierPath.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87390E2E260226005B375C /* JAMStyledBezierPath.m */; };
		0D873AFD2E260226005B375C /* BHInfiniteScrollViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737CB2E260226005B375C /* BHInfiniteScrollViewCell.m */; };
		0D873AFE2E260226005B375C /* MAFilterChooseCityCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738062E260226005B375C /* MAFilterChooseCityCell.m */; };
		0D873AFF2E260226005B375C /* MAPointModalView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87381E2E260226005B375C /* MAPointModalView.m */; };
		0D873B002E260226005B375C /* MAAllFilterView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737EE2E260226005B375C /* MAAllFilterView.m */; };
		0D873B012E260226005B375C /* MJRefreshNormalHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87395E2E260226005B375C /* MJRefreshNormalHeader.m */; };
		0D873B022E260226005B375C /* TZProgressView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87398D2E260226005B375C /* TZProgressView.m */; };
		0D873B032E260226005B375C /* MACopyListView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738502E260226005B375C /* MACopyListView.m */; };
		0D873B042E260226005B375C /* MJRefreshBackNormalFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739562E260226005B375C /* MJRefreshBackNormalFooter.m */; };
		0D873B052E260226005B375C /* MABottomToast.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737EC2E260226005B375C /* MABottomToast.m */; };
		0D873B062E260226005B375C /* UIScrollView+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87396C2E260226005B375C /* UIScrollView+MJRefresh.m */; };
		0D873B072E260226005B375C /* MABuniessEditDrawViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739D12E260226005B375C /* MABuniessEditDrawViewController.m */; };
		0D873B082E260226005B375C /* ZKPhoto.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737DD2E260226005B375C /* ZKPhoto.m */; };
		0D873B092E260226005B375C /* MJRefreshAutoGifFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87394D2E260226005B375C /* MJRefreshAutoGifFooter.m */; };
		0D873B0A2E260226005B375C /* MAEditBuniessViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739CD2E260226005B375C /* MAEditBuniessViewController.m */; };
		0D873B0B2E260226005B375C /* MJRefreshAutoFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739422E260226005B375C /* MJRefreshAutoFooter.m */; };
		0D873B0C2E260226005B375C /* MAAddOpenStoreViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739C72E260226005B375C /* MAAddOpenStoreViewController.m */; };
		0D873B0D2E260226005B375C /* MAEditPointViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739BF2E260226005B375C /* MAEditPointViewController.m */; };
		0D873B0E2E260226005B375C /* MJRefreshComponent.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739462E260226005B375C /* MJRefreshComponent.m */; };
		0D873B0F2E260226005B375C /* JAMSVGGradientParts.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739122E260226005B375C /* JAMSVGGradientParts.m */; };
		0D873B102E260226005B375C /* TZAssetCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739762E260226005B375C /* TZAssetCell.m */; };
		0D873B112E260226005B375C /* TZVideoCropController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87398F2E260226005B375C /* TZVideoCropController.m */; };
		0D873B122E260226005B375C /* MABussinessPolygonRenderer.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737AD2E260226005B375C /* MABussinessPolygonRenderer.m */; };
		0D873B132E260226005B375C /* MAPoiTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738222E260226005B375C /* MAPoiTableViewCell.m */; };
		0D873B142E260226005B375C /* MASingleSelectView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87382E2E260226005B375C /* MASingleSelectView.m */; };
		0D873B152E260226005B375C /* MABuniessTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737F82E260226005B375C /* MABuniessTableViewCell.m */; };
		0D873B162E260226005B375C /* UICollectionViewLeftAlignedLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738042E260226005B375C /* UICollectionViewLeftAlignedLayout.m */; };
		0D873B172E260226005B375C /* UIScrollView+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87396A2E260226005B375C /* UIScrollView+MJExtension.m */; };
		0D873B182E260226005B375C /* MASearchTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738282E260226005B375C /* MASearchTextField.m */; };
		0D873B192E260226005B375C /* MABaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739E12E260226005B375C /* MABaseViewController.m */; };
		0D873B1A2E260226005B375C /* MABussinessPolygon.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737AB2E260226005B375C /* MABussinessPolygon.m */; };
		0D873B1B2E260226005B375C /* MAMapSwitchView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87381A2E260226005B375C /* MAMapSwitchView.m */; };
		0D873B1C2E260226005B375C /* MACreatBuniessPolygonRender.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737B52E260226005B375C /* MACreatBuniessPolygonRender.m */; };
		0D873B1D2E260226005B375C /* MASearchLoadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738242E260226005B375C /* MASearchLoadingView.m */; };
		0D873B1E2E260226005B375C /* BHInfiniteScrollViewTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737CD2E260226005B375C /* BHInfiniteScrollViewTitleView.m */; };
		0D873B1F2E260226005B375C /* JAMSVGImage.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739172E260226005B375C /* JAMSVGImage.m */; };
		0D873B202E260226005B375C /* MJRefreshBackStateFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739582E260226005B375C /* MJRefreshBackStateFooter.m */; };
		0D873B212E260226005B375C /* UITextView+Common.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739AE2E260226005B375C /* UITextView+Common.m */; };
		0D873B222E260226005B375C /* MACreatBuniessPolygon.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737B32E260226005B375C /* MACreatBuniessPolygon.m */; };
		0D873B232E260226005B375C /* ModalScaleTranstion.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739B22E260226005B375C /* ModalScaleTranstion.m */; };
		0D873B242E260226005B375C /* MAMutiChooseCityTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87380A2E260226005B375C /* MAMutiChooseCityTableViewCell.m */; };
		0D873B252E260226005B375C /* ZKPhotoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737E32E260226005B375C /* ZKPhotoView.m */; };
		0D873B262E260226005B375C /* MAAddBuniessViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739C92E260226005B375C /* MAAddBuniessViewController.m */; };
		0D873B272E260226005B375C /* MAAddPointViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739BD2E260226005B375C /* MAAddPointViewController.m */; };
		0D873B282E260226005B375C /* NSArray+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739342E260226005B375C /* NSArray+MASAdditions.m */; };
		0D873B292E260226005B375C /* MAPointEditPoiViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739C32E260226005B375C /* MAPointEditPoiViewController.m */; };
		0D873B2A2E260226005B375C /* JAMSVGParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739142E260226005B375C /* JAMSVGParser.m */; };
		0D873B2B2E260226005B375C /* MAWriteFollowUpViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739D92E260226005B375C /* MAWriteFollowUpViewController.m */; };
		0D873B2C2E260226005B375C /* MKLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738422E260226005B375C /* MKLabel.m */; };
		0D873B2D2E260226005B375C /* TZAssetModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739782E260226005B375C /* TZAssetModel.m */; };
		0D873B2E2E260226005B375C /* NSBundle+TZImagePicker.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739742E260226005B375C /* NSBundle+TZImagePicker.m */; };
		0D873B2F2E260226005B375C /* UIScrollView+EmptyDataSet.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739712E260226005B375C /* UIScrollView+EmptyDataSet.m */; };
		0D873B302E260226005B375C /* MANativeAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87381C2E260226005B375C /* MANativeAlert.m */; };
		0D873B312E260226005B375C /* UIImage+SVG.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739192E260226005B375C /* UIImage+SVG.m */; };
		0D873B322E260226005B375C /* MAUserAnnotationView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737BD2E260226005B375C /* MAUserAnnotationView.m */; };
		0D873B332E260226005B375C /* MJRefreshAutoStateFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739512E260226005B375C /* MJRefreshAutoStateFooter.m */; };
		0D873B342E260226005B375C /* MABuniessWriteFollowViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739CF2E260226005B375C /* MABuniessWriteFollowViewController.m */; };
		0D873B352E260226005B375C /* NSArray+Common.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87399A2E260226005B375C /* NSArray+Common.m */; };
		0D873B362E260226005B375C /* PushZoomScaleBuniessTranstion.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739A22E260226005B375C /* PushZoomScaleBuniessTranstion.m */; };
		0D873B372E260226005B375C /* NSLayoutConstraint+MASDebugAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739372E260226005B375C /* NSLayoutConstraint+MASDebugAdditions.m */; };
		0D873B382E260226005B375C /* MAOpenStorePolyline.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737C52E260226005B375C /* MAOpenStorePolyline.m */; };
		0D873B392E260226005B375C /* MAChooseLocationViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739C12E260226005B375C /* MAChooseLocationViewController.m */; };
		0D873B3A2E260226005B375C /* MAFilterTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738102E260226005B375C /* MAFilterTextField.m */; };
		0D873B3B2E260226005B375C /* MAVisibleAreaListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739DF2E260226005B375C /* MAVisibleAreaListViewController.m */; };
		0D873B3C2E260226005B375C /* MACenterBuniessAnnView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737C12E260226005B375C /* MACenterBuniessAnnView.m */; };
		0D873B3D2E260226005B375C /* MJRefreshConst.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739662E260226005B375C /* MJRefreshConst.m */; };
		0D873B3E2E260226005B375C /* PushZoomScaleTranstion.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739A42E260226005B375C /* PushZoomScaleTranstion.m */; };
		0D873B3F2E260226005B375C /* MAAppleMapViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739E32E260226005B375C /* MAAppleMapViewController.m */; };
		0D873B402E260226005B375C /* MAUploadFileView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738342E260226005B375C /* MAUploadFileView.m */; };
		0D873B412E260226005B375C /* ModalHalfScreenTranstion.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739B42E260226005B375C /* ModalHalfScreenTranstion.m */; };
		0D873B422E260226005B375C /* MAFilterModal.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87380E2E260226005B375C /* MAFilterModal.m */; };
		0D873B432E260226005B375C /* MACreatBuniessPointAnnotation.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737AF2E260226005B375C /* MACreatBuniessPointAnnotation.m */; };
		0D873B442E260226005B375C /* PushCellScaleBuniessTranstion.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87399E2E260226005B375C /* PushCellScaleBuniessTranstion.m */; };
		0D873B452E260226005B375C /* MACreatBuniessDrawBoardView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737FE2E260226005B375C /* MACreatBuniessDrawBoardView.m */; };
		0D873B462E260226005B375C /* MAPointDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739BB2E260226005B375C /* MAPointDetailViewController.m */; };
		0D873B472E260226005B375C /* MABottomMutilSelectAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8737F02E260226005B375C /* MABottomMutilSelectAlert.m */; };
		0D873B482E260226005B375C /* MALookPointViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739D52E260226005B375C /* MALookPointViewController.m */; };
		0D873B492E260226005B375C /* MAHorScrollview.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738122E260226005B375C /* MAHorScrollview.m */; };
		0D873B4A2E260226005B375C /* UIView+TZLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739952E260226005B375C /* UIView+TZLayout.m */; };
		0D873B4B2E260226005B375C /* MASConstraintMaker.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D87392A2E260226005B375C /* MASConstraintMaker.m */; };
		0D873B4C2E260226005B375C /* MASearchTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738262E260226005B375C /* MASearchTableViewCell.m */; };
		0D873B4D2E260226005B375C /* MAEditOpenStoreView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738582E260226005B375C /* MAEditOpenStoreView.m */; };
		0D873B4E2E260226005B375C /* MALoadAlphaView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8738142E260226005B375C /* MALoadAlphaView.m */; };
		0D873B4F2E260226005B375C /* MABuniessDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739CB2E260226005B375C /* MABuniessDetailViewController.m */; };
		0D873B502E260226005B375C /* MAPremissionCheckViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739E52E260226005B375C /* MAPremissionCheckViewController.m */; };
		0D873B512E260226005B375C /* MAAddLandlordViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739B92E260226005B375C /* MAAddLandlordViewController.m */; };
		0D873B522E260226005B375C /* TZPhotoPickerController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D8739872E260226005B375C /* TZPhotoPickerController.m */; };
		0D873B542E260444005B375C /* PushNativeMapBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D873B532E260444005B375C /* PushNativeMapBridge.m */; };
		0D873B562E2604B5005B375C /* PushNativeMapManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0D873B552E2604B5005B375C /* PushNativeMapManager.swift */; };
		0D873B582E265FA3005B375C /* XLBNavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0D873B572E265FA3005B375C /* XLBNavigationController.swift */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		14D4F9FC9A4247C9A18851A6 /* D-DIN-PRO-700-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6D46254509D54B868A5BA581 /* D-DIN-PRO-700-Bold.ttf */; };
		1D3A32662E2739E5000CCE3A /* RNBrigeManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 1D3A32642E2739E5000CCE3A /* RNBrigeManager.m */; };
		1D3A32692E2774B6000CCE3A /* RemoteNotificationCenter.m in Sources */ = {isa = PBXBuildFile; fileRef = 1D3A32682E2774B6000CCE3A /* RemoteNotificationCenter.m */; };
		1D3A326E2E2774C3000CCE3A /* XLBBackRunningManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 1D3A326C2E2774C3000CCE3A /* XLBBackRunningManager.m */; };
		1D3A326F2E2774C3000CCE3A /* RunInBackground.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 1D3A326A2E2774C3000CCE3A /* RunInBackground.mp3 */; };
		1FE43F87D49942D8BE07FDA2 /* HarmonyOS_Sans_Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7769087312B64BCBB3359D19 /* HarmonyOS_Sans_Medium.ttf */; };
		3685EA5CCAECA33F54038DDC /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		4AFD156F0D2C4559AEEF4789 /* D-DIN-PRO-800-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A5448D110CD14B1D929F69B7 /* D-DIN-PRO-800-ExtraBold.ttf */; };
		5942790D265647FAB1DB9CA6 /* HarmonyOS_Sans_Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B1D5EA243F204F86865596A2 /* HarmonyOS_Sans_Thin.ttf */; };
		5D317FF4DE3A4308829E592D /* HarmonyOS_Sans_Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D0DF41FE3CB84BAE8A985A48 /* HarmonyOS_Sans_Light.ttf */; };
		6354F9E4F362409D87E21586 /* D-DIN-PRO-400-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AC148BEB1DE645BD84C0EED0 /* D-DIN-PRO-400-Regular.ttf */; };
		6B58C57A2E262A7F0066EA87 /* XLBAVPlayerBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 6B58C5782E262A7F0066EA87 /* XLBAVPlayerBridge.m */; };
		6B58C57B2E262A7F0066EA87 /* XLBAVPlayerBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6B58C5792E262A7F0066EA87 /* XLBAVPlayerBridge.swift */; };
		6B58C5822E2FA1470066EA87 /* UMAnalytics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6B58C5802E2FA1470066EA87 /* UMAnalytics.framework */; };
		6B58C5832E2FA1470066EA87 /* UMCommon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6B58C5812E2FA1470066EA87 /* UMCommon.framework */; };
		6B58C5952E3083C40066EA87 /* NetworkSettings.m in Sources */ = {isa = PBXBuildFile; fileRef = 6B58C5932E3083C40066EA87 /* NetworkSettings.m */; };
		6BAD713B2E1F525100EDED32 /* OpenWechat.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6BAD713A2E1F525100EDED32 /* OpenWechat.swift */; };
		6BAD713D2E1F526200EDED32 /* OpenWechatBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 6BAD713C2E1F526200EDED32 /* OpenWechatBridge.m */; };
		6BEFD4DD661744D4B2A96A57 /* D-DIN-PRO-500-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C71B3538CED94B7D8B2DFF35 /* D-DIN-PRO-500-Medium.ttf */; };
		761780ED2CA45674006654EE /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 761780EC2CA45674006654EE /* AppDelegate.swift */; };
		79E80A5522EF42A89C77CB29 /* D-DIN-PRO-600-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A345BC639074571AABC9A9A /* D-DIN-PRO-600-SemiBold.ttf */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		832310179A344A518436DC22 /* HarmonyOS_Sans_Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 383706F31D884FD0B0914700 /* HarmonyOS_Sans_Regular.ttf */; };
		894BADB92E0D4E4A00F48566 /* SyncScrollManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 894BADB82E0D4E3F00F48566 /* SyncScrollManager.swift */; };
		894BADBB2E0D4E5D00F48566 /* SyncScrollView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 894BADBA2E0D4E5A00F48566 /* SyncScrollView.swift */; };
		894BADBD2E0D500D00F48566 /* SyncScrollManagerBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 894BADBC2E0D500A00F48566 /* SyncScrollManagerBridge.m */; };
		89EE81882DF7F70800C65C80 /* BridgingHeader.h in Sources */ = {isa = PBXBuildFile; fileRef = 89EE81872DF7F6F000C65C80 /* BridgingHeader.h */; };
		A32DC72B534345C48AEACF1E /* iconfontNew.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 951D4EE0D01D411BA62CC452 /* iconfontNew.ttf */; };
		ABD6718E3E42467E82181889 /* iconfont.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 00FD693FAB0F4655ACCD40D0 /* iconfont.ttf */; };
		B3DD93DBE3B02EACE4FD29E5 /* libPods-a608.a in Frameworks */ = {isa = PBXBuildFile; fileRef = D22C2632ED6E3E8DF696E473 /* libPods-a608.a */; };
		BF9918C62E23DC5100277B25 /* NativeHotUpdate.swift in Sources */ = {isa = PBXBuildFile; fileRef = BF9918C52E23DC4A00277B25 /* NativeHotUpdate.swift */; };
		BF9918C92E23EC4500277B25 /* NativeHotUpdate.m in Sources */ = {isa = PBXBuildFile; fileRef = BF9918C82E23EC4500277B25 /* NativeHotUpdate.m */; };
		C7B217CE2E1D4E4300AF5693 /* ScreenRotateManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C7B217CD2E1D4E4300AF5693 /* ScreenRotateManager.swift */; };
		C7B217D22E1E12A300AF5693 /* ScreenRotateManagerBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = C7B217D12E1E12A300AF5693 /* ScreenRotateManagerBridge.m */; };
		CC0ED7382C5E4C6088985723 /* HarmonyOS_Sans_Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 421BE7740F4548B4B877A31A /* HarmonyOS_Sans_Black.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		00FD693FAB0F4655ACCD40D0 /* iconfont.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = iconfont.ttf; path = ../packages/common/src/assets/fonts/iconfont.ttf; sourceTree = "<group>"; };
		0D8737AA2E260226005B375C /* MABussinessPolygon.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABussinessPolygon.h; sourceTree = "<group>"; };
		0D8737AB2E260226005B375C /* MABussinessPolygon.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABussinessPolygon.m; sourceTree = "<group>"; };
		0D8737AC2E260226005B375C /* MABussinessPolygonRenderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABussinessPolygonRenderer.h; sourceTree = "<group>"; };
		0D8737AD2E260226005B375C /* MABussinessPolygonRenderer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABussinessPolygonRenderer.m; sourceTree = "<group>"; };
		0D8737AE2E260226005B375C /* MACreatBuniessPointAnnotation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACreatBuniessPointAnnotation.h; sourceTree = "<group>"; };
		0D8737AF2E260226005B375C /* MACreatBuniessPointAnnotation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACreatBuniessPointAnnotation.m; sourceTree = "<group>"; };
		0D8737B02E260226005B375C /* MACreatBuniessPointAnnView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACreatBuniessPointAnnView.h; sourceTree = "<group>"; };
		0D8737B12E260226005B375C /* MACreatBuniessPointAnnView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACreatBuniessPointAnnView.m; sourceTree = "<group>"; };
		0D8737B22E260226005B375C /* MACreatBuniessPolygon.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACreatBuniessPolygon.h; sourceTree = "<group>"; };
		0D8737B32E260226005B375C /* MACreatBuniessPolygon.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACreatBuniessPolygon.m; sourceTree = "<group>"; };
		0D8737B42E260226005B375C /* MACreatBuniessPolygonRender.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACreatBuniessPolygonRender.h; sourceTree = "<group>"; };
		0D8737B52E260226005B375C /* MACreatBuniessPolygonRender.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACreatBuniessPolygonRender.m; sourceTree = "<group>"; };
		0D8737B62E260226005B375C /* MACreatBuniessPolyline.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACreatBuniessPolyline.h; sourceTree = "<group>"; };
		0D8737B72E260226005B375C /* MACreatBuniessPolyline.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACreatBuniessPolyline.m; sourceTree = "<group>"; };
		0D8737B82E260226005B375C /* MALocalAnnotation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MALocalAnnotation.h; sourceTree = "<group>"; };
		0D8737B92E260226005B375C /* MALocalAnnotation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MALocalAnnotation.m; sourceTree = "<group>"; };
		0D8737BA2E260226005B375C /* MALocalAnnotationView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MALocalAnnotationView.h; sourceTree = "<group>"; };
		0D8737BB2E260226005B375C /* MALocalAnnotationView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MALocalAnnotationView.m; sourceTree = "<group>"; };
		0D8737BC2E260226005B375C /* MAUserAnnotationView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAUserAnnotationView.h; sourceTree = "<group>"; };
		0D8737BD2E260226005B375C /* MAUserAnnotationView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAUserAnnotationView.m; sourceTree = "<group>"; };
		0D8737BE2E260226005B375C /* MACenterBuniessAnnotation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACenterBuniessAnnotation.h; sourceTree = "<group>"; };
		0D8737BF2E260226005B375C /* MACenterBuniessAnnotation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACenterBuniessAnnotation.m; sourceTree = "<group>"; };
		0D8737C02E260226005B375C /* MACenterBuniessAnnView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACenterBuniessAnnView.h; sourceTree = "<group>"; };
		0D8737C12E260226005B375C /* MACenterBuniessAnnView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACenterBuniessAnnView.m; sourceTree = "<group>"; };
		0D8737C22E260226005B375C /* MAOpenStorePolygon.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAOpenStorePolygon.h; sourceTree = "<group>"; };
		0D8737C32E260226005B375C /* MAOpenStorePolygon.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAOpenStorePolygon.m; sourceTree = "<group>"; };
		0D8737C42E260226005B375C /* MAOpenStorePolyline.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAOpenStorePolyline.h; sourceTree = "<group>"; };
		0D8737C52E260226005B375C /* MAOpenStorePolyline.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAOpenStorePolyline.m; sourceTree = "<group>"; };
		0D8737C72E260226005B375C /* FXPageControl.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FXPageControl.h; sourceTree = "<group>"; };
		0D8737C82E260226005B375C /* FXPageControl.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FXPageControl.m; sourceTree = "<group>"; };
		0D8737CA2E260226005B375C /* BHInfiniteScrollViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BHInfiniteScrollViewCell.h; sourceTree = "<group>"; };
		0D8737CB2E260226005B375C /* BHInfiniteScrollViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BHInfiniteScrollViewCell.m; sourceTree = "<group>"; };
		0D8737CC2E260226005B375C /* BHInfiniteScrollViewTitleView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BHInfiniteScrollViewTitleView.h; sourceTree = "<group>"; };
		0D8737CD2E260226005B375C /* BHInfiniteScrollViewTitleView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BHInfiniteScrollViewTitleView.m; sourceTree = "<group>"; };
		0D8737CF2E260226005B375C /* BHInfiniteScrollView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BHInfiniteScrollView.h; sourceTree = "<group>"; };
		0D8737D02E260226005B375C /* BHInfiniteScrollView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BHInfiniteScrollView.m; sourceTree = "<group>"; };
		0D8737D32E260226005B375C /* UIView+Toast.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+Toast.h"; sourceTree = "<group>"; };
		0D8737D42E260226005B375C /* UIView+Toast.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+Toast.m"; sourceTree = "<group>"; };
		0D8737D62E260226005B375C /* ZKPhotoBrowser.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = ZKPhotoBrowser.bundle; sourceTree = "<group>"; };
		0D8737D72E260226005B375C /* ZKPhotoLoadingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZKPhotoLoadingView.h; sourceTree = "<group>"; };
		0D8737D82E260226005B375C /* ZKPhotoLoadingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZKPhotoLoadingView.m; sourceTree = "<group>"; };
		0D8737D92E260226005B375C /* ZKPhotoProgressView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZKPhotoProgressView.h; sourceTree = "<group>"; };
		0D8737DA2E260226005B375C /* ZKPhotoProgressView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZKPhotoProgressView.m; sourceTree = "<group>"; };
		0D8737DC2E260226005B375C /* ZKPhoto.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZKPhoto.h; sourceTree = "<group>"; };
		0D8737DD2E260226005B375C /* ZKPhoto.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZKPhoto.m; sourceTree = "<group>"; };
		0D8737DE2E260226005B375C /* ZKPhotoBrowser.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZKPhotoBrowser.h; sourceTree = "<group>"; };
		0D8737DF2E260226005B375C /* ZKPhotoBrowser.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZKPhotoBrowser.m; sourceTree = "<group>"; };
		0D8737E02E260226005B375C /* ZKPhotoToolbar.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZKPhotoToolbar.h; sourceTree = "<group>"; };
		0D8737E12E260226005B375C /* ZKPhotoToolbar.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZKPhotoToolbar.m; sourceTree = "<group>"; };
		0D8737E22E260226005B375C /* ZKPhotoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZKPhotoView.h; sourceTree = "<group>"; };
		0D8737E32E260226005B375C /* ZKPhotoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZKPhotoView.m; sourceTree = "<group>"; };
		0D8737E52E260226005B375C /* MAAllFilterItemView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAAllFilterItemView.h; sourceTree = "<group>"; };
		0D8737E62E260226005B375C /* MAAllFilterItemView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAAllFilterItemView.m; sourceTree = "<group>"; };
		0D8737E72E260226005B375C /* MATopToastView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MATopToastView.h; sourceTree = "<group>"; };
		0D8737E82E260226005B375C /* MATopToastView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MATopToastView.m; sourceTree = "<group>"; };
		0D8737E92E260226005B375C /* MATopNotiflyView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MATopNotiflyView.h; sourceTree = "<group>"; };
		0D8737EA2E260226005B375C /* MATopNotiflyView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MATopNotiflyView.m; sourceTree = "<group>"; };
		0D8737EB2E260226005B375C /* MABottomToast.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABottomToast.h; sourceTree = "<group>"; };
		0D8737EC2E260226005B375C /* MABottomToast.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABottomToast.m; sourceTree = "<group>"; };
		0D8737ED2E260226005B375C /* MAAllFilterView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAAllFilterView.h; sourceTree = "<group>"; };
		0D8737EE2E260226005B375C /* MAAllFilterView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAAllFilterView.m; sourceTree = "<group>"; };
		0D8737EF2E260226005B375C /* MABottomMutilSelectAlert.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABottomMutilSelectAlert.h; sourceTree = "<group>"; };
		0D8737F02E260226005B375C /* MABottomMutilSelectAlert.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABottomMutilSelectAlert.m; sourceTree = "<group>"; };
		0D8737F12E260226005B375C /* MABottomPointAlert.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABottomPointAlert.h; sourceTree = "<group>"; };
		0D8737F22E260226005B375C /* MABottomPointAlert.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABottomPointAlert.m; sourceTree = "<group>"; };
		0D8737F32E260226005B375C /* MABottomSingleSelectAlert.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABottomSingleSelectAlert.h; sourceTree = "<group>"; };
		0D8737F42E260226005B375C /* MABottomSingleSelectAlert.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABottomSingleSelectAlert.m; sourceTree = "<group>"; };
		0D8737F52E260226005B375C /* MABuniessScrollView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABuniessScrollView.h; sourceTree = "<group>"; };
		0D8737F62E260226005B375C /* MABuniessScrollView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABuniessScrollView.m; sourceTree = "<group>"; };
		0D8737F72E260226005B375C /* MABuniessTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABuniessTableViewCell.h; sourceTree = "<group>"; };
		0D8737F82E260226005B375C /* MABuniessTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABuniessTableViewCell.m; sourceTree = "<group>"; };
		0D8737F92E260226005B375C /* MAChooseCityView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAChooseCityView.h; sourceTree = "<group>"; };
		0D8737FA2E260226005B375C /* MAChooseCityView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAChooseCityView.m; sourceTree = "<group>"; };
		0D8737FB2E260226005B375C /* MACooperateTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACooperateTableViewCell.h; sourceTree = "<group>"; };
		0D8737FC2E260226005B375C /* MACooperateTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACooperateTableViewCell.m; sourceTree = "<group>"; };
		0D8737FD2E260226005B375C /* MACreatBuniessDrawBoardView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACreatBuniessDrawBoardView.h; sourceTree = "<group>"; };
		0D8737FE2E260226005B375C /* MACreatBuniessDrawBoardView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACreatBuniessDrawBoardView.m; sourceTree = "<group>"; };
		0D8737FF2E260226005B375C /* MACreatBuniessView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACreatBuniessView.h; sourceTree = "<group>"; };
		0D8738002E260226005B375C /* MACreatBuniessView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACreatBuniessView.m; sourceTree = "<group>"; };
		0D8738012E260226005B375C /* MAFilterItemView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAFilterItemView.h; sourceTree = "<group>"; };
		0D8738022E260226005B375C /* MAFilterItemView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAFilterItemView.m; sourceTree = "<group>"; };
		0D8738032E260226005B375C /* UICollectionViewLeftAlignedLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UICollectionViewLeftAlignedLayout.h; sourceTree = "<group>"; };
		0D8738042E260226005B375C /* UICollectionViewLeftAlignedLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UICollectionViewLeftAlignedLayout.m; sourceTree = "<group>"; };
		0D8738052E260226005B375C /* MAFilterChooseCityCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAFilterChooseCityCell.h; sourceTree = "<group>"; };
		0D8738062E260226005B375C /* MAFilterChooseCityCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAFilterChooseCityCell.m; sourceTree = "<group>"; };
		0D8738072E260226005B375C /* MAFilterChooseCityItem.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAFilterChooseCityItem.h; sourceTree = "<group>"; };
		0D8738082E260226005B375C /* MAFilterChooseCityItem.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAFilterChooseCityItem.m; sourceTree = "<group>"; };
		0D8738092E260226005B375C /* MAMutiChooseCityTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAMutiChooseCityTableViewCell.h; sourceTree = "<group>"; };
		0D87380A2E260226005B375C /* MAMutiChooseCityTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAMutiChooseCityTableViewCell.m; sourceTree = "<group>"; };
		0D87380B2E260226005B375C /* MABottomMutiChooseCityView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABottomMutiChooseCityView.h; sourceTree = "<group>"; };
		0D87380C2E260226005B375C /* MABottomMutiChooseCityView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABottomMutiChooseCityView.m; sourceTree = "<group>"; };
		0D87380D2E260226005B375C /* MAFilterModal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAFilterModal.h; sourceTree = "<group>"; };
		0D87380E2E260226005B375C /* MAFilterModal.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAFilterModal.m; sourceTree = "<group>"; };
		0D87380F2E260226005B375C /* MAFilterTextField.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAFilterTextField.h; sourceTree = "<group>"; };
		0D8738102E260226005B375C /* MAFilterTextField.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAFilterTextField.m; sourceTree = "<group>"; };
		0D8738112E260226005B375C /* MAHorScrollview.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAHorScrollview.h; sourceTree = "<group>"; };
		0D8738122E260226005B375C /* MAHorScrollview.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAHorScrollview.m; sourceTree = "<group>"; };
		0D8738132E260226005B375C /* MALoadAlphaView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MALoadAlphaView.h; sourceTree = "<group>"; };
		0D8738142E260226005B375C /* MALoadAlphaView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MALoadAlphaView.m; sourceTree = "<group>"; };
		0D8738152E260226005B375C /* MALoadWaveView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MALoadWaveView.h; sourceTree = "<group>"; };
		0D8738162E260226005B375C /* MALoadWaveView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MALoadWaveView.m; sourceTree = "<group>"; };
		0D8738172E260226005B375C /* MAManagerFilterTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAManagerFilterTableViewCell.h; sourceTree = "<group>"; };
		0D8738182E260226005B375C /* MAManagerFilterTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAManagerFilterTableViewCell.m; sourceTree = "<group>"; };
		0D8738192E260226005B375C /* MAMapSwitchView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAMapSwitchView.h; sourceTree = "<group>"; };
		0D87381A2E260226005B375C /* MAMapSwitchView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAMapSwitchView.m; sourceTree = "<group>"; };
		0D87381B2E260226005B375C /* MANativeAlert.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MANativeAlert.h; sourceTree = "<group>"; };
		0D87381C2E260226005B375C /* MANativeAlert.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MANativeAlert.m; sourceTree = "<group>"; };
		0D87381D2E260226005B375C /* MAPointModalView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAPointModalView.h; sourceTree = "<group>"; };
		0D87381E2E260226005B375C /* MAPointModalView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAPointModalView.m; sourceTree = "<group>"; };
		0D87381F2E260226005B375C /* MAPointTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAPointTableViewCell.h; sourceTree = "<group>"; };
		0D8738202E260226005B375C /* MAPointTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAPointTableViewCell.m; sourceTree = "<group>"; };
		0D8738212E260226005B375C /* MAPoiTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAPoiTableViewCell.h; sourceTree = "<group>"; };
		0D8738222E260226005B375C /* MAPoiTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAPoiTableViewCell.m; sourceTree = "<group>"; };
		0D8738232E260226005B375C /* MASearchLoadingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASearchLoadingView.h; sourceTree = "<group>"; };
		0D8738242E260226005B375C /* MASearchLoadingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASearchLoadingView.m; sourceTree = "<group>"; };
		0D8738252E260226005B375C /* MASearchTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASearchTableViewCell.h; sourceTree = "<group>"; };
		0D8738262E260226005B375C /* MASearchTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASearchTableViewCell.m; sourceTree = "<group>"; };
		0D8738272E260226005B375C /* MASearchTextField.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASearchTextField.h; sourceTree = "<group>"; };
		0D8738282E260226005B375C /* MASearchTextField.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASearchTextField.m; sourceTree = "<group>"; };
		0D8738292E260226005B375C /* MAShareUITableView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAShareUITableView.h; sourceTree = "<group>"; };
		0D87382A2E260226005B375C /* MAShareUITableView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAShareUITableView.m; sourceTree = "<group>"; };
		0D87382B2E260226005B375C /* MASingleDateView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASingleDateView.h; sourceTree = "<group>"; };
		0D87382C2E260226005B375C /* MASingleDateView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASingleDateView.m; sourceTree = "<group>"; };
		0D87382D2E260226005B375C /* MASingleSelectView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASingleSelectView.h; sourceTree = "<group>"; };
		0D87382E2E260226005B375C /* MASingleSelectView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASingleSelectView.m; sourceTree = "<group>"; };
		0D87382F2E260226005B375C /* MAStoreTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAStoreTableViewCell.h; sourceTree = "<group>"; };
		0D8738302E260226005B375C /* MAStoreTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAStoreTableViewCell.m; sourceTree = "<group>"; };
		0D8738312E260226005B375C /* MAUIScrollView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAUIScrollView.h; sourceTree = "<group>"; };
		0D8738322E260226005B375C /* MAUIScrollView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAUIScrollView.m; sourceTree = "<group>"; };
		0D8738332E260226005B375C /* MAUploadFileView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAUploadFileView.h; sourceTree = "<group>"; };
		0D8738342E260226005B375C /* MAUploadFileView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAUploadFileView.m; sourceTree = "<group>"; };
		0D8738352E260226005B375C /* MAUploadFileViewTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAUploadFileViewTableViewCell.h; sourceTree = "<group>"; };
		0D8738362E260226005B375C /* MAUploadFileViewTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAUploadFileViewTableViewCell.m; sourceTree = "<group>"; };
		0D8738372E260226005B375C /* MAUploadMutilCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAUploadMutilCell.h; sourceTree = "<group>"; };
		0D8738382E260226005B375C /* MAUploadMutilCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAUploadMutilCell.m; sourceTree = "<group>"; };
		0D8738392E260226005B375C /* MAUploadMutilImageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAUploadMutilImageView.h; sourceTree = "<group>"; };
		0D87383A2E260226005B375C /* MAUploadMutilImageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAUploadMutilImageView.m; sourceTree = "<group>"; };
		0D87383B2E260226005B375C /* MACustomTableView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACustomTableView.h; sourceTree = "<group>"; };
		0D87383C2E260226005B375C /* MACustomTableView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACustomTableView.m; sourceTree = "<group>"; };
		0D87383D2E260226005B375C /* MACustomBuniessTableView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACustomBuniessTableView.h; sourceTree = "<group>"; };
		0D87383E2E260226005B375C /* MACustomBuniessTableView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACustomBuniessTableView.m; sourceTree = "<group>"; };
		0D87383F2E260226005B375C /* MAUploadSingleImageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAUploadSingleImageView.h; sourceTree = "<group>"; };
		0D8738402E260226005B375C /* MAUploadSingleImageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAUploadSingleImageView.m; sourceTree = "<group>"; };
		0D8738412E260226005B375C /* MKLabel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MKLabel.h; sourceTree = "<group>"; };
		0D8738422E260226005B375C /* MKLabel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MKLabel.m; sourceTree = "<group>"; };
		0D8738432E260226005B375C /* MAShareView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAShareView.h; sourceTree = "<group>"; };
		0D8738442E260226005B375C /* MAShareView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAShareView.m; sourceTree = "<group>"; };
		0D8738452E260226005B375C /* MAShareChooseView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAShareChooseView.h; sourceTree = "<group>"; };
		0D8738462E260226005B375C /* MAShareChooseView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAShareChooseView.m; sourceTree = "<group>"; };
		0D8738472E260226005B375C /* MAWriteMeassageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAWriteMeassageView.h; sourceTree = "<group>"; };
		0D8738482E260226005B375C /* MAWriteMeassageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAWriteMeassageView.m; sourceTree = "<group>"; };
		0D8738492E260226005B375C /* MAWriteReplyView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAWriteReplyView.h; sourceTree = "<group>"; };
		0D87384A2E260226005B375C /* MAWriteReplyView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAWriteReplyView.m; sourceTree = "<group>"; };
		0D87384B2E260226005B375C /* MABottomOpereateView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABottomOpereateView.h; sourceTree = "<group>"; };
		0D87384C2E260226005B375C /* MABottomOpereateView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABottomOpereateView.m; sourceTree = "<group>"; };
		0D87384D2E260226005B375C /* MAApprovalRecordView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAApprovalRecordView.h; sourceTree = "<group>"; };
		0D87384E2E260226005B375C /* MAApprovalRecordView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAApprovalRecordView.m; sourceTree = "<group>"; };
		0D87384F2E260226005B375C /* MACopyListView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MACopyListView.h; sourceTree = "<group>"; };
		0D8738502E260226005B375C /* MACopyListView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MACopyListView.m; sourceTree = "<group>"; };
		0D8738512E260226005B375C /* MAReadListView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAReadListView.h; sourceTree = "<group>"; };
		0D8738522E260226005B375C /* MAReadListView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAReadListView.m; sourceTree = "<group>"; };
		0D8738532E260226005B375C /* MAOpenStoreView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAOpenStoreView.h; sourceTree = "<group>"; };
		0D8738542E260226005B375C /* MAOpenStoreView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAOpenStoreView.m; sourceTree = "<group>"; };
		0D8738552E260226005B375C /* MAOpenStoreViewTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAOpenStoreViewTableViewCell.h; sourceTree = "<group>"; };
		0D8738562E260226005B375C /* MAOpenStoreViewTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAOpenStoreViewTableViewCell.m; sourceTree = "<group>"; };
		0D8738572E260226005B375C /* MAEditOpenStoreView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAEditOpenStoreView.h; sourceTree = "<group>"; };
		0D8738582E260226005B375C /* MAEditOpenStoreView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAEditOpenStoreView.m; sourceTree = "<group>"; };
		0D87385A2E260226005B375C /* icon_showmap.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_showmap.png; sourceTree = "<group>"; };
		0D87385B2E260226005B375C /* openstorearea_bottom.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = openstorearea_bottom.png; sourceTree = "<group>"; };
		0D87385C2E260226005B375C /* openstorearea_mid.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = openstorearea_mid.png; sourceTree = "<group>"; };
		0D87385D2E260226005B375C /* openstorearea_top.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = openstorearea_top.png; sourceTree = "<group>"; };
		0D87385E2E260226005B375C /* openstoreline_bottom.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = openstoreline_bottom.png; sourceTree = "<group>"; };
		0D87385F2E260226005B375C /* openstoreline_mid.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = openstoreline_mid.png; sourceTree = "<group>"; };
		0D8738602E260226005B375C /* openstoreline_top.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = openstoreline_top.png; sourceTree = "<group>"; };
		0D8738612E260226005B375C /* openstore_top.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = openstore_top.png; sourceTree = "<group>"; };
		0D8738622E260226005B375C /* openstore_mid.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = openstore_mid.png; sourceTree = "<group>"; };
		0D8738632E260226005B375C /* openstore_bottom.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = openstore_bottom.png; sourceTree = "<group>"; };
		0D8738642E260226005B375C /* draw_point.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = draw_point.png; sourceTree = "<group>"; };
		0D8738652E260226005B375C /* open_creatpoint.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = open_creatpoint.png; sourceTree = "<group>"; };
		0D8738662E260226005B375C /* changeitemdelete.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = changeitemdelete.png; sourceTree = "<group>"; };
		0D8738672E260226005B375C /* navtargeticon.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = navtargeticon.png; sourceTree = "<group>"; };
		0D8738682E260226005B375C /* locationchangeitem.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = locationchangeitem.png; sourceTree = "<group>"; };
		0D8738692E260226005B375C /* locationrefresh.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = locationrefresh.png; sourceTree = "<group>"; };
		0D87386A2E260226005B375C /* infonomal.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = infonomal.png; sourceTree = "<group>"; };
		0D87386B2E260226005B375C /* notifierror.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = notifierror.png; sourceTree = "<group>"; };
		0D87386C2E260226005B375C /* notifisuccess.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = notifisuccess.png; sourceTree = "<group>"; };
		0D87386D2E260226005B375C /* icon_tipfollow.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_tipfollow.png; sourceTree = "<group>"; };
		0D87386E2E260226005B375C /* map_blue.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = map_blue.png; sourceTree = "<group>"; };
		0D87386F2E260226005B375C /* map_gray.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = map_gray.png; sourceTree = "<group>"; };
		0D8738702E260226005B375C /* map_green.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = map_green.png; sourceTree = "<group>"; };
		0D8738712E260226005B375C /* map_oregen.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = map_oregen.png; sourceTree = "<group>"; };
		0D8738722E260226005B375C /* map_red.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = map_red.png; sourceTree = "<group>"; };
		0D8738732E260226005B375C /* search_place.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = search_place.png; sourceTree = "<group>"; };
		0D8738742E260226005B375C /* apple_navi.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = apple_navi.png; sourceTree = "<group>"; };
		0D8738752E260226005B375C /* apple_loc.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = apple_loc.png; sourceTree = "<group>"; };
		0D8738762E260226005B375C /* icon_copyname.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_copyname.png; sourceTree = "<group>"; };
		0D8738772E260226005B375C /* apple_back.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = apple_back.png; sourceTree = "<group>"; };
		0D8738782E260226005B375C /* city_nochose.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = city_nochose.png; sourceTree = "<group>"; };
		0D8738792E260226005B375C /* city_halfchoose.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = city_halfchoose.png; sourceTree = "<group>"; };
		0D87387A2E260226005B375C /* city_allchoose.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = city_allchoose.png; sourceTree = "<group>"; };
		0D87387B2E260226005B375C /* buniess_time.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = buniess_time.png; sourceTree = "<group>"; };
		0D87387C2E260226005B375C /* share_lookhead.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = share_lookhead.png; sourceTree = "<group>"; };
		0D87387D2E260226005B375C /* audit_notread.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = audit_notread.png; sourceTree = "<group>"; };
		0D87387E2E260226005B375C /* audit_read.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = audit_read.png; sourceTree = "<group>"; };
		0D87387F2E260226005B375C /* audit_system.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = audit_system.png; sourceTree = "<group>"; };
		0D8738802E260226005B375C /* audit_reject.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = audit_reject.png; sourceTree = "<group>"; };
		0D8738812E260226005B375C /* audit_wait.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = audit_wait.png; sourceTree = "<group>"; };
		0D8738822E260226005B375C /* audit_withdraw.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = audit_withdraw.png; sourceTree = "<group>"; };
		0D8738832E260226005B375C /* audit_approve.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = audit_approve.png; sourceTree = "<group>"; };
		0D8738842E260226005B375C /* detail_comment.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = detail_comment.png; sourceTree = "<group>"; };
		0D8738852E260226005B375C /* bottom_reopen.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bottom_reopen.png; sourceTree = "<group>"; };
		0D8738862E260226005B375C /* bottom_agree.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bottom_agree.png; sourceTree = "<group>"; };
		0D8738872E260226005B375C /* bottom_approve.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bottom_approve.png; sourceTree = "<group>"; };
		0D8738882E260226005B375C /* bottom_change.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bottom_change.png; sourceTree = "<group>"; };
		0D8738892E260226005B375C /* bottom_delete.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bottom_delete.png; sourceTree = "<group>"; };
		0D87388A2E260226005B375C /* bottom_edit.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bottom_edit.png; sourceTree = "<group>"; };
		0D87388B2E260226005B375C /* bottom_copy.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bottom_copy.png; sourceTree = "<group>"; };
		0D87388C2E260226005B375C /* bottom_follow.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bottom_follow.png; sourceTree = "<group>"; };
		0D87388D2E260226005B375C /* bottom_more.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bottom_more.png; sourceTree = "<group>"; };
		0D87388E2E260226005B375C /* bottom_reject.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bottom_reject.png; sourceTree = "<group>"; };
		0D87388F2E260226005B375C /* bottom_revoke.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bottom_revoke.png; sourceTree = "<group>"; };
		0D8738902E260226005B375C /* follow_noshareright.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = follow_noshareright.png; sourceTree = "<group>"; };
		0D8738912E260226005B375C /* follow_noshare.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = follow_noshare.png; sourceTree = "<group>"; };
		0D8738922E260226005B375C /* share_close.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = share_close.png; sourceTree = "<group>"; };
		0D8738932E260226005B375C /* icon_tip.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_tip.png; sourceTree = "<group>"; };
		0D8738942E260226005B375C /* icon_mylo.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_mylo.png; sourceTree = "<group>"; };
		0D8738952E260226005B375C /* icon_closeshare.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_closeshare.png; sourceTree = "<group>"; };
		0D8738962E260226005B375C /* icon_share.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_share.png; sourceTree = "<group>"; };
		0D8738972E260226005B375C /* add_landlord.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = add_landlord.png; sourceTree = "<group>"; };
		0D8738982E260226005B375C /* add.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = add.png; sourceTree = "<group>"; };
		0D8738992E260226005B375C /* audit_record.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = audit_record.png; sourceTree = "<group>"; };
		0D87389A2E260226005B375C /* back_draw.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = back_draw.png; sourceTree = "<group>"; };
		0D87389B2E260226005B375C /* back_drawNo.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = back_drawNo.png; sourceTree = "<group>"; };
		0D87389C2E260226005B375C /* back_user.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = back_user.png; sourceTree = "<group>"; };
		0D87389D2E260226005B375C /* back_userS.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = back_userS.png; sourceTree = "<group>"; };
		0D87389E2E260226005B375C /* blue_map.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = blue_map.png; sourceTree = "<group>"; };
		0D87389F2E260226005B375C /* blue_newmap.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = blue_newmap.png; sourceTree = "<group>"; };
		0D8738A02E260226005B375C /* bottom_close.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bottom_close.png; sourceTree = "<group>"; };
		0D8738A12E260226005B375C /* build_num.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = build_num.png; sourceTree = "<group>"; };
		0D8738A22E260226005B375C /* buiness_miniblue.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = buiness_miniblue.png; sourceTree = "<group>"; };
		0D8738A32E260226005B375C /* buiness_minigray.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = buiness_minigray.png; sourceTree = "<group>"; };
		0D8738A42E260226005B375C /* buniess_blue.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = buniess_blue.png; sourceTree = "<group>"; };
		0D8738A52E260226005B375C /* buniess_gray.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = buniess_gray.png; sourceTree = "<group>"; };
		0D8738A62E260226005B375C /* buniess_minigreen.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = buniess_minigreen.png; sourceTree = "<group>"; };
		0D8738A72E260226005B375C /* buniess_green.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = buniess_green.png; sourceTree = "<group>"; };
		0D8738A82E260226005B375C /* buniess_minioragon.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = buniess_minioragon.png; sourceTree = "<group>"; };
		0D8738A92E260226005B375C /* buniess_yellow.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = buniess_yellow.png; sourceTree = "<group>"; };
		0D8738AA2E260226005B375C /* clear.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = clear.png; sourceTree = "<group>"; };
		0D8738AB2E260226005B375C /* close_btn.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = close_btn.png; sourceTree = "<group>"; };
		0D8738AC2E260226005B375C /* complete_draw.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = complete_draw.png; sourceTree = "<group>"; };
		0D8738AD2E260226005B375C /* complete_drawNo.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = complete_drawNo.png; sourceTree = "<group>"; };
		0D8738AE2E260226005B375C /* creat_buniess.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = creat_buniess.png; sourceTree = "<group>"; };
		0D8738AF2E260226005B375C /* file_excel.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = file_excel.png; sourceTree = "<group>"; };
		0D8738B02E260226005B375C /* file_img.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = file_img.png; sourceTree = "<group>"; };
		0D8738B12E260226005B375C /* file_music.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = file_music.png; sourceTree = "<group>"; };
		0D8738B22E260226005B375C /* file_no.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = file_no.png; sourceTree = "<group>"; };
		0D8738B32E260226005B375C /* file_pdf.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = file_pdf.png; sourceTree = "<group>"; };
		0D8738B42E260226005B375C /* file_ppt.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = file_ppt.png; sourceTree = "<group>"; };
		0D8738B52E260226005B375C /* file_word.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = file_word.png; sourceTree = "<group>"; };
		0D8738B62E260226005B375C /* gonggong_map.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = gonggong_map.png; sourceTree = "<group>"; };
		0D8738B72E260226005B375C /* heat_map.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = heat_map.png; sourceTree = "<group>"; };
		0D8738B82E260226005B375C /* heat_mapS.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = heat_mapS.png; sourceTree = "<group>"; };
		0D8738B92E260226005B375C /* help_icon.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = help_icon.png; sourceTree = "<group>"; };
		0D8738BA2E260226005B375C /* icon_addp.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_addp.png; sourceTree = "<group>"; };
		0D8738BB2E260226005B375C /* icon_blue.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_blue.png; sourceTree = "<group>"; };
		0D8738BC2E260226005B375C /* icon_camer.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_camer.png; sourceTree = "<group>"; };
		0D8738BD2E260226005B375C /* icon_cardb.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_cardb.png; sourceTree = "<group>"; };
		0D8738BE2E260226005B375C /* icon_cardf.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_cardf.png; sourceTree = "<group>"; };
		0D8738BF2E260226005B375C /* icon_cbuniess.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_cbuniess.png; sourceTree = "<group>"; };
		0D8738C02E260226005B375C /* icon_choose.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_choose.png; sourceTree = "<group>"; };
		0D8738C12E260226005B375C /* icon_closeAlert.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_closeAlert.png; sourceTree = "<group>"; };
		0D8738C22E260226005B375C /* icon_cpoint.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_cpoint.png; sourceTree = "<group>"; };
		0D8738C32E260226005B375C /* icon_creatp.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_creatp.png; sourceTree = "<group>"; };
		0D8738C42E260226005B375C /* icon_deleL.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_deleL.png; sourceTree = "<group>"; };
		0D8738C52E260226005B375C /* icon_deletefile.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_deletefile.png; sourceTree = "<group>"; };
		0D8738C62E260226005B375C /* icon_deleteL.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_deleteL.png; sourceTree = "<group>"; };
		0D8738C72E260226005B375C /* icon_deleteS.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_deleteS.png; sourceTree = "<group>"; };
		0D8738C82E260226005B375C /* icon_direct.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_direct.png; sourceTree = "<group>"; };
		0D8738C92E260226005B375C /* icon_downarrow.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_downarrow.png; sourceTree = "<group>"; };
		0D8738CA2E260226005B375C /* icon_draft.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_draft.png; sourceTree = "<group>"; };
		0D8738CB2E260226005B375C /* icon_drag.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_drag.png; sourceTree = "<group>"; };
		0D8738CC2E260226005B375C /* icon_edit.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_edit.png; sourceTree = "<group>"; };
		0D8738CD2E260226005B375C /* icon_empty.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_empty.png; sourceTree = "<group>"; };
		0D8738CE2E260226005B375C /* icon_gray.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_gray.png; sourceTree = "<group>"; };
		0D8738CF2E260226005B375C /* icon_green.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_green.png; sourceTree = "<group>"; };
		0D8738D02E260226005B375C /* icon_halfselect.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_halfselect.png; sourceTree = "<group>"; };
		0D8738D12E260226005B375C /* icon_head.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_head.png; sourceTree = "<group>"; };
		0D8738D22E260226005B375C /* icon_leftse.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_leftse.png; sourceTree = "<group>"; };
		0D8738D32E260226005B375C /* icon_leftstore.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_leftstore.png; sourceTree = "<group>"; };
		0D8738D42E260226005B375C /* icon_liscen.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_liscen.png; sourceTree = "<group>"; };
		0D8738D52E260226005B375C /* icon_more.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_more.png; sourceTree = "<group>"; };
		0D8738D62E260226005B375C /* icon_mutilno.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_mutilno.png; sourceTree = "<group>"; };
		0D8738D72E260226005B375C /* icon_mutilselect.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_mutilselect.png; sourceTree = "<group>"; };
		0D8738D82E260226005B375C /* icon_newback.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_newback.png; sourceTree = "<group>"; };
		0D8738D92E260226005B375C /* icon_newpla.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_newpla.png; sourceTree = "<group>"; };
		0D8738DA2E260226005B375C /* icon_newsearch.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_newsearch.png; sourceTree = "<group>"; };
		0D8738DB2E260226005B375C /* icon_nochoose.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_nochoose.png; sourceTree = "<group>"; };
		0D8738DC2E260226005B375C /* icon_orange.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_orange.png; sourceTree = "<group>"; };
		0D8738DD2E260226005B375C /* icon_pla.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_pla.png; sourceTree = "<group>"; };
		0D8738DE2E260226005B375C /* icon_play.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_play.png; sourceTree = "<group>"; };
		0D8738DF2E260226005B375C /* icon_red.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_red.png; sourceTree = "<group>"; };
		0D8738E02E260226005B375C /* icon_rightstore.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_rightstore.png; sourceTree = "<group>"; };
		0D8738E12E260226005B375C /* icon_searchclear.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_searchclear.png; sourceTree = "<group>"; };
		0D8738E22E260226005B375C /* icon_searchnone.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_searchnone.png; sourceTree = "<group>"; };
		0D8738E32E260226005B375C /* icon_shangquan.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_shangquan.png; sourceTree = "<group>"; };
		0D8738E42E260226005B375C /* icon_single.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon_single.png; sourceTree = "<group>"; };
		0D8738E52E260226005B375C /* icoon_delete.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icoon_delete.png; sourceTree = "<group>"; };
		0D8738E62E260226005B375C /* item_newdele.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = item_newdele.png; sourceTree = "<group>"; };
		0D8738E72E260226005B375C /* item_rotate.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = item_rotate.png; sourceTree = "<group>"; };
		0D8738E82E260226005B375C /* list_map.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = list_map.png; sourceTree = "<group>"; };
		0D8738E92E260226005B375C /* list_maps.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = list_maps.png; sourceTree = "<group>"; };
		0D8738EA2E260226005B375C /* logo_back.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = logo_back.png; sourceTree = "<group>"; };
		0D8738EB2E260226005B375C /* look_eye.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = look_eye.png; sourceTree = "<group>"; };
		0D8738EC2E260226005B375C /* look_landlord.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = look_landlord.png; sourceTree = "<group>"; };
		0D8738ED2E260226005B375C /* mark_name.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = mark_name.png; sourceTree = "<group>"; };
		0D8738EE2E260226005B375C /* mini_blue.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = mini_blue.png; sourceTree = "<group>"; };
		0D8738EF2E260226005B375C /* mini_gray.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = mini_gray.png; sourceTree = "<group>"; };
		0D8738F02E260226005B375C /* mini_grenn.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = mini_grenn.png; sourceTree = "<group>"; };
		0D8738F12E260226005B375C /* mini_oragan.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = mini_oragan.png; sourceTree = "<group>"; };
		0D8738F22E260226005B375C /* mini_red.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = mini_red.png; sourceTree = "<group>"; };
		0D8738F32E260226005B375C /* move_figer.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = move_figer.png; sourceTree = "<group>"; };
		0D8738F42E260226005B375C /* move_view.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = move_view.png; sourceTree = "<group>"; };
		0D8738F52E260226005B375C /* nav_back.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = nav_back.png; sourceTree = "<group>"; };
		0D8738F62E260226005B375C /* nav_man.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = nav_man.png; sourceTree = "<group>"; };
		0D8738F72E260226005B375C /* nav_serach.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = nav_serach.png; sourceTree = "<group>"; };
		0D8738F82E260226005B375C /* play_video.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = play_video.png; sourceTree = "<group>"; };
		0D8738F92E260226005B375C /* poi_loc.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = poi_loc.png; sourceTree = "<group>"; };
		0D8738FA2E260226005B375C /* point_add.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = point_add.png; sourceTree = "<group>"; };
		0D8738FB2E260226005B375C /* point_detail.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = point_detail.png; sourceTree = "<group>"; };
		0D8738FC2E260226005B375C /* point_follow.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = point_follow.png; sourceTree = "<group>"; };
		0D8738FD2E260226005B375C /* point_follows.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = point_follows.png; sourceTree = "<group>"; };
		0D8738FE2E260226005B375C /* point_home.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = point_home.png; sourceTree = "<group>"; };
		0D8738FF2E260226005B375C /* point_landlord.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = point_landlord.png; sourceTree = "<group>"; };
		0D8739002E260226005B375C /* point_location.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = point_location.png; sourceTree = "<group>"; };
		0D8739012E260226005B375C /* point_state.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = point_state.png; sourceTree = "<group>"; };
		0D8739022E260226005B375C /* search_delete.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = search_delete.png; sourceTree = "<group>"; };
		0D8739032E260226005B375C /* stand_map.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = stand_map.png; sourceTree = "<group>"; };
		0D8739042E260226005B375C /* switch_map.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = switch_map.png; sourceTree = "<group>"; };
		0D8739052E260226005B375C /* switch_mapS.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = switch_mapS.png; sourceTree = "<group>"; };
		0D8739062E260226005B375C /* tab_nodata.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = tab_nodata.png; sourceTree = "<group>"; };
		0D8739072E260226005B375C /* userArrow.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = userArrow.png; sourceTree = "<group>"; };
		0D8739082E260226005B375C /* weixing_map.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = weixing_map.png; sourceTree = "<group>"; };
		0D8739092E260226005B375C /* expand_task.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = expand_task.png; sourceTree = "<group>"; };
		0D87390B2E260226005B375C /* style.data */ = {isa = PBXFileReference; lastKnownFileType = file; path = style.data; sourceTree = "<group>"; };
		0D87390C2E260226005B375C /* style_extra.data */ = {isa = PBXFileReference; lastKnownFileType = file; path = style_extra.data; sourceTree = "<group>"; };
		0D87390D2E260226005B375C /* JAMStyledBezierPath.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JAMStyledBezierPath.h; sourceTree = "<group>"; };
		0D87390E2E260226005B375C /* JAMStyledBezierPath.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JAMStyledBezierPath.m; sourceTree = "<group>"; };
		0D87390F2E260226005B375C /* JAMStyledBezierPathFactory.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JAMStyledBezierPathFactory.h; sourceTree = "<group>"; };
		0D8739102E260226005B375C /* JAMStyledBezierPathFactory.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JAMStyledBezierPathFactory.m; sourceTree = "<group>"; };
		0D8739112E260226005B375C /* JAMSVGGradientParts.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JAMSVGGradientParts.h; sourceTree = "<group>"; };
		0D8739122E260226005B375C /* JAMSVGGradientParts.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JAMSVGGradientParts.m; sourceTree = "<group>"; };
		0D8739132E260226005B375C /* JAMSVGParser.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JAMSVGParser.h; sourceTree = "<group>"; };
		0D8739142E260226005B375C /* JAMSVGParser.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JAMSVGParser.m; sourceTree = "<group>"; };
		0D8739162E260226005B375C /* JAMSVGImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JAMSVGImage.h; sourceTree = "<group>"; };
		0D8739172E260226005B375C /* JAMSVGImage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JAMSVGImage.m; sourceTree = "<group>"; };
		0D8739182E260226005B375C /* UIImage+SVG.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+SVG.h"; sourceTree = "<group>"; };
		0D8739192E260226005B375C /* UIImage+SVG.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+SVG.m"; sourceTree = "<group>"; };
		0D87391B2E260226005B375C /* JAMSVGButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JAMSVGButton.h; sourceTree = "<group>"; };
		0D87391C2E260226005B375C /* JAMSVGButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JAMSVGButton.m; sourceTree = "<group>"; };
		0D87391D2E260226005B375C /* JAMSVGImageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JAMSVGImageView.h; sourceTree = "<group>"; };
		0D87391E2E260226005B375C /* JAMSVGImageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JAMSVGImageView.m; sourceTree = "<group>"; };
		0D8739202E260226005B375C /* JAMSVGUtilities.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JAMSVGUtilities.h; sourceTree = "<group>"; };
		0D8739212E260226005B375C /* JAMSVGUtilities.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JAMSVGUtilities.m; sourceTree = "<group>"; };
		0D8739242E260226005B375C /* MASCompositeConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASCompositeConstraint.h; sourceTree = "<group>"; };
		0D8739252E260226005B375C /* MASCompositeConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASCompositeConstraint.m; sourceTree = "<group>"; };
		0D8739262E260226005B375C /* MASConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASConstraint.h; sourceTree = "<group>"; };
		0D8739272E260226005B375C /* MASConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASConstraint.m; sourceTree = "<group>"; };
		0D8739282E260226005B375C /* MASConstraint+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "MASConstraint+Private.h"; sourceTree = "<group>"; };
		0D8739292E260226005B375C /* MASConstraintMaker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASConstraintMaker.h; sourceTree = "<group>"; };
		0D87392A2E260226005B375C /* MASConstraintMaker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASConstraintMaker.m; sourceTree = "<group>"; };
		0D87392B2E260226005B375C /* MASLayoutConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASLayoutConstraint.h; sourceTree = "<group>"; };
		0D87392C2E260226005B375C /* MASLayoutConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASLayoutConstraint.m; sourceTree = "<group>"; };
		0D87392D2E260226005B375C /* Masonry.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Masonry.h; sourceTree = "<group>"; };
		0D87392E2E260226005B375C /* MASUtilities.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASUtilities.h; sourceTree = "<group>"; };
		0D87392F2E260226005B375C /* MASViewAttribute.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASViewAttribute.h; sourceTree = "<group>"; };
		0D8739302E260226005B375C /* MASViewAttribute.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASViewAttribute.m; sourceTree = "<group>"; };
		0D8739312E260226005B375C /* MASViewConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASViewConstraint.h; sourceTree = "<group>"; };
		0D8739322E260226005B375C /* MASViewConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASViewConstraint.m; sourceTree = "<group>"; };
		0D8739332E260226005B375C /* NSArray+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASAdditions.h"; sourceTree = "<group>"; };
		0D8739342E260226005B375C /* NSArray+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSArray+MASAdditions.m"; sourceTree = "<group>"; };
		0D8739352E260226005B375C /* NSArray+MASShorthandAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		0D8739362E260226005B375C /* NSLayoutConstraint+MASDebugAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSLayoutConstraint+MASDebugAdditions.h"; sourceTree = "<group>"; };
		0D8739372E260226005B375C /* NSLayoutConstraint+MASDebugAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSLayoutConstraint+MASDebugAdditions.m"; sourceTree = "<group>"; };
		0D8739382E260226005B375C /* View+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "View+MASAdditions.h"; sourceTree = "<group>"; };
		0D8739392E260226005B375C /* View+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "View+MASAdditions.m"; sourceTree = "<group>"; };
		0D87393A2E260226005B375C /* View+MASShorthandAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "View+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		0D87393B2E260226005B375C /* ViewController+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "ViewController+MASAdditions.h"; sourceTree = "<group>"; };
		0D87393C2E260226005B375C /* ViewController+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "ViewController+MASAdditions.m"; sourceTree = "<group>"; };
		0D87393E2E260226005B375C /* LICENSE */ = {isa = PBXFileReference; lastKnownFileType = text; path = LICENSE; sourceTree = "<group>"; };
		0D87393F2E260226005B375C /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		0D8739412E260226005B375C /* MJRefreshAutoFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshAutoFooter.h; sourceTree = "<group>"; };
		0D8739422E260226005B375C /* MJRefreshAutoFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshAutoFooter.m; sourceTree = "<group>"; };
		0D8739432E260226005B375C /* MJRefreshBackFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshBackFooter.h; sourceTree = "<group>"; };
		0D8739442E260226005B375C /* MJRefreshBackFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshBackFooter.m; sourceTree = "<group>"; };
		0D8739452E260226005B375C /* MJRefreshComponent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshComponent.h; sourceTree = "<group>"; };
		0D8739462E260226005B375C /* MJRefreshComponent.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshComponent.m; sourceTree = "<group>"; };
		0D8739472E260226005B375C /* MJRefreshFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshFooter.h; sourceTree = "<group>"; };
		0D8739482E260226005B375C /* MJRefreshFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshFooter.m; sourceTree = "<group>"; };
		0D8739492E260226005B375C /* MJRefreshHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshHeader.h; sourceTree = "<group>"; };
		0D87394A2E260226005B375C /* MJRefreshHeader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshHeader.m; sourceTree = "<group>"; };
		0D87394C2E260226005B375C /* MJRefreshAutoGifFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshAutoGifFooter.h; sourceTree = "<group>"; };
		0D87394D2E260226005B375C /* MJRefreshAutoGifFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshAutoGifFooter.m; sourceTree = "<group>"; };
		0D87394E2E260226005B375C /* MJRefreshAutoNormalFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshAutoNormalFooter.h; sourceTree = "<group>"; };
		0D87394F2E260226005B375C /* MJRefreshAutoNormalFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshAutoNormalFooter.m; sourceTree = "<group>"; };
		0D8739502E260226005B375C /* MJRefreshAutoStateFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshAutoStateFooter.h; sourceTree = "<group>"; };
		0D8739512E260226005B375C /* MJRefreshAutoStateFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshAutoStateFooter.m; sourceTree = "<group>"; };
		0D8739532E260226005B375C /* MJRefreshBackGifFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshBackGifFooter.h; sourceTree = "<group>"; };
		0D8739542E260226005B375C /* MJRefreshBackGifFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshBackGifFooter.m; sourceTree = "<group>"; };
		0D8739552E260226005B375C /* MJRefreshBackNormalFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshBackNormalFooter.h; sourceTree = "<group>"; };
		0D8739562E260226005B375C /* MJRefreshBackNormalFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshBackNormalFooter.m; sourceTree = "<group>"; };
		0D8739572E260226005B375C /* MJRefreshBackStateFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshBackStateFooter.h; sourceTree = "<group>"; };
		0D8739582E260226005B375C /* MJRefreshBackStateFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshBackStateFooter.m; sourceTree = "<group>"; };
		0D87395B2E260226005B375C /* MJRefreshGifHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshGifHeader.h; sourceTree = "<group>"; };
		0D87395C2E260226005B375C /* MJRefreshGifHeader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshGifHeader.m; sourceTree = "<group>"; };
		0D87395D2E260226005B375C /* MJRefreshNormalHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshNormalHeader.h; sourceTree = "<group>"; };
		0D87395E2E260226005B375C /* MJRefreshNormalHeader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshNormalHeader.m; sourceTree = "<group>"; };
		0D87395F2E260226005B375C /* MJRefreshStateHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshStateHeader.h; sourceTree = "<group>"; };
		0D8739602E260226005B375C /* MJRefreshStateHeader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshStateHeader.m; sourceTree = "<group>"; };
		0D8739632E260226005B375C /* MJRefresh.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = MJRefresh.bundle; sourceTree = "<group>"; };
		0D8739642E260226005B375C /* MJRefresh.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefresh.h; sourceTree = "<group>"; };
		0D8739652E260226005B375C /* MJRefreshConst.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshConst.h; sourceTree = "<group>"; };
		0D8739662E260226005B375C /* MJRefreshConst.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshConst.m; sourceTree = "<group>"; };
		0D8739672E260226005B375C /* NSBundle+MJRefresh.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSBundle+MJRefresh.h"; sourceTree = "<group>"; };
		0D8739682E260226005B375C /* NSBundle+MJRefresh.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSBundle+MJRefresh.m"; sourceTree = "<group>"; };
		0D8739692E260226005B375C /* UIScrollView+MJExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIScrollView+MJExtension.h"; sourceTree = "<group>"; };
		0D87396A2E260226005B375C /* UIScrollView+MJExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIScrollView+MJExtension.m"; sourceTree = "<group>"; };
		0D87396B2E260226005B375C /* UIScrollView+MJRefresh.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIScrollView+MJRefresh.h"; sourceTree = "<group>"; };
		0D87396C2E260226005B375C /* UIScrollView+MJRefresh.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIScrollView+MJRefresh.m"; sourceTree = "<group>"; };
		0D87396D2E260226005B375C /* UIView+MJExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+MJExtension.h"; sourceTree = "<group>"; };
		0D87396E2E260226005B375C /* UIView+MJExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+MJExtension.m"; sourceTree = "<group>"; };
		0D8739702E260226005B375C /* UIScrollView+EmptyDataSet.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIScrollView+EmptyDataSet.h"; sourceTree = "<group>"; };
		0D8739712E260226005B375C /* UIScrollView+EmptyDataSet.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIScrollView+EmptyDataSet.m"; sourceTree = "<group>"; };
		0D8739732E260226005B375C /* NSBundle+TZImagePicker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSBundle+TZImagePicker.h"; sourceTree = "<group>"; };
		0D8739742E260226005B375C /* NSBundle+TZImagePicker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSBundle+TZImagePicker.m"; sourceTree = "<group>"; };
		0D8739752E260226005B375C /* TZAssetCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZAssetCell.h; sourceTree = "<group>"; };
		0D8739762E260226005B375C /* TZAssetCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZAssetCell.m; sourceTree = "<group>"; };
		0D8739772E260226005B375C /* TZAssetModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZAssetModel.h; sourceTree = "<group>"; };
		0D8739782E260226005B375C /* TZAssetModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZAssetModel.m; sourceTree = "<group>"; };
		0D8739792E260226005B375C /* TZAuthLimitedFooterTipView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZAuthLimitedFooterTipView.h; sourceTree = "<group>"; };
		0D87397A2E260226005B375C /* TZAuthLimitedFooterTipView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZAuthLimitedFooterTipView.m; sourceTree = "<group>"; };
		0D87397B2E260226005B375C /* TZGifPhotoPreviewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZGifPhotoPreviewController.h; sourceTree = "<group>"; };
		0D87397C2E260226005B375C /* TZGifPhotoPreviewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZGifPhotoPreviewController.m; sourceTree = "<group>"; };
		0D87397D2E260226005B375C /* TZImageCropManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZImageCropManager.h; sourceTree = "<group>"; };
		0D87397E2E260226005B375C /* TZImageCropManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZImageCropManager.m; sourceTree = "<group>"; };
		0D87397F2E260226005B375C /* TZImageManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZImageManager.h; sourceTree = "<group>"; };
		0D8739802E260226005B375C /* TZImageManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZImageManager.m; sourceTree = "<group>"; };
		0D8739812E260226005B375C /* TZImagePickerController.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = TZImagePickerController.bundle; sourceTree = "<group>"; };
		0D8739822E260226005B375C /* TZImagePickerController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZImagePickerController.h; sourceTree = "<group>"; };
		0D8739832E260226005B375C /* TZImagePickerController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZImagePickerController.m; sourceTree = "<group>"; };
		0D8739842E260226005B375C /* TZImageRequestOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZImageRequestOperation.h; sourceTree = "<group>"; };
		0D8739852E260226005B375C /* TZImageRequestOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZImageRequestOperation.m; sourceTree = "<group>"; };
		0D8739862E260226005B375C /* TZPhotoPickerController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZPhotoPickerController.h; sourceTree = "<group>"; };
		0D8739872E260226005B375C /* TZPhotoPickerController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZPhotoPickerController.m; sourceTree = "<group>"; };
		0D8739882E260226005B375C /* TZPhotoPreviewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZPhotoPreviewCell.h; sourceTree = "<group>"; };
		0D8739892E260226005B375C /* TZPhotoPreviewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZPhotoPreviewCell.m; sourceTree = "<group>"; };
		0D87398A2E260226005B375C /* TZPhotoPreviewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZPhotoPreviewController.h; sourceTree = "<group>"; };
		0D87398B2E260226005B375C /* TZPhotoPreviewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZPhotoPreviewController.m; sourceTree = "<group>"; };
		0D87398C2E260226005B375C /* TZProgressView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZProgressView.h; sourceTree = "<group>"; };
		0D87398D2E260226005B375C /* TZProgressView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZProgressView.m; sourceTree = "<group>"; };
		0D87398E2E260226005B375C /* TZVideoCropController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZVideoCropController.h; sourceTree = "<group>"; };
		0D87398F2E260226005B375C /* TZVideoCropController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZVideoCropController.m; sourceTree = "<group>"; };
		0D8739902E260226005B375C /* TZVideoEditedPreviewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZVideoEditedPreviewController.h; sourceTree = "<group>"; };
		0D8739912E260226005B375C /* TZVideoEditedPreviewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZVideoEditedPreviewController.m; sourceTree = "<group>"; };
		0D8739922E260226005B375C /* TZVideoPlayerController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TZVideoPlayerController.h; sourceTree = "<group>"; };
		0D8739932E260226005B375C /* TZVideoPlayerController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TZVideoPlayerController.m; sourceTree = "<group>"; };
		0D8739942E260226005B375C /* UIView+TZLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+TZLayout.h"; sourceTree = "<group>"; };
		0D8739952E260226005B375C /* UIView+TZLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+TZLayout.m"; sourceTree = "<group>"; };
		0D8739972E260226005B375C /* MAHttpTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAHttpTool.h; sourceTree = "<group>"; };
		0D8739982E260226005B375C /* MAHttpTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAHttpTool.m; sourceTree = "<group>"; };
		0D8739992E260226005B375C /* NSArray+Common.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSArray+Common.h"; sourceTree = "<group>"; };
		0D87399A2E260226005B375C /* NSArray+Common.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSArray+Common.m"; sourceTree = "<group>"; };
		0D87399B2E260226005B375C /* NSDictionary+Common.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSDictionary+Common.h"; sourceTree = "<group>"; };
		0D87399C2E260226005B375C /* NSDictionary+Common.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSDictionary+Common.m"; sourceTree = "<group>"; };
		0D87399D2E260226005B375C /* PushCellScaleBuniessTranstion.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PushCellScaleBuniessTranstion.h; sourceTree = "<group>"; };
		0D87399E2E260226005B375C /* PushCellScaleBuniessTranstion.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PushCellScaleBuniessTranstion.m; sourceTree = "<group>"; };
		0D87399F2E260226005B375C /* PushCellScaleTranstion.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PushCellScaleTranstion.h; sourceTree = "<group>"; };
		0D8739A02E260226005B375C /* PushCellScaleTranstion.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PushCellScaleTranstion.m; sourceTree = "<group>"; };
		0D8739A12E260226005B375C /* PushZoomScaleBuniessTranstion.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PushZoomScaleBuniessTranstion.h; sourceTree = "<group>"; };
		0D8739A22E260226005B375C /* PushZoomScaleBuniessTranstion.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PushZoomScaleBuniessTranstion.m; sourceTree = "<group>"; };
		0D8739A32E260226005B375C /* PushZoomScaleTranstion.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PushZoomScaleTranstion.h; sourceTree = "<group>"; };
		0D8739A42E260226005B375C /* PushZoomScaleTranstion.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PushZoomScaleTranstion.m; sourceTree = "<group>"; };
		0D8739A52E260226005B375C /* UIButton+Common.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIButton+Common.h"; sourceTree = "<group>"; };
		0D8739A62E260226005B375C /* UIButton+Common.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIButton+Common.m"; sourceTree = "<group>"; };
		0D8739A72E260226005B375C /* UILabel+STFakeAnimation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UILabel+STFakeAnimation.h"; sourceTree = "<group>"; };
		0D8739A82E260226005B375C /* UILabel+STFakeAnimation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UILabel+STFakeAnimation.m"; sourceTree = "<group>"; };
		0D8739A92E260226005B375C /* UITapGestureRecognizer+Common.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UITapGestureRecognizer+Common.h"; sourceTree = "<group>"; };
		0D8739AA2E260226005B375C /* UITapGestureRecognizer+Common.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UITapGestureRecognizer+Common.m"; sourceTree = "<group>"; };
		0D8739AB2E260226005B375C /* UITextField+Common.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UITextField+Common.h"; sourceTree = "<group>"; };
		0D8739AC2E260226005B375C /* UITextField+Common.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UITextField+Common.m"; sourceTree = "<group>"; };
		0D8739AD2E260226005B375C /* UITextView+Common.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UITextView+Common.h"; sourceTree = "<group>"; };
		0D8739AE2E260226005B375C /* UITextView+Common.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UITextView+Common.m"; sourceTree = "<group>"; };
		0D8739AF2E260226005B375C /* UIView+Common.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+Common.h"; sourceTree = "<group>"; };
		0D8739B02E260226005B375C /* UIView+Common.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+Common.m"; sourceTree = "<group>"; };
		0D8739B12E260226005B375C /* ModalScaleTranstion.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ModalScaleTranstion.h; sourceTree = "<group>"; };
		0D8739B22E260226005B375C /* ModalScaleTranstion.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ModalScaleTranstion.m; sourceTree = "<group>"; };
		0D8739B32E260226005B375C /* ModalHalfScreenTranstion.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ModalHalfScreenTranstion.h; sourceTree = "<group>"; };
		0D8739B42E260226005B375C /* ModalHalfScreenTranstion.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ModalHalfScreenTranstion.m; sourceTree = "<group>"; };
		0D8739B62E260226005B375C /* AmapNativeMapViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AmapNativeMapViewController.h; sourceTree = "<group>"; };
		0D8739B72E260226005B375C /* AmapNativeMapViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AmapNativeMapViewController.m; sourceTree = "<group>"; };
		0D8739B82E260226005B375C /* MAAddLandlordViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAAddLandlordViewController.h; sourceTree = "<group>"; };
		0D8739B92E260226005B375C /* MAAddLandlordViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAAddLandlordViewController.m; sourceTree = "<group>"; };
		0D8739BA2E260226005B375C /* MAPointDetailViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAPointDetailViewController.h; sourceTree = "<group>"; };
		0D8739BB2E260226005B375C /* MAPointDetailViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAPointDetailViewController.m; sourceTree = "<group>"; };
		0D8739BC2E260226005B375C /* MAAddPointViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAAddPointViewController.h; sourceTree = "<group>"; };
		0D8739BD2E260226005B375C /* MAAddPointViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAAddPointViewController.m; sourceTree = "<group>"; };
		0D8739BE2E260226005B375C /* MAEditPointViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAEditPointViewController.h; sourceTree = "<group>"; };
		0D8739BF2E260226005B375C /* MAEditPointViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAEditPointViewController.m; sourceTree = "<group>"; };
		0D8739C02E260226005B375C /* MAChooseLocationViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAChooseLocationViewController.h; sourceTree = "<group>"; };
		0D8739C12E260226005B375C /* MAChooseLocationViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAChooseLocationViewController.m; sourceTree = "<group>"; };
		0D8739C22E260226005B375C /* MAPointEditPoiViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAPointEditPoiViewController.h; sourceTree = "<group>"; };
		0D8739C32E260226005B375C /* MAPointEditPoiViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAPointEditPoiViewController.m; sourceTree = "<group>"; };
		0D8739C42E260226005B375C /* MASearchPoiViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASearchPoiViewController.h; sourceTree = "<group>"; };
		0D8739C52E260226005B375C /* MASearchPoiViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASearchPoiViewController.m; sourceTree = "<group>"; };
		0D8739C62E260226005B375C /* MAAddOpenStoreViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAAddOpenStoreViewController.h; sourceTree = "<group>"; };
		0D8739C72E260226005B375C /* MAAddOpenStoreViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAAddOpenStoreViewController.m; sourceTree = "<group>"; };
		0D8739C82E260226005B375C /* MAAddBuniessViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAAddBuniessViewController.h; sourceTree = "<group>"; };
		0D8739C92E260226005B375C /* MAAddBuniessViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAAddBuniessViewController.m; sourceTree = "<group>"; };
		0D8739CA2E260226005B375C /* MABuniessDetailViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABuniessDetailViewController.h; sourceTree = "<group>"; };
		0D8739CB2E260226005B375C /* MABuniessDetailViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABuniessDetailViewController.m; sourceTree = "<group>"; };
		0D8739CC2E260226005B375C /* MAEditBuniessViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAEditBuniessViewController.h; sourceTree = "<group>"; };
		0D8739CD2E260226005B375C /* MAEditBuniessViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAEditBuniessViewController.m; sourceTree = "<group>"; };
		0D8739CE2E260226005B375C /* MABuniessWriteFollowViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABuniessWriteFollowViewController.h; sourceTree = "<group>"; };
		0D8739CF2E260226005B375C /* MABuniessWriteFollowViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABuniessWriteFollowViewController.m; sourceTree = "<group>"; };
		0D8739D02E260226005B375C /* MABuniessEditDrawViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABuniessEditDrawViewController.h; sourceTree = "<group>"; };
		0D8739D12E260226005B375C /* MABuniessEditDrawViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABuniessEditDrawViewController.m; sourceTree = "<group>"; };
		0D8739D22E260226005B375C /* MAH5ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAH5ViewController.h; sourceTree = "<group>"; };
		0D8739D32E260226005B375C /* MAH5ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAH5ViewController.m; sourceTree = "<group>"; };
		0D8739D42E260226005B375C /* MALookPointViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MALookPointViewController.h; sourceTree = "<group>"; };
		0D8739D52E260226005B375C /* MALookPointViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MALookPointViewController.m; sourceTree = "<group>"; };
		0D8739D62E260226005B375C /* MAManagerFilterViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAManagerFilterViewController.h; sourceTree = "<group>"; };
		0D8739D72E260226005B375C /* MAManagerFilterViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAManagerFilterViewController.m; sourceTree = "<group>"; };
		0D8739D82E260226005B375C /* MAWriteFollowUpViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAWriteFollowUpViewController.h; sourceTree = "<group>"; };
		0D8739D92E260226005B375C /* MAWriteFollowUpViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAWriteFollowUpViewController.m; sourceTree = "<group>"; };
		0D8739DA2E260226005B375C /* MATakeLookViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MATakeLookViewController.h; sourceTree = "<group>"; };
		0D8739DB2E260226005B375C /* MATakeLookViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MATakeLookViewController.m; sourceTree = "<group>"; };
		0D8739DC2E260226005B375C /* MASearchPointViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASearchPointViewController.h; sourceTree = "<group>"; };
		0D8739DD2E260226005B375C /* MASearchPointViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASearchPointViewController.m; sourceTree = "<group>"; };
		0D8739DE2E260226005B375C /* MAVisibleAreaListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAVisibleAreaListViewController.h; sourceTree = "<group>"; };
		0D8739DF2E260226005B375C /* MAVisibleAreaListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAVisibleAreaListViewController.m; sourceTree = "<group>"; };
		0D8739E02E260226005B375C /* MABaseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MABaseViewController.h; sourceTree = "<group>"; };
		0D8739E12E260226005B375C /* MABaseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MABaseViewController.m; sourceTree = "<group>"; };
		0D8739E22E260226005B375C /* MAAppleMapViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAAppleMapViewController.h; sourceTree = "<group>"; };
		0D8739E32E260226005B375C /* MAAppleMapViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAAppleMapViewController.m; sourceTree = "<group>"; };
		0D8739E42E260226005B375C /* MAPremissionCheckViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MAPremissionCheckViewController.h; sourceTree = "<group>"; };
		0D8739E52E260226005B375C /* MAPremissionCheckViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MAPremissionCheckViewController.m; sourceTree = "<group>"; };
		0D873B532E260444005B375C /* PushNativeMapBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PushNativeMapBridge.m; sourceTree = "<group>"; };
		0D873B552E2604B5005B375C /* PushNativeMapManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PushNativeMapManager.swift; sourceTree = "<group>"; };
		0D873B572E265FA3005B375C /* XLBNavigationController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XLBNavigationController.swift; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* xlb.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = xlb.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = a608/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = a608/Info.plist; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = a608/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		1769E8F44A0D621C65901D6D /* Pods-a608.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-a608.debug.xcconfig"; path = "Target Support Files/Pods-a608/Pods-a608.debug.xcconfig"; sourceTree = "<group>"; };
		1D3A32632E2739E5000CCE3A /* RNBrigeManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNBrigeManager.h; sourceTree = "<group>"; };
		1D3A32642E2739E5000CCE3A /* RNBrigeManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNBrigeManager.m; sourceTree = "<group>"; };
		1D3A32672E2774B3000CCE3A /* RemoteNotificationCenter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RemoteNotificationCenter.h; sourceTree = "<group>"; };
		1D3A32682E2774B6000CCE3A /* RemoteNotificationCenter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RemoteNotificationCenter.m; sourceTree = "<group>"; };
		1D3A326A2E2774C3000CCE3A /* RunInBackground.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = RunInBackground.mp3; sourceTree = "<group>"; };
		1D3A326B2E2774C3000CCE3A /* XLBBackRunningManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XLBBackRunningManager.h; sourceTree = "<group>"; };
		1D3A326C2E2774C3000CCE3A /* XLBBackRunningManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XLBBackRunningManager.m; sourceTree = "<group>"; };
		383706F31D884FD0B0914700 /* HarmonyOS_Sans_Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HarmonyOS_Sans_Regular.ttf; path = ../packages/common/src/assets/fonts/HarmonyOS_Sans_Regular.ttf; sourceTree = "<group>"; };
		3C6FCF9D2E2648C4004F419F /* a608.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = a608.entitlements; path = a608/a608.entitlements; sourceTree = "<group>"; };
		421BE7740F4548B4B877A31A /* HarmonyOS_Sans_Black.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HarmonyOS_Sans_Black.ttf; path = ../packages/common/src/assets/fonts/HarmonyOS_Sans_Black.ttf; sourceTree = "<group>"; };
		5A8570CD27644EDD9F1299A1 /* HarmonyOS_Sans_Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HarmonyOS_Sans_Bold.ttf; path = ../packages/common/src/assets/fonts/HarmonyOS_Sans_Bold.ttf; sourceTree = "<group>"; };
		5BD63A742E20B7660022CA8F /* Config.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = Config.xcconfig; sourceTree = "<group>"; };
		6A345BC639074571AABC9A9A /* D-DIN-PRO-600-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "D-DIN-PRO-600-SemiBold.ttf"; path = "../packages/common/src/assets/fonts/D-DIN-PRO-600-SemiBold.ttf"; sourceTree = "<group>"; };
		6B58C5782E262A7F0066EA87 /* XLBAVPlayerBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XLBAVPlayerBridge.m; sourceTree = "<group>"; };
		6B58C5792E262A7F0066EA87 /* XLBAVPlayerBridge.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XLBAVPlayerBridge.swift; sourceTree = "<group>"; };
		6B58C5802E2FA1470066EA87 /* UMAnalytics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UMAnalytics.framework; sourceTree = "<group>"; };
		6B58C5812E2FA1470066EA87 /* UMCommon.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UMCommon.framework; sourceTree = "<group>"; };
		6B58C5922E3083C40066EA87 /* NetworkSettings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NetworkSettings.h; sourceTree = "<group>"; };
		6B58C5932E3083C40066EA87 /* NetworkSettings.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NetworkSettings.m; sourceTree = "<group>"; };
		6BAD713A2E1F525100EDED32 /* OpenWechat.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OpenWechat.swift; sourceTree = "<group>"; };
		6BAD713C2E1F526200EDED32 /* OpenWechatBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OpenWechatBridge.m; sourceTree = "<group>"; };
		6D46254509D54B868A5BA581 /* D-DIN-PRO-700-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "D-DIN-PRO-700-Bold.ttf"; path = "../packages/common/src/assets/fonts/D-DIN-PRO-700-Bold.ttf"; sourceTree = "<group>"; };
		761780EC2CA45674006654EE /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = AppDelegate.swift; path = a608/AppDelegate.swift; sourceTree = "<group>"; };
		7769087312B64BCBB3359D19 /* HarmonyOS_Sans_Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HarmonyOS_Sans_Medium.ttf; path = ../packages/common/src/assets/fonts/HarmonyOS_Sans_Medium.ttf; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = a608/LaunchScreen.storyboard; sourceTree = "<group>"; };
		894BADB82E0D4E3F00F48566 /* SyncScrollManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SyncScrollManager.swift; sourceTree = "<group>"; };
		894BADBA2E0D4E5A00F48566 /* SyncScrollView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SyncScrollView.swift; sourceTree = "<group>"; };
		894BADBC2E0D500A00F48566 /* SyncScrollManagerBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SyncScrollManagerBridge.m; sourceTree = "<group>"; };
		89EE81872DF7F6F000C65C80 /* BridgingHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BridgingHeader.h; sourceTree = "<group>"; };
		90D5497654C14CDC92262297 /* Pods-a608.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-a608.release.xcconfig"; path = "Target Support Files/Pods-a608/Pods-a608.release.xcconfig"; sourceTree = "<group>"; };
		951D4EE0D01D411BA62CC452 /* iconfontNew.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = iconfontNew.ttf; path = ../packages/common/src/assets/fonts/iconfontNew.ttf; sourceTree = "<group>"; };
		A5448D110CD14B1D929F69B7 /* D-DIN-PRO-800-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "D-DIN-PRO-800-ExtraBold.ttf"; path = "../packages/common/src/assets/fonts/D-DIN-PRO-800-ExtraBold.ttf"; sourceTree = "<group>"; };
		AC148BEB1DE645BD84C0EED0 /* D-DIN-PRO-400-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "D-DIN-PRO-400-Regular.ttf"; path = "../packages/common/src/assets/fonts/D-DIN-PRO-400-Regular.ttf"; sourceTree = "<group>"; };
		B1D5EA243F204F86865596A2 /* HarmonyOS_Sans_Thin.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HarmonyOS_Sans_Thin.ttf; path = ../packages/common/src/assets/fonts/HarmonyOS_Sans_Thin.ttf; sourceTree = "<group>"; };
		BF9918C52E23DC4A00277B25 /* NativeHotUpdate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NativeHotUpdate.swift; sourceTree = "<group>"; };
		BF9918C82E23EC4500277B25 /* NativeHotUpdate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NativeHotUpdate.m; sourceTree = "<group>"; };
		C71B3538CED94B7D8B2DFF35 /* D-DIN-PRO-500-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "D-DIN-PRO-500-Medium.ttf"; path = "../packages/common/src/assets/fonts/D-DIN-PRO-500-Medium.ttf"; sourceTree = "<group>"; };
		C7B217CD2E1D4E4300AF5693 /* ScreenRotateManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScreenRotateManager.swift; sourceTree = "<group>"; };
		C7B217D12E1E12A300AF5693 /* ScreenRotateManagerBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ScreenRotateManagerBridge.m; sourceTree = "<group>"; };
		D0DF41FE3CB84BAE8A985A48 /* HarmonyOS_Sans_Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HarmonyOS_Sans_Light.ttf; path = ../packages/common/src/assets/fonts/HarmonyOS_Sans_Light.ttf; sourceTree = "<group>"; };
		D22C2632ED6E3E8DF696E473 /* libPods-a608.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-a608.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		6B58C5862E3074EF0066EA87 /* UMengAnalysis */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
			);
			explicitFileTypes = {
			};
			explicitFolders = (
			);
			path = UMengAnalysis;
			sourceTree = "<group>";
		};
		6B58C5892E3074EF0066EA87 /* UMengReactBridge */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
			);
			explicitFileTypes = {
			};
			explicitFolders = (
			);
			path = UMengReactBridge;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6B58C5822E2FA1470066EA87 /* UMAnalytics.framework in Frameworks */,
				6B58C5832E2FA1470066EA87 /* UMCommon.framework in Frameworks */,
				B3DD93DBE3B02EACE4FD29E5 /* libPods-a608.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0D8737C62E260226005B375C /* CustomMapMarker */ = {
			isa = PBXGroup;
			children = (
				0D8737AA2E260226005B375C /* MABussinessPolygon.h */,
				0D8737AB2E260226005B375C /* MABussinessPolygon.m */,
				0D8737AC2E260226005B375C /* MABussinessPolygonRenderer.h */,
				0D8737AD2E260226005B375C /* MABussinessPolygonRenderer.m */,
				0D8737AE2E260226005B375C /* MACreatBuniessPointAnnotation.h */,
				0D8737AF2E260226005B375C /* MACreatBuniessPointAnnotation.m */,
				0D8737B02E260226005B375C /* MACreatBuniessPointAnnView.h */,
				0D8737B12E260226005B375C /* MACreatBuniessPointAnnView.m */,
				0D8737B22E260226005B375C /* MACreatBuniessPolygon.h */,
				0D8737B32E260226005B375C /* MACreatBuniessPolygon.m */,
				0D8737B42E260226005B375C /* MACreatBuniessPolygonRender.h */,
				0D8737B52E260226005B375C /* MACreatBuniessPolygonRender.m */,
				0D8737B62E260226005B375C /* MACreatBuniessPolyline.h */,
				0D8737B72E260226005B375C /* MACreatBuniessPolyline.m */,
				0D8737B82E260226005B375C /* MALocalAnnotation.h */,
				0D8737B92E260226005B375C /* MALocalAnnotation.m */,
				0D8737BA2E260226005B375C /* MALocalAnnotationView.h */,
				0D8737BB2E260226005B375C /* MALocalAnnotationView.m */,
				0D8737BC2E260226005B375C /* MAUserAnnotationView.h */,
				0D8737BD2E260226005B375C /* MAUserAnnotationView.m */,
				0D8737BE2E260226005B375C /* MACenterBuniessAnnotation.h */,
				0D8737BF2E260226005B375C /* MACenterBuniessAnnotation.m */,
				0D8737C02E260226005B375C /* MACenterBuniessAnnView.h */,
				0D8737C12E260226005B375C /* MACenterBuniessAnnView.m */,
				0D8737C22E260226005B375C /* MAOpenStorePolygon.h */,
				0D8737C32E260226005B375C /* MAOpenStorePolygon.m */,
				0D8737C42E260226005B375C /* MAOpenStorePolyline.h */,
				0D8737C52E260226005B375C /* MAOpenStorePolyline.m */,
			);
			path = CustomMapMarker;
			sourceTree = "<group>";
		};
		0D8737C92E260226005B375C /* FXPageControl */ = {
			isa = PBXGroup;
			children = (
				0D8737C72E260226005B375C /* FXPageControl.h */,
				0D8737C82E260226005B375C /* FXPageControl.m */,
			);
			path = FXPageControl;
			sourceTree = "<group>";
		};
		0D8737CE2E260226005B375C /* Views */ = {
			isa = PBXGroup;
			children = (
				0D8737CA2E260226005B375C /* BHInfiniteScrollViewCell.h */,
				0D8737CB2E260226005B375C /* BHInfiniteScrollViewCell.m */,
				0D8737CC2E260226005B375C /* BHInfiniteScrollViewTitleView.h */,
				0D8737CD2E260226005B375C /* BHInfiniteScrollViewTitleView.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		0D8737D12E260226005B375C /* BHInfiniteScrollView */ = {
			isa = PBXGroup;
			children = (
				0D8737C92E260226005B375C /* FXPageControl */,
				0D8737CE2E260226005B375C /* Views */,
				0D8737CF2E260226005B375C /* BHInfiniteScrollView.h */,
				0D8737D02E260226005B375C /* BHInfiniteScrollView.m */,
			);
			path = BHInfiniteScrollView;
			sourceTree = "<group>";
		};
		0D8737D22E260226005B375C /* BannerView */ = {
			isa = PBXGroup;
			children = (
				0D8737D12E260226005B375C /* BHInfiniteScrollView */,
			);
			path = BannerView;
			sourceTree = "<group>";
		};
		0D8737D52E260226005B375C /* Toast */ = {
			isa = PBXGroup;
			children = (
				0D8737D32E260226005B375C /* UIView+Toast.h */,
				0D8737D42E260226005B375C /* UIView+Toast.m */,
			);
			path = Toast;
			sourceTree = "<group>";
		};
		0D8737DB2E260226005B375C /* Supporting */ = {
			isa = PBXGroup;
			children = (
				0D8737D62E260226005B375C /* ZKPhotoBrowser.bundle */,
				0D8737D72E260226005B375C /* ZKPhotoLoadingView.h */,
				0D8737D82E260226005B375C /* ZKPhotoLoadingView.m */,
				0D8737D92E260226005B375C /* ZKPhotoProgressView.h */,
				0D8737DA2E260226005B375C /* ZKPhotoProgressView.m */,
			);
			path = Supporting;
			sourceTree = "<group>";
		};
		0D8737E42E260226005B375C /* ZKPhotoBrowser */ = {
			isa = PBXGroup;
			children = (
				0D8737DB2E260226005B375C /* Supporting */,
				0D8737DC2E260226005B375C /* ZKPhoto.h */,
				0D8737DD2E260226005B375C /* ZKPhoto.m */,
				0D8737DE2E260226005B375C /* ZKPhotoBrowser.h */,
				0D8737DF2E260226005B375C /* ZKPhotoBrowser.m */,
				0D8737E02E260226005B375C /* ZKPhotoToolbar.h */,
				0D8737E12E260226005B375C /* ZKPhotoToolbar.m */,
				0D8737E22E260226005B375C /* ZKPhotoView.h */,
				0D8737E32E260226005B375C /* ZKPhotoView.m */,
			);
			path = ZKPhotoBrowser;
			sourceTree = "<group>";
		};
		0D8738592E260226005B375C /* CustomView */ = {
			isa = PBXGroup;
			children = (
				0D8737D22E260226005B375C /* BannerView */,
				0D8737D52E260226005B375C /* Toast */,
				0D8737E42E260226005B375C /* ZKPhotoBrowser */,
				0D8737E52E260226005B375C /* MAAllFilterItemView.h */,
				0D8737E62E260226005B375C /* MAAllFilterItemView.m */,
				0D8737E72E260226005B375C /* MATopToastView.h */,
				0D8737E82E260226005B375C /* MATopToastView.m */,
				0D8737E92E260226005B375C /* MATopNotiflyView.h */,
				0D8737EA2E260226005B375C /* MATopNotiflyView.m */,
				0D8737EB2E260226005B375C /* MABottomToast.h */,
				0D8737EC2E260226005B375C /* MABottomToast.m */,
				0D8737ED2E260226005B375C /* MAAllFilterView.h */,
				0D8737EE2E260226005B375C /* MAAllFilterView.m */,
				0D8737EF2E260226005B375C /* MABottomMutilSelectAlert.h */,
				0D8737F02E260226005B375C /* MABottomMutilSelectAlert.m */,
				0D8737F12E260226005B375C /* MABottomPointAlert.h */,
				0D8737F22E260226005B375C /* MABottomPointAlert.m */,
				0D8737F32E260226005B375C /* MABottomSingleSelectAlert.h */,
				0D8737F42E260226005B375C /* MABottomSingleSelectAlert.m */,
				0D8737F52E260226005B375C /* MABuniessScrollView.h */,
				0D8737F62E260226005B375C /* MABuniessScrollView.m */,
				0D8737F72E260226005B375C /* MABuniessTableViewCell.h */,
				0D8737F82E260226005B375C /* MABuniessTableViewCell.m */,
				0D8737F92E260226005B375C /* MAChooseCityView.h */,
				0D8737FA2E260226005B375C /* MAChooseCityView.m */,
				0D8737FB2E260226005B375C /* MACooperateTableViewCell.h */,
				0D8737FC2E260226005B375C /* MACooperateTableViewCell.m */,
				0D8737FD2E260226005B375C /* MACreatBuniessDrawBoardView.h */,
				0D8737FE2E260226005B375C /* MACreatBuniessDrawBoardView.m */,
				0D8737FF2E260226005B375C /* MACreatBuniessView.h */,
				0D8738002E260226005B375C /* MACreatBuniessView.m */,
				0D8738012E260226005B375C /* MAFilterItemView.h */,
				0D8738022E260226005B375C /* MAFilterItemView.m */,
				0D8738032E260226005B375C /* UICollectionViewLeftAlignedLayout.h */,
				0D8738042E260226005B375C /* UICollectionViewLeftAlignedLayout.m */,
				0D8738052E260226005B375C /* MAFilterChooseCityCell.h */,
				0D8738062E260226005B375C /* MAFilterChooseCityCell.m */,
				0D8738072E260226005B375C /* MAFilterChooseCityItem.h */,
				0D8738082E260226005B375C /* MAFilterChooseCityItem.m */,
				0D8738092E260226005B375C /* MAMutiChooseCityTableViewCell.h */,
				0D87380A2E260226005B375C /* MAMutiChooseCityTableViewCell.m */,
				0D87380B2E260226005B375C /* MABottomMutiChooseCityView.h */,
				0D87380C2E260226005B375C /* MABottomMutiChooseCityView.m */,
				0D87380D2E260226005B375C /* MAFilterModal.h */,
				0D87380E2E260226005B375C /* MAFilterModal.m */,
				0D87380F2E260226005B375C /* MAFilterTextField.h */,
				0D8738102E260226005B375C /* MAFilterTextField.m */,
				0D8738112E260226005B375C /* MAHorScrollview.h */,
				0D8738122E260226005B375C /* MAHorScrollview.m */,
				0D8738132E260226005B375C /* MALoadAlphaView.h */,
				0D8738142E260226005B375C /* MALoadAlphaView.m */,
				0D8738152E260226005B375C /* MALoadWaveView.h */,
				0D8738162E260226005B375C /* MALoadWaveView.m */,
				0D8738172E260226005B375C /* MAManagerFilterTableViewCell.h */,
				0D8738182E260226005B375C /* MAManagerFilterTableViewCell.m */,
				0D8738192E260226005B375C /* MAMapSwitchView.h */,
				0D87381A2E260226005B375C /* MAMapSwitchView.m */,
				0D87381B2E260226005B375C /* MANativeAlert.h */,
				0D87381C2E260226005B375C /* MANativeAlert.m */,
				0D87381D2E260226005B375C /* MAPointModalView.h */,
				0D87381E2E260226005B375C /* MAPointModalView.m */,
				0D87381F2E260226005B375C /* MAPointTableViewCell.h */,
				0D8738202E260226005B375C /* MAPointTableViewCell.m */,
				0D8738212E260226005B375C /* MAPoiTableViewCell.h */,
				0D8738222E260226005B375C /* MAPoiTableViewCell.m */,
				0D8738232E260226005B375C /* MASearchLoadingView.h */,
				0D8738242E260226005B375C /* MASearchLoadingView.m */,
				0D8738252E260226005B375C /* MASearchTableViewCell.h */,
				0D8738262E260226005B375C /* MASearchTableViewCell.m */,
				0D8738272E260226005B375C /* MASearchTextField.h */,
				0D8738282E260226005B375C /* MASearchTextField.m */,
				0D8738292E260226005B375C /* MAShareUITableView.h */,
				0D87382A2E260226005B375C /* MAShareUITableView.m */,
				0D87382B2E260226005B375C /* MASingleDateView.h */,
				0D87382C2E260226005B375C /* MASingleDateView.m */,
				0D87382D2E260226005B375C /* MASingleSelectView.h */,
				0D87382E2E260226005B375C /* MASingleSelectView.m */,
				0D87382F2E260226005B375C /* MAStoreTableViewCell.h */,
				0D8738302E260226005B375C /* MAStoreTableViewCell.m */,
				0D8738312E260226005B375C /* MAUIScrollView.h */,
				0D8738322E260226005B375C /* MAUIScrollView.m */,
				0D8738332E260226005B375C /* MAUploadFileView.h */,
				0D8738342E260226005B375C /* MAUploadFileView.m */,
				0D8738352E260226005B375C /* MAUploadFileViewTableViewCell.h */,
				0D8738362E260226005B375C /* MAUploadFileViewTableViewCell.m */,
				0D8738372E260226005B375C /* MAUploadMutilCell.h */,
				0D8738382E260226005B375C /* MAUploadMutilCell.m */,
				0D8738392E260226005B375C /* MAUploadMutilImageView.h */,
				0D87383A2E260226005B375C /* MAUploadMutilImageView.m */,
				0D87383B2E260226005B375C /* MACustomTableView.h */,
				0D87383C2E260226005B375C /* MACustomTableView.m */,
				0D87383D2E260226005B375C /* MACustomBuniessTableView.h */,
				0D87383E2E260226005B375C /* MACustomBuniessTableView.m */,
				0D87383F2E260226005B375C /* MAUploadSingleImageView.h */,
				0D8738402E260226005B375C /* MAUploadSingleImageView.m */,
				0D8738412E260226005B375C /* MKLabel.h */,
				0D8738422E260226005B375C /* MKLabel.m */,
				0D8738432E260226005B375C /* MAShareView.h */,
				0D8738442E260226005B375C /* MAShareView.m */,
				0D8738452E260226005B375C /* MAShareChooseView.h */,
				0D8738462E260226005B375C /* MAShareChooseView.m */,
				0D8738472E260226005B375C /* MAWriteMeassageView.h */,
				0D8738482E260226005B375C /* MAWriteMeassageView.m */,
				0D8738492E260226005B375C /* MAWriteReplyView.h */,
				0D87384A2E260226005B375C /* MAWriteReplyView.m */,
				0D87384B2E260226005B375C /* MABottomOpereateView.h */,
				0D87384C2E260226005B375C /* MABottomOpereateView.m */,
				0D87384D2E260226005B375C /* MAApprovalRecordView.h */,
				0D87384E2E260226005B375C /* MAApprovalRecordView.m */,
				0D87384F2E260226005B375C /* MACopyListView.h */,
				0D8738502E260226005B375C /* MACopyListView.m */,
				0D8738512E260226005B375C /* MAReadListView.h */,
				0D8738522E260226005B375C /* MAReadListView.m */,
				0D8738532E260226005B375C /* MAOpenStoreView.h */,
				0D8738542E260226005B375C /* MAOpenStoreView.m */,
				0D8738552E260226005B375C /* MAOpenStoreViewTableViewCell.h */,
				0D8738562E260226005B375C /* MAOpenStoreViewTableViewCell.m */,
				0D8738572E260226005B375C /* MAEditOpenStoreView.h */,
				0D8738582E260226005B375C /* MAEditOpenStoreView.m */,
			);
			path = CustomView;
			sourceTree = "<group>";
		};
		0D87390A2E260226005B375C /* ImageSource */ = {
			isa = PBXGroup;
			children = (
				0D87385A2E260226005B375C /* icon_showmap.png */,
				0D87385B2E260226005B375C /* openstorearea_bottom.png */,
				0D87385C2E260226005B375C /* openstorearea_mid.png */,
				0D87385D2E260226005B375C /* openstorearea_top.png */,
				0D87385E2E260226005B375C /* openstoreline_bottom.png */,
				0D87385F2E260226005B375C /* openstoreline_mid.png */,
				0D8738602E260226005B375C /* openstoreline_top.png */,
				0D8738612E260226005B375C /* openstore_top.png */,
				0D8738622E260226005B375C /* openstore_mid.png */,
				0D8738632E260226005B375C /* openstore_bottom.png */,
				0D8738642E260226005B375C /* draw_point.png */,
				0D8738652E260226005B375C /* open_creatpoint.png */,
				0D8738662E260226005B375C /* changeitemdelete.png */,
				0D8738672E260226005B375C /* navtargeticon.png */,
				0D8738682E260226005B375C /* locationchangeitem.png */,
				0D8738692E260226005B375C /* locationrefresh.png */,
				0D87386A2E260226005B375C /* infonomal.png */,
				0D87386B2E260226005B375C /* notifierror.png */,
				0D87386C2E260226005B375C /* notifisuccess.png */,
				0D87386D2E260226005B375C /* icon_tipfollow.png */,
				0D87386E2E260226005B375C /* map_blue.png */,
				0D87386F2E260226005B375C /* map_gray.png */,
				0D8738702E260226005B375C /* map_green.png */,
				0D8738712E260226005B375C /* map_oregen.png */,
				0D8738722E260226005B375C /* map_red.png */,
				0D8738732E260226005B375C /* search_place.png */,
				0D8738742E260226005B375C /* apple_navi.png */,
				0D8738752E260226005B375C /* apple_loc.png */,
				0D8738762E260226005B375C /* icon_copyname.png */,
				0D8738772E260226005B375C /* apple_back.png */,
				0D8738782E260226005B375C /* city_nochose.png */,
				0D8738792E260226005B375C /* city_halfchoose.png */,
				0D87387A2E260226005B375C /* city_allchoose.png */,
				0D87387B2E260226005B375C /* buniess_time.png */,
				0D87387C2E260226005B375C /* share_lookhead.png */,
				0D87387D2E260226005B375C /* audit_notread.png */,
				0D87387E2E260226005B375C /* audit_read.png */,
				0D87387F2E260226005B375C /* audit_system.png */,
				0D8738802E260226005B375C /* audit_reject.png */,
				0D8738812E260226005B375C /* audit_wait.png */,
				0D8738822E260226005B375C /* audit_withdraw.png */,
				0D8738832E260226005B375C /* audit_approve.png */,
				0D8738842E260226005B375C /* detail_comment.png */,
				0D8738852E260226005B375C /* bottom_reopen.png */,
				0D8738862E260226005B375C /* bottom_agree.png */,
				0D8738872E260226005B375C /* bottom_approve.png */,
				0D8738882E260226005B375C /* bottom_change.png */,
				0D8738892E260226005B375C /* bottom_delete.png */,
				0D87388A2E260226005B375C /* bottom_edit.png */,
				0D87388B2E260226005B375C /* bottom_copy.png */,
				0D87388C2E260226005B375C /* bottom_follow.png */,
				0D87388D2E260226005B375C /* bottom_more.png */,
				0D87388E2E260226005B375C /* bottom_reject.png */,
				0D87388F2E260226005B375C /* bottom_revoke.png */,
				0D8738902E260226005B375C /* follow_noshareright.png */,
				0D8738912E260226005B375C /* follow_noshare.png */,
				0D8738922E260226005B375C /* share_close.png */,
				0D8738932E260226005B375C /* icon_tip.png */,
				0D8738942E260226005B375C /* icon_mylo.png */,
				0D8738952E260226005B375C /* icon_closeshare.png */,
				0D8738962E260226005B375C /* icon_share.png */,
				0D8738972E260226005B375C /* add_landlord.png */,
				0D8738982E260226005B375C /* add.png */,
				0D8738992E260226005B375C /* audit_record.png */,
				0D87389A2E260226005B375C /* back_draw.png */,
				0D87389B2E260226005B375C /* back_drawNo.png */,
				0D87389C2E260226005B375C /* back_user.png */,
				0D87389D2E260226005B375C /* back_userS.png */,
				0D87389E2E260226005B375C /* blue_map.png */,
				0D87389F2E260226005B375C /* blue_newmap.png */,
				0D8738A02E260226005B375C /* bottom_close.png */,
				0D8738A12E260226005B375C /* build_num.png */,
				0D8738A22E260226005B375C /* buiness_miniblue.png */,
				0D8738A32E260226005B375C /* buiness_minigray.png */,
				0D8738A42E260226005B375C /* buniess_blue.png */,
				0D8738A52E260226005B375C /* buniess_gray.png */,
				0D8738A62E260226005B375C /* buniess_minigreen.png */,
				0D8738A72E260226005B375C /* buniess_green.png */,
				0D8738A82E260226005B375C /* buniess_minioragon.png */,
				0D8738A92E260226005B375C /* buniess_yellow.png */,
				0D8738AA2E260226005B375C /* clear.png */,
				0D8738AB2E260226005B375C /* close_btn.png */,
				0D8738AC2E260226005B375C /* complete_draw.png */,
				0D8738AD2E260226005B375C /* complete_drawNo.png */,
				0D8738AE2E260226005B375C /* creat_buniess.png */,
				0D8738AF2E260226005B375C /* file_excel.png */,
				0D8738B02E260226005B375C /* file_img.png */,
				0D8738B12E260226005B375C /* file_music.png */,
				0D8738B22E260226005B375C /* file_no.png */,
				0D8738B32E260226005B375C /* file_pdf.png */,
				0D8738B42E260226005B375C /* file_ppt.png */,
				0D8738B52E260226005B375C /* file_word.png */,
				0D8738B62E260226005B375C /* gonggong_map.png */,
				0D8738B72E260226005B375C /* heat_map.png */,
				0D8738B82E260226005B375C /* heat_mapS.png */,
				0D8738B92E260226005B375C /* help_icon.png */,
				0D8738BA2E260226005B375C /* icon_addp.png */,
				0D8738BB2E260226005B375C /* icon_blue.png */,
				0D8738BC2E260226005B375C /* icon_camer.png */,
				0D8738BD2E260226005B375C /* icon_cardb.png */,
				0D8738BE2E260226005B375C /* icon_cardf.png */,
				0D8738BF2E260226005B375C /* icon_cbuniess.png */,
				0D8738C02E260226005B375C /* icon_choose.png */,
				0D8738C12E260226005B375C /* icon_closeAlert.png */,
				0D8738C22E260226005B375C /* icon_cpoint.png */,
				0D8738C32E260226005B375C /* icon_creatp.png */,
				0D8738C42E260226005B375C /* icon_deleL.png */,
				0D8738C52E260226005B375C /* icon_deletefile.png */,
				0D8738C62E260226005B375C /* icon_deleteL.png */,
				0D8738C72E260226005B375C /* icon_deleteS.png */,
				0D8738C82E260226005B375C /* icon_direct.png */,
				0D8738C92E260226005B375C /* icon_downarrow.png */,
				0D8738CA2E260226005B375C /* icon_draft.png */,
				0D8738CB2E260226005B375C /* icon_drag.png */,
				0D8738CC2E260226005B375C /* icon_edit.png */,
				0D8738CD2E260226005B375C /* icon_empty.png */,
				0D8738CE2E260226005B375C /* icon_gray.png */,
				0D8738CF2E260226005B375C /* icon_green.png */,
				0D8738D02E260226005B375C /* icon_halfselect.png */,
				0D8738D12E260226005B375C /* icon_head.png */,
				0D8738D22E260226005B375C /* icon_leftse.png */,
				0D8738D32E260226005B375C /* icon_leftstore.png */,
				0D8738D42E260226005B375C /* icon_liscen.png */,
				0D8738D52E260226005B375C /* icon_more.png */,
				0D8738D62E260226005B375C /* icon_mutilno.png */,
				0D8738D72E260226005B375C /* icon_mutilselect.png */,
				0D8738D82E260226005B375C /* icon_newback.png */,
				0D8738D92E260226005B375C /* icon_newpla.png */,
				0D8738DA2E260226005B375C /* icon_newsearch.png */,
				0D8738DB2E260226005B375C /* icon_nochoose.png */,
				0D8738DC2E260226005B375C /* icon_orange.png */,
				0D8738DD2E260226005B375C /* icon_pla.png */,
				0D8738DE2E260226005B375C /* icon_play.png */,
				0D8738DF2E260226005B375C /* icon_red.png */,
				0D8738E02E260226005B375C /* icon_rightstore.png */,
				0D8738E12E260226005B375C /* icon_searchclear.png */,
				0D8738E22E260226005B375C /* icon_searchnone.png */,
				0D8738E32E260226005B375C /* icon_shangquan.png */,
				0D8738E42E260226005B375C /* icon_single.png */,
				0D8738E52E260226005B375C /* icoon_delete.png */,
				0D8738E62E260226005B375C /* item_newdele.png */,
				0D8738E72E260226005B375C /* item_rotate.png */,
				0D8738E82E260226005B375C /* list_map.png */,
				0D8738E92E260226005B375C /* list_maps.png */,
				0D8738EA2E260226005B375C /* logo_back.png */,
				0D8738EB2E260226005B375C /* look_eye.png */,
				0D8738EC2E260226005B375C /* look_landlord.png */,
				0D8738ED2E260226005B375C /* mark_name.png */,
				0D8738EE2E260226005B375C /* mini_blue.png */,
				0D8738EF2E260226005B375C /* mini_gray.png */,
				0D8738F02E260226005B375C /* mini_grenn.png */,
				0D8738F12E260226005B375C /* mini_oragan.png */,
				0D8738F22E260226005B375C /* mini_red.png */,
				0D8738F32E260226005B375C /* move_figer.png */,
				0D8738F42E260226005B375C /* move_view.png */,
				0D8738F52E260226005B375C /* nav_back.png */,
				0D8738F62E260226005B375C /* nav_man.png */,
				0D8738F72E260226005B375C /* nav_serach.png */,
				0D8738F82E260226005B375C /* play_video.png */,
				0D8738F92E260226005B375C /* poi_loc.png */,
				0D8738FA2E260226005B375C /* point_add.png */,
				0D8738FB2E260226005B375C /* point_detail.png */,
				0D8738FC2E260226005B375C /* point_follow.png */,
				0D8738FD2E260226005B375C /* point_follows.png */,
				0D8738FE2E260226005B375C /* point_home.png */,
				0D8738FF2E260226005B375C /* point_landlord.png */,
				0D8739002E260226005B375C /* point_location.png */,
				0D8739012E260226005B375C /* point_state.png */,
				0D8739022E260226005B375C /* search_delete.png */,
				0D8739032E260226005B375C /* stand_map.png */,
				0D8739042E260226005B375C /* switch_map.png */,
				0D8739052E260226005B375C /* switch_mapS.png */,
				0D8739062E260226005B375C /* tab_nodata.png */,
				0D8739072E260226005B375C /* userArrow.png */,
				0D8739082E260226005B375C /* weixing_map.png */,
				0D8739092E260226005B375C /* expand_task.png */,
			);
			path = ImageSource;
			sourceTree = "<group>";
		};
		0D8739152E260226005B375C /* Path and Parser */ = {
			isa = PBXGroup;
			children = (
				0D87390D2E260226005B375C /* JAMStyledBezierPath.h */,
				0D87390E2E260226005B375C /* JAMStyledBezierPath.m */,
				0D87390F2E260226005B375C /* JAMStyledBezierPathFactory.h */,
				0D8739102E260226005B375C /* JAMStyledBezierPathFactory.m */,
				0D8739112E260226005B375C /* JAMSVGGradientParts.h */,
				0D8739122E260226005B375C /* JAMSVGGradientParts.m */,
				0D8739132E260226005B375C /* JAMSVGParser.h */,
				0D8739142E260226005B375C /* JAMSVGParser.m */,
			);
			path = "Path and Parser";
			sourceTree = "<group>";
		};
		0D87391A2E260226005B375C /* SVG Image */ = {
			isa = PBXGroup;
			children = (
				0D8739162E260226005B375C /* JAMSVGImage.h */,
				0D8739172E260226005B375C /* JAMSVGImage.m */,
				0D8739182E260226005B375C /* UIImage+SVG.h */,
				0D8739192E260226005B375C /* UIImage+SVG.m */,
			);
			path = "SVG Image";
			sourceTree = "<group>";
		};
		0D87391F2E260226005B375C /* UI and View */ = {
			isa = PBXGroup;
			children = (
				0D87391B2E260226005B375C /* JAMSVGButton.h */,
				0D87391C2E260226005B375C /* JAMSVGButton.m */,
				0D87391D2E260226005B375C /* JAMSVGImageView.h */,
				0D87391E2E260226005B375C /* JAMSVGImageView.m */,
			);
			path = "UI and View";
			sourceTree = "<group>";
		};
		0D8739222E260226005B375C /* Utilities */ = {
			isa = PBXGroup;
			children = (
				0D8739202E260226005B375C /* JAMSVGUtilities.h */,
				0D8739212E260226005B375C /* JAMSVGUtilities.m */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		0D8739232E260226005B375C /* JAMSVGImage */ = {
			isa = PBXGroup;
			children = (
				0D8739152E260226005B375C /* Path and Parser */,
				0D87391A2E260226005B375C /* SVG Image */,
				0D87391F2E260226005B375C /* UI and View */,
				0D8739222E260226005B375C /* Utilities */,
			);
			path = JAMSVGImage;
			sourceTree = "<group>";
		};
		0D87393D2E260226005B375C /* Masonry */ = {
			isa = PBXGroup;
			children = (
				0D8739242E260226005B375C /* MASCompositeConstraint.h */,
				0D8739252E260226005B375C /* MASCompositeConstraint.m */,
				0D8739262E260226005B375C /* MASConstraint.h */,
				0D8739272E260226005B375C /* MASConstraint.m */,
				0D8739282E260226005B375C /* MASConstraint+Private.h */,
				0D8739292E260226005B375C /* MASConstraintMaker.h */,
				0D87392A2E260226005B375C /* MASConstraintMaker.m */,
				0D87392B2E260226005B375C /* MASLayoutConstraint.h */,
				0D87392C2E260226005B375C /* MASLayoutConstraint.m */,
				0D87392D2E260226005B375C /* Masonry.h */,
				0D87392E2E260226005B375C /* MASUtilities.h */,
				0D87392F2E260226005B375C /* MASViewAttribute.h */,
				0D8739302E260226005B375C /* MASViewAttribute.m */,
				0D8739312E260226005B375C /* MASViewConstraint.h */,
				0D8739322E260226005B375C /* MASViewConstraint.m */,
				0D8739332E260226005B375C /* NSArray+MASAdditions.h */,
				0D8739342E260226005B375C /* NSArray+MASAdditions.m */,
				0D8739352E260226005B375C /* NSArray+MASShorthandAdditions.h */,
				0D8739362E260226005B375C /* NSLayoutConstraint+MASDebugAdditions.h */,
				0D8739372E260226005B375C /* NSLayoutConstraint+MASDebugAdditions.m */,
				0D8739382E260226005B375C /* View+MASAdditions.h */,
				0D8739392E260226005B375C /* View+MASAdditions.m */,
				0D87393A2E260226005B375C /* View+MASShorthandAdditions.h */,
				0D87393B2E260226005B375C /* ViewController+MASAdditions.h */,
				0D87393C2E260226005B375C /* ViewController+MASAdditions.m */,
			);
			path = Masonry;
			sourceTree = "<group>";
		};
		0D8739402E260226005B375C /* Masonry */ = {
			isa = PBXGroup;
			children = (
				0D87393D2E260226005B375C /* Masonry */,
				0D87393E2E260226005B375C /* LICENSE */,
				0D87393F2E260226005B375C /* README.md */,
			);
			path = Masonry;
			sourceTree = "<group>";
		};
		0D87394B2E260226005B375C /* Base */ = {
			isa = PBXGroup;
			children = (
				0D8739412E260226005B375C /* MJRefreshAutoFooter.h */,
				0D8739422E260226005B375C /* MJRefreshAutoFooter.m */,
				0D8739432E260226005B375C /* MJRefreshBackFooter.h */,
				0D8739442E260226005B375C /* MJRefreshBackFooter.m */,
				0D8739452E260226005B375C /* MJRefreshComponent.h */,
				0D8739462E260226005B375C /* MJRefreshComponent.m */,
				0D8739472E260226005B375C /* MJRefreshFooter.h */,
				0D8739482E260226005B375C /* MJRefreshFooter.m */,
				0D8739492E260226005B375C /* MJRefreshHeader.h */,
				0D87394A2E260226005B375C /* MJRefreshHeader.m */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		0D8739522E260226005B375C /* Auto */ = {
			isa = PBXGroup;
			children = (
				0D87394C2E260226005B375C /* MJRefreshAutoGifFooter.h */,
				0D87394D2E260226005B375C /* MJRefreshAutoGifFooter.m */,
				0D87394E2E260226005B375C /* MJRefreshAutoNormalFooter.h */,
				0D87394F2E260226005B375C /* MJRefreshAutoNormalFooter.m */,
				0D8739502E260226005B375C /* MJRefreshAutoStateFooter.h */,
				0D8739512E260226005B375C /* MJRefreshAutoStateFooter.m */,
			);
			path = Auto;
			sourceTree = "<group>";
		};
		0D8739592E260226005B375C /* Back */ = {
			isa = PBXGroup;
			children = (
				0D8739532E260226005B375C /* MJRefreshBackGifFooter.h */,
				0D8739542E260226005B375C /* MJRefreshBackGifFooter.m */,
				0D8739552E260226005B375C /* MJRefreshBackNormalFooter.h */,
				0D8739562E260226005B375C /* MJRefreshBackNormalFooter.m */,
				0D8739572E260226005B375C /* MJRefreshBackStateFooter.h */,
				0D8739582E260226005B375C /* MJRefreshBackStateFooter.m */,
			);
			path = Back;
			sourceTree = "<group>";
		};
		0D87395A2E260226005B375C /* Footer */ = {
			isa = PBXGroup;
			children = (
				0D8739522E260226005B375C /* Auto */,
				0D8739592E260226005B375C /* Back */,
			);
			path = Footer;
			sourceTree = "<group>";
		};
		0D8739612E260226005B375C /* Header */ = {
			isa = PBXGroup;
			children = (
				0D87395B2E260226005B375C /* MJRefreshGifHeader.h */,
				0D87395C2E260226005B375C /* MJRefreshGifHeader.m */,
				0D87395D2E260226005B375C /* MJRefreshNormalHeader.h */,
				0D87395E2E260226005B375C /* MJRefreshNormalHeader.m */,
				0D87395F2E260226005B375C /* MJRefreshStateHeader.h */,
				0D8739602E260226005B375C /* MJRefreshStateHeader.m */,
			);
			path = Header;
			sourceTree = "<group>";
		};
		0D8739622E260226005B375C /* Custom */ = {
			isa = PBXGroup;
			children = (
				0D87395A2E260226005B375C /* Footer */,
				0D8739612E260226005B375C /* Header */,
			);
			path = Custom;
			sourceTree = "<group>";
		};
		0D87396F2E260226005B375C /* MJRefresh */ = {
			isa = PBXGroup;
			children = (
				0D87394B2E260226005B375C /* Base */,
				0D8739622E260226005B375C /* Custom */,
				0D8739632E260226005B375C /* MJRefresh.bundle */,
				0D8739642E260226005B375C /* MJRefresh.h */,
				0D8739652E260226005B375C /* MJRefreshConst.h */,
				0D8739662E260226005B375C /* MJRefreshConst.m */,
				0D8739672E260226005B375C /* NSBundle+MJRefresh.h */,
				0D8739682E260226005B375C /* NSBundle+MJRefresh.m */,
				0D8739692E260226005B375C /* UIScrollView+MJExtension.h */,
				0D87396A2E260226005B375C /* UIScrollView+MJExtension.m */,
				0D87396B2E260226005B375C /* UIScrollView+MJRefresh.h */,
				0D87396C2E260226005B375C /* UIScrollView+MJRefresh.m */,
				0D87396D2E260226005B375C /* UIView+MJExtension.h */,
				0D87396E2E260226005B375C /* UIView+MJExtension.m */,
			);
			path = MJRefresh;
			sourceTree = "<group>";
		};
		0D8739722E260226005B375C /* Source */ = {
			isa = PBXGroup;
			children = (
				0D8739702E260226005B375C /* UIScrollView+EmptyDataSet.h */,
				0D8739712E260226005B375C /* UIScrollView+EmptyDataSet.m */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		0D8739962E260226005B375C /* TZImagePickerController */ = {
			isa = PBXGroup;
			children = (
				0D8739732E260226005B375C /* NSBundle+TZImagePicker.h */,
				0D8739742E260226005B375C /* NSBundle+TZImagePicker.m */,
				0D8739752E260226005B375C /* TZAssetCell.h */,
				0D8739762E260226005B375C /* TZAssetCell.m */,
				0D8739772E260226005B375C /* TZAssetModel.h */,
				0D8739782E260226005B375C /* TZAssetModel.m */,
				0D8739792E260226005B375C /* TZAuthLimitedFooterTipView.h */,
				0D87397A2E260226005B375C /* TZAuthLimitedFooterTipView.m */,
				0D87397B2E260226005B375C /* TZGifPhotoPreviewController.h */,
				0D87397C2E260226005B375C /* TZGifPhotoPreviewController.m */,
				0D87397D2E260226005B375C /* TZImageCropManager.h */,
				0D87397E2E260226005B375C /* TZImageCropManager.m */,
				0D87397F2E260226005B375C /* TZImageManager.h */,
				0D8739802E260226005B375C /* TZImageManager.m */,
				0D8739812E260226005B375C /* TZImagePickerController.bundle */,
				0D8739822E260226005B375C /* TZImagePickerController.h */,
				0D8739832E260226005B375C /* TZImagePickerController.m */,
				0D8739842E260226005B375C /* TZImageRequestOperation.h */,
				0D8739852E260226005B375C /* TZImageRequestOperation.m */,
				0D8739862E260226005B375C /* TZPhotoPickerController.h */,
				0D8739872E260226005B375C /* TZPhotoPickerController.m */,
				0D8739882E260226005B375C /* TZPhotoPreviewCell.h */,
				0D8739892E260226005B375C /* TZPhotoPreviewCell.m */,
				0D87398A2E260226005B375C /* TZPhotoPreviewController.h */,
				0D87398B2E260226005B375C /* TZPhotoPreviewController.m */,
				0D87398C2E260226005B375C /* TZProgressView.h */,
				0D87398D2E260226005B375C /* TZProgressView.m */,
				0D87398E2E260226005B375C /* TZVideoCropController.h */,
				0D87398F2E260226005B375C /* TZVideoCropController.m */,
				0D8739902E260226005B375C /* TZVideoEditedPreviewController.h */,
				0D8739912E260226005B375C /* TZVideoEditedPreviewController.m */,
				0D8739922E260226005B375C /* TZVideoPlayerController.h */,
				0D8739932E260226005B375C /* TZVideoPlayerController.m */,
				0D8739942E260226005B375C /* UIView+TZLayout.h */,
				0D8739952E260226005B375C /* UIView+TZLayout.m */,
			);
			path = TZImagePickerController;
			sourceTree = "<group>";
		};
		0D8739B52E260226005B375C /* Utils */ = {
			isa = PBXGroup;
			children = (
				0D87390B2E260226005B375C /* style.data */,
				0D87390C2E260226005B375C /* style_extra.data */,
				0D8739232E260226005B375C /* JAMSVGImage */,
				0D8739402E260226005B375C /* Masonry */,
				0D87396F2E260226005B375C /* MJRefresh */,
				0D8739722E260226005B375C /* Source */,
				0D8739962E260226005B375C /* TZImagePickerController */,
				0D8739972E260226005B375C /* MAHttpTool.h */,
				0D8739982E260226005B375C /* MAHttpTool.m */,
				0D8739992E260226005B375C /* NSArray+Common.h */,
				0D87399A2E260226005B375C /* NSArray+Common.m */,
				0D87399B2E260226005B375C /* NSDictionary+Common.h */,
				0D87399C2E260226005B375C /* NSDictionary+Common.m */,
				0D87399D2E260226005B375C /* PushCellScaleBuniessTranstion.h */,
				0D87399E2E260226005B375C /* PushCellScaleBuniessTranstion.m */,
				0D87399F2E260226005B375C /* PushCellScaleTranstion.h */,
				0D8739A02E260226005B375C /* PushCellScaleTranstion.m */,
				0D8739A12E260226005B375C /* PushZoomScaleBuniessTranstion.h */,
				0D8739A22E260226005B375C /* PushZoomScaleBuniessTranstion.m */,
				0D8739A32E260226005B375C /* PushZoomScaleTranstion.h */,
				0D8739A42E260226005B375C /* PushZoomScaleTranstion.m */,
				0D8739A52E260226005B375C /* UIButton+Common.h */,
				0D8739A62E260226005B375C /* UIButton+Common.m */,
				0D8739A72E260226005B375C /* UILabel+STFakeAnimation.h */,
				0D8739A82E260226005B375C /* UILabel+STFakeAnimation.m */,
				0D8739A92E260226005B375C /* UITapGestureRecognizer+Common.h */,
				0D8739AA2E260226005B375C /* UITapGestureRecognizer+Common.m */,
				0D8739AB2E260226005B375C /* UITextField+Common.h */,
				0D8739AC2E260226005B375C /* UITextField+Common.m */,
				0D8739AD2E260226005B375C /* UITextView+Common.h */,
				0D8739AE2E260226005B375C /* UITextView+Common.m */,
				0D8739AF2E260226005B375C /* UIView+Common.h */,
				0D8739B02E260226005B375C /* UIView+Common.m */,
				0D8739B12E260226005B375C /* ModalScaleTranstion.h */,
				0D8739B22E260226005B375C /* ModalScaleTranstion.m */,
				0D8739B32E260226005B375C /* ModalHalfScreenTranstion.h */,
				0D8739B42E260226005B375C /* ModalHalfScreenTranstion.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		0D8739E82E260226005B375C /* NativeMap */ = {
			isa = PBXGroup;
			children = (
				0D8737C62E260226005B375C /* CustomMapMarker */,
				0D8738592E260226005B375C /* CustomView */,
				0D87390A2E260226005B375C /* ImageSource */,
				0D8739B52E260226005B375C /* Utils */,
				0D8739B62E260226005B375C /* AmapNativeMapViewController.h */,
				0D8739B72E260226005B375C /* AmapNativeMapViewController.m */,
				0D8739B82E260226005B375C /* MAAddLandlordViewController.h */,
				0D8739B92E260226005B375C /* MAAddLandlordViewController.m */,
				0D8739BA2E260226005B375C /* MAPointDetailViewController.h */,
				0D8739BB2E260226005B375C /* MAPointDetailViewController.m */,
				0D8739BC2E260226005B375C /* MAAddPointViewController.h */,
				0D8739BD2E260226005B375C /* MAAddPointViewController.m */,
				0D8739BE2E260226005B375C /* MAEditPointViewController.h */,
				0D8739BF2E260226005B375C /* MAEditPointViewController.m */,
				0D8739C02E260226005B375C /* MAChooseLocationViewController.h */,
				0D8739C12E260226005B375C /* MAChooseLocationViewController.m */,
				0D8739C22E260226005B375C /* MAPointEditPoiViewController.h */,
				0D8739C32E260226005B375C /* MAPointEditPoiViewController.m */,
				0D8739C42E260226005B375C /* MASearchPoiViewController.h */,
				0D8739C52E260226005B375C /* MASearchPoiViewController.m */,
				0D8739C62E260226005B375C /* MAAddOpenStoreViewController.h */,
				0D8739C72E260226005B375C /* MAAddOpenStoreViewController.m */,
				0D8739C82E260226005B375C /* MAAddBuniessViewController.h */,
				0D8739C92E260226005B375C /* MAAddBuniessViewController.m */,
				0D8739CA2E260226005B375C /* MABuniessDetailViewController.h */,
				0D8739CB2E260226005B375C /* MABuniessDetailViewController.m */,
				0D8739CC2E260226005B375C /* MAEditBuniessViewController.h */,
				0D8739CD2E260226005B375C /* MAEditBuniessViewController.m */,
				0D8739CE2E260226005B375C /* MABuniessWriteFollowViewController.h */,
				0D8739CF2E260226005B375C /* MABuniessWriteFollowViewController.m */,
				0D8739D02E260226005B375C /* MABuniessEditDrawViewController.h */,
				0D8739D12E260226005B375C /* MABuniessEditDrawViewController.m */,
				0D8739D22E260226005B375C /* MAH5ViewController.h */,
				0D8739D32E260226005B375C /* MAH5ViewController.m */,
				0D8739D42E260226005B375C /* MALookPointViewController.h */,
				0D8739D52E260226005B375C /* MALookPointViewController.m */,
				0D8739D62E260226005B375C /* MAManagerFilterViewController.h */,
				0D8739D72E260226005B375C /* MAManagerFilterViewController.m */,
				0D8739D82E260226005B375C /* MAWriteFollowUpViewController.h */,
				0D8739D92E260226005B375C /* MAWriteFollowUpViewController.m */,
				0D8739DA2E260226005B375C /* MATakeLookViewController.h */,
				0D8739DB2E260226005B375C /* MATakeLookViewController.m */,
				0D8739DC2E260226005B375C /* MASearchPointViewController.h */,
				0D8739DD2E260226005B375C /* MASearchPointViewController.m */,
				0D8739DE2E260226005B375C /* MAVisibleAreaListViewController.h */,
				0D8739DF2E260226005B375C /* MAVisibleAreaListViewController.m */,
				0D8739E02E260226005B375C /* MABaseViewController.h */,
				0D8739E12E260226005B375C /* MABaseViewController.m */,
				0D8739E22E260226005B375C /* MAAppleMapViewController.h */,
				0D8739E32E260226005B375C /* MAAppleMapViewController.m */,
				0D8739E42E260226005B375C /* MAPremissionCheckViewController.h */,
				0D8739E52E260226005B375C /* MAPremissionCheckViewController.m */,
				0D873B532E260444005B375C /* PushNativeMapBridge.m */,
				0D873B552E2604B5005B375C /* PushNativeMapManager.swift */,
			);
			path = NativeMap;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* a608 */ = {
			isa = PBXGroup;
			children = (
				6B58C5942E3083C40066EA87 /* NetworkSettings */,
				6B58C5862E3074EF0066EA87 /* UMengAnalysis */,
				6B58C5892E3074EF0066EA87 /* UMengReactBridge */,
				6B58C5802E2FA1470066EA87 /* UMAnalytics.framework */,
				6B58C5812E2FA1470066EA87 /* UMCommon.framework */,
				3C6FCF9D2E2648C4004F419F /* a608.entitlements */,
				1D3A32652E2739E5000CCE3A /* RNBrigeManager */,
				6B58C5782E262A7F0066EA87 /* XLBAVPlayerBridge.m */,
				6B58C5792E262A7F0066EA87 /* XLBAVPlayerBridge.swift */,
				BF9918CA2E23ECE200277B25 /* nativehotupdate */,
				6BAD713C2E1F526200EDED32 /* OpenWechatBridge.m */,
				6BAD713A2E1F525100EDED32 /* OpenWechat.swift */,
				C7B217CD2E1D4E4300AF5693 /* ScreenRotateManager.swift */,
				894BADBC2E0D500A00F48566 /* SyncScrollManagerBridge.m */,
				894BADBA2E0D4E5A00F48566 /* SyncScrollView.swift */,
				894BADB82E0D4E3F00F48566 /* SyncScrollManager.swift */,
				89EE81872DF7F6F000C65C80 /* BridgingHeader.h */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				761780EC2CA45674006654EE /* AppDelegate.swift */,
				0D873B572E265FA3005B375C /* XLBNavigationController.swift */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
				C7B217D12E1E12A300AF5693 /* ScreenRotateManagerBridge.m */,
				0D8739E82E260226005B375C /* NativeMap */,
			);
			name = a608;
			sourceTree = "<group>";
		};
		1D3A32652E2739E5000CCE3A /* RNBrigeManager */ = {
			isa = PBXGroup;
			children = (
				1D3A326D2E2774C3000CCE3A /* BackGroundmanager */,
				1D3A32672E2774B3000CCE3A /* RemoteNotificationCenter.h */,
				1D3A32682E2774B6000CCE3A /* RemoteNotificationCenter.m */,
				1D3A32632E2739E5000CCE3A /* RNBrigeManager.h */,
				1D3A32642E2739E5000CCE3A /* RNBrigeManager.m */,
			);
			path = RNBrigeManager;
			sourceTree = "<group>";
		};
		1D3A326D2E2774C3000CCE3A /* BackGroundmanager */ = {
			isa = PBXGroup;
			children = (
				1D3A326A2E2774C3000CCE3A /* RunInBackground.mp3 */,
				1D3A326B2E2774C3000CCE3A /* XLBBackRunningManager.h */,
				1D3A326C2E2774C3000CCE3A /* XLBBackRunningManager.m */,
			);
			path = BackGroundmanager;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				D22C2632ED6E3E8DF696E473 /* libPods-a608.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		6B58C5942E3083C40066EA87 /* NetworkSettings */ = {
			isa = PBXGroup;
			children = (
				6B58C5922E3083C40066EA87 /* NetworkSettings.h */,
				6B58C5932E3083C40066EA87 /* NetworkSettings.m */,
			);
			path = NetworkSettings;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* a608 */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				C794E08F2C7E47188D68A8CA /* Resources */,
				5BD63A742E20B7660022CA8F /* Config.xcconfig */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* xlb.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				1769E8F44A0D621C65901D6D /* Pods-a608.debug.xcconfig */,
				90D5497654C14CDC92262297 /* Pods-a608.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		BF9918CA2E23ECE200277B25 /* nativehotupdate */ = {
			isa = PBXGroup;
			children = (
				BF9918C52E23DC4A00277B25 /* NativeHotUpdate.swift */,
				BF9918C82E23EC4500277B25 /* NativeHotUpdate.m */,
			);
			path = nativehotupdate;
			sourceTree = "<group>";
		};
		C794E08F2C7E47188D68A8CA /* Resources */ = {
			isa = PBXGroup;
			children = (
				AC148BEB1DE645BD84C0EED0 /* D-DIN-PRO-400-Regular.ttf */,
				C71B3538CED94B7D8B2DFF35 /* D-DIN-PRO-500-Medium.ttf */,
				6A345BC639074571AABC9A9A /* D-DIN-PRO-600-SemiBold.ttf */,
				6D46254509D54B868A5BA581 /* D-DIN-PRO-700-Bold.ttf */,
				A5448D110CD14B1D929F69B7 /* D-DIN-PRO-800-ExtraBold.ttf */,
				421BE7740F4548B4B877A31A /* HarmonyOS_Sans_Black.ttf */,
				5A8570CD27644EDD9F1299A1 /* HarmonyOS_Sans_Bold.ttf */,
				D0DF41FE3CB84BAE8A985A48 /* HarmonyOS_Sans_Light.ttf */,
				7769087312B64BCBB3359D19 /* HarmonyOS_Sans_Medium.ttf */,
				383706F31D884FD0B0914700 /* HarmonyOS_Sans_Regular.ttf */,
				B1D5EA243F204F86865596A2 /* HarmonyOS_Sans_Thin.ttf */,
				00FD693FAB0F4655ACCD40D0 /* iconfont.ttf */,
				951D4EE0D01D411BA62CC452 /* iconfontNew.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* a608 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "a608" */;
			buildPhases = (
				30F3F3E9153041E1DC61861A /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				3279EE84EA7C04D4A3774854 /* [CP] Embed Pods Frameworks */,
				3EBCE0547CEB953EC6CFFAE5 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				6B58C5862E3074EF0066EA87 /* UMengAnalysis */,
				6B58C5892E3074EF0066EA87 /* UMengReactBridge */,
			);
			name = a608;
			productName = a608;
			productReference = 13B07F961A680F5B00A75B9A /* xlb.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "a608" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* a608 */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				1D3A326F2E2774C3000CCE3A /* RunInBackground.mp3 in Resources */,
				3685EA5CCAECA33F54038DDC /* PrivacyInfo.xcprivacy in Resources */,
				6354F9E4F362409D87E21586 /* D-DIN-PRO-400-Regular.ttf in Resources */,
				0D8739E92E260226005B375C /* icon_showmap.png in Resources */,
				0D8739EA2E260226005B375C /* bottom_copy.png in Resources */,
				0D8739EB2E260226005B375C /* audit_reject.png in Resources */,
				0D8739EC2E260226005B375C /* bottom_edit.png in Resources */,
				0D8739ED2E260226005B375C /* map_oregen.png in Resources */,
				0D8739EE2E260226005B375C /* icon_red.png in Resources */,
				0D8739EF2E260226005B375C /* add.png in Resources */,
				0D8739F02E260226005B375C /* icon_newsearch.png in Resources */,
				0D8739F12E260226005B375C /* move_figer.png in Resources */,
				0D8739F22E260226005B375C /* draw_point.png in Resources */,
				0D8739F32E260226005B375C /* icon_liscen.png in Resources */,
				0D8739F42E260226005B375C /* file_music.png in Resources */,
				0D8739F52E260226005B375C /* buniess_minigreen.png in Resources */,
				0D8739F62E260226005B375C /* map_blue.png in Resources */,
				0D8739F72E260226005B375C /* audit_withdraw.png in Resources */,
				0D8739F82E260226005B375C /* look_eye.png in Resources */,
				0D8739F92E260226005B375C /* infonomal.png in Resources */,
				0D8739FA2E260226005B375C /* file_pdf.png in Resources */,
				0D8739FB2E260226005B375C /* icon_head.png in Resources */,
				0D8739FC2E260226005B375C /* icon_deleL.png in Resources */,
				0D8739FD2E260226005B375C /* locationrefresh.png in Resources */,
				0D8739FE2E260226005B375C /* bottom_close.png in Resources */,
				0D8739FF2E260226005B375C /* icon_closeAlert.png in Resources */,
				0D873A002E260226005B375C /* file_excel.png in Resources */,
				0D873A012E260226005B375C /* mini_grenn.png in Resources */,
				0D873A022E260226005B375C /* icon_searchnone.png in Resources */,
				0D873A032E260226005B375C /* mini_blue.png in Resources */,
				0D873A042E260226005B375C /* map_gray.png in Resources */,
				0D873A052E260226005B375C /* follow_noshare.png in Resources */,
				0D873A062E260226005B375C /* openstoreline_bottom.png in Resources */,
				0D873A072E260226005B375C /* icon_direct.png in Resources */,
				0D873A082E260226005B375C /* share_lookhead.png in Resources */,
				0D873A092E260226005B375C /* changeitemdelete.png in Resources */,
				0D873A0A2E260226005B375C /* map_green.png in Resources */,
				0D873A0B2E260226005B375C /* point_home.png in Resources */,
				0D873A0C2E260226005B375C /* buniess_minioragon.png in Resources */,
				0D873A0D2E260226005B375C /* icoon_delete.png in Resources */,
				0D873A0E2E260226005B375C /* icon_searchclear.png in Resources */,
				0D873A0F2E260226005B375C /* logo_back.png in Resources */,
				0D873A102E260226005B375C /* back_userS.png in Resources */,
				0D873A112E260226005B375C /* creat_buniess.png in Resources */,
				0D873A122E260226005B375C /* bottom_approve.png in Resources */,
				0D873A132E260226005B375C /* icon_shangquan.png in Resources */,
				0D873A142E260226005B375C /* map_red.png in Resources */,
				0D873A152E260226005B375C /* move_view.png in Resources */,
				0D873A162E260226005B375C /* complete_drawNo.png in Resources */,
				0D873A172E260226005B375C /* complete_draw.png in Resources */,
				0D873A182E260226005B375C /* bottom_more.png in Resources */,
				0D873A192E260226005B375C /* bottom_agree.png in Resources */,
				0D873A1A2E260226005B375C /* stand_map.png in Resources */,
				0D873A1B2E260226005B375C /* point_landlord.png in Resources */,
				0D873A1C2E260226005B375C /* bottom_follow.png in Resources */,
				0D873A1D2E260226005B375C /* file_word.png in Resources */,
				0D873A1E2E260226005B375C /* blue_map.png in Resources */,
				0D873A1F2E260226005B375C /* icon_empty.png in Resources */,
				0D873A202E260226005B375C /* notifisuccess.png in Resources */,
				0D873A212E260226005B375C /* heat_mapS.png in Resources */,
				0D873A222E260226005B375C /* point_detail.png in Resources */,
				0D873A232E260226005B375C /* icon_cardb.png in Resources */,
				0D873A242E260226005B375C /* TZImagePickerController.bundle in Resources */,
				0D873A252E260226005B375C /* buniess_yellow.png in Resources */,
				0D873A272E260226005B375C /* poi_loc.png in Resources */,
				0D873A282E260226005B375C /* item_rotate.png in Resources */,
				0D873A292E260226005B375C /* openstoreline_mid.png in Resources */,
				0D873A2A2E260226005B375C /* weixing_map.png in Resources */,
				0D873A2B2E260226005B375C /* userArrow.png in Resources */,
				0D873A2C2E260226005B375C /* apple_navi.png in Resources */,
				0D873A2D2E260226005B375C /* openstorearea_bottom.png in Resources */,
				0D873A2E2E260226005B375C /* README.md in Resources */,
				0D873A2F2E260226005B375C /* audit_notread.png in Resources */,
				0D873A302E260226005B375C /* mark_name.png in Resources */,
				0D873A312E260226005B375C /* icon_downarrow.png in Resources */,
				0D873A322E260226005B375C /* audit_system.png in Resources */,
				0D873A332E260226005B375C /* openstore_mid.png in Resources */,
				0D873A342E260226005B375C /* back_drawNo.png in Resources */,
				0D873A352E260226005B375C /* point_state.png in Resources */,
				0D873A362E260226005B375C /* city_allchoose.png in Resources */,
				0D873A372E260226005B375C /* openstorearea_top.png in Resources */,
				0D873A382E260226005B375C /* nav_man.png in Resources */,
				0D873A392E260226005B375C /* mini_oragan.png in Resources */,
				0D873A3A2E260226005B375C /* bottom_reject.png in Resources */,
				0D873A3B2E260226005B375C /* icon_orange.png in Resources */,
				0D873A3C2E260226005B375C /* apple_loc.png in Resources */,
				0D873A3D2E260226005B375C /* heat_map.png in Resources */,
				0D873A3E2E260226005B375C /* icon_creatp.png in Resources */,
				0D873A3F2E260226005B375C /* icon_camer.png in Resources */,
				0D873A402E260226005B375C /* audit_wait.png in Resources */,
				0D873A412E260226005B375C /* icon_leftse.png in Resources */,
				0D873A422E260226005B375C /* file_ppt.png in Resources */,
				0D873A432E260226005B375C /* back_user.png in Resources */,
				0D873A442E260226005B375C /* back_draw.png in Resources */,
				0D873A452E260226005B375C /* MJRefresh.bundle in Resources */,
				0D873A462E260226005B375C /* LICENSE in Resources */,
				0D873A472E260226005B375C /* point_follow.png in Resources */,
				0D873A482E260226005B375C /* icon_play.png in Resources */,
				0D873A492E260226005B375C /* point_add.png in Resources */,
				0D873A4A2E260226005B375C /* icon_cbuniess.png in Resources */,
				0D873A4B2E260226005B375C /* search_delete.png in Resources */,
				0D873A4C2E260226005B375C /* nav_back.png in Resources */,
				0D873A4D2E260226005B375C /* icon_deleteL.png in Resources */,
				0D873A4E2E260226005B375C /* mini_red.png in Resources */,
				0D873A4F2E260226005B375C /* close_btn.png in Resources */,
				0D873A502E260226005B375C /* add_landlord.png in Resources */,
				0D873A512E260226005B375C /* mini_gray.png in Resources */,
				0D873A522E260226005B375C /* tab_nodata.png in Resources */,
				0D873A532E260226005B375C /* icon_leftstore.png in Resources */,
				0D873A542E260226005B375C /* icon_halfselect.png in Resources */,
				0D873A552E260226005B375C /* icon_mylo.png in Resources */,
				0D873A562E260226005B375C /* buiness_miniblue.png in Resources */,
				0D873A572E260226005B375C /* ZKPhotoBrowser.bundle in Resources */,
				0D873A582E260226005B375C /* audit_read.png in Resources */,
				0D873A592E260226005B375C /* icon_cardf.png in Resources */,
				0D873A5A2E260226005B375C /* icon_deleteS.png in Resources */,
				0D873A5B2E260226005B375C /* icon_edit.png in Resources */,
				0D873A5C2E260226005B375C /* icon_share.png in Resources */,
				0D873A5D2E260226005B375C /* icon_choose.png in Resources */,
				0D873A5E2E260226005B375C /* style.data in Resources */,
				0D873A5F2E260226005B375C /* icon_blue.png in Resources */,
				0D873A602E260226005B375C /* style_extra.data in Resources */,
				0D873A612E260226005B375C /* list_maps.png in Resources */,
				0D873A622E260226005B375C /* icon_draft.png in Resources */,
				0D873A632E260226005B375C /* point_location.png in Resources */,
				0D873A642E260226005B375C /* clear.png in Resources */,
				0D873A652E260226005B375C /* icon_cpoint.png in Resources */,
				0D873A662E260226005B375C /* switch_map.png in Resources */,
				0D873A672E260226005B375C /* city_nochose.png in Resources */,
				0D873A682E260226005B375C /* icon_mutilno.png in Resources */,
				0D873A692E260226005B375C /* play_video.png in Resources */,
				0D873A6A2E260226005B375C /* help_icon.png in Resources */,
				0D873A6B2E260226005B375C /* buniess_green.png in Resources */,
				0D873A6C2E260226005B375C /* icon_mutilselect.png in Resources */,
				0D873A6D2E260226005B375C /* file_no.png in Resources */,
				0D873A6E2E260226005B375C /* icon_rightstore.png in Resources */,
				0D873A6F2E260226005B375C /* icon_addp.png in Resources */,
				0D873A702E260226005B375C /* buniess_gray.png in Resources */,
				0D873A712E260226005B375C /* buniess_time.png in Resources */,
				0D873A722E260226005B375C /* icon_copyname.png in Resources */,
				0D873A732E260226005B375C /* nav_serach.png in Resources */,
				0D873A742E260226005B375C /* list_map.png in Resources */,
				0D873A752E260226005B375C /* openstorearea_mid.png in Resources */,
				0D873A762E260226005B375C /* navtargeticon.png in Resources */,
				0D873A772E260226005B375C /* item_newdele.png in Resources */,
				0D873A782E260226005B375C /* follow_noshareright.png in Resources */,
				0D873A792E260226005B375C /* icon_tipfollow.png in Resources */,
				0D873A7A2E260226005B375C /* bottom_change.png in Resources */,
				0D873A7B2E260226005B375C /* file_img.png in Resources */,
				0D873A7C2E260226005B375C /* openstore_top.png in Resources */,
				0D873A7D2E260226005B375C /* switch_mapS.png in Resources */,
				0D873A7E2E260226005B375C /* icon_newback.png in Resources */,
				0D873A7F2E260226005B375C /* notifierror.png in Resources */,
				0D873A802E260226005B375C /* icon_green.png in Resources */,
				0D873A812E260226005B375C /* buiness_minigray.png in Resources */,
				0D873A822E260226005B375C /* search_place.png in Resources */,
				0D873A832E260226005B375C /* icon_closeshare.png in Resources */,
				0D873A842E260226005B375C /* look_landlord.png in Resources */,
				0D873A852E260226005B375C /* open_creatpoint.png in Resources */,
				0D873A862E260226005B375C /* buniess_blue.png in Resources */,
				0D873A872E260226005B375C /* apple_back.png in Resources */,
				0D873A882E260226005B375C /* city_halfchoose.png in Resources */,
				0D873A892E260226005B375C /* locationchangeitem.png in Resources */,
				0D873A8A2E260226005B375C /* bottom_delete.png in Resources */,
				0D873A8B2E260226005B375C /* point_follows.png in Resources */,
				0D873A8C2E260226005B375C /* gonggong_map.png in Resources */,
				0D873A8D2E260226005B375C /* icon_nochoose.png in Resources */,
				0D873A8E2E260226005B375C /* openstoreline_top.png in Resources */,
				0D873A8F2E260226005B375C /* icon_deletefile.png in Resources */,
				0D873A902E260226005B375C /* share_close.png in Resources */,
				0D873A912E260226005B375C /* detail_comment.png in Resources */,
				0D873A922E260226005B375C /* build_num.png in Resources */,
				0D873A932E260226005B375C /* icon_more.png in Resources */,
				0D873A942E260226005B375C /* icon_drag.png in Resources */,
				0D873A952E260226005B375C /* icon_gray.png in Resources */,
				0D873A962E260226005B375C /* audit_record.png in Resources */,
				0D873A972E260226005B375C /* bottom_revoke.png in Resources */,
				0D873A982E260226005B375C /* icon_tip.png in Resources */,
				0D873A992E260226005B375C /* blue_newmap.png in Resources */,
				0D873A9A2E260226005B375C /* openstore_bottom.png in Resources */,
				0D873A9B2E260226005B375C /* audit_approve.png in Resources */,
				0D873A9C2E260226005B375C /* bottom_reopen.png in Resources */,
				0D873A9D2E260226005B375C /* icon_newpla.png in Resources */,
				0D873A9E2E260226005B375C /* icon_pla.png in Resources */,
				0D873A9F2E260226005B375C /* icon_single.png in Resources */,
				6BEFD4DD661744D4B2A96A57 /* D-DIN-PRO-500-Medium.ttf in Resources */,
				79E80A5522EF42A89C77CB29 /* D-DIN-PRO-600-SemiBold.ttf in Resources */,
				14D4F9FC9A4247C9A18851A6 /* D-DIN-PRO-700-Bold.ttf in Resources */,
				4AFD156F0D2C4559AEEF4789 /* D-DIN-PRO-800-ExtraBold.ttf in Resources */,
				CC0ED7382C5E4C6088985723 /* HarmonyOS_Sans_Black.ttf in Resources */,
				07598A9F619F456AA4872AA7 /* HarmonyOS_Sans_Bold.ttf in Resources */,
				5D317FF4DE3A4308829E592D /* HarmonyOS_Sans_Light.ttf in Resources */,
				1FE43F87D49942D8BE07FDA2 /* HarmonyOS_Sans_Medium.ttf in Resources */,
				832310179A344A518436DC22 /* HarmonyOS_Sans_Regular.ttf in Resources */,
				5942790D265647FAB1DB9CA6 /* HarmonyOS_Sans_Thin.ttf in Resources */,
				ABD6718E3E42467E82181889 /* iconfont.ttf in Resources */,
				A32DC72B534345C48AEACF1E /* iconfontNew.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nif [[ -f \"$PODS_ROOT/../.xcode.env\" ]]; then\nsource \"$PODS_ROOT/../.xcode.env\"\nfi\nif [[ -f \"$PODS_ROOT/../.xcode.env.local\" ]]; then\nsource \"$PODS_ROOT/../.xcode.env.local\"\nfi\n\nexport CLI_PATH=\"$(\"$NODE_BINARY\" --print \"require('path').dirname(require.resolve('@react-native-community/cli/package.json')) + '/build/bin.js'\")\"\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\nexport BUILD_FROM_XCODE=true\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		30F3F3E9153041E1DC61861A /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-a608-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		3279EE84EA7C04D4A3774854 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-a608/Pods-a608-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-a608/Pods-a608-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-a608/Pods-a608-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		3EBCE0547CEB953EC6CFFAE5 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-a608/Pods-a608-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-a608/Pods-a608-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-a608/Pods-a608-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6B58C57A2E262A7F0066EA87 /* XLBAVPlayerBridge.m in Sources */,
				6B58C57B2E262A7F0066EA87 /* XLBAVPlayerBridge.swift in Sources */,
				894BADBD2E0D500D00F48566 /* SyncScrollManagerBridge.m in Sources */,
				6BAD713B2E1F525100EDED32 /* OpenWechat.swift in Sources */,
				0D873AA02E260226005B375C /* MASViewAttribute.m in Sources */,
				0D873AA12E260226005B375C /* TZVideoEditedPreviewController.m in Sources */,
				0D873AA22E260226005B375C /* MACustomBuniessTableView.m in Sources */,
				0D873AA32E260226005B375C /* UILabel+STFakeAnimation.m in Sources */,
				0D873AA42E260226005B375C /* MAReadListView.m in Sources */,
				0D873AA52E260226005B375C /* MJRefreshHeader.m in Sources */,
				0D873AA62E260226005B375C /* MABottomSingleSelectAlert.m in Sources */,
				0D873AA72E260226005B375C /* PushCellScaleTranstion.m in Sources */,
				0D873AA82E260226005B375C /* MASCompositeConstraint.m in Sources */,
				0D873AA92E260226005B375C /* MACreatBuniessPolyline.m in Sources */,
				0D873AAA2E260226005B375C /* MJRefreshFooter.m in Sources */,
				0D873AAB2E260226005B375C /* TZPhotoPreviewController.m in Sources */,
				0D873AAC2E260226005B375C /* TZImageCropManager.m in Sources */,
				0D873AAD2E260226005B375C /* MASearchPoiViewController.m in Sources */,
				0D873AAE2E260226005B375C /* MAFilterItemView.m in Sources */,
				0D873AAF2E260226005B375C /* TZImageManager.m in Sources */,
				0D873AB02E260226005B375C /* TZGifPhotoPreviewController.m in Sources */,
				0D873AB12E260226005B375C /* MAWriteReplyView.m in Sources */,
				0D873AB22E260226005B375C /* MASLayoutConstraint.m in Sources */,
				0D873B562E2604B5005B375C /* PushNativeMapManager.swift in Sources */,
				0D873AB32E260226005B375C /* MACenterBuniessAnnotation.m in Sources */,
				0D873AB42E260226005B375C /* JAMSVGImageView.m in Sources */,
				0D873AB52E260226005B375C /* ZKPhotoProgressView.m in Sources */,
				0D873AB62E260226005B375C /* MALocalAnnotation.m in Sources */,
				0D873AB72E260226005B375C /* MACooperateTableViewCell.m in Sources */,
				0D873AB82E260226005B375C /* MALoadWaveView.m in Sources */,
				0D873AB92E260226005B375C /* TZImageRequestOperation.m in Sources */,
				0D873ABA2E260226005B375C /* JAMSVGButton.m in Sources */,
				0D873ABB2E260226005B375C /* UIButton+Common.m in Sources */,
				0D873ABC2E260226005B375C /* MABottomOpereateView.m in Sources */,
				0D873ABD2E260226005B375C /* MASingleDateView.m in Sources */,
				0D873ABE2E260226005B375C /* JAMStyledBezierPathFactory.m in Sources */,
				0D873ABF2E260226005B375C /* MAHttpTool.m in Sources */,
				0D873AC02E260226005B375C /* MASearchPointViewController.m in Sources */,
				0D873B582E265FA3005B375C /* XLBNavigationController.swift in Sources */,
				0D873AC12E260226005B375C /* UITextField+Common.m in Sources */,
				0D873AC22E260226005B375C /* UIView+Toast.m in Sources */,
				0D873AC32E260226005B375C /* BHInfiniteScrollView.m in Sources */,
				0D873AC42E260226005B375C /* MAUIScrollView.m in Sources */,
				0D873AC52E260226005B375C /* MAUploadFileViewTableViewCell.m in Sources */,
				0D873B542E260444005B375C /* PushNativeMapBridge.m in Sources */,
				0D873AC62E260226005B375C /* MJRefreshStateHeader.m in Sources */,
				0D873AC72E260226005B375C /* MATakeLookViewController.m in Sources */,
				0D873AC82E260226005B375C /* ZKPhotoBrowser.m in Sources */,
				0D873AC92E260226005B375C /* MATopNotiflyView.m in Sources */,
				0D873ACA2E260226005B375C /* MAStoreTableViewCell.m in Sources */,
				0D873ACB2E260226005B375C /* MJRefreshGifHeader.m in Sources */,
				0D873ACC2E260226005B375C /* TZVideoPlayerController.m in Sources */,
				0D873ACE2E260226005B375C /* MAShareChooseView.m in Sources */,
				0D873ACF2E260226005B375C /* MAH5ViewController.m in Sources */,
				0D873AD02E260226005B375C /* MAOpenStorePolygon.m in Sources */,
				0D873AD12E260226005B375C /* TZAuthLimitedFooterTipView.m in Sources */,
				0D873AD22E260226005B375C /* MASViewConstraint.m in Sources */,
				0D873AD32E260226005B375C /* View+MASAdditions.m in Sources */,
				0D873AD42E260226005B375C /* MJRefreshBackFooter.m in Sources */,
				0D873AD52E260226005B375C /* MAAllFilterItemView.m in Sources */,
				0D873AD62E260226005B375C /* UIView+MJExtension.m in Sources */,
				0D873AD72E260226005B375C /* MATopToastView.m in Sources */,
				0D873AD82E260226005B375C /* ZKPhotoToolbar.m in Sources */,
				0D873AD92E260226005B375C /* UIView+Common.m in Sources */,
				0D873ADA2E260226005B375C /* MAUploadMutilImageView.m in Sources */,
				0D873ADB2E260226005B375C /* MABuniessScrollView.m in Sources */,
				0D873ADC2E260226005B375C /* ZKPhotoLoadingView.m in Sources */,
				0D873ADD2E260226005B375C /* MAFilterChooseCityItem.m in Sources */,
				0D873ADE2E260226005B375C /* MJRefreshBackGifFooter.m in Sources */,
				0D873ADF2E260226005B375C /* MABottomMutiChooseCityView.m in Sources */,
				0D873AE02E260226005B375C /* MASConstraint.m in Sources */,
				0D873AE12E260226005B375C /* MAChooseCityView.m in Sources */,
				0D873AE22E260226005B375C /* FXPageControl.m in Sources */,
				0D873AE32E260226005B375C /* MACustomTableView.m in Sources */,
				0D873AE42E260226005B375C /* JAMSVGUtilities.m in Sources */,
				0D873AE52E260226005B375C /* MAManagerFilterTableViewCell.m in Sources */,
				0D873AE62E260226005B375C /* NSBundle+MJRefresh.m in Sources */,
				0D873AE72E260226005B375C /* MACreatBuniessPointAnnView.m in Sources */,
				0D873AE82E260226005B375C /* MAManagerFilterViewController.m in Sources */,
				0D873AE92E260226005B375C /* TZPhotoPreviewCell.m in Sources */,
				0D873AEA2E260226005B375C /* MAShareView.m in Sources */,
				0D873AEB2E260226005B375C /* AmapNativeMapViewController.m in Sources */,
				0D873AEC2E260226005B375C /* MACreatBuniessView.m in Sources */,
				0D873AED2E260226005B375C /* MJRefreshAutoNormalFooter.m in Sources */,
				0D873AEE2E260226005B375C /* MAOpenStoreView.m in Sources */,
				0D873AEF2E260226005B375C /* MAApprovalRecordView.m in Sources */,
				0D873AF02E260226005B375C /* MAShareUITableView.m in Sources */,
				0D873AF12E260226005B375C /* MAOpenStoreViewTableViewCell.m in Sources */,
				0D873AF22E260226005B375C /* NSDictionary+Common.m in Sources */,
				0D873AF32E260226005B375C /* MALocalAnnotationView.m in Sources */,
				0D873AF42E260226005B375C /* MAUploadMutilCell.m in Sources */,
				0D873AF52E260226005B375C /* UITapGestureRecognizer+Common.m in Sources */,
				0D873AF62E260226005B375C /* MAWriteMeassageView.m in Sources */,
				0D873AF72E260226005B375C /* TZImagePickerController.m in Sources */,
				0D873AF82E260226005B375C /* MABottomPointAlert.m in Sources */,
				0D873AF92E260226005B375C /* MAPointTableViewCell.m in Sources */,
				0D873AFA2E260226005B375C /* ViewController+MASAdditions.m in Sources */,
				0D873AFB2E260226005B375C /* MAUploadSingleImageView.m in Sources */,
				0D873AFC2E260226005B375C /* JAMStyledBezierPath.m in Sources */,
				0D873AFD2E260226005B375C /* BHInfiniteScrollViewCell.m in Sources */,
				0D873AFE2E260226005B375C /* MAFilterChooseCityCell.m in Sources */,
				0D873AFF2E260226005B375C /* MAPointModalView.m in Sources */,
				0D873B002E260226005B375C /* MAAllFilterView.m in Sources */,
				0D873B012E260226005B375C /* MJRefreshNormalHeader.m in Sources */,
				0D873B022E260226005B375C /* TZProgressView.m in Sources */,
				0D873B032E260226005B375C /* MACopyListView.m in Sources */,
				0D873B042E260226005B375C /* MJRefreshBackNormalFooter.m in Sources */,
				0D873B052E260226005B375C /* MABottomToast.m in Sources */,
				0D873B062E260226005B375C /* UIScrollView+MJRefresh.m in Sources */,
				0D873B072E260226005B375C /* MABuniessEditDrawViewController.m in Sources */,
				0D873B082E260226005B375C /* ZKPhoto.m in Sources */,
				0D873B092E260226005B375C /* MJRefreshAutoGifFooter.m in Sources */,
				0D873B0A2E260226005B375C /* MAEditBuniessViewController.m in Sources */,
				0D873B0B2E260226005B375C /* MJRefreshAutoFooter.m in Sources */,
				0D873B0C2E260226005B375C /* MAAddOpenStoreViewController.m in Sources */,
				0D873B0D2E260226005B375C /* MAEditPointViewController.m in Sources */,
				0D873B0E2E260226005B375C /* MJRefreshComponent.m in Sources */,
				0D873B0F2E260226005B375C /* JAMSVGGradientParts.m in Sources */,
				0D873B102E260226005B375C /* TZAssetCell.m in Sources */,
				0D873B112E260226005B375C /* TZVideoCropController.m in Sources */,
				0D873B122E260226005B375C /* MABussinessPolygonRenderer.m in Sources */,
				0D873B132E260226005B375C /* MAPoiTableViewCell.m in Sources */,
				0D873B142E260226005B375C /* MASingleSelectView.m in Sources */,
				0D873B152E260226005B375C /* MABuniessTableViewCell.m in Sources */,
				0D873B162E260226005B375C /* UICollectionViewLeftAlignedLayout.m in Sources */,
				6B58C5952E3083C40066EA87 /* NetworkSettings.m in Sources */,
				0D873B172E260226005B375C /* UIScrollView+MJExtension.m in Sources */,
				0D873B182E260226005B375C /* MASearchTextField.m in Sources */,
				0D873B192E260226005B375C /* MABaseViewController.m in Sources */,
				0D873B1A2E260226005B375C /* MABussinessPolygon.m in Sources */,
				0D873B1B2E260226005B375C /* MAMapSwitchView.m in Sources */,
				0D873B1C2E260226005B375C /* MACreatBuniessPolygonRender.m in Sources */,
				0D873B1D2E260226005B375C /* MASearchLoadingView.m in Sources */,
				0D873B1E2E260226005B375C /* BHInfiniteScrollViewTitleView.m in Sources */,
				0D873B1F2E260226005B375C /* JAMSVGImage.m in Sources */,
				0D873B202E260226005B375C /* MJRefreshBackStateFooter.m in Sources */,
				0D873B212E260226005B375C /* UITextView+Common.m in Sources */,
				0D873B222E260226005B375C /* MACreatBuniessPolygon.m in Sources */,
				0D873B232E260226005B375C /* ModalScaleTranstion.m in Sources */,
				0D873B242E260226005B375C /* MAMutiChooseCityTableViewCell.m in Sources */,
				0D873B252E260226005B375C /* ZKPhotoView.m in Sources */,
				0D873B262E260226005B375C /* MAAddBuniessViewController.m in Sources */,
				0D873B272E260226005B375C /* MAAddPointViewController.m in Sources */,
				0D873B282E260226005B375C /* NSArray+MASAdditions.m in Sources */,
				0D873B292E260226005B375C /* MAPointEditPoiViewController.m in Sources */,
				0D873B2A2E260226005B375C /* JAMSVGParser.m in Sources */,
				0D873B2B2E260226005B375C /* MAWriteFollowUpViewController.m in Sources */,
				0D873B2C2E260226005B375C /* MKLabel.m in Sources */,
				0D873B2D2E260226005B375C /* TZAssetModel.m in Sources */,
				0D873B2E2E260226005B375C /* NSBundle+TZImagePicker.m in Sources */,
				0D873B2F2E260226005B375C /* UIScrollView+EmptyDataSet.m in Sources */,
				0D873B302E260226005B375C /* MANativeAlert.m in Sources */,
				0D873B312E260226005B375C /* UIImage+SVG.m in Sources */,
				0D873B322E260226005B375C /* MAUserAnnotationView.m in Sources */,
				0D873B332E260226005B375C /* MJRefreshAutoStateFooter.m in Sources */,
				0D873B342E260226005B375C /* MABuniessWriteFollowViewController.m in Sources */,
				0D873B352E260226005B375C /* NSArray+Common.m in Sources */,
				0D873B362E260226005B375C /* PushZoomScaleBuniessTranstion.m in Sources */,
				0D873B372E260226005B375C /* NSLayoutConstraint+MASDebugAdditions.m in Sources */,
				0D873B382E260226005B375C /* MAOpenStorePolyline.m in Sources */,
				0D873B392E260226005B375C /* MAChooseLocationViewController.m in Sources */,
				0D873B3A2E260226005B375C /* MAFilterTextField.m in Sources */,
				0D873B3B2E260226005B375C /* MAVisibleAreaListViewController.m in Sources */,
				0D873B3C2E260226005B375C /* MACenterBuniessAnnView.m in Sources */,
				0D873B3D2E260226005B375C /* MJRefreshConst.m in Sources */,
				0D873B3E2E260226005B375C /* PushZoomScaleTranstion.m in Sources */,
				0D873B3F2E260226005B375C /* MAAppleMapViewController.m in Sources */,
				0D873B402E260226005B375C /* MAUploadFileView.m in Sources */,
				0D873B412E260226005B375C /* ModalHalfScreenTranstion.m in Sources */,
				0D873B422E260226005B375C /* MAFilterModal.m in Sources */,
				0D873B432E260226005B375C /* MACreatBuniessPointAnnotation.m in Sources */,
				0D873B442E260226005B375C /* PushCellScaleBuniessTranstion.m in Sources */,
				0D873B452E260226005B375C /* MACreatBuniessDrawBoardView.m in Sources */,
				0D873B462E260226005B375C /* MAPointDetailViewController.m in Sources */,
				0D873B472E260226005B375C /* MABottomMutilSelectAlert.m in Sources */,
				0D873B482E260226005B375C /* MALookPointViewController.m in Sources */,
				0D873B492E260226005B375C /* MAHorScrollview.m in Sources */,
				0D873B4A2E260226005B375C /* UIView+TZLayout.m in Sources */,
				0D873B4B2E260226005B375C /* MASConstraintMaker.m in Sources */,
				0D873B4C2E260226005B375C /* MASearchTableViewCell.m in Sources */,
				0D873B4D2E260226005B375C /* MAEditOpenStoreView.m in Sources */,
				0D873B4E2E260226005B375C /* MALoadAlphaView.m in Sources */,
				0D873B4F2E260226005B375C /* MABuniessDetailViewController.m in Sources */,
				0D873B502E260226005B375C /* MAPremissionCheckViewController.m in Sources */,
				0D873B512E260226005B375C /* MAAddLandlordViewController.m in Sources */,
				0D873B522E260226005B375C /* TZPhotoPickerController.m in Sources */,
				89EE81882DF7F70800C65C80 /* BridgingHeader.h in Sources */,
				1D3A326E2E2774C3000CCE3A /* XLBBackRunningManager.m in Sources */,
				6BAD713D2E1F526200EDED32 /* OpenWechatBridge.m in Sources */,
				894BADBB2E0D4E5D00F48566 /* SyncScrollView.swift in Sources */,
				761780ED2CA45674006654EE /* AppDelegate.swift in Sources */,
				1D3A32692E2774B6000CCE3A /* RemoteNotificationCenter.m in Sources */,
				C7B217D22E1E12A300AF5693 /* ScreenRotateManagerBridge.m in Sources */,
				894BADB92E0D4E4A00F48566 /* SyncScrollManager.swift in Sources */,
				C7B217CE2E1D4E4300AF5693 /* ScreenRotateManager.swift in Sources */,
				1D3A32662E2739E5000CCE3A /* RNBrigeManager.m in Sources */,
				BF9918C92E23EC4500277B25 /* NativeHotUpdate.m in Sources */,
				BF9918C62E23DC5100277B25 /* NativeHotUpdate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1769E8F44A0D621C65901D6D /* Pods-a608.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = a608/a608.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = T657G754LH;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = a608/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR = YES;
				PRODUCT_BUNDLE_IDENTIFIER = org.reactjs.native.example.xlb.dev;
				PRODUCT_NAME = xlb;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/BridgingHeader.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 90D5497654C14CDC92262297 /* Pods-a608.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = a608/a608.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = T657G754LH;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = a608/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR = YES;
				PRODUCT_BUNDLE_IDENTIFIER = org.reactjs.native.example.xlb.dev;
				PRODUCT_NAME = xlb;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/BridgingHeader.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5BD63A742E20B7660022CA8F /* Config.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				APPLY_RULES_IN_COPY_HEADERS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = T657G754LH;
				EAGER_LINKING = YES;
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu99 c++20";
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				SWIFT_SKIP_AUTOLINKING_FRAMEWORKS = "";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5BD63A742E20B7660022CA8F /* Config.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				APPLY_RULES_IN_COPY_HEADERS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = T657G754LH;
				EAGER_LINKING = YES;
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu99 c++20";
				MTL_ENABLE_DEBUG_INFO = NO;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_SKIP_AUTOLINKING_FRAMEWORKS = "";
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "a608" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "a608" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
