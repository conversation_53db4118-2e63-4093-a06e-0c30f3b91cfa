import React, {Component} from 'react';
import {
  Dimensions,
  Linking,
  PermissionsAndroid,
  Platform,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import RNFS from 'react-native-fs';
import FastImage from 'react-native-fast-image';
import Toast from 'react-native-root-toast';
import Config from 'react-native-config';
import {Toast as ToastModal} from 'native-base';
import {Button, XText} from '@xlb/common/src/components';
import commonStyles, {colors} from '@xlb/components-rn/styles';
import ProgressBar from '@xlb/business-base/src/screens/home/<USER>/sms/components/ProgressBar.tsx';
import {updateApi} from '@/components/xlbUpdate/api.ts';
import nativeHotUpdate from '@/NativeModules/nativeHotUpdate.ts';
import {$modalAlert} from '@/components/xAlert';
import {safeMath} from '@xlb/common/src/utils';
import packageJson from '../../../package.json';
import {bundlePrefix} from '@/components/xlbUpdate/constant.ts';

// 类型定义
interface XlbUpdateState {
  isShow: boolean;
  downloadProgress: number;
  buildVersion?: string;
  totalSize: string;
  isDownloadComplete: boolean;
}

interface VersionInfo {
  localVersion: string;
  downloadUrl?: string;
  downloadTime?: string;
  fileSize?: number;
}

let _this: any = null;
const height = Dimensions.get('screen').height;
const width = Dimensions.get('screen').width;

class XlbUpdate extends Component<{}, XlbUpdateState> {
  constructor(props: {}) {
    super(props);
    _this = this;
    this.state = {
      isShow: false,
      downloadProgress: 0,
      totalSize: '0 MB',
      buildVersion: Config.VERSION_NAME,
      isDownloadComplete: false,
    };
  }

  // 路径常量
  private readonly dirPath = `${RNFS.DocumentDirectoryPath}/${Config.ENV}`;
  private readonly downloadDest = `${this.dirPath}/xlb.apk`;
  private readonly versionFilePath = `${this.dirPath}/version.json`;

  async componentDidMount() {
    try {
      // 预加载
      await this.checkUpdate();
    } catch (error) {
      console.error('检查更新失败：', error);
      this.showErrorToast('检查更新失败');
    }
  }

  // 显示更新弹窗
  private showUpdateAlert = async (
    downloadUrl: string,
    buildVersion: string,
  ) => {
    const isExit = await this.checkExistingApk(buildVersion);
    if (isExit) {
      this.setState({
        isShow: true,
        downloadProgress: 100,
      });
    } else {
      $modalAlert(
        '提示',
        <View style={{width: '100%', flex: 1}}>
          <Text style={styles.alertText}>检测到新版本，请下载安装新版本</Text>
          <View style={styles.buttonContainer}>
            <Button
              titleStyle={styles.buttonTitle}
              buttonStyle={styles.primaryButton}
              containerStyle={styles.primaryButtonContainer}
              onPress={async () => {
                ToastModal.close('toast-alert');
                if (Platform.OS === 'android') {
                  await this.downloadAndInstallApk(downloadUrl, buildVersion);
                } else {
                  try {
                    await Linking.openURL(downloadUrl);
                  } catch (e) {
                    Toast.show('请自行前往应用市场下载');
                  }
                }
              }}>
              直接下载
            </Button>
            <Button
              titleStyle={styles.cancelButtonTitle}
              buttonStyle={styles.cancelButton}
              containerStyle={styles.cancelButtonContainer}
              onPress={() => {
                ToastModal.close('toast-alert');
              }}>
              取消
            </Button>
            <Text style={styles.helpText}>
              下载遇到问题？尝试跳转
              <Text
                style={styles.linkText}
                onPress={() => {
                  Linking.openURL(
                    'https://hxl-applet.oss-cn-hangzhou.aliyuncs.com/mobile-applet/xlb-production.apk',
                  );
                }}>
                浏览器下载
              </Text>
            </Text>
          </View>
        </View>,
        () => {},
        () => {},
        '',
        '',
        false,
        {width: 295, height: 248, paddingHorizontal: 20},
      );
    }
  };

  /**
   * 检查原生更新，有更新则不走热更新
   */
  private checkUpdate = async () => {
    if (__DEV__) return console.warn('本地不执行热更新逻辑----checkUpdate');
    try {
      const response = await updateApi.requestCheckApk();
      const {buildVersion, downloadUrl, buildIosVersion, downloadIosUrl} =
        response;
      this.setState({
        buildVersion: buildVersion,
      });

      if (Platform.OS === 'ios') {
        if (buildIosVersion !== Config.VERSION_NAME && downloadIosUrl) {
          await this.showUpdateAlert(downloadIosUrl, buildVersion);
        } else {
          await this.checkHotUpdate();
        }
      } else {
        if (buildVersion !== Config.VERSION_NAME && downloadUrl) {
          await this.showUpdateAlert(downloadUrl, buildVersion);
        } else {
          await this.checkHotUpdate();
        }
      }
    } catch (error) {
      console.error('检查原生更新失败:', error);
      this.showErrorToast('检查原生更新失败');
    }
  };

  // 检查是否有更新
  static getIsUpdate = async () => {
    if(__DEV__) return console.warn('本地不执行热更新逻辑----getIsUpdate')
    try {
      const response = await updateApi.requestCheckBundle();
      console.log(
          '热更新版本信息:',
          response[packageJson.name][Config.BRANCH_NAME!],
      );
      const bundleVersion = response[packageJson.name][Config.BRANCH_NAME!];
      return [await nativeHotUpdate.hasUpdate(bundleVersion), bundleVersion];
    } catch (error) {
      console.error('检查热更新失败:', error);
      return [false, ''];
    }
  };

  /**
   * 热更新(主包)
   */
  private checkHotUpdate = async () => {
    try {
      const [isUpdate, bundleVersion] = await XlbUpdate.getIsUpdate();
      if (isUpdate) {
        nativeHotUpdate.runDownload(
          bundleVersion,
          `${bundlePrefix}/host-app-xlb/${bundleVersion}/generated/${Platform.OS}/index.bundle.js`,
        );
      }
    } catch (error) {
      console.error('检查热更新失败:', error);
      this.showErrorToast('检查热更新失败');
    }
  };



  static hide = () => {
    if (_this) {
      _this.setState({
        isShow: false,
        isDownloadComplete: false,
        downloadProgress: 0,
      });
    }
  };

  // 文件操作工具方法
  private ensureDirectoryExists = async (): Promise<boolean> => {
    try {
      const dirExists = await RNFS.exists(this.dirPath);
      if (!dirExists) {
        await RNFS.mkdir(this.dirPath);
        console.log('创建目录:', this.dirPath);
      }
      return true;
    } catch (error) {
      console.error('创建目录失败:', error);
      this.showErrorToast('存储权限不足');
      return false;
    }
  };

  private readVersionInfo = async (): Promise<VersionInfo | null> => {
    try {
      const versionFileExist = await RNFS.exists(this.versionFilePath);
      if (versionFileExist) {
        const jsonInfo = await RNFS.readFile(this.versionFilePath, 'utf8');
        return JSON.parse(jsonInfo);
      }
      return null;
    } catch (error) {
      console.warn('读取版本信息失败:', error);
      return null;
    }
  };

  private saveVersionInfo = async (
    version: string,
    downloadUrl: string,
  ): Promise<void> => {
    try {
      const versionInfo: VersionInfo = {
        localVersion: version,
        downloadUrl: downloadUrl,
        downloadTime: new Date().toISOString(),
        fileSize: 0, // 可以在下载完成后更新
      };
      await RNFS.writeFile(
        this.versionFilePath,
        JSON.stringify(versionInfo, null, 2),
        'utf8',
      );
    } catch (error) {
      console.warn('保存版本信息失败:', error);
    }
  };

  private checkExistingApk = async (buildVersion: string): Promise<boolean> => {
    try {
      const apkExist = await RNFS.exists(this.downloadDest);
      if (apkExist) {
        const versionInfo = await this.readVersionInfo();
        const localVersion = versionInfo?.localVersion || '';

        console.log('本地版本:', localVersion, '远程版本:', buildVersion);
        if (localVersion === buildVersion) {
          // 版本一致，可以直接安装
          this.setState({
            isShow: true,
            isDownloadComplete: true,
          });
          return true;
        } else {
          // 版本不一致，删除旧文件
          await this.cleanUpOldFiles();
        }
      }
      return false;
    } catch (error) {
      console.warn('检查现有APK失败:', error);
      return false;
    }
  };

  private cleanUpOldFiles = async (): Promise<void> => {
    try {
      const filesToDelete = [this.downloadDest, this.versionFilePath];
      for (const filePath of filesToDelete) {
        const fileExists = await RNFS.exists(filePath);
        if (fileExists) {
          await RNFS.unlink(filePath);
        }
      }
    } catch (error) {
      console.warn('清理旧文件失败:', error);
    }
  };

  // 下载APK
  private downloadApk = async (url: string): Promise<boolean> => {
    return new Promise(resolve => {
      const download = RNFS.downloadFile({
        fromUrl: url,
        toFile: this.downloadDest,
        progress: res => {
          if (res.contentLength > 0) {
            this.setState({
              totalSize:
                safeMath
                  .divide(res.contentLength, safeMath.multiply(1024, 1024))
                  .toFixed(2) + ' MB',
              downloadProgress: parseInt(
                safeMath
                  .multiply(
                    safeMath.divide(res.bytesWritten, res.contentLength),
                    100,
                  )
                  .toFixed(0),
              ),
            });
          }
        },
        progressDivider: 5,
      });

      download.promise
        .then(result => {
          if (result.statusCode === 200) {
            resolve(true);
          } else {
            this.showErrorToast(`下载失败，状态码: ${result.statusCode}`);
            resolve(false);
          }
        })
        .catch(error => {
          console.error('下载失败:', error);
          this.showErrorToast('下载失败，请检查网络连接');
          resolve(false);
        });
    });
  };

  // 主要业务逻辑方法
  async downloadAndInstallApk(
    url: string,
    buildVersion: string,
  ): Promise<void> {
    if (Platform.OS !== 'android') {
      return;
    }

    try {
      // 请求存储权限
      const hasPermission = await this.requestStoragePermission();
      if (!hasPermission) {
        return;
      }

      // 确保存储目录存在
      const dirReady = await this.ensureDirectoryExists();
      if (!dirReady) {
        return;
      }

      // 检查是否已有相同版本的APK
      const canInstallDirectly = await this.checkExistingApk(buildVersion);
      if (canInstallDirectly) {
        return;
      }

      // 开始下载
      this.setState({
        isShow: true,
        isDownloadComplete: false,
        totalSize: '0 MB',
        downloadProgress: 0,
      });

      const downloadSuccess = await this.downloadApk(url);
      if (downloadSuccess) {
        // 下载成功后保存版本信息
        await this.saveVersionInfo(buildVersion, url);

        // 更新状态准备安装
        this.setState({
          isDownloadComplete: true,
          downloadProgress: 100,
        });
      }
    } catch (error: any) {
      console.error('下载APK失败:', error);
      this.showErrorToast(`操作失败: ${error.message}`);
      this.setState({isShow: false});
    }
  }

  // 权限处理
  private requestStoragePermission = async (): Promise<boolean> => {
    try {
      if (Platform.OS === 'android' && Platform.Version <= 23) {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      }
      return true;
    } catch (error) {
      console.warn('权限请求失败:', error);
      this.showErrorToast('存储权限获取失败');
      return false;
    }
  };

  // 安装APK
  private installApk = async () => {
    try {
      const installResult = await nativeHotUpdate.installApk(this.downloadDest);
      console.log('安装启动结果:', installResult);
      // 安装启动后隐藏界面
      this.setState({isShow: false});
    } catch (error: any) {
      console.error('启动安装失败:', error);
      this.showErrorToast('启动安装失败: ' + (error.message || '未知错误'));
    }
  };

  // 错误提示
  private showErrorToast = (message: string) => {
    Toast.show(message, {
      duration: Toast.durations.LONG,
      position: Toast.positions.BOTTOM,
    });
  };

  render() {
    if (!this.state.isShow) {
      return <></>;
    }

    return (
      <View style={styles.container}>
        <View style={styles.infoBox}>
          <View style={commonStyles.horBox}>
            <View style={{flex: 1}}>
              <View style={{...commonStyles.verBox}}>
                <XText bold style={styles.titleText}>
                  发现新版本
                </XText>
                <XText bold style={styles.versionText}>
                  {this.state.buildVersion}
                </XText>
              </View>
            </View>
            <FastImage
              source={require('@xlb/common/src/assets/images/appUpdate.png')}
              resizeMode={'contain'}
              style={styles.imageBox}
            />
          </View>
          <View style={commonStyles.centerBox}>
            <XText style={styles.statusText}>
              {this.state.isDownloadComplete
                ? '本地安装包已就绪，是否直接安装'
                : '正在更新...请勿操作'}
            </XText>

            {this.state.isDownloadComplete ? (
              <View style={styles.installButtonContainer}>
                <Button
                  titleStyle={styles.installButtonTitle}
                  buttonStyle={styles.installButton}
                  containerStyle={styles.installButtonWrapper}
                  onPress={this.installApk}>
                  直接安装
                </Button>
                <Button
                  titleStyle={styles.cancelInstallButtonTitle}
                  buttonStyle={styles.cancelInstallButton}
                  containerStyle={styles.cancelInstallButtonWrapper}
                  onPress={() => XlbUpdate.hide()}>
                  取消
                </Button>
              </View>
            ) : (
              <View style={styles.progressBarContainer}>
                <ProgressBar
                  label={this.state.totalSize}
                  progress={this.state.downloadProgress}
                  colors={[colors.blueBg, colors.blue]}
                />
              </View>
            )}
          </View>
        </View>
      </View>
    );
  }
}

// 样式抽取
const styles = StyleSheet.create({
  container: {
    height: height,
    width: width,
    position: 'absolute',
    zIndex: 100,
    paddingHorizontal: 40,
    backgroundColor: 'rgba(0,0,0,.15)',
    ...commonStyles.centerBox,
  },
  infoBox: {
    borderRadius: 15,
    width: width * 0.8,
    backgroundColor: '#fff',
    paddingBottom: 40,
  },
  imageBox: {
    flex: 1,
    width: 140,
    height: 140,
    bottom: 30,
    position: 'relative',
  },
  // 弹窗样式
  alertText: {
    fontSize: 14,
    marginTop: 12,
    color: '#1E2126B3',
    lineHeight: 20,
    textAlign: 'center',
  },
  buttonContainer: {
    flex: 1,
    marginTop: 20,
    width: '100%',
  },
  buttonTitle: {
    paddingVertical: 0,
    fontSize: 16,
  },
  primaryButton: {
    backgroundColor: '#1A6AFFFF',
    height: '100%',
  },
  primaryButtonContainer: {
    backgroundColor: '#1A6AFFFF',
    height: 40,
    borderRadius: 4,
  },
  cancelButtonTitle: {
    color: '#1F2126FF',
    fontSize: 16,
  },
  cancelButton: {
    backgroundColor: '#FFFFFFFF',
    height: '100%',
  },
  cancelButtonContainer: {
    backgroundColor: '#FFFFFFFF',
    height: 40,
    borderRadius: 4,
    marginTop: 12,
    borderWidth: 0.5,
    borderColor: '#1E212626',
  },
  helpText: {
    fontSize: 14,
    marginTop: 12,
    color: '#1E2126B3',
    lineHeight: 20,
    textAlign: 'center',
  },
  linkText: {
    color: '#1A6AFFFF',
  },
  // 标题样式
  titleText: {
    marginLeft: 20,
    marginTop: 50,
    fontSize: 22,
    color: '#1D2129',
  },
  versionText: {
    marginLeft: 20,
    fontSize: 22,
    color: '#1D2129',
  },
  statusText: {
    fontSize: 16,
    color: '#1D2129',
    marginBottom: 20,
  },
  // 进度条样式
  progressBarContainer: {
    width: '80%',
    marginTop: 20,
    alignItems: 'center',
  },
  progressText: {
    marginTop: 10,
    fontSize: 14,
    color: '#1D2129',
  },
  // 安装按钮样式
  installButtonContainer: {
    padding: 20,
    width: '100%',
  },
  installButtonTitle: {
    paddingVertical: 0,
    fontSize: 16,
  },
  installButton: {
    backgroundColor: '#1A6AFFFF',
    height: '100%',
  },
  installButtonWrapper: {
    backgroundColor: '#1A6AFFFF',
    height: 40,
    borderRadius: 4,
    width: '100%',
    marginBottom: 10,
  },
  cancelInstallButtonTitle: {
    color: '#1F2126FF',
    fontSize: 16,
  },
  cancelInstallButton: {
    backgroundColor: '#FFFFFFFF',
    height: '100%',
  },
  cancelInstallButtonWrapper: {
    backgroundColor: '#FFFFFFFF',
    height: 40,
    borderRadius: 4,
    width: '100%',
    borderWidth: 0.5,
    borderColor: '#1E212626',
  },
});

export default XlbUpdate;
