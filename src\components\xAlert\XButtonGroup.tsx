import React from 'react'
import { View } from 'react-native'
import { Button } from 'native-base'
import { moderateScale } from 'react-native-size-matters'

// 根据屏幕自适应大小
export function normalize(number: number, factor = 0.25) {
  return moderateScale(number, factor)
}

/**
 * 按钮组组件
 * 注意所有的 radius 是针对于整个按钮组的，而不是单个按钮
 * @param props 继承 nb，作用在最外层容器上，按钮暂无其他属性暴露
 * @constructor
 */
export default function XButtonGroup(props: any) {
  const {
    onCancel = null,
    onConfirm = null,
    borderBottomLeftRadius,
    borderBottomRightRadius,
    borderTopLeftRadius,
    borderTopRightRadius,
    cancelText = '取消',
    confirmText = '确认',
    variant,
    cancelColorScheme = 'white',
    borderRightWidth = 1,
    borderRightColor = '#E5E6EA',
    cancleStyle={},
    confirmStyle={}
  } = props

  const _isLink = variant == 'link'
  const _borderColor = '#f0f0f0'
  const _borderWidth = _isLink ? 0.5 : 0
  const _bg = _isLink ? '#fff' : ''

  return (
    <View
      style={{
        borderTopWidth: _borderWidth,
        borderTopColor: _borderColor,
        backgroundColor: _bg,
        alignItems: 'center',
        flexDirection: 'row',
      }}
    >
      <Button
        flex={1}
        onPress={onCancel}
        variant={variant}
        borderRadius={0}
        borderBottomLeftRadius={borderBottomLeftRadius}
        borderTopLeftRadius={borderTopLeftRadius}
        borderRightWidth={borderRightWidth}
        borderRightColor={borderRightColor}
        _text={{ textDecorationLine: 'none', fontSize: normalize(16), color: '#1F84FF' }}
        colorScheme={cancelColorScheme}
        style={{ ...cancleStyle }}
      >
        {cancelText}
      </Button>
      <Button
        flex={1}
        onPress={onConfirm}
        variant={variant}
        borderRadius={0}
        borderBottomRightRadius={borderBottomRightRadius}
        borderTopRightRadius={borderTopRightRadius}
        _text={{ textDecorationLine: 'none', fontSize: normalize(16), color: '#1F84FF', fontWeight: 'bold' }}
        colorScheme={cancelColorScheme}
        style={{ ...confirmStyle }}
      >
        {confirmText}
      </Button>
    </View>
  )
}
