package com.hxl.xlb.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.provider.OpenableColumns;
import android.util.Log;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;

import com.hjq.permissions.XXPermissions;
import com.hjq.permissions.permission.PermissionLists;
import com.hjq.permissions.permission.base.IPermission;
import com.hxl.xlb.contract.PickUtilResultContract;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class PickerUtil {
    private final Activity activity;
    private ActivityResultLauncher<Intent> launcher;

    private String operatorId;

    // Permissions
    private static final String[] CAMERA_PERMISSIONS = {
            Manifest.permission.CAMERA,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
    };

    private File lastCapturedPhotoFile; // Store the last captured photo file

    public PickerUtil(Activity activity, ActivityResultLauncher<Intent> launcher, String id) {
        this.activity = activity;
        this.launcher = launcher;
        Log.i("PickerUtil", "PickerUtil: " + id);
        this.operatorId = id;
    }

    public void setFilePickerLauncher(ActivityResultLauncher<Intent> launcher) {
        this.launcher = launcher;
    }

    public static class PickerResult {
        public final String id;
        public final List<Uri> uris;

        public PickerResult(String id, List<Uri> uris) {
            this.id = id;
            this.uris = uris;
        }
    }

    public File getFileFromUri(Uri uri) {
        String filePath = null;
        if ("content".equals(uri.getScheme())) {
            Cursor cursor = activity.getContentResolver().query(uri, null, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                int columnIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME);
                String fileName = cursor.getString(columnIndex);
                File cacheDir = activity.getCacheDir();
                File file = new File(cacheDir, fileName);
                try (InputStream inputStream = activity.getContentResolver().openInputStream(uri);
                     FileOutputStream outputStream = new FileOutputStream(file)) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                filePath = file.getAbsolutePath();
            }
        } else if ("file".equals(uri.getScheme())) {
            filePath = uri.getPath();
        }
        return filePath != null ? new File(filePath) : null;
    }

    /**
     * 处理 ActivityResult 并返回选中的文件 Uri 列表
     */
    public PickerResult handleActivityResult(PickUtilResultContract.ActivityResultWithExtra result) {
        Intent data = result.getData();
        List<Uri> uris = new ArrayList<>();

        Log.i("TAG", "---------PickerUtil---------handleActivityResult called with operatorId: " + operatorId);
        Log.i("TAG", "---------PickerUtil---------handleActivityResult called with data: " + data);

        if (data != null) {
            // Single selection
            if (data.getData() != null) {
                uris.add(data.getData());
                Log.d("TAG", "---------PickerUtil--------Single URI: " + data.getData());
            }
            // Multiple selection
            else if (data.getClipData() != null) {
                for (int i = 0; i < data.getClipData().getItemCount(); i++) {
                    Uri uri = data.getClipData().getItemAt(i).getUri();
                    uris.add(uri);
                    Log.d("TAG", "---------PickerUtil--------Multi URI: " + uri);
                }
            }
        } else if (lastCapturedPhotoFile != null && lastCapturedPhotoFile.exists()) {
            // Return the URI of the last captured photo
            Uri photoUri = Uri.fromFile(lastCapturedPhotoFile);
            uris.add(photoUri);
            Log.d("TAG", "---------PickerUtil--------Captured photo URI: " + photoUri);
        } else {
            Log.w("TAG", "---------PickerUtil--------No data or captured photo found in ActivityResult");
        }

        return new PickerResult(operatorId, uris);
    }

    /**
     * 打开文件选择器选择图片
     */
    public void pickImagesFromGallery() {
        Intent intent = new Intent(Intent.ACTION_PICK);
        intent.setType("image/*");
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
        intent.putExtra("extra_data", operatorId); // Add operatorId
        Log.d("PickerUtil", "pickImagesFromGallery: operatorId = " + operatorId);
        launcher.launch(intent);
    }

    /**
     * 打开文件选择器选择视频
     */
    public void pickVideosFromGallery() {
        Intent intent = new Intent(Intent.ACTION_PICK);
        intent.setType("video/*");
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
        intent.putExtra("extra_data", operatorId); // Add operatorId
        Log.d("PickerUtil", "pickVideosFromGallery: operatorId = " + operatorId);
        launcher.launch(intent);
    }

    public void pickMixFilesFromGallery() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("*/*");
        String[] mimeTypes = {"image/*", "video/*"};
        intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
        intent.putExtra("extra_data", operatorId);
        Log.d("PickerUtil", "pickVideosFromGallery: operatorId = " + operatorId);
        launcher.launch(intent);
    }

    public File getLastCapturedPhotoFile() {
        return lastCapturedPhotoFile;
    }

    private File createImageFile() {
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String fileName = "IMG_" + timeStamp;
        File storageDir = activity.getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        try {
            File imageFile = File.createTempFile(fileName, ".jpg", storageDir);
            lastCapturedPhotoFile = imageFile; // Save the file reference for later use
            return imageFile;
        } catch (IOException e) {
            Log.i("createImageFile", e.toString());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启动拍照功能
     */
    public void captureImage() {
        if (hasCameraPermissions()) {
            File photoFile = createImageFile();
            if (photoFile != null) {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                Uri photoUri;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {  //Android7.0以上
                    photoUri= FileProvider.getUriForFile(activity, activity.getPackageName()+".mainfileprovider", photoFile);
                    intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                    intent.putExtra("extra_data", operatorId); // Add operatorId
                    // Add these flags to ensure data comes back
                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
                    Log.i("TAG", "--------PickerUtil--------captureImage: operatorId = " + operatorId);
                    Log.i("TAG", "--------PickerUtil--------captureImage: photoFile = " + photoFile);
                    lastCapturedPhotoFile = photoFile;
                    launcher.launch(intent);

                } else {
                    photoUri = Uri.fromFile(new File(Environment.getExternalStorageDirectory(), "image.jpg"));//7.0以下打开相机拍照的方法
                    intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);//保存照片
                    launcher.launch(intent);
                }
            }
        } else {
            requestCameraPermissions();
        }
    }

    /**
     * 启动录像功能
     */
    public void captureVideo() {
        if (hasCameraPermissions()) {
            Intent intent = new Intent(MediaStore.ACTION_VIDEO_CAPTURE);
            intent.putExtra("extra_data", operatorId); // Add operatorId
            Log.d("PickerUtil", "captureVideo: operatorId = " + operatorId);
            launcher.launch(intent);
        } else {
            requestCameraPermissions();
        }
    }

    /**
     * 检查是否拥有相机和存储权限
     */
    private boolean hasCameraPermissions() {
//        for (String permission : CAMERA_PERMISSIONS) {
//            if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
//                return false;
//            }
//        }
//        return true;

        return XXPermissions.isGrantedPermission(activity, PermissionLists.getCameraPermission());
    }

    /**
     * 请求相机和存储权限
     */
    private void requestCameraPermissions() {
        ActivityCompat.requestPermissions(activity, CAMERA_PERMISSIONS, 1001);
    }

    /**
     * 权限请求结果处理
     */
    public void handlePermissionResult(int requestCode, int[] grantResults) {
        if (requestCode == 1001) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            if (!allGranted) {
                Toast.makeText(activity, "Camera permissions are required to use this feature", Toast.LENGTH_SHORT).show();
            }
        }
    }
}