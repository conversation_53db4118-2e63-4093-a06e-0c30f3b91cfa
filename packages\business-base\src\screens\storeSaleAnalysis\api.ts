import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'

// 查询pos销量营业日汇总数据
const getPossDay = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.possale.datesummary.find', params, {
    timeout: 20000,
  })
}

// 查询pos销量门店汇总数据
const getPossStore = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.possale.storesummary.find', params, {
    timeout: 20000,
  })
}

// 查询今日新店数据
const getTodayNewStore = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.possale.storesummary.new.find', params, {
    timeout: 20000,
  })
}

const initSearchCondition = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.functionfiltertemplate.save', params, {
    timeout: 20000,
  })
}

// 设置筛选条件
const setSearchCondition = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.functionfiltertemplate.update', params, {
    timeout: 20000,
  })
}

// 查询筛选条件
const getSearchCondition = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.functionfiltertemplate.find', params, {
    timeout: 20000,
  })
}
export const storeSaleApi = {
  getPossStore,
  getPossDay,
  getTodayNewStore,
  setSearchCondition,
  getSearchCondition,
  initSearchCondition,
}
