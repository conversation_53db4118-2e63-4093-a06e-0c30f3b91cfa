import React, {
  RefObject,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import {
  commonSty<PERSON>,
  Header,
  HeaderTitle,
  LazyLoad,
  XIcon,
  XText,
  BottomUp,
  Space,
  cs,
  Switch,
} from '@xlb/common/src/components';
import XlbTipHelp, {
  XlbTipProps,
} from '@xlb/common/src/components/features/xlbTipHelp';
import XlbRed from '@xlb/common/src/components/features/xlbRed';
import Toast from 'react-native-root-toast';
import {images} from '@xlb/common/src/config/images';
import {colors, normalize} from '@xlb/common/src/config/theme';
import {Routes} from '@xlb/common/src/config/route';
import {mineModel} from '@xlb/business-base/src/models/mine';
import {useDefined} from 'foca';
import {$alert} from '@xlb/common/src/utils/overlay';
import {authModel, roleAuth} from '../../models/auth';
import {useNavigation, CommonActions} from '@react-navigation/native';
import {
  Image,
  Modal,
  Pressable,
  ScrollView,
  StyleSheet,
  View,
  DeviceEventEmitter,
  TouchableWithoutFeedback,
  Text,
  TouchableOpacity,
  Platform,
  TextInput,
  Linking,
  NativeModules,
} from 'react-native';
import {getAuth} from '@xlb/common/src/utils/combineDeliverOrderInAuth';
import useEntranceStore from '../entrance/useEntranceStore';
import {userDept} from '@xlb/common/src/config/enum';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttp';
import Loading from '@xlb/common/src/components/RootView/Loading';
import {Divider} from 'native-base';
import useSelectStore from '../home/<USER>/ErpHome/store';
import useStore from '@xlb/common/src/components/features/xlbStoreText/model';
import {XlbScanCode} from '@xlb/common/src/components/features';
import SelectList from './components/selectList';
import {systemData} from '@xlb/common/src/utils/dataSystem';
import useRefreshStore from '@xlb/common/src/models/refresh';
// import * as AliyunPush from 'aliyun-react-native-push'
import useAliCloudPush from '@xlb/common/src/hooks/useAliCloudPush';
import XLBStorage from '@xlb/common/src/utils/storage';
// import { ApprovalTakRoute } from '@xlb/business-kms/src/screens/approvalTask/route'
// import { ApprovalCenterRoute } from '@xlb/business-base/src/screens/approvalCenter/route'
// import Tts from 'react-native-tts'
import {FONT_STATE_KEY, VOICE_STATE_KEY} from '@xlb/common/src/config/constant';
import {$modalAlert} from '@xlb/common/src/components/SecondModal';
// import { AMapSdk } from '@xlb/react-native-amap3d'
import {SubsidyApi} from '../mileageSubsidy/server';
import Clipboard from '@react-native-clipboard/clipboard';
export const RouteTabs = [
  {
    key: 'home',
    title: '首页',
  },
  {
    key: 'application',
    title: '应用',
  },
  {
    key: 'mine',
    title: '我的',
  },
];
/**
 * 我的页面
 */
const Mine: React.FC = () => {
  const {initPush} = useAliCloudPush();
  const [show, setShow] = useState(false);
  const [accountShow, setAccountShow] = useState(false);
  const model = useDefined(mineModel);
  const setStoreList = useSelectStore((state: any) => state.setStoreList);
  const setRefresh = useRefreshStore((state: any) => state.setHomeRefresh);
  const setStoreLists = useStore((state: any) => state.setStoreList);
  const navigation = useNavigation<any>();
  const [, updateState] = useState<any>();
  const xlbTipHelpRef = useRef<any>();
  const xlbVoiceRef = useRef<any>();
  const xlbFontRef = useRef<any>();
  const [ScanItem, setScanItem] = useState<any>({});
  // 补货单推送状态
  const [pushState, setPushState] = useState<boolean>(false);
  // 语音播报状态
  const [voiceState, setVoiceState] = useState<boolean>(false);
  // 字体设置状态
  const [fontState, setFontState] = useState<boolean>(false);

  const [tmsCheckData, setTmsCheckData] = useState<any>({});

  const _goTo = (route: string, params: any = null) =>
    navigation.navigate(route, params);
  // 使用函数
  const _logout = () => {
    // Tts.setDucking(true);
    // playText('PA01-01 库位 奥利奥曲奇饼 10个');
    $alert('退出提示', '确定退出登录吗？', () => {
      // 清除aliyun绑定
      // AliyunPush.unbindAccount().then((result) => {
      //   console.log('----解绑', result)
      // })
      model.logout(authModel?.state?.userInfos?.id);
      authModel.setUserInfo({});
      authModel.setUserInfos({});
      authModel.setIsLoggedIn(false);
      // 解决首页切换门店问题
      setStoreList([]);
      setStoreLists([]);
    });
  };
  const [accountList, setAccountList] = useState<any>([]);

  const system = authModel?.state?.userInfos.supplier
    ? 'SCM'
    : useEntranceStore((state: any) => state.system);
  // console.log('system', system)
  const setSystem = useEntranceStore((state: any) => state.setSystem);
  type key = 'store' | 'account';
  const changeStoreAccount = async (key: key) => {
    if (authModel?.state?.userInfos?.access_token?.length <= 6) {
      Toast.show('无操作权限');
      return;
    }
    if (key === 'store') {
      navigation.navigate('ErpSelectStore', {
        isMultiple: false,
        postUrl: '/erp/hxl.erp.user.switchstore.page',
        storeChange,
      });
    } else {
      Loading.show();
      const res = await ErpHttp.post<CommonResponse>(
        '/erp/hxl.erp.account.user.find',
        {tel: authModel.state.userInfos?.tel},
      );
      Loading.hide();
      if (res.code === 0) {
        setAccountList(res.data.account_user_list);
        if ((res.data.account_user_list ?? []).length > 0) {
          setAccountShow(true);
        }
      }
    }
  };
  const storeChange = async (item: any) => {
    const res = await ErpHttp.post<CommonResponse>(
      '/erp/hxl.erp.user.store.switch',
      {id: item?.id},
    );
    if (res?.code === 0 && res?.data) {
      authModel?.setUserInfos(res.data);
      authModel?.setSystem(roleAuth(res.data));
    }
    // 解决首页切换门店问题
    setStoreList([]);
    setStoreLists([]);
    setRefresh();
    DeviceEventEmitter.emit('DepartDocumenRefresh'); //刷新待办事项内的发车单
  };

  const accountChange = async (account: any, company_id: any, v: any) => {
    setAccountList([]);
    // 解决首页切换门店问题
    setStoreList([]);
    setStoreLists([]);
    setAccountShow(false);
    authModel
      .onLogin(account, company_id, undefined, undefined)
      .then(async () => {
        setRefresh();
        XLBStorage.removeItem('PointPlanAdd');
        XLBStorage.removeItem('BusinessDistrictAdd');
        XLBStorage.removeItem('NewStockAdjustOrderAdd');
      });
    // AliyunPush.unbindAccount().then(() => initPush())
  };

  const getCombineAuth = async () => {
    await getAuth();
  };
  const getPushState = async () => {
    const id = authModel?.state?.userInfos?.id;
    if (!id) return;
    const res = await ErpHttp.post<CommonResponse>(
      '/erp/hxl.erp.user.notice.status.read',
      {
        id,
      },
      {
        timeout: 10000,
      },
    );
    if (res.code === 0) {
      setPushState(res.data.request_order_notice_state);
    }
  };
  const setPushStates = async (state?: boolean) => {
    const id = authModel?.state?.userInfos?.id;
    if (!id) return;
    const res = await ErpHttp.post<CommonResponse>(
      '/erp/hxl.erp.user.notice.status.update',
      {
        id,
        request_order_notice_state: state,
      },
    );
    if (res.code == 0) {
      setPushState(!pushState);
    }
  };

  const getVoiceState = async () => {
    const res = await authModel.getVoiceState();
    setVoiceState(res);
    authModel.setVoiceState(res);
  };

  const getFontState = async () => {
    const res = await authModel.getFontState();
    setFontState(res);
    authModel.setFontState(res);
  };

  const getTmsCheckStates = async () => {
    const id = authModel?.state?.userInfos?.id;
    // console.log('authModel?.state?.userInfos', authModel?.state?.userInfos)
    if (!id) return;
    const res = await ErpHttp.post<CommonResponse>(
      '/tms/hxl.tms.sign.certification.check',
      {
        tel: authModel?.state?.userInfos?.tel,
      },
      {timeout: 20000},
    );
    if (res.code == 0) {
      setTmsCheckData(res.data);
    }
  };

  const toCheck = async () => {
    Loading.show();
    const res = await ErpHttp.post<CommonResponse>(
      '/tms/hxl.tms.sign.certification',
      {
        tel: authModel?.state?.userInfos?.tel,
        end_point: 'H5',
      },
    );
    if (res.code == 0) {
      if (res.data.console_url) {
        Loading.hide();
        $modalAlert(
          '是否跳转认证链接?',
          '',
          async () => {
            Linking.openURL(res.data.console_url);
          },
          () => {
            Clipboard.setString(res.data.console_url);
            Toast.show('复制成功，请去浏览器粘贴打开');
          },
          '一键复制',
        );
      } else {
        Loading.hide();
        Toast.show('数据有误');
      }
    }
  };

  useEffect(() => {
    const WMSWaitHandleRefresh = DeviceEventEmitter.addListener(
      'TMSCheckRefresh',
      () => {
        getTmsCheckStates();
      },
    );
    return () => {
      WMSWaitHandleRefresh.remove();
    };
  }, []);

  useEffect(() => {
    getTmsCheckStates();
    getCombineAuth();
    getPushState();
    getVoiceState();
    getFontState();
  }, []);

  // const getKmsFollowUserAuth = async () => {
  //   const res: any = await ErpHttp.post<CommonResponse>('/kms/hxl.kms.clientfollowuser.authority.find', {})
  //    if(res.code === 0){
  //     setIsKmsFollowUser(res.data)
  //    }
  // }

  useEffect(() => {
    ScanItem.code && navigation.navigate('ScanLoginWeb', {code: ScanItem.code});
  }, [ScanItem]);

  // 地图监听事件 && 导航模块需要

  const endNavi = async () => {
    const res = await SubsidyApi.getMileageDriving({});
    if (res?.code === 0 && res?.data?.fid) {
      navigation.dispatch((state: any) => {
        const index = state.routes.findIndex(r => r?.name === 'MileageSubsidy');
        if (index === -1) {
          //如果不在里程补贴页面
          const routes = state.routes;
          return CommonActions.reset({
            routes: [
              ...routes,
              {name: 'MileageSubsidy'},
              {name: 'ExitNavi', params: {itemData: res?.data}},
            ],
            index: routes.length + 1,
          });
        } else {
          const currentIndex = state.routes.findIndex(
            r => r?.name === 'ExitNavi',
          );
          if (currentIndex === -1) {
            //如果不在退出导航页面
            const routes = state.routes.slice(0, index + 1);
            return CommonActions.reset({
              routes: [
                ...routes,
                {name: 'ExitNavi', params: {itemData: res?.data}},
              ],
              index: routes.length,
            });
          }
        }
      });
    }
  };

  useEffect(() => {
    const stopSubscribe = DeviceEventEmitter.addListener(
      'naviMapStop',
      (data: any) => {
        if (Platform.OS == 'ios') {
          AMapSdk.stopGaterAndPack();
        } else {
          NativeModules.AMapSdkJavaVersion.stopLocationService();
          // TraceService.getInstance().stopCustomTimer();
        }
        endNavi();
      },
    );

    return () => {
      stopSubscribe.remove();
    };
  }, []);

  return (
    <>
      <Header
        centerComponent={<HeaderTitle title="我的" />}
        rightComponent={
          <View style={{marginRight: 10}}>
            <XlbScanCode setBarCodes={setScanItem} color={'white'} />
          </View>
        }>
        <Image source={images['loginBackground']} style={styles.img} />
      </Header>

      {/*主体内容*/}
      <ScrollView>
        <View style={styles.view_content}>
          {/* 模块一 */}
          <View
            style={[
              styles.xShadowBox,
              {
                marginBottom: normalize(8),
                paddingBottom: normalize(28),
                paddingTop: normalize(6),
              },
            ]}>
            <Image
              source={images['loginPersonalbg']}
              style={{
                width: 268,
                height: 126,
                position: 'absolute',
                bottom: 0,
                right: 0,
              }}
            />
            <View
              style={[
                styles.flexDirectionRow,
                {justifyContent: 'space-between', marginBottom: 20},
              ]}>
              {authModel.state.userInfos?.avatar_url ? (
                <Image
                  style={styles.avatar}
                  source={{
                    uri: authModel.state.userInfos?.avatar_url,
                  }}
                />
              ) : (
                <View style={styles.avatar}>
                  <XIcon name={'iconMineActive'} size={55} color="white" />
                </View>
              )}
              <View style={styles.ids}>
                <XText size10 style={{color: '#C9CDD4'}}>
                  ID:{authModel.state.userInfos?.account || '--'}
                </XText>
              </View>
            </View>
            <View style={{marginLeft: 14}}>
              <View style={[styles.flexDirectionRow, styles.edit_content]}>
                <XText size16 style={{color: '#1D2129'}}>
                  {authModel.state.userInfos?.name || '--'}
                </XText>
                {authModel?.state?.userInfos?.isTemporarily ? (
                  <></>
                ) : (
                  <View style={[styles.editBtn, styles.jac]}>
                    <XText
                      size12
                      style={{color: '#1D2129'}}
                      onPress={() => _goTo(Routes.Profile)}>
                      编辑资料
                    </XText>
                  </View>
                )}
              </View>
              <XText size13 style={{color: '#86909C', marginTop: 8}}>
                {authModel.state.userInfos?.company_name}
              </XText>
              <XText size13 style={{color: '#86909C', marginTop: 8}}>
                {authModel.state.userInfos?.store?.store_name || '--'} ｜
                {userDept[authModel.state.userInfos?.business_dept] || '--'}
              </XText>
              <XText size13 style={{color: '#1D2129', marginTop: 8}}>
                {authModel.state.userInfos?.tel}
              </XText>
            </View>
          </View>

          <View style={[styles.xShadowBox]}>
            <Pressable
              style={[
                styles.xItem,
                {borderBottomColor: '#F2F2F2', borderBottomWidth: 1},
              ]}
              onPress={() => {
                if (authModel?.state?.userInfos?.isTemporarily) return;
                setShow(true);
              }}>
              <View style={styles.leftView}>
                <XText style={{marginLeft: normalize(10)}} size14>
                  切换系统
                </XText>
              </View>

              <View style={styles.rightView}>
                <XText style={styles.text_content} size14>
                  {system
                    ? (systemData.find(v => v.value === system)?.name ?? '')
                    : '应用'}
                </XText>
                <XIcon name={'iconleft'} size={16} color={'#C9CDD4'} />
              </View>
            </Pressable>

            <Pressable
              style={[
                styles.xItem,
                {borderBottomWidth: 1, borderBottomColor: '#F2F2F2'},
              ]}
              onPress={() => {
                if (authModel?.state?.loginMethod === 'phone') {
                  return $modalAlert(
                    '提示',
                    '账号密码登录用户无法切换账户',
                    () => {},
                    () => {},
                    '',
                    '确认',
                    true,
                  );
                }
                if (authModel?.state?.userInfos?.isTemporarily) return;
                changeStoreAccount('account');
              }}>
              <View style={styles.leftView}>
                <XText style={{marginLeft: normalize(10)}} size14>
                  切换账户
                </XText>
              </View>
              <View style={styles.rightView}>
                <XText style={styles.text_content} size14>
                  {authModel.state.userInfos.store?.store_name ||
                    authModel.state.userInfos.supplier?.name}
                  --{authModel.state.userInfos.account}
                </XText>
                <XIcon name={'iconleft'} size={16} color={'#C9CDD4'} />
              </View>
            </Pressable>
            <Pressable
              style={[
                styles.xItem,
                {
                  borderBottomWidth: 1,
                  borderBottomColor: '#F2F2F2',
                  display: authModel.state.userInfos.store ? 'flex' : 'none',
                },
              ]}
              onPress={() => {
                if (authModel?.state?.userInfos?.isTemporarily) return;
                changeStoreAccount('store');
              }}>
              <View style={styles.leftView}>
                <XText style={{marginLeft: normalize(10)}} size14>
                  切换门店
                </XText>
              </View>
              <View style={styles.rightView}>
                <XText style={styles.text_content} size14>
                  {authModel.state.userInfos.store?.store_name}
                </XText>
                <XIcon name={'iconleft'} size={16} color={'#C9CDD4'} />
              </View>
            </Pressable>

            {authModel?.state?.userInfos?.center_id &&
              authModel?.state?.userInfos?.center_id !== 0 && (
                <Pressable
                  style={[
                    styles.xItem,
                    {
                      borderBottomWidth: 1,
                      borderBottomColor: '#F2F2F2',
                      display: authModel.state.userInfos.store
                        ? 'flex'
                        : 'none',
                    },
                  ]}
                  onPress={() => {
                    // if (authModel?.state?.userInfos?.isTemporarily) return
                    // changeStoreAccount('store')
                    // navigation.navigate(ApprovalCenterRoute.List)
                    (navigation as any).navigate('RemoteAppBpm.OA_H5_PAGE', {
                      params: {
                        onlyCenter: true,
                      },
                    });
                  }}>
                  <View style={styles.leftView}>
                    <XText style={{marginLeft: normalize(10)}} size14>
                      审批中心
                    </XText>
                  </View>
                  <View style={styles.rightView}>
                    <XText style={styles.text_content} size14>
                      {/* {authModel.state.userInfos.store?.store_name} */}
                    </XText>
                    <XIcon name={'iconleft'} size={16} color={'#C9CDD4'} />
                  </View>
                </Pressable>
              )}
            {(system == 'WMS' || system == 'ERP') && (
              <Pressable
                style={[
                  styles.xItem,
                  {
                    borderBottomWidth: 1,
                    borderBottomColor: '#F2F2F2',
                    display: authModel.state.userInfos.store ? 'flex' : 'none',
                  },
                ]}
                onPress={() => {
                  toCheck();
                }}>
                <View style={styles.leftView}>
                  <XText style={{marginLeft: normalize(10)}} size14>
                    电子签认证
                  </XText>
                </View>
                <View style={styles.rightView}>
                  <XText style={styles.text_content} size14>
                    {tmsCheckData?.is_activated &&
                    tmsCheckData.proxy_operator_is_verified
                      ? '已认证'
                      : '去认证'}
                  </XText>
                  <XIcon name={'iconleft'} size={16} color={'#C9CDD4'} />
                </View>
              </Pressable>
            )}
            {/* 里程补贴 */}
            {
              <Pressable
                style={[
                  styles.xItem,
                  {
                    borderBottomWidth: 1,
                    borderBottomColor: '#F2F2F2',
                  },
                ]}
                onPress={() => _goTo(Routes.MileageSubsidy)}>
                <View style={styles.leftView}>
                  <XText style={{marginLeft: normalize(10)}} size14>
                    里程补贴
                  </XText>
                </View>
                <View style={styles.rightView}>
                  <XIcon name={'iconleft'} size={16} color={'#C9CDD4'} />
                </View>
              </Pressable>
            }
            <Pressable
              style={styles.xItem}
              onPress={() => _goTo(Routes.AboutVersion)}>
              <View style={styles.leftView}>
                <XText style={{marginLeft: normalize(10)}} size14>
                  关于版本
                </XText>
              </View>
              <View style={styles.rightView}>
                <XIcon name={'iconleft'} size={16} color={'#C9CDD4'} />
              </View>
            </Pressable>
          </View>
          {authModel?.state?.userInfos?.isTemporarily ? (
            <></>
          ) : (
            <>
              <XText
                size12
                textGrey
                style={[commonStyles.mt12, commonStyles.mb6, commonStyles.px1]}>
                通知
              </XText>
              <View style={[styles.xShadowBox]}>
                <Pressable
                  style={[
                    styles.xItem,
                    {
                      borderRadius: 12,
                      borderBottomWidth: Platform.OS === 'android' ? 1 : 0,
                      borderBottomColor: '#F2F2F2',
                    },
                  ]}
                  onPress={() => xlbTipHelpRef?.current?.onClick()}>
                  <View style={styles.leftView}>
                    <XText style={{marginLeft: normalize(10)}} size14>
                      补货单推送状态
                    </XText>
                    <XlbRed
                      redBox={{marginLeft: 2}}
                      redParams={{tabKey: RouteTabs[2].key, key: 'storePush'}}
                      isTab={false}
                    />
                    <XlbTipHelp
                      ref={xlbTipHelpRef}
                      style={{marginLeft: 4}}
                      size={20}
                      color={'#C9CDD4'}
                      showProps={{
                        type: 'tips',
                        desc: '开启后可接收到门店补货的配送通知消息',
                        key: 'storePush',
                      }}
                    />
                  </View>
                  <View style={styles.rightView}>
                    <Switch
                      onValueChange={() => {
                        setPushStates(!pushState);
                      }}
                      value={pushState}
                    />
                  </View>
                </Pressable>
                {Platform.OS === 'android' && system == 'WMS' && (
                  <Pressable
                    style={[styles.xItem, {borderRadius: 12}]}
                    onPress={() => xlbVoiceRef?.current?.onClick()}>
                    <View style={styles.leftView}>
                      <XText style={{marginLeft: normalize(10)}} size14>
                        语音播报
                      </XText>
                      <XlbRed
                        redBox={{marginLeft: 2}}
                        redParams={{tabKey: RouteTabs[2].key, key: 'voiceKey'}}
                        isTab={false}
                      />
                      <XlbTipHelp
                        ref={xlbVoiceRef}
                        style={{marginLeft: 4}}
                        size={20}
                        color={'#C9CDD4'}
                        showProps={{
                          type: 'tips',
                          desc: '开启后拣货任务可接听语音播报',
                          key: 'voiceKey',
                        }}
                      />
                    </View>
                    <View style={styles.rightView}>
                      <Switch
                        onValueChange={async () => {
                          await XLBStorage.setItem(
                            VOICE_STATE_KEY,
                            !voiceState,
                          );
                          authModel.setVoiceState(!voiceState);
                          setVoiceState(!voiceState);
                        }}
                        value={voiceState}
                      />
                    </View>
                  </Pressable>
                )}
                {Platform.OS === 'android' && system == 'WMS' && (
                  <Pressable
                    style={[styles.xItem, {borderRadius: 12}]}
                    onPress={() => xlbFontRef?.current?.onClick()}>
                    <View style={styles.leftView}>
                      <XText style={{marginLeft: normalize(10)}} size14>
                        字体大小跟随系统
                      </XText>
                      <XlbRed
                        redBox={{marginLeft: 2}}
                        redParams={{tabKey: RouteTabs[2].key, key: 'fontKey'}}
                        isTab={false}
                      />
                      <XlbTipHelp
                        ref={xlbFontRef}
                        style={{marginLeft: 4}}
                        size={20}
                        color={'#C9CDD4'}
                        showProps={{
                          type: 'tips',
                          desc: '开启后拣货任务字体大小跟随系统设置',
                          key: 'fontKey',
                        }}
                      />
                    </View>
                    <View style={styles.rightView}>
                      <Switch
                        onValueChange={async () => {
                          await XLBStorage.setItem(FONT_STATE_KEY, !fontState);
                          authModel.setFontState(!fontState);
                          setFontState(!fontState);
                          DeviceEventEmitter.emit('WMSFontRefresh', !fontState);
                        }}
                        value={fontState}
                      />
                    </View>
                  </Pressable>
                )}
              </View>
              <XText
                size12
                textGrey
                style={[commonStyles.mt12, commonStyles.mb6, commonStyles.px1]}>
                硬件设置
              </XText>
              <View style={[styles.xShadowBox]}>
                <Pressable
                  style={[styles.xItem, {borderRadius: 12}]}
                  onPress={() => _goTo('LabelPrintPage')}>
                  <View style={styles.leftView}>
                    <XText style={{marginLeft: normalize(10)}} size14>
                      标签打印机
                    </XText>
                  </View>
                  <View style={styles.rightView}>
                    <XIcon name={'iconleft'} size={16} color={'#C9CDD4'} />
                  </View>
                </Pressable>
                <Pressable
                  style={[styles.xItem, {borderRadius: 12}]}
                  onPress={() => _goTo('ScanPage')}>
                  <View style={styles.leftView}>
                    <XText style={{marginLeft: normalize(10)}} size14>
                      扫描设备
                    </XText>
                  </View>
                  <View style={styles.rightView}>
                    <XIcon name={'iconleft'} size={16} color={'#C9CDD4'} />
                  </View>
                </Pressable>
              </View>
            </>
          )}
          {/* 退出登录 */}
          <View style={{marginTop: 8}}>
            <TouchableOpacity style={styles.button} onPress={_logout}>
              <Text
                style={{
                  color: '#F53F3F',
                }}>
                退出登录
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      <BottomUp
        type="view"
        percent={0.8}
        isVisible={show}
        onBackdropPress={() => setShow(false)}>
        <Space style={{marginLeft: 10}}>
          <TouchableWithoutFeedback
            onPress={() => {
              setShow(false);
            }}>
            <Text style={[cs.second, cs.fz14]}>取消</Text>
          </TouchableWithoutFeedback>

          <Text style={[cs.bold, cs.fz16, cs.black]}>{'选择系统'}</Text>
          <Text style={[cs.bold, cs.first, cs.fz14, {opacity: 0}]}>确定</Text>
        </Space>
        <SelectList
          selected={(item: any) => {
            return item.value === system;
          }}
          noAuthority={(item: any) => {
            if (item.value === 'HRS') return false;
            // ! 目前是拆分kms模块，权限还没配，先用kms权限， 后面再改
            const kmsSplitedValue = ['SDS', 'CRM'];
            const module = kmsSplitedValue.includes(item.value)
              ? 'KMS'
              : item.value;

            return !(authModel?.state?.userInfos?.authorities ?? []).some(
              (v: any) => v.app_type === module,
            );
          }}
          onItemPress={(data: any) => {
            setShow(false);
            setSystem(data.value);
            // 在本地记住
            XLBStorage.setItem('system', data.value);
            // if(data.value === 'KMS') getKmsFollowUserAuth()
          }}
          selectedIdx={-1}
        />
      </BottomUp>

      <BottomUp
        type="view"
        percent={0.6}
        isVisible={accountShow}
        onBackdropPress={() => setAccountShow(false)}>
        <Space style={{marginLeft: 10}}>
          <TouchableWithoutFeedback
            onPress={() => {
              setAccountShow(false);
            }}>
            <Text style={[cs.second, cs.fz14]}>取消</Text>
          </TouchableWithoutFeedback>

          <Text style={[cs.bold, cs.fz16, cs.black]}>{'选择账户'}</Text>
          <Text style={[cs.bold, cs.first, cs.fz14, {opacity: 0}]}>确定</Text>
        </Space>
        <ScrollView style={styles.modalContextBox}>
          <View style={commonStyles.verBox}>
            {accountList.map((v: any) => {
              return (
                <Pressable
                  key={v.company_id + (v?.name ?? '')}
                  onPress={() => accountChange(v.account, v.company_id, v)}
                  style={{paddingHorizontal: 16}}>
                  <View style={{...styles.modalItemBox, height: 60}}>
                    <View style={styles.modalItemBoxLeft}>
                      <XText
                        style={{
                          color:
                            authModel.state.userInfos?.company_id +
                              authModel.state.userInfos?.account ===
                            v.company_id + v.account
                              ? '#1A6AFF'
                              : '#333',
                        }}>
                        {v.company_id}丨{v.company_name}
                      </XText>
                      <XText
                        style={{
                          color:
                            authModel.state.userInfos?.company_id +
                              authModel.state.userInfos?.account ===
                            v.company_id + v.account
                              ? '#1A6AFF'
                              : '#999',
                        }}
                        size12>
                        {v.store_name}-{v.account}
                      </XText>
                    </View>
                    {authModel.state.userInfos?.company_id +
                      authModel.state.userInfos?.account ===
                      v.company_id + v.account && (
                      <XIcon name={'iconPitchOn'} size={20} color={'#1A6AFF'} />
                    )}
                  </View>
                  <Divider />
                </Pressable>
              );
            })}
          </View>
        </ScrollView>
      </BottomUp>
    </>
  );
};

const styles = StyleSheet.create({
  button: {
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    fontSize: 14,
    backgroundColor: '#fff',
    padding: 0,
    marginRight: 5,
    marginBottom: 10,
    height: 44,
  },
  view_content: {
    paddingTop: normalize(38),
    paddingLeft: normalize(10),
    paddingRight: normalize(10),
  },
  edit_content: {
    justifyContent: 'space-between',
  },
  img: {
    position: 'absolute',
    width: '520%',
    height: normalize(200),
    // zIndex: -1,
    borderBottomLeftRadius: 5,
    borderBottomRightRadius: 5,
  },
  avatar: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: normalize(11),
    width: normalize(64),
    height: normalize(64),
    marginRight: normalize(10),
    marginTop: -40,
    borderWidth: 2,
    borderColor: '#fff',
    borderRadius: 64,
    backgroundColor: '#3469f5',
  },
  editBtn: {
    backgroundColor: '#F2F3F5',
    marginRight: 14,
    paddingLeft: 14,
    paddingRight: 14,
    borderRadius: 18,
    height: 28,
  },
  ids: {
    marginRight: 14,
    marginTop: 14,
  },
  barButton: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: 60,
  },
  text_content: {
    marginRight: 8,
    color: '#86909C',
  },
  view: {
    padding: normalize(10),
  },
  vStock: {
    justifyContent: 'center',
  },
  flexDirectionRow: {
    flexDirection: 'row',
  },
  row: {
    marginBottom: 6,
  },
  xShadowBox: {
    // height:182,
    backgroundColor: '#FFF',
    borderRadius: 12,
    paddingLeft: 5,
    paddingRight: 5,
  },
  xItem: {
    paddingLeft: 5,
    alignItems: 'center',
    fontSize: normalize(14),
    flexDirection: 'row',
    justifyContent: 'space-around',
    height: 44,
  },
  rightView: {
    flexDirection: 'row',
    width: '50%',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingRight: normalize(3),
  },
  leftView: {
    flexDirection: 'row',
    width: '50%',
  },
  modalContextBox: {
    backgroundColor: colors.white,
    flex: 1,
  },
  jac: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalItemBox: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    paddingLeft: 5,
    // borderBottomWidth: 0.5,
    // borderBottomColor: '#b7babf',
  },
  modalItemBoxLeft: {
    flex: 1,
    alignItems: 'flex-start',
  },
});

const LazyMine = () => {
  return (
    <LazyLoad>
      <Mine />
    </LazyLoad>
  );
};

export default LazyMine;
