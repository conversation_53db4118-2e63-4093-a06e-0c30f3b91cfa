<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/parent_rl"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="14dp"
    android:layout_marginBottom="6dp"
    android:background="@drawable/map_point_detail_bg"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/img_ll"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:orientation="vertical">

        <com.hxl.xlb.widget.BannerView
            android:id="@+id/banner_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.hxl.xlb.widget.RadiusImageView
            android:id="@+id/erp_store_default_iv"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:scaleType="centerCrop"
            android:src="@drawable/map_marker_detail_erp_store_img"
            android:visibility="gone" />
    </LinearLayout>

    <ImageView
        android:id="@+id/close_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:src="@drawable/map_dialog_close" />

    <TextView
        android:id="@+id/name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/img_ll"
        android:layout_marginStart="12dp"
        android:layout_marginTop="10dp"
        android:singleLine="true"
        android:text="点位大哥1"
        android:textColor="@color/color_map_manage"
        android:textSize="16sp"
        android:textStyle="bold" />

    <LinearLayout
        android:id="@+id/state_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/name_tv"
        android:layout_marginStart="12dp"
        android:layout_marginTop="5dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/state_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/map_detail_dialog_text_blue_bg"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1.5dp"
            android:singleLine="true"
            android:text="制单"
            android:textColor="@color/white"
            android:textSize="11sp" />

        <TextView
            android:id="@+id/grade_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="7dp"
            android:background="@drawable/map_detail_dialog_text_red_bg"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1.5dp"
            android:singleLine="true"
            android:text="A+"
            android:textColor="@color/white"
            android:textSize="11sp" />

        <TextView
            android:id="@+id/look_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="7dp"
            android:background="@drawable/map_detail_dialog_label_grey_bg"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1.5dp"
            android:singleLine="true"
            android:text="允许带看"
            android:textColor="@color/color_map_template_default"
            android:textSize="11sp" />

        <TextView
            android:id="@+id/develop_status_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="7dp"
            android:background="@drawable/map_detail_dialog_text_grey_bg"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1.5dp"
            android:singleLine="true"
            android:text="开发中"
            android:textColor="@color/white"
            android:textSize="11sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/main_label_name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="7dp"
            android:background="@drawable/map_detail_dialog_label_grey_bg"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1.5dp"
            android:singleLine="true"
            android:text="主标签名称"
            android:textColor="@color/color_map_template_default"
            android:textSize="11sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/sub_label_name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="7dp"
            android:background="@drawable/map_detail_dialog_label_grey_bg"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1.5dp"
            android:singleLine="true"
            android:text="主副标签名称"
            android:textColor="@color/color_map_template_default"
            android:textSize="11sp"
            android:visibility="gone" />


        <HorizontalScrollView
            android:id="@+id/label_horizontal_sv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="7dp"
            android:fillViewport="false"
            android:scrollbars="none"
            android:visibility="gone">

            <LinearLayout
                android:id="@+id/label_ll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- 动态添加的布局会在这里 -->

            </LinearLayout>
        </HorizontalScrollView>

        <ImageView
            android:id="@+id/distance_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/map_detail_dialog_distance_img"
            android:visibility="gone" />

        <TextView
            android:id="@+id/distance_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="5dp"
            android:text="0km"
            android:textColor="@color/color_map_template_default"
            android:textSize="13sp"
            android:visibility="gone" />

    </LinearLayout>

    <TextView
        android:id="@+id/business_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/state_ll"
        android:layout_marginStart="12dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="12dp"
        android:singleLine="true"
        android:text="测试商圈1"
        android:textColor="@color/color_map_template_default"
        android:textSize="13sp" />
</RelativeLayout>