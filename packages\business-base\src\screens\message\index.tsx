import React, {useEffect, useRef, useState} from 'react';
import useEntranceStore from '../entrance/useEntranceStore';
import {
  DeviceEventEmitter,
  ImageBackground,
  NativeModules,
  Platform,
  StatusBar,
  Text,
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {authModel, roleAuth} from '@xlb/business-base/src/models/auth';
import {
  commonStyles,
  Header,
  Item,
  XIcon,
  XLoading,
  XText,
} from '@xlb/common/src/components';
import {CommonActions, useNavigation} from '@react-navigation/native';
import {colors, normalize} from '@xlb/common/src/config/theme';
import {XlbIconfont, XlbCard, XlbSearch} from '@xlb/components-rn';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttp';
import {useFocusEffect} from '@react-navigation/native';
import useAliCloudPush from '@xlb/common/src/hooks/useAliCloudPush';
import {
  getErpWaitHandle,
  getScmWaitHandle,
  getWMSWaitHandle,
  getKMSWaitHandle,
  updateEmsWaitHandle,
} from '../staging/server';
import useMessage from '@xlb/business-base/src/hook/useMessage';

// import { SampleDeliveryRoutes } from '@xlb/business-scm/src/screens/sampleDelivery/routes'
// import { NewProductApplicationRoutes } from '@xlb/business-scm/src/screens/newProductApplication/routes'
// import { InvoiceRoutes } from '@xlb/business-scm/src/screens/invoice'
// import { SupplyPrepareRoutes } from '@xlb/business-scm/src/screens/supplyPrepare/routes'
import Toast from 'react-native-root-toast';
import {applicationFields} from '@xlb/common/src/config/fields';
// import DepartDocumentApi from '@xlb/business-non-wms/src/screens/departDocument/api'
// import { DepartDocumentRoutes } from '@xlb/business-non-wms/src/screens/departDocument/routes'
// import { FsmsRoute } from '../../../../business-fsms/src/router'
// import { QualityReportRoutes } from '../../../../bussiness-erp/src/screens/qualityReport/routes'
// import { AttendanceRoute } from '@xlb/business-hrs/src/screens/attendance/route'
import {kmsWebRoutes} from '@xlb/common/src/config/routeWeb';
// import { HrsWebPageParams } from '@xlb/business-hrs/src/components/hrsH5page'
// import { CommonApi } from '@xlb/business-non-wms/src/api/common/server'
/**
 * 首页
 * @constructor`
 */
const Index: React.FC = () => {
  const navigation: any = useNavigation();
  const [searchText, setSearchText] = useState<string>('');
  const {todoLists, getTodoList, loading} = useMessage();
  const [departList, setDepartList] = useState<any>([]); //部门信息
  const userInfos = authModel?.state?.userInfos;
  const authorities = authModel?.state?.userInfos?.authorities ?? [];
  const isERP = authorities.some(v => v.app_type === 'ERP');
  const isEMS = authorities.some(v => v.app_type === 'EMS');
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false); // 避免重复加载
  const WaitClick = (item: any) => {
    switch (item.name) {
      // SMS店内任务
      case '店内任务':
        navigation.navigate('RemoteAppSms.ShopPatrolTask', {
          ...item.condition,
          isRoute: true,
        });
        break;
      case '样品寄送':
        navigation.navigate('RemoteAppScm.SampleDelivery', {
          ...item.condition,
          isRoute: true,
        });
        break;
      case '商品检测报告':
        navigation.navigate('RemoteAppScm.GoodsCheckReport', {
          ...item.condition,
          isRoute: true,
        });
        break;
      case '新品申请':
        navigation.navigate('RemoteAppScm.NewProductApplication', {
          ...item.condition,
          isRoute: true,
        });
        break;
      case '发货单':
        navigation.navigate('RemoteAppScm.InvoiceOrder', {
          ...item.condition,
          isRoute: true,
        });
        break;
      case '备货单':
        navigation.navigate('RemoteAppScm.SupplyPrepare', {
          ...item.condition,
          isRoute: true,
        });
        break;
      // case '入库质检':
      //   navigation.navigate('QualityCheck', {
      //     ...item.condition,
      //     isRoute: true,
      //     storehouse_id: null,
      //   });
      //   break;
      // case '出库质检':
      //   navigation.navigate('OutQualityCheck', {
      //     ...item.condition,
      //     isRoute: true,
      //   });
      //   break;
      // case '上架任务':
      //   navigation.navigate('UpTaskPage', {...item.condition, isRoute: true});
      //   break;
      // case '移库任务':
      //   navigation.navigate('MoveTaskPage', {...item.condition, isRoute: true});
      //   break;
      // case '拣货任务':
      //   navigation.navigate('pickingOperation', {
      //     ...item.condition,
      //     isRoute: true,
      //   });
      //   break;
      case '门店营业执照':
      case '门店食品经营许可证':
        navigation.navigate('StoreFile', {
          type: item.name,
          store_id: authModel?.state?.userInfos?.store?.id,
        });
        break;
      case '商户进件':
        navigation.navigate('MerchantRegister', {});
        break;
      case '门店经营环境':
        navigation.navigate('StoreEnvironment', {
          type: item.name,
          store_id: authModel?.state?.userInfos?.store?.id,
        });
        break;

      case '商圈规划':
      case '点位规划':
      case '建店申请':
        navigation.navigate(
          applicationFields.SDS.find(v => v.name === item.name)?.route,
          {state: item.state, isHandle: true},
        );
        break;
      case '证件管理':
        navigation.navigate('RemoteAppErp.StoreInformation', {});
        break;
      case '质量提报':
        navigation.navigate('RemoteAppFsms.QualityReporting', {});
        break;
      case '新施工项目待分派':
      case '施工项目分派通知':
      case '空间设计师待分派':
      case '平面设计师待分派':
      case '设计师分派通知':
      case '项目工期待制定':
      case '设备材料下单提醒':
      case '施工项目待验收':
      case '发起现场结算单':
        navigation.dispatch(
          CommonActions.navigate('RemoteAppHrs.HRS_H5_PAGE', {
            emsUrl: kmsWebRoutes.constructionManageDetail,
            params: {
              id: item?.business_id,
            },
          } as HrsWebPageParams),
        );
        break;

      case '新工单待分派':
      case '新工单待分派工程师':
      case '您有新工单待处理':

      case '费用确认完成':

      case '完修打卡确认':
        navigation.dispatch(
          CommonActions.navigate('RemoteAppHrs.HRS_H5_PAGE', {
            emsUrl: kmsWebRoutes.workOrderServerDetail,
            params: {
              id: item?.business_id,
            },
          } as HrsWebPageParams),
        );
        break;
      case '工单已被工程师接单':
      case '工单上门时间已确认':

      case '工程师已到店服务':

      case '工单费用待确认':

      case '完修打卡确认':

      case '工单被取消':
        navigation.dispatch(
          CommonActions.navigate('RemoteAppHrs.HRS_H5_PAGE', {
            emsUrl: kmsWebRoutes.workOrderClientDetail,
            params: {
              id: item?.business_id,
            },
          } as HrsWebPageParams),
        );
        break;
      case '日管控待处理':
        navigation.navigate('RemoteAppFsms.DayCheckDetails', {});
        break;
      case '仓库巡检待审核':
        navigation.navigate('RemoteAppFsms.WarehouseInspection', {
          state: item?.condition,
        });
        break;
      case '仓库巡检待发起':
        navigation.navigate('RemoteAppFsms.WarehouseInspection', {});
        break;
      default:
        Toast.show('暂未开发');
    }
  };

  const getDepart = async () => {
    if (!authModel?.state?.userInfos.store_id) return;
    const departRes = await DepartDocumentApi('departurecenter.store.find', {
      store_id: authModel?.state?.userInfos?.store?.id,
    });
    if (departRes?.code == 0) {
      console.log('departRes', departRes.data.length);
      setDepartList(departRes?.data);
    }
  };

  const getHouseList = async () => {
    const res = await CommonApi.getWmsHouseAll({
      status: true,
      store_id: authModel?.state?.userInfos?.store.id,
    });
    const defaultItem = res.data.find(v => v.default_flag && v.status);
    const arr = res.data.filter((v: any) => v.status);
    const houseList = defaultItem ? [defaultItem] : [arr[0]];
    houseList.map((v: any) => {
      v.active = true;
      v.checked = true;
      v.label = v.name;
      v.value = v.id;
    });
    authModel?.setStorehouseList(arr);
    authModel?.setDefaulStorehousId(houseList);
  };

  // 当屏幕获得焦点时刷新数据，但仅限于首次加载之后
  useFocusEffect(
    React.useCallback(() => {
      // 执行刷新数据的逻辑
      if (hasLoadedOnce) {
        getTodoList('init');
      }
    }, [hasLoadedOnce]),
  );

  useEffect(() => {
    setHasLoadedOnce(true); // 标记为已加载
    if (isERP) {
      getDepart();
    }
    getHouseList();
  }, []);

  return (
    <View style={{flex: 1}}>
      <XLoading loading={loading} />
      {/* <StatusBar  hidden={false} translucent={false} backgroundColor={'#fff'} barStyle={'dark-content'} /> */}
      <Header
        headerBgColor="#fff"
        centerComponent={
          <XText size16 semiBold>
            待办
          </XText>
        }
        leftComponent={
          <XIcon
            name={'back'}
            color="#000"
            onPress={() => {
              navigation.goBack();
            }}
          />
        }></Header>
      <XlbSearch
        placeholder="搜索标题/内容"
        onChange={e => {
          setSearchText(e);
        }}></XlbSearch>
      <ScrollView style={{flex: 1}}>
        {!!departList?.length && (
          <XlbCard
            title={'配送中'}
            onPress={() => {
              navigation.navigate(
                DepartDocumentRoutes.departDocument,
                departList,
              );
            }}>
            <View
              style={{
                marginTop: normalize(8),
              }}>
              <Text style={styles['item-text']}>
                您有{departList?.length || 0}车货物正在配送中
              </Text>
            </View>
          </XlbCard>
        )}

        {todoLists
          .filter(e => {
            return (
              e?.name?.includes(searchText) || e?.memo?.includes(searchText)
            );
          })
          ?.map((item: any, index: number) => {
            return (
              <XlbCard
                key={index}
                title={item.name}
                onPress={async () => {
                  if (item?.__type === 'EMS') {
                    const {code, data: hasAuth} = await updateEmsWaitHandle({
                      erp_user_id: userInfos?.id,
                      id: item.id,
                    });
                    if (code == 0 && hasAuth == false) {
                      Toast.show('暂无权限，请联系信息部');
                      return void 0;
                    }
                  }

                  WaitClick(item);
                }}
                // footer={
                //   <View style={styles['item-footer']}>
                //     <Text style={styles['item-footer__text1']}></Text>
                //     <Text
                //       style={styles['item-footer__text2']}
                //       onPress={() => {
                //         WaitClick(item)
                //       }}
                //     >
                //       查看详情
                //     </Text>
                //   </View>
                // }
              >
                <View
                  style={{
                    marginTop: normalize(8),
                  }}>
                  <Text style={styles['item-text']}>
                    {item.wait_handle_desc || item.memo}
                    {!!item?.count && `${item.count}个`}
                  </Text>
                </View>
              </XlbCard>
            );
          })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  'select-icon': {
    position: 'absolute',
    right: normalize(12),
    top: '50%',
    marginTop: normalize(4),
  },
  'item-footer': {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  'item-footer__text1': {
    fontSize: normalize(14),
    color: 'rgba(30,33,38,0.45)',
  },
  'item-footer__text2': {
    fontSize: normalize(14),
    color: 'rgba(26, 106, 255, 1)',
  },
  'item-text': {
    color: 'rgba(30, 33, 38, 0.70)',
    fontSize: normalize(13),
  },
});

export default Index;
