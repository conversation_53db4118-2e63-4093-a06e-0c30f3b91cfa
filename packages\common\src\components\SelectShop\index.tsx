import React, { PropsWithChildren, useCallback, useEffect, useRef, useState } from 'react'
import { TouchableOpacity } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import XlbIcon from '@xlb/common/src/assets/iconfont'
import Icon from '@xlb/common/src/assets/xlbIconFont'
import { XText } from '@xlb/common/src/components'
import { normalize } from '@xlb/common/src/config/theme'
interface SelectShopProps {
  storeChange: (item: any) => void
  storeInfo: any
  disabled?: boolean
  style?: Object
}
const SelectShop: React.FC<PropsWithChildren<SelectShopProps>> = ({ storeChange, storeInfo, disabled = false, style = {} }) => {
  const navigation = useNavigation<any>()

  const handleStore = async (data: any) => {
    storeChange({ storeIds: data['list'][0].id, storeName: data['list'][0].store_name })
  }
  const addRoute = () => {
    if (disabled) return
    navigation.navigate('selectStoreH5', {
      isMultiple: false,
      postUrl: '/erp/hxl.erp.user.switchstore.page',
      storeIds: storeInfo?.storeIds,
      onSelect: handleStore,
    })
  }
  return (
    <TouchableOpacity
      activeOpacity={0.6}
      style={{ alignItems: 'center', flexDirection: 'row' }}
      onPress={() => {
        addRoute()
      }}
    >
      <XlbIcon name="dianpu" size={normalize(16)} style={{ marginRight: normalize(4) }} />
      <XText style={style} numberOfLines={1} semiBold>
        {storeInfo?.storeName || '请选择门店'}
      </XText>
      {disabled ? null : <Icon name="iconDropDown" size={14} style={{ marginLeft: 4 }} />}
    </TouchableOpacity>
  )
}
export default SelectShop
