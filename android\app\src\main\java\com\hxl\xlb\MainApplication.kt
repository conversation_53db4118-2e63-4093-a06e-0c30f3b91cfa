package com.hxl.xlb

import android.util.Log
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactHost
import com.facebook.react.ReactNativeApplicationEntryPoint.loadReactNative
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.defaults.DefaultReactHost.getDefaultReactHost
import com.facebook.react.defaults.DefaultReactNativeHost
import com.hxl.xlb.map.PushNativeMapPackage
import com.hxl.xlb.nativehotupdate.NativeHotUpdatePackage
import com.hxl.xlb.network.NetworkRequiredInfo
import com.hxl.xlb.openwechat.OpenWeChatPackage
import com.hxl.xlb.syncscrollview.SyncScrollViewPackage
import com.hxl.xlb.reactscan.ScanpdaPackage
import com.hxl.xlb.umeng.DplusReactPackage
import com.xlb.mvplibrary.BaseApplication
import com.xlb.mvplibrary.network.NetworkApi
import java.io.File
import android.database.CursorWindow;
import com.hxl.xlb.NativeModuleManager.NativeKeyboardAdjustPackage
import com.hxl.xlb.keyeventlister.KeyEventListerPackage

class MainApplication : BaseApplication(), ReactApplication {


    override val reactNativeHost: ReactNativeHost =
        object : DefaultReactNativeHost(this) {

            override fun getPackages(): List<ReactPackage> =
                PackageList(this).packages.apply {
                    // Packages that cannot be autolinked yet can be added manually here, for example:
                    add(NativeHotUpdatePackage())
                    add(PushNativeMapPackage())
                    add(SyncScrollViewPackage())
                    add(DplusReactPackage())
                    add(OpenWeChatPackage())
                    add(ScanpdaPackage())
                    add(KeyEventListerPackage())
                    add(NativeKeyboardAdjustPackage())
                }

            override fun getJSMainModuleName(): String = "index"

            override fun getUseDeveloperSupport(): Boolean = BuildConfig.DEBUG

            override val isNewArchEnabled: Boolean = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED
            override val isHermesEnabled: Boolean = BuildConfig.IS_HERMES_ENABLED

            override fun getJSBundleFile(): String? {
                // 读取当前缓存的版本号, getUseDeveloperSupport为true这里默认永远是8081的本地bundle, 本地测试可设置为false
                val sharedPref = applicationContext.getSharedPreferences("hot_update_version", MODE_PRIVATE)
                val version = sharedPref.getString("current_version", null)
                val bundleFile = File(applicationContext.cacheDir, "RNHotUpdateBundle/index.bundle")
                if (version != null && bundleFile.exists()) {
                    return bundleFile.absolutePath
                }
                // 没有缓存则返回 null，RN 会自动加载 assets 里的 bundle
                return super.getJSBundleFile()
            }
        }

    override val reactHost: ReactHost
        get() = getDefaultReactHost(applicationContext, reactNativeHost)

    override fun onCreate() {
        super.onCreate()
        loadReactNative(this)

        NetworkApi.init(NetworkRequiredInfo(this))

        // 扩大AsyncStorage存储大小
        try {
            val field = CursorWindow::class.java.getDeclaredField("sCursorWindowSize")
            field.isAccessible = true
            field.set(null, 100 * 1024 * 1024) //100MB
        } catch (e: Exception) {
            if (BuildConfig.DEBUG) {
                e.printStackTrace()
            }
        }

        if (BuildConfig.DEBUG) {
            Thread.setDefaultUncaughtExceptionHandler { thread: Thread?, throwable: Throwable? ->
                Log.e("CrashHandler", "Uncaught exception", throwable)
            }
        }
    }

}
