import React, { useEffect, useRef, FC, useState, useMemo } from 'react'
import { View, Text, StyleSheet, TouchableOpacity, LayoutChangeEvent, Animated, ScrollView, LayoutRectangle } from 'react-native'

import cloneDeep from 'lodash/cloneDeep'

const styles = StyleSheet.create({
  'xlb-tab': {
    width: '100%',
    position: 'relative',
    height: 45,
  },
  'xlb-tab_scroll': {
    paddingHorizontal: 2,
  },

  'xlb-tab_default': {
    backgroundColor: '#fff',
  },
  'xlb-tab_blue': {
    backgroundColor: '#1C6AFF',
  },

  'xlb-tab_Tabs': {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    
  },
  'xlb-tab_Tabs__text': {
    flex: 1,
    paddingTop: 10,
    paddingBottom: 15,
  },

  'Tabs-text_style': {
    textAlign: 'center',
    fontSize: 14,
  },
  'Tabs-text_style__default': {
    color: '#86909C',
  },
  'Tabs-text_style__blue': {
    color: '#96C5FF',
  },
  'Tabs-text_style__default___active': {
    color: '#1C6AFF',
    fontSize: 15,
    fontWeight: '500',
  },
  'Tabs-text_style__blue___active': {
    color: '#fff',
    fontSize: 15,
    fontWeight: '500',
  },

  'Tabs-text_scroll': {
    flex: undefined,
    paddingHorizontal: 12,
  },

  'xlb-tab_line': {
    position: 'absolute',
    bottom: 6,
    height: 2,
    left: 0,
    display: 'flex',
    alignItems: 'center',
    opacity: 0,
   
  },

  'xlb-tab_line__mark': {
    width: 20,
    height: '100%',
  },
  'xlb-tab_line__mark___default': {
    backgroundColor: '#1C6AFF',
  },
  'xlb-tab_line__mark___blue': {
    backgroundColor: '#fff',
  },
})

interface TabProps {
  data: { label: string; value: string | number }[]
  onChange: (val: string | number) => void
  value: string | number
  theme?: 'default' | 'blue'
  loading?: boolean
  callback: (layout: LayoutRectangle) => void
}

const Index: FC<TabProps> = (props) => {
  const { data, onChange, value, theme, loading, callback } = props
  const canScroll = useMemo(() => data.length > 2, [data]) // 滚动逻辑，tab大于3开启滚动

  // const [layout, setLayout] = useState<Record<string, { width: number; left: number }>>({})
  const [refesh,setRefesh] = useState<number>(0)

  const layout = useRef<Record<string, { width: number; left: number }>>({}).current // tab布局属性
  const scrollViewRef = useRef<ScrollView>(null)

  const transform = useRef(new Animated.Value(0)).current // 动画实例
  const opcity = useRef(new Animated.Value(0)).current

  const containerLayout = useRef<LayoutRectangle | null>(null) // 外层元素layout

  useEffect(() => {
    if (Object.keys(layout).length && value) {
      Animated.timing(transform, {
        toValue: layout[value].left,
        duration: 300,
        useNativeDriver: true,
      }).start()
      clickCenter()
    }
  }, [value])

  useEffect(() => {
    if (!loading)
      Animated.timing(opcity, {
        toValue: 1,
        duration: 300,
        delay: 300,
        useNativeDriver: true,
      }).start()
  }, [loading])

  /** 点击判断当前tab是否居中，不是则移动tab */
  const clickCenter = () => {
    if (!containerLayout.current) return

    const center = containerLayout.current.width / 2 // tab中心点

    // 找出当前tab中心点
    const curTab = layout[value]
    const curTabCenter = curTab.left + curTab.width / 2

    if (curTabCenter > center) {
      scrollViewRef.current?.scrollTo({ x: curTabCenter - center, y: 0, animated: true })
    } else {
      scrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true })
    }
  }

  const getPosition = (dom, callback) => {
    new Promise((resolve, reject) => {
      dom?.measure(
        (x, y, width, height, pageX, pageY) => {
          resolve({ x, y, width, height, pageX, pageY })
        },
        () => reject
      )
    }).then((res: any) => {
      callback(res)
    })
  }

  const tabRef = useRef(null)

  useEffect(() => {
    if (tabRef.current) {
      getPosition(tabRef.current, (res) => {
        callback(res)
      })
    }
  })

  return (
    <View
      ref={tabRef}
      style={StyleSheet.flatten([styles['xlb-tab'], styles[`xlb-tab_${theme!}`], canScroll ? styles['xlb-tab_scroll'] : {}])}
      onLayout={(e: LayoutChangeEvent) => {
        containerLayout.current = e.nativeEvent.layout
        callback(e.nativeEvent.layout)
      }}
    >
      <ScrollView horizontal showsHorizontalScrollIndicator={false} ref={scrollViewRef} contentContainerStyle={canScroll ? {} : { width: '100%' }}>
        <View style={styles['xlb-tab_Tabs']}>
          {data.map((item) => (
            <TouchableOpacity
              activeOpacity={1}
              style={StyleSheet.flatten([styles['xlb-tab_Tabs__text'], canScroll ? styles['Tabs-text_scroll'] : {}])}
              key={item.value}
              onPress={() => {
                onChange(item.value)
              }}
              onLayout={(e: LayoutChangeEvent) => {
                layout[item.value] = { width: e.nativeEvent.layout.width, left: e.nativeEvent.layout.x }
                setRefesh(refesh+1)
              }}
            >
              <Text
                style={StyleSheet.flatten([
                  styles['Tabs-text_style'],
                  styles[`Tabs-text_style__${theme!}`],
                  value === item.value ? styles[`Tabs-text_style__${theme!}___active`] : {},
                ])}
              >
                {item.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        {Object.keys(layout).length ? (
          <Animated.View
            style={StyleSheet.flatten([
              styles['xlb-tab_line'],
              { width: layout?.[value]?.width || 0, transform: [{ translateX: transform }], opacity: opcity },
            ])}
          >
            <View style={StyleSheet.flatten([styles['xlb-tab_line__mark'], styles[`xlb-tab_line__mark___${theme!}`]])}></View>
          </Animated.View>
        ) : null}
      </ScrollView>
    </View>
  )
}

Index.defaultProps = {
  theme: 'default',
}

export default Index
