// bundleLoader.ts
import {<PERSON>ript<PERSON>anager, Script} from '@callstack/repack/client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {remoteAppsConfig} from '@/config/remoteApps.ts';
import {Platform} from 'react-native';
import Config from 'react-native-config';
import './packages/common/src/models/system';
import {bundlePrefix} from '@/components/xlbUpdate/constant.ts';

const oss = bundlePrefix;

let host = 'localhost';

// 缓存版本信息，避免频繁请求
let versionCache: Record<string, string> = {};
let versionCacheTime: number = 0;
const VERSION_CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

console.log('Config __DEV__', Config, __DEV__);
ScriptManager.shared.setStorage(AsyncStorage);
// 获取远程应用版本信息
async function getRemoteAppVersion(remoteAppName: string) {
  const now = Date.now();

  // 检查缓存
  if (
    versionCache[remoteAppName] &&
    now - versionCacheTime < VERSION_CACHE_DURATION
  ) {
    return versionCache[remoteAppName];
  }

  try {
    const res = await fetch(`${oss}/version.json`);
    const version = await res.json();
    const remoteAppVersion = version[remoteAppName]?.[Config.BRANCH_NAME!];

    // 更新缓存
    versionCache[remoteAppName] = remoteAppVersion;
    versionCacheTime = now;

    return remoteAppVersion;
  } catch (error) {
    console.error('获取版本信息失败:', error);
    return null;
  }
}

// 清除版本缓存（用于强制更新）
export function clearVersionCache() {
  versionCache = {};
  versionCacheTime = 0;
}

for (let key in remoteAppsConfig) {
  ScriptManager.shared.addResolver(
    async (scriptId, caller, referenceUrl) => {
      const appName = caller || scriptId;
      let port = remoteAppsConfig[appName]?.port || 8081;

      if (referenceUrl === `prefetch:///${appName}.container.js.bundle`) {
        if (appName === 'RemoteAppBase') {
          debugger;
        }
        return {
          url: `http://localhost:${remoteAppsConfig[appName].port}/index.bundle?platform=ios`,
          method: 'GET',
          cache: false,
        };
      }

      let url: string | undefined;

      if (__DEV__) {
        try {
          let ping = await fetch(`http://${host}:${port}`);
          let text = await ping.text();
          if (port == 8888) {
            console.log(port, text, ping.status);
          }

          url =
            scriptId.startsWith('RemoteApp') || scriptId.startsWith('HostApp')
              ? `http://${host}:${port}/${Platform.OS}/${appName}.container.js.bundle`
              : `http://${host}:${port}/${Platform.OS}/${scriptId}.chunk.bundle`;
        } catch (error) {
          console.warn(
            `Failed to fetch from http://${host}:${port}, using CDN instead.`,
          );
        }
      }

      if (url === undefined) {
        let remote_app_name = appName.replace(
          /[A-Z]/g,
          (A, i) => (i ? '-' : '') + A.toLowerCase(),
        );

        // 获取远程应用版本
        const remote_app_version = await getRemoteAppVersion(remote_app_name);

        if (remote_app_version) {
          url =
            scriptId.startsWith('RemoteApp') || scriptId.startsWith('HostApp')
              ? `${oss}/${remote_app_name}/${remote_app_version}/generated/${Platform.OS}/${appName}.container.js.bundle`
              : `${oss}/${remote_app_name}/${remote_app_version}/generated/${Platform.OS}/${scriptId}.chunk.bundle`;
        } else {
          console.error(`无法获取 ${remote_app_name} 的版本信息`);
        }
      }

      if (!url) {
        throw new Error(`无法解析 ${scriptId} 的 URL`);
      }

      console.log('remote url', url);

      return {
        url: Script.getRemoteURL(url, {excludeExtension: true}),
        method: 'GET',
        cache: !__DEV__,
      };
    },
    {
      key,
    },
  );
}
