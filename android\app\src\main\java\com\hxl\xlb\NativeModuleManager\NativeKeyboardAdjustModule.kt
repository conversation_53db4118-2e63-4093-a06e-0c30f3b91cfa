package com.hxl.xlb.NativeModuleManager

import android.content.Intent
import android.view.WindowManager
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.module.annotations.ReactModule
import com.hxl.xlb.nativehotupdate.NativeHotUpdateModule


class NativeKeyboardAdjustModule(reactContext: ReactApplicationContext): ReactContextBaseJavaModule(reactContext) {
    override fun getName(): String {
        return "NativeKeyboardAdjustManager"
    }

    @ReactMethod
    fun adjustKeyboardToResize(){
        getCurrentActivity()?.runOnUiThread {
            val intent = Intent().apply {
                putExtra("isAdjust", true) // 添加广播传送的参数
                action = "change_keyboard_events" // 需跟注册广播时填写的一致
            }
            getCurrentActivity()?.sendBroadcast(intent)
        }
    }

    @ReactMethod
    fun resetKeyboardAdjust(){
        getCurrentActivity()?.runOnUiThread {
            val intent = Intent().apply {
                putExtra("isAdjust", false) // 添加广播传送的参数
                action = "change_keyboard_events" // 需跟注册广播时填写的一致
            }
            getCurrentActivity()?.sendBroadcast(intent)
        }
    }

}