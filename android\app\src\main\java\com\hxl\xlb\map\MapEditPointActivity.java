package com.hxl.xlb.map;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.amap.api.maps.model.LatLng;
import com.amap.api.services.core.PoiItemV2;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.ToNumberPolicy;
import com.google.gson.reflect.TypeToken;
import com.hxl.xlb.R;
import com.hxl.xlb.adapter.CustomComponentAdapter;
import com.hxl.xlb.bean.AddressBean;
import com.hxl.xlb.bean.ContentBean;
import com.hxl.xlb.bean.DraftBean;
import com.hxl.xlb.bean.FileBean;
import com.hxl.xlb.bean.Info;
import com.hxl.xlb.bean.ItemChangeBean;
import com.hxl.xlb.bean.ItemChangeContentDetailBean;
import com.hxl.xlb.bean.LandlordBean;
import com.hxl.xlb.bean.MapFromBuniessReponseBean;
import com.hxl.xlb.bean.MapStorePlanDetailReponseBean;
import com.hxl.xlb.bean.eventbus.AddLandlordEvent;
import com.hxl.xlb.bean.eventbus.MessageEvent;
import com.hxl.xlb.bean.eventbus.PoiItemChangeEvent;
import com.hxl.xlb.contract.MapEditPointContract;
import com.hxl.xlb.contract.PickUtilResultContract;
import com.hxl.xlb.requestbody.BusinessAreaLatlngRequest;
import com.hxl.xlb.requestbody.MapStoreEditRequest;
import com.hxl.xlb.requestbody.MapStorePlanDetailRequest;
import com.hxl.xlb.responsebody.ItemChangeReponse;
import com.hxl.xlb.responsebody.MapStorePlanDetailReponse;
import com.hxl.xlb.utils.CustomUploadCollection;
import com.hxl.xlb.utils.DraftUtils;
import com.hxl.xlb.utils.MySPTool;
import com.hxl.xlb.utils.PickerUtil;
import com.hxl.xlb.utils.ViewUtils;
import com.hxl.xlb.widget.BottomSheetDialogHelper;
import com.hxl.xlb.widget.CustomUpload;
import com.hxl.xlb.widget.DialogTemplateView;
import com.xlb.mvplibrary.mvp.MvpActivity;
import com.xlb.mvplibrary.network.BaseResponse;
import com.xlb.mvplibrary.network.errorhandler.ExceptionHandle;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.io.File;
import java.lang.reflect.Type;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

public class MapEditPointActivity extends MvpActivity<MapEditPointContract.MapEditPointPresenter> implements MapEditPointContract.IEditPointView, View.OnClickListener {

    private Context mContext;
    private NestedScrollView contentSv;
    private TextView selectedTextView; // 用来保存当前选中的tab上的TextView
    private View indicatorView;       // 蓝色指示条
    private HorizontalScrollView horizontalScrollView;  // 水平scrollview

    private ActivityResultLauncher<Intent> filePickerLauncher;

    // 基本信息必填
    private Map<String, Boolean> baseInfoRequireMap = new HashMap<>();
    // 基本信息展示
    private Map<String, Boolean> baseInfoShowMap = new HashMap<>();


    private List<ItemChangeBean> changeBeanList = new ArrayList<ItemChangeBean>();


    private PickerUtil pickerUtil;

    private MapStorePlanDetailReponseBean storePlanData = new MapStorePlanDetailReponseBean();

    private Map<String, Object> dataMap = new HashMap<>();

    private int companyId;//公司id
    private long orgId;
    private boolean isFirstFindBusinessFlag = true;

    private boolean isFirstLoad = true;//是否首次进来

    private int storePlanId;


    final int POI_DIALOG_REQUEST_CODE = 2001;

    private HashMap receivedMap;//传入的参数

    private ArrayList<String> tabsList;//顶部tab数据

    private HashMap<String, Object> basicCheckDic = new HashMap<>();//基本信息校验

    private CustomComponentAdapter customComponentAdapter;

    private FileBean storeHeadFile;

    private List<ItemChangeBean> changeableList = new ArrayList<>();

    private RecyclerView changeableRV;


    RelativeLayout headRl, nameRL, orgNameRL, followByRL, storeLevelRL, addressRL, fullAddressRL, businessPlanNameRL, storeAttributesRL, storeTypeRL, subStoreTypeRL, allowLookRL, flippedStoreRL, pointStoreHeadRL, storeBrandFrontRL, storeBrandRightRL, storeBrandLeftRL, inRoomRL, businessDistircitEnviromentRL, upload_camera_rl, store_area_type_rl, draftContainerRl;
    TextView followByTV, storeLevelTV, addressTV, businessPlanNameTV, storeAttributesTV, storeTypeTV, subStoreTypeTV, orgNameTv;
    LinearLayout basicInfoLl, landlordAllLl, pointStoreHeadLL, storeBrandFrontLL, storeBrandRightLL, storeBrandLeftLL, inRoomLL, businessDistircitEnviromentLL, nameLL, followByLL, storeLevelLL, addressLL, fullAddressLL, businessPlanNameLL, storeAttributesLL, storeTypeLL, subStoreTypeLL, allowLookLL, flippedStoreLL, storeAreaTypeLL, pointStoreContentLL, storeBrandFrontContentLL, storeBrandRightContentLL, storeBrandLeftContentLL, inRoomContentLL, businessDistircitEnviromentContentLL, store_area_type_ll;
    ImageView pointStoreHeadIV;
    EditText nameET, fullAddressET;

    RadioGroup allowLookRG, flippedStoreRG, storeAreaTypeRG;

    private Map<String, String> sotreLevelValueMap = new HashMap<String, String>() {{
        put("1", "第一顺位");
        put("2", "第二顺位");
        put("3", "第三顺位");
        put("4", "第四顺位");
    }};


    RadioButton allowLookYesRB, allowLookNoRB, flippedStoreYesRB, flippedStoreNoRB, storeAreaTypeYesRB, storeAreaTypeNoRB;

    CustomUpload storeBrandFrontUpload, storeBrandRightUpload, storeBrandLeftUpload, inRoomUpload, businessDistircitUpload;

    CustomUpload curUploadInstance;

    TextView nameRequireTL, followByRequireTL, storeLevelRequireTL, addressRequireTL, fulladdressRequireTL, businessPlanNameRequireTL, storeAttributesRequireTL, storeTypeRequireTL, allowLookRequireTL, flippedStoreRequireTL, pointStoreHeadRequireTL, storeBrandFrontRequireTL, storeBrandRightRequireTL, storeBrandLeftRequireTL, inRoomRequireTL, businessDistircitRequireTL, storeAreaTypeRequireTl, buseinessAreaTV, titleTextTv;

    // 选择的是主标签还是副标签
    private boolean isMainTab = true;

    private Gson gson = new Gson();

    private CustomUploadCollection uploadCollection = CustomUploadCollection.getInstance("STORE_ADD");

    private boolean isAddLandlord;//房东信息是否必填

    private DraftUtils draftUtils;
    //是否从商圈编辑页面修改功能跳转而来
  //  private boolean MapEditBusinessJumpEditFlag;

    private ArrayList<HashMap<String, Object>> landlordList = new ArrayList<>();//房东信息数据

    // 基本信息字段与变动项api_name映射
    final private Map<String, String> basicInfoApiNameMap = new HashMap<String, String>() {{
        put("name", "basic_info_store_plan_name");
        put("org_name", "basic_info_name_org_name");
        put("follow_by", "basic_info_follow_name");
        put("store_level", "basic_info_store_level");
        put("address", "basic_info_store_plan_address");
        put("full_address", "basic_info_full_address");
        put("business_plan_name", "basic_info_business_plan_name");
        put("store_attributes", "basic_info_store_attributes");
        put("store_type", "basic_info_store_type");

        put("sub_store_type", "basic_info_sub_store_type");
        put("store_area_type", "basic_info_store_area_type");
        put("allow_look", "basic_info_allow_look");
        put("flipped_store", "basic_info_flipped_store");
        put("point_store_head", "basic_info_store_head_file");
        put("store_brand_front", "basic_info_brand_front_file");
        put("store_brand_right", "basic_info_brand_right_file");
        put("store_brand_left", "basic_info_brand_left_file");
        put("inRoom", "basic_info_brand_right_file");
        put("business_distircit_enviroment", "basic_info_environment_file");
    }};

    @Override
    protected MapEditPointContract.MapEditPointPresenter createPresenter() {
        return new MapEditPointContract.MapEditPointPresenter();
    }


    private void handleUploadFile(File file, Map<String, Object> query) {
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);//表单类型
        Map<String, RequestBody> map = new HashMap<>();


        RequestBody requestBody = RequestBody.create(MediaType.parse("multipart/form-data"), file);

        builder.addFormDataPart("name", file.getName());
        builder.addFormDataPart("size", "" + file.length());
        builder.addFormDataPart("ref_id", "" + new Date().getTime());
        builder.addFormDataPart("ref_type", "ItemImage");
//        builder.addFormDataPart("fileType", "image/png");
//        builder.addFormDataPart("fid", "image/png");


        builder.addFormDataPart("file", file.getName(), requestBody);
        MultipartBody body = builder.build();

//        MultipartBody.Part filePart = MultipartBody.Part.createFormData("file", file.getName(), requestBody);
        Log.i("CustomUpload", "UploadStart");

        mPresenter.uploadPointFilePOST(body, query);
    }

    private void setupUploadCallbacks() {
        setupUploadCallback(storeBrandFrontUpload, "storeBrandFront");
        setupUploadCallback(storeBrandLeftUpload, "storeBrandLeft");
        setupUploadCallback(storeBrandRightUpload, "storeBrandRight");
        setupUploadCallback(inRoomUpload, "inRoom");
        setupUploadCallback(businessDistircitUpload, "businessDistircitEnviroment");
    }

    private void setupUploadCallback(CustomUpload uploadInstance, String fileType) {
//        uploadInstance.setUploadCallback(new CustomUpload.UploadCallback() {
//            @Override
//            public void onFileUploaded(File file) {
//                Map<String, Object> query = new HashMap<>();
//                query.put("fileType", fileType);
//                if (receivedMap != null && receivedMap.containsKey("id")) {
//                    int id = ((Double) receivedMap.get("id")).intValue();
//                    query.put("fid", id);
//                }
//                Log.i("CustomUpload", "UploadParams");
//                handleUploadFile(file, uploadInstance, query);
//            }
//
//            @Override
//            public void onProgressUpdated(int progress) {
//
//            }
//        });
        Map<String, Object> query = new HashMap<>();
        query.put("fileType", fileType);
        query.put("fid", storePlanId);
//        Log.i("CustomUpload", "setupUploadCallback: " + query.toString());
//        Log.i("CustomUpload", "receivedMap: " + receivedMap.toString());
        uploadInstance.setUploadExtraParams(query);
        uploadInstance.setUploadListener(new CustomUpload.onUploadListener() {
            @Override
            public void onUploadSuccess(String fieldName) {
                dataMap.put(fieldName, uploadInstance.getFiles());
            }
        });


    }


    private String getBaseInfoApiName(String key) {
        return basicInfoApiNameMap.get(key);
    }

    private void initBaseInfoDisplayAndRequired() {


//        if (!TextUtils.isEmpty(getBaseInfoApiName("follow_by"))) {
//            String apiName = getBaseInfoApiName("follow_by");
//            followByRL.setVisibility(baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
//            followByRequireTL.setVisibility(baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
//        }

        if (!TextUtils.isEmpty(getBaseInfoApiName("store_level"))) {
            String apiName = getBaseInfoApiName("store_level");
            if (!TextUtils.isEmpty(apiName)) {

                storeLevelLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                storeLevelRequireTL.setVisibility(null != baseInfoRequireMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
            }
        }
//        if (!TextUtils.isEmpty(getBaseInfoApiName("address"))) {
//            String apiName = getBaseInfoApiName("address");
//            addressRL.setVisibility(baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
//            addressRequireTL.setVisibility(baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
//        }
        if (!TextUtils.isEmpty(getBaseInfoApiName("full_address"))) {
            String apiName = getBaseInfoApiName("full_address");
            if (!TextUtils.isEmpty(apiName)) {
                fullAddressLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                fulladdressRequireTL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
            }
        }
        if (!TextUtils.isEmpty(getBaseInfoApiName("business_plan_name"))) {
            String apiName = getBaseInfoApiName("business_plan_name");
            if (!TextUtils.isEmpty(apiName)) {
                businessPlanNameLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                businessPlanNameRequireTL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
            }
        }
        if (!TextUtils.isEmpty(getBaseInfoApiName("store_attributes"))) {
            String apiName = getBaseInfoApiName("store_attributes");
            if (!TextUtils.isEmpty(apiName)) {
                storeAttributesLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                storeAttributesRequireTL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
            }

        }
        if (!TextUtils.isEmpty(getBaseInfoApiName("store_type"))) {
            String apiName = getBaseInfoApiName("store_type");
            if (!TextUtils.isEmpty(apiName)) {
                storeTypeLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                storeTypeRequireTL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
            }

        }
        if (!TextUtils.isEmpty(getBaseInfoApiName("sub_store_type"))) {
            String apiName = getBaseInfoApiName("sub_store_type");
            if (!TextUtils.isEmpty(apiName)) {
                subStoreTypeLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
//            sub.setVisibility(baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
            }
        }

        if (!TextUtils.isEmpty(getBaseInfoApiName("allow_look"))) {
            String apiName = getBaseInfoApiName("allow_look");
            if (!TextUtils.isEmpty(apiName)) {
                allowLookLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                allowLookRequireTL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
            }
        }
        if (!TextUtils.isEmpty(getBaseInfoApiName("flipped_store"))) {
            String apiName = getBaseInfoApiName("flipped_store");
            if (!TextUtils.isEmpty(apiName)) {
                flippedStoreLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                flippedStoreRequireTL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
            }
        }
        if (!TextUtils.isEmpty(getBaseInfoApiName("store_area_type"))) {
            String apiName = getBaseInfoApiName("store_area_type");
            if (!TextUtils.isEmpty(apiName)) {
                store_area_type_ll.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                storeAreaTypeRequireTl.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
            }

        }

        if (!TextUtils.isEmpty(getBaseInfoApiName("point_store_head"))) {
            String apiName = getBaseInfoApiName("point_store_head");
            if (!TextUtils.isEmpty(apiName)) {
                pointStoreContentLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                pointStoreHeadRequireTL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
//            pointStoreHeadLL.setVisibility(baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
            }
        }

        if (!TextUtils.isEmpty(getBaseInfoApiName("store_brand_front"))) {
            String apiName = getBaseInfoApiName("store_brand_front");
            if (!TextUtils.isEmpty(apiName)) {
                storeBrandFrontContentLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                storeBrandFrontRequireTL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
//            storeBrandFrontLL.setVisibility(baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
            }
        }

        if (!TextUtils.isEmpty(getBaseInfoApiName("store_brand_right"))) {
            String apiName = getBaseInfoApiName("store_brand_right");
            if (!TextUtils.isEmpty(apiName)) {
                storeBrandRightContentLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                storeBrandRightRequireTL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
//            storeBrandRightLL.setVisibility(baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
            }

        }

        if (!TextUtils.isEmpty(getBaseInfoApiName("store_brand_left"))) {
            String apiName = getBaseInfoApiName("store_brand_left");
            if (!TextUtils.isEmpty(apiName)) {
                storeBrandLeftContentLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                storeBrandLeftRequireTL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
//            storeBrandLeftLL.setVisibility(baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
            }
        }

        if (!TextUtils.isEmpty(getBaseInfoApiName("inRoom"))) {
            String apiName = getBaseInfoApiName("inRoom");
            if (!TextUtils.isEmpty(apiName)) {
                inRoomContentLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                inRoomRequireTL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
//            inRoomLL.setVisibility(baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
            }
        }

        if (!TextUtils.isEmpty(getBaseInfoApiName("business_distircit_enviroment"))) {
            String apiName = getBaseInfoApiName("business_distircit_enviroment");
            if (!TextUtils.isEmpty(apiName)) {
                businessDistircitEnviromentContentLL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
                businessDistircitRequireTL.setVisibility(null != baseInfoShowMap.get(apiName) && baseInfoRequireMap.get(apiName) ? View.VISIBLE : View.GONE);
//            businessDistircitEnviromentLL.setVisibility(baseInfoShowMap.get(apiName) ? View.VISIBLE : View.GONE);
            }
        }


    }

    private void initBaseInfoView() {
        contentSv = findViewById(R.id.content_nsv);
        headRl = findViewById(R.id.head_rl);
        basicInfoLl = findViewById(R.id.basic_info_ll);
        landlordAllLl = findViewById(R.id.landlord_all_ll);
        nameRL = findViewById(R.id.name_rl);
        followByRL = findViewById(R.id.follow_by_rl);
        storeLevelRL = findViewById(R.id.store_level_rl);
        addressRL = findViewById(R.id.address_rl);
        fullAddressRL = findViewById(R.id.full_address_rl);
        businessPlanNameRL = findViewById(R.id.business_plan_name_rl);
        storeAttributesRL = findViewById(R.id.store_attribute_rl);
        storeTypeRL = findViewById(R.id.store_type_name_rl);
        subStoreTypeRL = findViewById(R.id.sub_store_type_name_rl);
        upload_camera_rl = findViewById(R.id.upload_camera_rl);
        store_area_type_rl = findViewById(R.id.store_area_type_rl);
        pointStoreHeadIV = findViewById(R.id.pointStoreHead);

        allowLookRL = findViewById(R.id.allow_look_rl);
        flippedStoreRL = findViewById(R.id.flipped_store_rl);
        pointStoreHeadRL = findViewById(R.id.pointStoreHead_rl);
        storeBrandFrontRL = findViewById(R.id.storeBrandFront_rl);
        storeBrandRightRL = findViewById(R.id.storeBrandRight_rl);
        storeBrandLeftRL = findViewById(R.id.storeBrandLeft_rl);
        inRoomRL = findViewById(R.id.inRoom_rl);
        businessDistircitEnviromentRL = findViewById(R.id.businessDistircitEnviroment_rl);
        nameRequireTL = findViewById(R.id.name_require);
        followByRequireTL = findViewById(R.id.follow_by_require);
        storeLevelRequireTL = findViewById(R.id.store_level_require);
        addressRequireTL = findViewById(R.id.address_require);
        fulladdressRequireTL = findViewById(R.id.full_address_require);
        businessPlanNameRequireTL = findViewById(R.id.business_plan_name_require);
        storeAttributesRequireTL = findViewById(R.id.store_attribute_require);
        storeTypeRequireTL = findViewById(R.id.store_type_name_require);
        allowLookRequireTL = findViewById(R.id.allow_look_require);
        flippedStoreRequireTL = findViewById(R.id.flipped_store_require);
        pointStoreHeadRequireTL = findViewById(R.id.pointStoreHead_require);
        storeBrandFrontRequireTL = findViewById(R.id.storeBrandFront_require);
        storeBrandRightRequireTL = findViewById(R.id.storeBrandRight_require);
        storeBrandLeftRequireTL = findViewById(R.id.storeBrandLeft_require);
        inRoomRequireTL = findViewById(R.id.inRoom_require);
        businessDistircitRequireTL = findViewById(R.id.businessDistircitEnviroment_require);

        storeBrandFrontLL = findViewById(R.id.storeBrandFront_ll);
        storeBrandRightLL = findViewById(R.id.storeBrandRight_ll);
        storeBrandLeftLL = findViewById(R.id.storeBrandLeft_ll);
        inRoomLL = findViewById(R.id.inRoom_ll);
        orgNameTv = findViewById(R.id.org_name);
        businessDistircitEnviromentLL = findViewById(R.id.businessDistircitEnviroment_ll);
        pointStoreHeadLL = findViewById(R.id.pointStoreHead_ll);

        store_area_type_ll = findViewById(R.id.store_area_type_ll);
        nameLL = findViewById(R.id.name_ll);
        followByLL = findViewById(R.id.follow_by_ll);
        storeLevelLL = findViewById(R.id.store_level_ll);
        addressLL = findViewById(R.id.address_ll);
        fullAddressLL = findViewById(R.id.full_address_ll);
        businessPlanNameLL = findViewById(R.id.business_plan_name_ll);
        storeAttributesLL = findViewById(R.id.store_attribute_ll);
        storeTypeLL = findViewById(R.id.store_type_name_ll);
        subStoreTypeLL = findViewById(R.id.sub_store_type_name_ll);
        allowLookLL = findViewById(R.id.allow_look_ll);
        flippedStoreLL = findViewById(R.id.flipped_store_ll);
        storeAreaTypeLL = findViewById(R.id.store_area_type_ll);
        pointStoreContentLL = findViewById(R.id.pointStoreHead_content_ll);
        storeBrandFrontContentLL = findViewById(R.id.storeBrandFront_content_ll);
        storeBrandRightContentLL = findViewById(R.id.storeBrandRight_content_ll);
        storeBrandLeftContentLL = findViewById(R.id.storeBrandLeft_content_ll);
        inRoomContentLL = findViewById(R.id.inRoom_content_ll);
        businessDistircitEnviromentContentLL = findViewById(R.id.businessDistircitEnviroment_content_ll);

        titleTextTv = findViewById(R.id.title_text);
        titleTextTv.setText("编辑点位");

        findViewById(R.id.subtitle_text).setVisibility(View.GONE);

        nameET = findViewById(R.id.name);

        fullAddressET = findViewById(R.id.full_address);
        followByTV = findViewById(R.id.follow_by);
        storeLevelTV = findViewById(R.id.store_level);
        businessPlanNameTV = findViewById(R.id.business_plan_name);
        addressTV = findViewById(R.id.address);
        storeAttributesTV = findViewById(R.id.store_attribute);
        storeTypeTV = findViewById(R.id.store_type_name);
        subStoreTypeTV = findViewById(R.id.sub_store_type_name);

        storeAreaTypeRequireTl = findViewById(R.id.store_area_type_require);

        storeBrandFrontUpload = findViewById(R.id.storeBrandFront);
        storeBrandLeftUpload = findViewById(R.id.storeBrandLeft);
        storeBrandRightUpload = findViewById(R.id.storeBrandRight);
        inRoomUpload = findViewById(R.id.inRoom);
        businessDistircitUpload = findViewById(R.id.businessDistircitEnviroment);
        draftContainerRl = findViewById(R.id.draft_container);
        draftContainerRl.setVisibility(View.GONE);


        allowLookNoRB = findViewById(R.id.allow_look_false);
        allowLookYesRB = findViewById(R.id.allow_look_true);
        flippedStoreNoRB = findViewById(R.id.flipped_store_false);
        flippedStoreYesRB = findViewById(R.id.flipped_store_true);
        storeAreaTypeNoRB = findViewById(R.id.store_area_type_false);
        storeAreaTypeYesRB = findViewById(R.id.store_area_type_true);


//        buseinessAreaTV = findViewById(R.id.business_plan_name);

        // 单选
        followByTV.setOnClickListener(this);
        storeLevelTV.setOnClickListener(this);
        storeAttributesTV.setOnClickListener(this);
        storeTypeTV.setOnClickListener(this);
        subStoreTypeTV.setOnClickListener(this);

        allowLookRG = findViewById(R.id.allow_look);
        flippedStoreRG = findViewById(R.id.flipped_store);
        storeAreaTypeRG = findViewById(R.id.store_area_type);
        pointStoreHeadLL.setOnClickListener(this);
        businessPlanNameTV.setOnClickListener(this);
        addressTV.setOnClickListener(this);

        nameET.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                storePlanData.setName(nameET.getText().toString());
                Log.i("SUBMIT_DATA", "nameEdit" + nameET.getText().toString());
                if (actionId == EditorInfo.IME_ACTION_DONE) {
                    InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                    imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                    return true;
                }
                return false;
            }
        });

        nameET.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() > 0) {
                    nameET.setError(null);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                storePlanData.setName(nameET.getText().toString());


            }
        });

        fullAddressET.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                storePlanData.setFull_address(fullAddressET.getText().toString());

                if (actionId == EditorInfo.IME_ACTION_DONE) {
                    InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                    imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                    return true;
                }
                return false;
            }
        });

        fullAddressET.addTextChangedListener(new TextWatcher() {

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        flippedStoreRG.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (checkedId == R.id.flipped_store_true) {
                    storePlanData.setFlipped_store(true);
                } else {
                    storePlanData.setFlipped_store(false);
                }
            }
        });

        allowLookRG.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (checkedId == R.id.allow_look_true) {
                    storePlanData.setAllow_look(true);
                } else {
                    storePlanData.setAllow_look(false);
                }
            }
        });

        storeAreaTypeRG.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (checkedId == R.id.store_area_type_true) {
                    storePlanData.setStore_area_type("URBAN");
                } else {
                    storePlanData.setStore_area_type("RURAL");
                }


            }
        });
    }

    private void initTableInfo() {
        Map<String, Object> tableMap = new HashMap<>();

        // 处理表格
        for (ItemChangeBean item : changeBeanList) {
            List<ItemChangeContentDetailBean> details = item.getDetails();
            if (details != null && !details.isEmpty()) {
                for (ItemChangeContentDetailBean detail : details) {
//                Log.i("tableMap", "initInfo: dataMap.get(detail.getId())" + dataMap.get(detail.getId()));

                    if ("TABLE".equals(detail.getComponent_type()) && dataMap.containsKey(detail.getId()) && dataMap.get(detail.getId()) != null && dataMap.get(detail.getId()) instanceof List) {
                        List<Map<String, Object>> tableData = (List<Map<String, Object>>) dataMap.get(detail.getId());
                        Log.i("tableMap", "initInfo: tableData" + tableData);

                        if (tableData != null && tableData.size() > 0) {
                            for (int i = 0; i < tableData.size(); i++) {
                                Map<String, Object> tableRow = tableData.get(i);
                                List<ItemChangeContentDetailBean> curDetails = detail.getDetails();
                                Log.i("tableMap", "initInfo: curDetails" + curDetails);

                                for (ItemChangeContentDetailBean curDetail : curDetails) {
                                    Log.i("tableMap", "initInfo: curDetail.getId()" + curDetail.getId());

                                    if (tableRow.containsKey(curDetail.getId())) {
                                        tableMap.put(detail.getId() + "." + i + "." + curDetail.getId(), tableRow.get(curDetail.getId()));
                                    }
                                }

                            }
                        }


                    }
                }
            }


        }
        Log.i("tableMap", "initInfo: tableMap" + tableMap);
        dataMap.putAll(tableMap);
    }


    private void initInfo() {
        Intent intent = getIntent();
        if (intent != null && intent.hasExtra("data")) {
            try {
                receivedMap = (HashMap<String, Object>) intent.getSerializableExtra("data");

                // 使用数据
                String name = (String) receivedMap.get("org_name");
                orgId = (long) receivedMap.get("org_id");

                if (receivedMap != null && receivedMap.containsKey("id")) {
                    int id = ((Double) receivedMap.get("id")).intValue();
                    storePlanId = id;
                    storePlanData.setId(id);
                }

                storePlanData.setOrg_id(orgId);
                storePlanData.setOrg_name(name);


//            int id = (int) receivedMap.get("id");
//                Log.i("TAG_INTENT", "name: " + name + ", org_id: " + org_id + ", id: " + ", item: " + receivedMap.get("chooseMap"));

                if (receivedMap.containsKey("chooseMap") && receivedMap.get("chooseMap") instanceof String && !TextUtils.isEmpty((String) receivedMap.get("chooseMap"))) {
                    PoiItemV2 selectedPoiItem = new Gson().fromJson((String) receivedMap.get("chooseMap"), PoiItemV2.class);
                    Log.i("TAG_INTENT", "name: " + name + ", org_id: " + orgId + ", id: " + ", 1item: " + selectedPoiItem.getProvinceName() + selectedPoiItem.getCityName() + selectedPoiItem.getAdName() + selectedPoiItem.getSnippet() + selectedPoiItem.getTitle());

                    String address = selectedPoiItem.getProvinceName() + selectedPoiItem.getCityName() + selectedPoiItem.getAdName() + selectedPoiItem.getSnippet();
                    storePlanData.setAddress(address);
                    if (storePlanData.getInfo() == null) {
                        storePlanData.setInfo(new Info());
                    }
                    storePlanData.getInfo().setLatitude(selectedPoiItem.getLatLonPoint().getLatitude());
                    storePlanData.getInfo().setLongitude(selectedPoiItem.getLatLonPoint().getLongitude());
                    storePlanData.getInfo().setPoint_name(selectedPoiItem.getTitle());
                    storePlanData.setArea_code(Integer.parseInt(selectedPoiItem.getAdCode()));
                    Log.i("TAG_INTENT", "selectedItem: " + storePlanData.getArea_code());
                    addressTV.setText(address);
                    orgNameTv.setText(name);


                }


            } catch (ClassCastException e) {
                // 处理类型转换异常
                Log.e("TAG", "数据类型不匹配", e);
            }
        } else if (intent != null && intent.hasExtra("storePlanData")) {

            try {
                String jsonStr = intent.getStringExtra("storePlanData");

                Log.i("TAG", "initInfo: " + jsonStr);
                storePlanData = new Gson().fromJson(jsonStr, MapStorePlanDetailReponseBean.class);

                Log.i("storePlanData", "initInfo: " + jsonStr);

                if ((!TextUtils.isEmpty(storePlanData.getState()) && !storePlanData.getState().equals("SECOND_AUDIT")) || (getIntent().hasExtra("isCopy") && getIntent().getBooleanExtra("isCopy", false))) {
                    findViewById(R.id.allow_look_false).setClickable(false);
                    findViewById(R.id.allow_look_true).setClickable(false);
                } else {
                    findViewById(R.id.allow_look_false).setClickable(true);
                    findViewById(R.id.allow_look_true).setClickable(true);
                }

                dataMap = ViewUtils.convertToMap(storePlanData);

                if (intent.hasExtra("isCopy")) {
                    dataMap.put("isCopy", intent.getBooleanExtra("isCopy", false));
                }

                if (intent.hasExtra("old_id")) {
                    dataMap.put("old_id", intent.getLongExtra("old_id", 0));

                }

                Log.i("storePlanData", "storePlanData.isCopy: " + dataMap.get("isCopy"));


                if (storePlanData.getContent() != null && !storePlanData.getContent().isEmpty()) {

                    for (int i = 0; i < storePlanData.getContent().size(); i++) {
                        ContentBean contentBean = (ContentBean) storePlanData.getContent().get(i);
                        if (contentBean.getDetails() != null && contentBean.getDetails() instanceof Map) {
                            dataMap.putAll(new HashMap<>((Map<String, Object>) contentBean.getDetails()));
                        }

                    }

                }
                receivedMap = new HashMap<String, Object>(dataMap);

                // 使用数据
                String name = (String) receivedMap.get("org_name");
                storePlanId = storePlanData.getId();

                if (!TextUtils.isEmpty(storePlanData.getAddress())) {
                    addressTV.setText(storePlanData.getAddress());
                }
                orgNameTv.setText(name);

                if (intent.hasExtra("isStartApply")) {
                    boolean isStartApply = intent.getBooleanExtra("isStartApply", false);
                    dataMap.put("isStartApply", isStartApply);
                }

            } catch (ClassCastException e) {
                // 处理类型转换异常
                Log.e("TAG", "数据类型不匹配", e);
            }
        }
//        storePlanId = getIntent().getIntExtra("storePlanId", -1);
//        MapEditBusinessJumpEditFlag = getIntent().getBooleanExtra("MapEditBusinessJumpEditFlag", false);
//        if (MapEditBusinessJumpEditFlag) {
//            getStorePlanDetail();
//        }

    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);


        filePickerLauncher = registerForActivityResult(
                new PickUtilResultContract(),
                result -> {
                    Intent data = result.getPhotoFileIntent();
                    Object extra = result.getExtra();
                    Log.d("MapAddPointActivity", "ActivityResult received: " + result);

                    if (data != null) {
                        // Log the entire Intent and its extras


                        String receivedId = (String) extra;
                        Log.d("MapAddPointActivity", "Received operatorId: " + receivedId);

                        if (receivedId != null) {
                            if (receivedId.equals("pointStoreHead")) {
                                handleStoreHeadResult(result);
                            } else {
                                CustomUpload uploadInstance = uploadCollection.getCustomUpload(receivedId);
                                if (uploadInstance != null) {
                                    uploadInstance.onFilePickerResult(result);
                                } else {
                                    Log.w("MapAddPointActivity", "No CustomUpload instance found for operatorId: " + receivedId);
                                }
                            }

                        }
                    } else {
                        Log.w("MapAddPointActivity", "No data received in ActivityResult");
                    }
                }
        );

        pickerUtil = new PickerUtil(this, filePickerLauncher, "pointStoreHead");

        // Set the content view after registering the launcher
        setContentView(R.layout.activity_map_add_point);

        // Initialize views before setting up upload callbacks
        initBaseInfoView();

        draftUtils = new DraftUtils("STORE_PLAN", new DraftUtils.DraftListener() {
            @Override
            public void onUpdate(Map<String, Object> record) {

//                storePlanData = (StorePlanData) record.get("data");
            }

            @Override
            public void onInit(DraftBean record) {
//                Log.i("DraftUtil", "record.getInfo().isEmpty() : " + record.getInfo().isEmpty()  + "record.getInfo() != null");
                if (record != null && record.getInfo() != null) {
                    if (!record.getInfo().isEmpty()) {
                        draftContainerRl.setVisibility(View.VISIBLE);
                    }
                } else {
                    Log.i("DraftUtil", "record.getInfo().isEmpty() : ");
                    runOnUiThread(() -> {
                        draftContainerRl.setVisibility(View.GONE);
                        draftContainerRl.requestLayout();
                    });


                }

                // This method is called when the DraftBean record is initialized
            }

            @Override
            public void onDelete() {

//                new Handler()

                draftContainerRl.setVisibility(View.GONE);
            }


        });

//        draftUtils.read();

        findViewById(R.id.draft_open_tv).setOnClickListener(this);
        findViewById(R.id.draft_delete_tv).setOnClickListener(this);

        // Pass the ActivityResultLauncher to CustomUpload instances
        storeBrandFrontUpload.setFilePickerLauncher(filePickerLauncher);
        storeBrandLeftUpload.setFilePickerLauncher(filePickerLauncher);
        storeBrandRightUpload.setFilePickerLauncher(filePickerLauncher);
        inRoomUpload.setFilePickerLauncher(filePickerLauncher);
        businessDistircitUpload.setFilePickerLauncher(filePickerLauncher);


//        storePlanData = new MapStorePlanDetailReponseBean();
//        dataMap = ViewUtils.convertToMap(storePlanData);

//        draftContainerRv = findViewById(R.id.draft_container);

        companyId = MySPTool.getInt(this, "app_companyId");
        tabsList = new ArrayList<>();

        initInfo();


        findViewById(R.id.back_ll).setOnClickListener(this);
        findViewById(R.id.click_submit).setOnClickListener(this);
        changeableRV = findViewById(R.id.changeableList);
        findViewById(R.id.addlandlord_btn).setOnClickListener(this);

//        loding
        showLoadingDialog();
        setupUploadCallbacks();

        //初始化变动项接口
        initChangeItem();

        findBusinessArea();

        updateViewData(dataMap);

        EventBus.getDefault().register(this);

    }


    @Subscribe
    public void onPoiItemEvent(PoiItemChangeEvent event) {
        if (event != null && !TextUtils.isEmpty(event.getMessage())) {

            HashMap<String, Object> map = event.getData();

            if ("EDIT_POI".equals(event.getMessage())) {


                if (map.containsKey("chooseMap") && map.get("chooseMap") instanceof String && !TextUtils.isEmpty((String) map.get("chooseMap"))) {
                    PoiItemV2 selectedPoiItem = new Gson().fromJson((String) map.get("chooseMap"), PoiItemV2.class);


//                Log.i("TAG_INTENT", "name: " + name + ", org_id: " + org_id + ", id: " + ", 1item: " + selectedPoiItem.getProvinceName() + selectedPoiItem.getCityName() + selectedPoiItem.getAdName() + selectedPoiItem.getSnippet() + selectedPoiItem.getTitle());
                    String address = selectedPoiItem.getProvinceName() + selectedPoiItem.getCityName() + selectedPoiItem.getAdName() + selectedPoiItem.getSnippet();
                    String eventBusinessId = (String) map.get("id");
                    if (map.containsKey("id") && !TextUtils.isEmpty(eventBusinessId)) {
//                        dataMap.put(eventBusinessId, new HashMap<String, Object>(){{
//                            put("address", address);
//                            put("longitude", selectedPoiItem.getLatLonPoint().getLongitude());
//                            put("latitude", selectedPoiItem.getLatLonPoint().getLatitude());
//
//                        }});
                        AddressBean addressBean = new AddressBean();
                        addressBean.setAddress(address);
                        addressBean.setLongitude(selectedPoiItem.getLatLonPoint().getLongitude());
                        addressBean.setLatitude(selectedPoiItem.getLatLonPoint().getLatitude());
                        dataMap.put(eventBusinessId, addressBean);

                        updateViewData(dataMap);
                    } else {
                        storePlanData.setAddress(address);
                        if (storePlanData.getInfo() == null) {
                            storePlanData.setInfo(new Info());
                        }
                        storePlanData.getInfo().setLatitude(selectedPoiItem.getLatLonPoint().getLatitude());
                        storePlanData.getInfo().setLongitude(selectedPoiItem.getLatLonPoint().getLongitude());
                        storePlanData.getInfo().setPoint_name(selectedPoiItem.getTitle());
                        storePlanData.setArea_code(Integer.parseInt(selectedPoiItem.getAdCode()));
                        addressTV.setText(address);
                        isFirstFindBusinessFlag = true;

                        findBusinessArea();
                    }


                }


            }
        }

    }

    @Subscribe
    public void onEvent(AddLandlordEvent event) {
        if (event != null && !TextUtils.isEmpty(event.getMessage())) {

            HashMap<String, Object> map = event.getData();

            if ("ADD_LANDLORD".equals(event.getMessage())) {//新增房东
                landlordList.add(map);
                dataMap.put("landlords", landlordList);
                refreshLandlordList();

            } else if ("EDIT_LANDLORD".equals(event.getMessage())) {//编辑房东

                int someIndex = (int) map.get("someIndex");
                landlordList.set(someIndex, map);
                dataMap.put("landlords", landlordList);

                refreshLandlordList();

            } else if ("DELETE_LANDLORD".equals(event.getMessage())) {//删除房东

                int someIndex = (int) map.get("someIndex");
                landlordList.remove(someIndex);
                dataMap.put("landlords", landlordList);

                refreshLandlordList();
            }
        }

    }

    private void refreshLandlordList() {
        LinearLayout layout = findViewById(R.id.add_landlord_content);
        layout.removeAllViews();

        for (int i = 0; i < landlordList.size(); i++) {

            HashMap<String, Object> map = landlordList.get(i);

//            分割线
            View divider = new View(context);
            divider.setBackgroundColor(Color.parseColor("#1a1F2126")); // 设置颜色，例如灰色
            LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    dpToPx(1)
            );
            dividerParams.leftMargin = dpToPx(16);
            divider.setLayoutParams(dividerParams);
            layout.addView(divider);

//            每一个小项view
            LinearLayout childLayout = new LinearLayout(MapEditPointActivity.this);
            childLayout.setOrientation(LinearLayout.HORIZONTAL);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT, // 宽度
                    LinearLayout.LayoutParams.WRAP_CONTENT  // 高度
            );
            childLayout.setPadding(dpToPx(16), dpToPx(14), dpToPx(16), dpToPx(14)); // 上下边距 14dp，左右为0
            childLayout.setLayoutParams(params);
            layout.addView(childLayout);

            int finalI = i;
            childLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // 点击事件逻辑
                    Intent mapIntent = new Intent(MapEditPointActivity.this, MapAddLandlordActivity.class);
                    mapIntent.putExtra("org_id", String.valueOf(receivedMap.get("org_id")));
                    int id = ((Number) receivedMap.get("id")).intValue();
                    mapIntent.putExtra("point_id", id);
                    mapIntent.putExtra("isEdit", true);
                    mapIntent.putExtra("someIndex", finalI);
                    mapIntent.putExtra("dataDic", map);
                    startActivity(mapIntent);
                }
            });


//            左边图片
            ImageView leftIm = new ImageView(MapEditPointActivity.this);
            leftIm.setScaleType(ImageView.ScaleType.CENTER_CROP);
            leftIm.setBackgroundColor(Color.parseColor("#F4F5F7"));
            LinearLayout.LayoutParams imageParams = new LinearLayout.LayoutParams(dpToPx(72), dpToPx(72));
            leftIm.setLayoutParams(imageParams);
            childLayout.addView(leftIm);

            ArrayList<String> arrayList = (ArrayList<String>) map.get("urls");
            if (arrayList != null && !arrayList.isEmpty()) {
                String url = arrayList.get(0);
                if (!TextUtils.isEmpty(url)) {
                    Glide.with(MapEditPointActivity.this) // 上下文
                            .load(url)
                            .transform(new RoundedCorners(dpToPx(4)))
                            .into(leftIm);
                }
            }

//            右边布局
            LinearLayout rightLayout = new LinearLayout(MapEditPointActivity.this);
            rightLayout.setOrientation(LinearLayout.VERTICAL);
            LinearLayout.LayoutParams rightLayoutParams = new LinearLayout.LayoutParams(
                    0, // 宽度设为 0，配合 weight 使用
                    LinearLayout.LayoutParams.WRAP_CONTENT
            );
            rightLayoutParams.weight = 1;
            rightLayoutParams.leftMargin = dpToPx(12);
            rightLayout.setLayoutParams(rightLayoutParams);
            childLayout.addView(rightLayout);

//            姓名电话
            TextView textView = new TextView(MapEditPointActivity.this);
            String namePhone = (TextUtils.isEmpty((String) map.get("name")) ? "-" : map.get("name")) + "  " + (TextUtils.isEmpty((String) map.get("phone")) ? "-" : map.get("phone"));
            textView.setText(namePhone);
            textView.setSingleLine();
            textView.setTypeface(Typeface.DEFAULT_BOLD); // 设置加粗
            textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16); // 设置字体大小为 16sp
            textView.setTextColor(Color.parseColor("#1F2126")); // 设置文字颜色（可选）
            LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    dpToPx(22)
            );
            textView.setLayoutParams(textParams);
            rightLayout.addView(textView);

//         租金 转让费
            LinearLayout horizontalLayout = new LinearLayout(MapEditPointActivity.this);
            horizontalLayout.setOrientation(LinearLayout.HORIZONTAL);
            LinearLayout.LayoutParams hLayoutParam = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    dpToPx(18)
            );
            hLayoutParam.topMargin = dpToPx(8);
            horizontalLayout.setLayoutParams(hLayoutParam);
            rightLayout.addView(horizontalLayout);

//          租金
            TextView leftText = new TextView(context);
            String rentMoney = "租金：" + (TextUtils.isEmpty(String.valueOf(map.get("rent_money"))) ? "-" : getFormattedMoney(String.valueOf(map.get("rent_money"))));
            leftText.setText(rentMoney);
            leftText.setSingleLine();
            leftText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13);
            leftText.setTextColor(Color.parseColor("#b31E2126"));

//          转让费
            TextView rightText = new TextView(context);
            String tranferMoney = "转让费：" + (TextUtils.isEmpty(String.valueOf(map.get("transfer_fee"))) ? "-" : getFormattedMoney(String.valueOf(map.get("transfer_fee"))));
            rightText.setText(tranferMoney);
            rightText.setSingleLine();
            rightText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13);
            rightText.setTextColor(Color.parseColor("#b31E2126"));

            LinearLayout.LayoutParams leftParam = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
            );

            LinearLayout.LayoutParams rightParam = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
            );

            View spacer = new View(context);
            LinearLayout.LayoutParams spacerParam = new LinearLayout.LayoutParams(
                    0,
                    0,
                    1.0f // weight = 1
            );

            horizontalLayout.addView(leftText, leftParam);
            horizontalLayout.addView(spacer, spacerParam);
            horizontalLayout.addView(rightText, rightParam);


//            备注
            TextView bottomTextView = new TextView(MapEditPointActivity.this);
            String memo;
            if (TextUtils.isEmpty((String) map.get("memo"))) {
                memo = "备注：" + "-";
            } else {
                memo = "备注：" + map.get("memo");
            }

            bottomTextView.setText(memo);
            bottomTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13); // 设置字体大小
            bottomTextView.setLineSpacing(0, 1.2f); // 可选：设置行间距
            bottomTextView.setSingleLine(false); // 允许换行
            bottomTextView.setTextColor(Color.parseColor("#b31E2126"));
            bottomTextView.setMinHeight(dpToPx(19));
            LinearLayout.LayoutParams bottomTextParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
            );

            bottomTextParams.topMargin = dpToPx(2);
            rightLayout.addView(bottomTextView, bottomTextParams);


        }
    }

    public static String getFormattedMoney(String money) {

        if (money == null || money.trim().isEmpty()) {
            return "-"; // 或者返回 "0.00"
        }

        try {
            double value = Double.parseDouble(money);
            DecimalFormat df = new DecimalFormat("0.00");
            return df.format(value);
        } catch (NumberFormatException e) {
            // 可选：打印异常或记录日志
            // e.printStackTrace();
            return "-"; // 格式化失败
        }
    }

    private void initTabs() {
        ArrayList<TextView> textViews = new ArrayList<>();
        // 找到 HorizontalScrollView 中的 LinearLayout
        LinearLayout linearLayout = findViewById(R.id.linear_layout_tabs);
        indicatorView = findViewById(R.id.indicator_view);
        horizontalScrollView = findViewById(R.id.tab_scrollview);

        TextView addlandlord_indicator = findViewById(R.id.addlandlord_indicator);
        if (!isAddLandlord) {
            addlandlord_indicator.setText("");
        }

        // 动态添加 Tab 的内容
        for (int i = 0; i < tabsList.size(); i++) {

            String tab = tabsList.get(i);
            // 创建一个新的 TextView 作为 Tab
            TextView tabTextView = new TextView(this);
            tabTextView.setText(tab);
            tabTextView.setTextSize(14);
            tabTextView.setHeight(dpToPx(20)); // 高度固定为 20dp
            tabTextView.setSingleLine(true); // 单行显示
            tabTextView.setTextColor(getResources().getColor(R.color.color_map_search_delete_text));


            // 设置布局参数
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.MATCH_PARENT
            );
            params.setMargins(dpToPx(12), 0, dpToPx(12), 0); // 左右各 12dp，总间距 24dp
            tabTextView.setLayoutParams(params);

            // 点击事件：实现选中效果
            tabTextView.setOnClickListener(v -> {
                // 如果已经有选中的 TextView，则重置其样式
                if (selectedTextView != null) {
                    selectedTextView.setTextColor(getResources().getColor(R.color.color_map_search_delete_text));
                    selectedTextView.setTypeface(Typeface.DEFAULT); // 恢复默认字体
                }

                // 设置当前点击的 TextView 为选中样式
                tabTextView.setTextColor(getResources().getColor(R.color.color_map_business_polygon_stroke_blue)); // 改为蓝色
                tabTextView.setTypeface(Typeface.DEFAULT_BOLD); // 设置加粗

                // 更新选中的 TextView
                selectedTextView = tabTextView;
                int position = textViews.indexOf(selectedTextView);
                // 更新指示条的位置
                tabTextView.post(() -> {

                    updateIndicatorPosition(tabTextView); // 等待布局完成后更新指示器位置
                    // 滚动 HorizontalScrollView 让选中的 TextView 居中
                    centerTextViewInHorizontalScrollView(horizontalScrollView, tabTextView);
                });

                Log.i("TAG", "-----------view-------position----" + position);
                int basicHeight = basicInfoLl.getHeight();
                int headRlHeight = headRl.getHeight();
                int hsvHeight = horizontalScrollView.getHeight();
                int landlordAllLlHeight = landlordAllLl.getHeight();
                if (0 == position) {
                    contentSv.smoothScrollTo(0, 0);
                } else if (1 == position) {
                    contentSv.smoothScrollTo(0, landlordAllLl.getTop() + headRlHeight + hsvHeight - 280);
                } else {
                    View view = changeableRV.getLayoutManager().findViewByPosition(position - 2);
                    if (view != null) {
                        Log.i("TAG", "-----------view-------view.getTop()----" + view.getTop());
                        Log.i("TAG", "-----------view-------basicHeight----" + basicHeight);
                        contentSv.smoothScrollTo(0, view.getTop() + basicHeight + headRlHeight + hsvHeight + landlordAllLlHeight - 210);
                    }
                }


            });

            // 将 TextView 添加到 LinearLayout
            linearLayout.addView(tabTextView);
            textViews.add(tabTextView);
            // 默认选中第一个 Tab
            if (i == 0) {
                tabTextView.setTextColor(getResources().getColor(R.color.color_map_business_polygon_stroke_blue));
                tabTextView.setTypeface(Typeface.DEFAULT_BOLD); // 设置为加粗
                selectedTextView = tabTextView; // 设置第一个 Tab 为选中
            }
        }


        // 初始化指示条的位置
        selectedTextView.post(() -> {
            int textViewWidth = selectedTextView.getWidth();
            int textViewLeft = selectedTextView.getLeft();

            FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) indicatorView.getLayoutParams();
            params.leftMargin = textViewLeft + (textViewWidth - dpToPx(20)) / 2;
            indicatorView.setLayoutParams(params);
            indicatorView.setVisibility(View.VISIBLE);
        });


    }

    /**
     * 让选中的 TextView 在 HorizontalScrollView 中居中显示
     *
     * @param scrollView HorizontalScrollView
     * @param textView   当前选中的 TextView
     */
    private void centerTextViewInHorizontalScrollView(HorizontalScrollView scrollView, TextView textView) {
        // 获取 TextView 的宽度和左边距
        int textViewWidth = textView.getWidth();
        int textViewLeft = textView.getLeft();

        // 获取 HorizontalScrollView 的宽度
        int scrollViewWidth = scrollView.getWidth();

        // 计算滚动的目标位置（TextView 的中心与 ScrollView 的中心对齐）
        int targetScrollX = textViewLeft + textViewWidth / 2 - scrollViewWidth / 2;

        // 滚动到目标位置
        scrollView.smoothScrollTo(targetScrollX, 0);
    }

    /**
     * 更新指示条的位置
     */
    private void updateIndicatorPosition(TextView selectedTextView) {
        // 获取选中 TextView 的宽度和位置
        int textViewWidth = selectedTextView.getWidth();
        int textViewLeft = selectedTextView.getLeft();

        // 更新指示条的位置和宽度
        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) indicatorView.getLayoutParams();
        int targetLeftMargin = textViewLeft + (textViewWidth - dpToPx(20)) / 2;
        int currentLeftMargin = params.leftMargin; // 当前的 leftMargin

        // 创建动画，让指示器平滑移动到目标位置
        ValueAnimator animator = ValueAnimator.ofInt(currentLeftMargin, targetLeftMargin);
        animator.setDuration(300); // 动画持续时间（单位：毫秒）
        animator.setInterpolator(new AccelerateDecelerateInterpolator()); // 设置插值器，动画更平滑
        animator.addUpdateListener(animation -> {
            // 更新 leftMargin
            params.leftMargin = (int) animation.getAnimatedValue();
            indicatorView.setLayoutParams(params);
        });

        // 启动动画
        animator.start();

        // 确保指示器可见
        indicatorView.setVisibility(View.VISIBLE);
    }


    /*点击事件*/
    public void onClick(View v) {
        if (v.getId() == R.id.back_ll) { // 点击左上角返回按钮
            finish();

        } else if (v.getId() == R.id.click_submit) { // 点击底部提交按钮
            submitCheck();

        } else if (v.getId() == R.id.pointStoreHead_ll) {
            showPointStoreHeadUploadOptions();

        } else if (v.getId() == R.id.follow_by) {
            findFollowBy();

        } else if (v.getId() == R.id.store_type_name) {
            isMainTab = true;
            findLabel();

        } else if (v.getId() == R.id.sub_store_type_name) {
            isMainTab = false;
            findLabel();

        } else if (v.getId() == R.id.store_level) {
            showStoreLevelDialog();

        } else if (v.getId() == R.id.store_attribute) {
            showStoreAttributeDialog();

        } else if (v.getId() == R.id.business_plan_name) {
            findBusinessArea();

        } else if (v.getId() == R.id.address) {
            handleSelectAddress();

        } else if (v.getId() == R.id.addlandlord_btn) { // 添加房东
            gotoLard();

        } else if (v.getId() == R.id.draft_delete_tv) {
            handleDeleteDraft();

        } else if (v.getId() == R.id.draft_open_tv) {
            handleUseDraft();
        }


    }


    private void handleSelectAddress() {

        Intent intent = new Intent(this, MapAddressActivity.class);
        if (storePlanData.getInfo() != null) {
            intent.putExtra("add_point_longitude", storePlanData.getInfo().getLongitude());
            intent.putExtra("add_point_latitude", storePlanData.getInfo().getLatitude());
        }

        intent.putExtra("actionType", "changeAddress");


//        startActivityForResult(intent, POI_DIALOG_REQUEST_CODE);
        startActivity(intent);

    }


    //    点击提交
    private void submitCheck() {
        Map<String, Object> data = getSubmitData();
        Log.d("TAG", "变动项里面存的数据======" + data);

//        基本信息校验
        if (TextUtils.isEmpty(nameET.getText())) {
            if (null != basicCheckDic.get("basic_info_store_plan_name")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_store_plan_name");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("店铺名称为必填项");
                    contentSv.smoothScrollTo(0, 0);
                    return;
                }
            }
        }


        if (TextUtils.isEmpty(fullAddressET.getText())) {
            if (null != basicCheckDic.get("basic_info_full_address")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_full_address");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("店铺详细地址为必填项");
                    contentSv.smoothScrollTo(0, 0);
                    return;
                }
            }
        }

        if (TextUtils.isEmpty(followByTV.getText()) || followByTV.getText().equals("请选择")) {
            if (null != basicCheckDic.get("basic_info_follow_name")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_follow_name");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("跟进人为必选项");
                    contentSv.smoothScrollTo(0, 0);
                    return;
                }
            }
        }

        if (TextUtils.isEmpty(businessPlanNameTV.getText()) || businessPlanNameTV.getText().equals("请选择")) {
            if (null != basicCheckDic.get("basic_info_business_plan_name")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_business_plan_name");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("所属商圈为必选项");
                    contentSv.smoothScrollTo(0, 0);
                    return;
                }
            }
        }

        if (TextUtils.isEmpty(storeLevelTV.getText()) || storeLevelTV.getText().equals("请选择")) {
            if (null != basicCheckDic.get("basic_info_store_level")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_store_level");
                boolean required = false, display = false;
                if (null != map.get("required")) {
                    required = (boolean) map.get("required");
                }
                if (null != map.get("display")) {
                    display = (boolean) map.get("display");
                }
                if (required && display) {
                    showMsg("店铺等级为必选项");
                    contentSv.smoothScrollTo(0, 0);
                    return;
                }
            }
        }

        if (TextUtils.isEmpty(storeAttributesTV.getText()) || storeAttributesTV.getText().equals("请选择")) {
            if (null != basicCheckDic.get("basic_info_store_attributes")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_store_attributes");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("店铺属性为必选项");
                    contentSv.smoothScrollTo(0, 0);
                    return;
                }
            }
        }

        if (TextUtils.isEmpty(storeTypeTV.getText()) || storeTypeTV.getText().equals("请选择")) {
            if (null != basicCheckDic.get("basic_info_store_type")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_store_type");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("店铺类型主标签为必选项");
                    contentSv.smoothScrollTo(0, 0);
                    return;
                }
            }
        }

        if (TextUtils.isEmpty(subStoreTypeTV.getText()) || subStoreTypeTV.getText().equals("请选择")) {
            if (null != basicCheckDic.get("basic_info_sub_store_type")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_sub_store_type");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("店铺类型副标签为必选项");
                    contentSv.smoothScrollTo(0, 0);
                    return;
                }
            }
        }


        if (flippedStoreRG.getCheckedRadioButtonId() == -1) {
            if (null != basicCheckDic.get("basic_info_flipped_store")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_flipped_store");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("是否翻牌门店为必选项");
                    scrollToTargerView(findViewById(R.id.flipped_store));
                    return;
                }
            }

        }

        if (storeAreaTypeRG.getCheckedRadioButtonId() == -1) {
            if (null != basicCheckDic.get("basic_info_store_area_type")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_store_area_type");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("店铺区县类型为必选项");
                    scrollToTargerView(findViewById(R.id.store_area_type));
                    return;
                }
            }
        }

        Log.d("TAG", "门头照=====" + storeHeadFile);
        if (storeHeadFile == null) {
            if (null != basicCheckDic.get("basic_info_store_head_file")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_store_head_file");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("门头正面照为必传项");
                    scrollToTargerView(findViewById(R.id.pointStoreHead));
                    return;
                }
            }

        }

        CustomUpload storeBrandFrontUpload = uploadCollection.getCustomUpload("storeBrandFront");
        List<FileBean> storeBrandFront = new ArrayList<FileBean>();
        if (storeBrandFrontUpload != null) {
            storeBrandFront = storeBrandFrontUpload.getFiles();
        }
        if (storeBrandFrontUpload == null || storeBrandFront == null || storeBrandFront.isEmpty()) {
            if (null != basicCheckDic.get("basic_info_brand_front_file")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_brand_front_file");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("店铺近景为必传项");
                    scrollToTargerView(findViewById(R.id.storeBrandFront));
                    return;
                }
            }
        }


        CustomUpload storeBrandLeftUpload = uploadCollection.getCustomUpload("storeBrandLeft");
        List<FileBean> storeBrandLeft = new ArrayList<FileBean>();
        if (storeBrandLeftUpload != null) {
            storeBrandLeft = storeBrandLeftUpload.getFiles();
        }

        if (storeBrandLeftUpload == null || storeBrandLeft == null || storeBrandLeft.isEmpty()) {
            if (null != basicCheckDic.get("basic_info_brand_left_file")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_brand_left_file");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("店铺中景为必传项");
                    scrollToTargerView(findViewById(R.id.storeBrandLeft));
                    return;
                }
            }
        }

        CustomUpload storeBrandRightUpload = uploadCollection.getCustomUpload("storeBrandRight");
        List<FileBean> storeBrandRight = new ArrayList<FileBean>();
        if (storeBrandRightUpload != null) {
            storeBrandRight = storeBrandRightUpload.getFiles();
        }

        if (storeBrandRightUpload == null || storeBrandRight == null || storeBrandRight.isEmpty()) {
            if (null != basicCheckDic.get("basic_info_brand_right_file")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_brand_right_file");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("店铺远景为必传项");
                    scrollToTargerView(findViewById(R.id.storeBrandRight));
                    return;
                }
            }
        }

        CustomUpload inRoomUpload = uploadCollection.getCustomUpload("inRoom");
        List<FileBean> inRoom = new ArrayList<FileBean>();
        if (inRoomUpload != null) {
            inRoom = inRoomUpload.getFiles();
        }

        if (inRoomUpload == null || inRoom == null || inRoom.isEmpty()) {
            if (null != basicCheckDic.get("basic_info_in_room_file")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_in_room_file");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("室内图片视频为必传项");
                    scrollToTargerView(findViewById(R.id.inRoom));
                    return;
                }
            }
        }

        CustomUpload businessDistircitEnviromentUpload = uploadCollection.getCustomUpload("businessDistircitEnviroment");
        List<FileBean> businessDistircitEnviroment = new ArrayList<FileBean>();
        if (businessDistircitEnviromentUpload != null) {
            businessDistircitEnviroment = businessDistircitEnviromentUpload.getFiles();
        }
        if (businessDistircitEnviromentUpload == null || businessDistircitEnviroment == null || businessDistircitEnviroment.isEmpty()) {
            if (null != basicCheckDic.get("basic_info_environment_file")) {
                HashMap<String, Object> map = (HashMap<String, Object>) basicCheckDic.get("basic_info_environment_file");
                boolean required = (boolean) map.get("required");
                boolean display = (boolean) map.get("display");
                if (required && display) {
                    showMsg("周边环境视频为必传项");
                    scrollToTargerView(findViewById(R.id.businessDistircitEnviroment));
                    return;
                }
            }
        }


//        校验房东
        if (isAddLandlord && landlordList.isEmpty()) {
            showMsg("房东信息为必填项");
            scrollToTargerView(findViewById(R.id.addlandlord_indicator));
            return;
        }


//        校验底部变动项
        boolean changeItemReque = false;
        for (ItemChangeBean bean : changeBeanList) {

            List<ItemChangeContentDetailBean> list = bean.getDetails();
            for (int i = 0; i < list.size(); i++) {
                ItemChangeContentDetailBean changeItem = list.get(i);
                if (changeItem.getComponent_type().equals("TABLE")) {
                    List<ItemChangeContentDetailBean> tableList = changeItem.getDetails();
                    List<Map<String, Object>> tableResults = (List<Map<String, Object>>) data.get(changeItem.getId());
                    if (tableResults != null && tableResults.size() > 0) {
                        for (int index = 0; index < tableResults.size(); index++) {
                            Map<String, Object> map = tableResults.get(index);
                            for (ItemChangeContentDetailBean tableItem : tableList) {
                                String key = changeItem.getId() + "." + index + "." + tableItem.getId();

                                if (tableItem.isRequired()) {
                                    if (tableItem.getComponent_type().equals("AMOUNT") || tableItem.getComponent_type().equals("DATE_TIME") || tableItem.getComponent_type().equals("LONG_TEXT") || tableItem.getComponent_type().equals("DATE") || tableItem.getComponent_type().equals("TEXT") || tableItem.getComponent_type().equals("NUMBER") || tableItem.getComponent_type().equals("SINGLE_CHOICE") || tableItem.getComponent_type().equals("SINGLE_RADIO")) {
                                        String itemValue = map.get(tableItem.getId()) != null ? String.valueOf(map.get(tableItem.getId())) : null;
                                        boolean display = false;
                                        if (map.get((key + "_show")) != null) {
                                            display = (boolean) map.get((key + "_show"));

                                        }

                                        if (TextUtils.isEmpty(itemValue) && display) {
                                            String taastString = tableItem.getTitle() + "为必填项";
                                            showMsg(taastString);
                                            changeItemReque = true;
                                            break;
                                        }
                                    } else if (tableItem.getComponent_type().equals("LOCATION")) {
                                        Object itemValue = map.get(tableItem.getId()) != null ? map.get(tableItem.getId()) : null;

                                        boolean display = false;
                                        if (data.get((key + "_show")) != null) {
                                            display = (boolean) data.get((key + "_show"));
                                        }

                                        Log.i("SUBMIT", "changeItem.getId() = " + changeItem.getId());
                                        Log.i("SUBMIT", "changeItem.display = " + display + "itemValue = " + itemValue);
                                        Log.i("SUBMIT", "data.get(changeItem.getId()) = " + data.get(changeItem.getId()) + " isString" + (data.get(changeItem.getId()) instanceof String));

                                        if (itemValue == null && display) {
                                            String taastString = tableItem.getTitle() + "为必填项";
                                            showMsg(taastString);
                                            changeItemReque = true;
                                            break;
                                        }
                                    } else {
                                        List itemValue = (List) map.get(tableItem.getId());
                                        boolean display = false;
                                        if (data.get((key + "_show")) != null) {
                                            display = (boolean) data.get(key + "_show");
                                        }

                                        if ((itemValue == null || itemValue.isEmpty()) && display) {
                                            String taastString = tableItem.getTitle() + "为必填项";
                                            showMsg(taastString);
                                            changeItemReque = true;
                                            break;
                                        }

                                    }
                                }
                            }
                        }
                    }

                } else if (changeItem.isRequired()) {//只有必填的才校验

                    if (changeItem.getComponent_type().equals("AMOUNT") || changeItem.getComponent_type().equals("DATE_TIME") || changeItem.getComponent_type().equals("LONG_TEXT") || changeItem.getComponent_type().equals("DATE") || changeItem.getComponent_type().equals("TEXT") || changeItem.getComponent_type().equals("NUMBER") || changeItem.getComponent_type().equals("SINGLE_CHOICE") || changeItem.getComponent_type().equals("SINGLE_RADIO")) {

                        String itemValue = data.get(changeItem.getId()) != null ? String.valueOf(data.get(changeItem.getId())) : null;
                        boolean display = false;
                        if (data.get((changeItem.getId() + "_show")) != null) {
                            display = (boolean) data.get((changeItem.getId() + "_show"));
                        }

                        Log.i("SUBMIT", "changeItem.getId() = " + changeItem.getId());
                        Log.i("SUBMIT", "changeItem.display = " + display + "itemValue = " + itemValue);
                        Log.i("SUBMIT", "data.get(changeItem.getId()) = " + data.get(changeItem.getId()) + " isString" + (data.get(changeItem.getId()) instanceof String));

                        if (TextUtils.isEmpty(itemValue) && display) {
                            String taastString = changeItem.getTitle() + "为必填项";
                            showMsg(taastString);
                            changeItemReque = true;
                            break;
                        }
                    } else if (changeItem.getComponent_type().equals("LOCATION")) {
                        Object itemValue = data.get(changeItem.getId()) != null ? data.get(String.valueOf(changeItem.getId())) : null;

                        boolean display = false;
                        if (data.get((changeItem.getId() + "_show")) != null) {
                            display = (boolean) data.get((changeItem.getId() + "_show"));
                        }

                        Log.i("SUBMIT", "changeItem.getId() = " + changeItem.getId());
                        Log.i("SUBMIT", "changeItem.display = " + display + "itemValue = " + itemValue);
                        Log.i("SUBMIT", "data.get(changeItem.getId()) = " + data.get(changeItem.getId()) + " isString" + (data.get(changeItem.getId()) instanceof String));

                        if (itemValue == null && display) {
                            String taastString = changeItem.getTitle() + "为必填项";
                            showMsg(taastString);
                            changeItemReque = true;
                            break;
                        }
                    } else {

                        Log.i("SUBMIT", "changeItem.getId() = " + changeItem.getId());

//                        Gson gson = new Gson();
//
//                        Type listType = new TypeToken<ArrayList<Map<String, Object>>>() {}.getType();
//
//                        List<CustomComponentItemAdapter.Option<String>> itemValue = gson.fromJson(gson.toJson(data.get(changeItem.getId())), listType);;

                        List itemValue = (List) data.get(changeItem.getId());
                        boolean display = false;
                        if (data.get((changeItem.getId() + "_show")) != null) {
                            display = (boolean) data.get((changeItem.getId() + "_show"));
                        }

                        if ((itemValue == null || itemValue.isEmpty()) && display) {
                            String taastString = changeItem.getTitle() + "为必填项";
                            showMsg(taastString);
                            changeItemReque = true;
                            break;
                        }

                    }


                }

            }

            if (changeItemReque) {
                break;
            }

        }

        if (changeItemReque) {
            return;
        }


        showSubmitDialog();

    }


    private void showSubmitDialog() {

        View.OnClickListener onConfimClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                initSubmit();

            }
        };
        showInfoDeleteDialog(onConfimClickListener, "确定要提交吗?");
    }

    protected void showInfoDeleteDialog(View.OnClickListener confirmOnClick, String str) {
        DialogTemplateView.Builder builder = new DialogTemplateView.Builder(this);
        builder.setButtonConfirm("确定", confirmOnClick);
        builder.setTitle(str);
        DialogTemplateView dialog = builder.create();
        dialog.show();
    }

    private void initSubmit() {
        Intent intent = getIntent();
        Map<String, Object> data = getSubmitData();
        data.put("landlords", landlordList);

        data.put("id", storePlanData.getId());
        data.put("area_code", storePlanData.getArea_code());

        String orgId = String.valueOf(receivedMap.get("org_id"));
        String name = (String) receivedMap.get("org_name");
        if (!TextUtils.isEmpty(orgId)) {
            data.put("org_id", orgId);
            data.put("org_name", name);
        }

        HashMap<String, Object> subMap = new HashMap<>(data);
        Map bodyData = ViewUtils.convertToMap(storePlanData);

        HashMap body = new HashMap(bodyData);
        body.put("content", subMap.get("content"));

        MapStoreEditRequest requestBody = new MapStoreEditRequest();
        requestBody.setContent(storePlanData.getContent());
        requestBody.setCompetitor_mark_ids(storePlanData.getCompetitor_mark_ids());
        requestBody.setId(storePlanData.getId());

        requestBody.setInfo(storePlanData.getInfo());
        requestBody.setAddress(storePlanData.getAddress());
        requestBody.setArea_name(storePlanData.getArea_name());
        requestBody.setArea_code(storePlanData.getArea_code());
        requestBody.setSub_store_type_id(storePlanData.getSub_store_type_id());
        requestBody.setSub_store_type_name(storePlanData.getSub_store_type_name());
        requestBody.setStore_type_id(storePlanData.getStore_type_id());
        requestBody.setStore_type_name(storePlanData.getStore_type_name());
        requestBody.setName(storePlanData.getName());
        requestBody.setUpdate_by(storePlanData.getUpdate_by());
        requestBody.setUpdate_time(storePlanData.getUpdate_time());
        requestBody.setSecond_audit_by(storePlanData.getSecond_audit_by());
        requestBody.setSecond_audit_time(storePlanData.getSecond_audit_time());
        requestBody.setReject_by(storePlanData.getReject_by());
        requestBody.setReject_time(storePlanData.getReject_time());
        requestBody.setOutside(storePlanData.isOutside());
        requestBody.setInit_by(storePlanData.getInit_by());
        requestBody.setInit_time(storePlanData.getInit_time());
        requestBody.setFull_address(storePlanData.getFull_address());
        requestBody.setFirst_audit_by(storePlanData.getFirst_audit_by());
        requestBody.setFirst_audit_time(storePlanData.getFirst_audit_time());
        requestBody.setDeadline_date(storePlanData.getDeadline_date());
        requestBody.setAll_files(storePlanData.getAll_files());
        requestBody.setIs_share(storePlanData.isIs_share());
        requestBody.setFlag_all_files(storePlanData.getFlag_all_files());

//            requestBody.setLatitude(storePlanData.getLatitude());

        requestBody.setFiles(storePlanData.getFiles());

        requestBody.setStore_area_type(storePlanData.getStore_area_type());
        requestBody.setStore_attributes(storePlanData.getStore_attributes());
        requestBody.setStore_level(storePlanData.getStore_level());
        requestBody.setAllow_look(storePlanData.isAllow_look());
        requestBody.setFlipped_store(storePlanData.isFlipped_store());
        requestBody.setBusiness_plan_id(storePlanData.getBusiness_plan_id());
        requestBody.setBusiness_plan_name(storePlanData.getBusiness_plan_name());
        requestBody.setClient_name(storePlanData.getClient_name());
        requestBody.setCompany_id(storePlanData.getCompany_id());
        requestBody.setStore_apply_fid(storePlanData.getStore_apply_fid());
        requestBody.setStore_apply_name(storePlanData.getStore_apply_name());
        requestBody.setSource(storePlanData.getSource());
        requestBody.setState(storePlanData.getState());
        requestBody.setSign_type(storePlanData.getSign_type());

        requestBody.setResult(storePlanData.getResult());
        requestBody.setReject_by(storePlanData.getReject_by());

        requestBody.setOpen_store(storePlanData.getOpen_store());
        requestBody.setFollow_by(storePlanData.getFollow_by());
        requestBody.setFollow_time(storePlanData.getFollow_time());
        requestBody.setFollow_id(storePlanData.getFollow_id());
        requestBody.setState(storePlanData.getState());
        requestBody.setStore_valuation(storePlanData.getStore_valuation());
        requestBody.setFollow_state(storePlanData.getFollow_state());
        requestBody.setFollow_count(storePlanData.getFollow_count());
        Gson gson = new Gson();
        List<LandlordBean> curLandlords = gson.fromJson(gson.toJson(landlordList), new TypeToken<List<LandlordBean>>() {
        }.getType());
        if (curLandlords != null) {
            requestBody.setLandlords(curLandlords);
        }

        Log.i("UPDATE_POINT", "subMap::allBody: " + subMap.get("content"));

        requestBody.setId(storePlanData.getId());
        requestBody.setArea_code(storePlanData.getArea_code());
        requestBody.setOrg_id((long) receivedMap.get("org_id"));
        requestBody.setOrg_name((String) receivedMap.get("org_name"));


        Log.i("UPDATE_POINT", "getContent: " + intent.getBooleanExtra("isStartApply", false));
        if (intent.hasExtra("isStartApply") && intent.getBooleanExtra("isStartApply", false)) {
            Log.i("UPDATE_POINT", "update: " + "startApply");

            mPresenter.startApplyPoint(requestBody);
        } else if (data.containsKey("isCopy") && data.get("isCopy") != null && (boolean) data.get("isCopy")) {
            mPresenter.storeCopyPOST(subMap);
        } else {
            Log.i("UPDATE_POINT", "update: " + requestBody.getFlag_all_files());
//
            mPresenter.updatePoint(requestBody);
        }
    }

    @Override
    public void submitPointPOST(BaseResponse reponse) {

        hideLoadingDialog();
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0 && reponse.data != null) {
            showMsg("提交成功");
            finish();
        }
    }

    @Override
    public void submitPointPOSTFailed(Throwable e) {
        if (e != null) {
            //服务器异常
            ExceptionHandle.ResponseThrowable responseThrowable = (ExceptionHandle.ResponseThrowable) e;
            String msg = responseThrowable.msg;
            //String msg = e.getMessage();
            if (!TextUtils.isEmpty(msg)) {
                showMsg(msg);
            }
        } else {
            showMsg("编辑点位失败");
        }
    }

    @Override
    public void updatePointPOST(BaseResponse reponse) {
        if (reponse != null && reponse.code == 0) {

            showMsg("更新成功");
            EventBus.getDefault().post(new MessageEvent("UPDATE_POINT_DETAIL"));
            finish();


        } else {
            showMsg(!TextUtils.isEmpty(reponse.msg) ? reponse.msg : "更新失败");
        }

    }

    @Override
    public void updatePointPOSTFailed(Throwable e) {
        if (e != null) {
            //服务器异常
            ExceptionHandle.ResponseThrowable responseThrowable = (ExceptionHandle.ResponseThrowable) e;
            String msg = responseThrowable.msg;
            // String msg = e.getMessage();
            if (!TextUtils.isEmpty(msg)) {
                showMsg(msg);
            }
        } else {
            showMsg("更新失败");
        }
    }

    @Override
    public void getPointPOST(MapStorePlanDetailReponse reponse) {

        if (reponse != null && reponse.code == 0 && reponse.data != null) {
            storePlanData = reponse.data;
            dataMap = ViewUtils.convertToMap(reponse.data);

            updateViewData(dataMap);
        }

    }


    @Override
    public void getPointPOSTFailed(Throwable e) {

    }

    @Override
    public void startApplyPointPOST(BaseResponse reponse) {
        if (reponse != null && reponse.code == 0) {

            showMsg("发起审批成功");
            EventBus.getDefault().post(new MessageEvent("UPDATE_POINT_DETAIL"));
            finish();

        } else {
            showMsg(!TextUtils.isEmpty(reponse.msg) ? reponse.msg : "发起审批失败");
        }
    }

    @Override
    public void startApplyPointPOSTFailed(Throwable e) {
        if (e != null) {
            //服务器异常
            ExceptionHandle.ResponseThrowable responseThrowable = (ExceptionHandle.ResponseThrowable) e;
            String msg = responseThrowable.msg;
            // String msg = e.getMessage();
            if (!TextUtils.isEmpty(msg)) {
                showMsg(msg);
            }
        } else {
            showMsg("发起审批失败");
        }
    }

    @Override
    public void storePlanCopyPOST(BaseResponse reponse) {

        Log.i("storePlanCopyPOST", "storePlanCopyPOST: " + reponse);

        if (reponse != null && reponse.code == 0) {

            showMsg("复制成功");
//            EventBus.getDefault().post(new MessageEvent("UPDATE_POINT_DETAIL"));
            finish();

        } else {
            showMsg(!TextUtils.isEmpty(reponse.msg) ? reponse.msg : "复制失败");
        }

    }

    @Override
    public void storePlanCopyPOSTFailed(Throwable e) {
        Log.i("storePlanCopyPOST", "storePlanCopyPOSTFailed: " + e.getMessage());
        if (e != null) {
            //服务器异常
            ExceptionHandle.ResponseThrowable responseThrowable = (ExceptionHandle.ResponseThrowable) e;
            String msg = responseThrowable.msg;
            // String msg = e.getMessage();
            if (!TextUtils.isEmpty(msg)) {
                showMsg(msg);
            }
        } else {
            showMsg("复制失败");
        }

    }

    @Override
    public void storePlanReadPOST(MapStorePlanDetailReponse reponse) {
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        String msg = reponse.msg;
        if (code == 0) {
            storePlanData = reponse.data;
            dataMap = ViewUtils.convertToMap(reponse.data);
            updateViewData(dataMap);
        }
    }

    @Override
    public void storePlanReadPOSTFailed(Throwable e) {

    }

    private void scrollToTargerView(View view) {

        int[] screenLocation = new int[2];
        view.getLocationOnScreen(screenLocation);
        int[] scrollOrigin = new int[2];
        contentSv.getLocationOnScreen(scrollOrigin);
        int relativeY = screenLocation[1] - scrollOrigin[1];
        int currentScrollY = contentSv.getScrollY();
        int targetScrollY = relativeY + currentScrollY - dpToPx(94);
        contentSv.smoothScrollTo(0, targetScrollY);
    }

    //    跳转到房东信息页面
    private void gotoLard() {
        Intent mapIntent = new Intent(this, MapAddLandlordActivity.class);
        mapIntent.putExtra("org_id", String.valueOf(receivedMap.get("org_id")));
//        int id = ((Double) receivedMap.get("id")).intValue();
        mapIntent.putExtra("point_id", storePlanData.getId());
        startActivity(mapIntent);
    }

    private void findFollowBy() {
        Log.i("MapAddPointActivity", "findFollowBy: ");
        HashMap<String, Object> body = new HashMap<>();

        body.put("company_id", companyId);
        body.put("org_id", storePlanData.getOrg_id());
        mPresenter.getFollowByPOST(body);
    }

    private void getStorePlanDetail() {

        MapStorePlanDetailRequest body = new MapStorePlanDetailRequest();
        body.setCompany_id(companyId);
        body.setId(storePlanId);
        mPresenter.storePlanReadPOST(body);

    }

    private void findBusinessArea() {

        Log.i("TAG", "findBusinessArea: ");
        if (storePlanData == null || storePlanData.getInfo() == null) {
            return;
        }
        if (storePlanData.getInfo() != null && TextUtils.isEmpty(String.valueOf(storePlanData.getInfo().getLongitude()))) {
            showMsg("请选择地址");
            return;
        }

        BusinessAreaLatlngRequest body = new BusinessAreaLatlngRequest();
        body.setCompany_id(companyId);
        body.setStates(new ArrayList<String>() {{
            add("SECOND_AUDIT");
        }});

        body.setLatlng(new LatLng(storePlanData.getInfo().getLatitude(), storePlanData.getInfo().getLongitude()));

        if (receivedMap != null && receivedMap.get("org_id") != null) {
            body.setOrg_id(String.valueOf(receivedMap.get("org_id")));
        }

        Log.i("TAG", "findBusinessArea: " + new Gson().toJson(body));
        mPresenter.getBusinessAreaPOST(body);
    }

    private void findLabel() {
        HashMap<String, Object> body = new HashMap<>();

        body.put("company_id", companyId);
        body.put("type", "TYPE");
        body.put("enable", true);
        if (receivedMap != null && receivedMap.get("org_id") != null) {
            body.put("org_id", String.valueOf(receivedMap.get("org_id")));
        }
        Log.i("FindLabel", "findLabelParams: " + new Gson().toJson(body));
        mPresenter.getLabelsPOST(body);
    }

    private void showStoreLevelDialog() {
        List<Map<String, String>> items = new ArrayList<>();

        items.add(createItem("1", "第一顺位"));
        items.add(createItem("2", "第二顺位"));
        items.add(createItem("3", "第三顺位"));
        items.add(createItem("4", "第四顺位"));
        BottomSheetDialogHelper.showSingleSelectBottomSheetDialog(
                this,
                "请选择等级",
                items,
                storePlanData.getStore_level() != null ? createItem(storePlanData.getStore_level(), "第二顺位") : null, // 可以为 null
                (id, name) -> {
                    if (!TextUtils.isEmpty(name)) {
                        storeLevelTV.setText(name);
                    }

                    storePlanData.setStore_level(id);

//                    System.out.println("选中了: ID = " + id + ", Name = " + name);
                }
        );

    }

    private void showStoreAttributeDialog() {

        List<Map<String, String>> items = new ArrayList<>();


        items.add(createItem("STANDARD", "标准店"));
        items.add(createItem("COMPREHENSIVE", "综合店"));
        items.add(createItem("FLAGSHIP", "旗舰店"));

        BottomSheetDialogHelper.showSingleSelectBottomSheetDialog(this, "请选择店铺类型", items, storePlanData.getStore_attributes() != null ? createItem(storePlanData.getStore_attributes(), "") : null, (id, name) -> {
            if (!TextUtils.isEmpty(name)) {
                storeAttributesTV.setText(name);
            }
            storePlanData.setStore_attributes(id);
        });
    }

    private void showPointStoreHeadUploadOptions() {
        BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(context);
        View bottomSheetView = LayoutInflater.from(context).inflate(R.layout.upload_bottom_sheet_upload_options, null);
        bottomSheetDialog.setContentView(bottomSheetView);
        bottomSheetView.findViewById(R.id.take_photo).setVisibility(View.VISIBLE);
        bottomSheetView.findViewById(R.id.choose_image).setVisibility(View.VISIBLE);
        bottomSheetView.findViewById(R.id.take_photo).setOnClickListener(v -> {
            pickerUtil.captureImage();
            bottomSheetDialog.dismiss();
        });
        bottomSheetView.findViewById(R.id.choose_image).setOnClickListener(v -> {
            pickerUtil.pickImagesFromGallery();
            bottomSheetDialog.dismiss();
        });
        bottomSheetView.findViewById(R.id.cancel).setOnClickListener(v -> bottomSheetDialog.dismiss());
        bottomSheetDialog.show();

    }

    private void handleStoreHeadResult(
            PickUtilResultContract.ActivityResultWithExtra result
    ) {
        PickerUtil.PickerResult pickerResult = pickerUtil.handleActivityResult(result);
        List<Uri> selectedUris = pickerResult.uris;
        Map<String, Object> query = new HashMap<>();
        query.put("fileType", "pointStoreHead");
//        if (receivedMap != null && receivedMap.containsKey("id")) {
//            int id = ((Double) receivedMap.get("id")).intValue();
//            query.put("fid", id);
//        }
        query.put("fid", storePlanId);
        for (Uri uri : selectedUris) {
            File file = pickerUtil.getFileFromUri(uri);
            if (file != null) {

                handleUploadFile(file, query);
            } else {
                Toast.makeText(context, "File not found", Toast.LENGTH_SHORT).show();
            }
        }

    }


    private void showSingeDialog() {
        List<Map<String, String>> items = new ArrayList<>();
        items.add(createItem("1", "选项 1"));
        items.add(createItem("2", "选项 2"));
        items.add(createItem("3", "选项 3"));
        items.add(createItem("4", "选项 4"));
        String lastSelectedId = null; // 记录上一次选中的 ID
        BottomSheetDialogHelper.showSingleSelectBottomSheetDialog(
                this,
                "请选择一个选项",
                items,
                createItem("2", "选项 2"), // 可以为 null
                (id, name) -> {

                    System.out.println("选中了: ID = " + id + ", Name = " + name);
                }
        );
    }


    private void showMultiDialog() {
        List<Map<String, String>> items = new ArrayList<>();
        items.add(createItem("1", "选项 1"));
        items.add(createItem("2", "选项 2"));
        items.add(createItem("3", "选项 3"));

        List<Map<String, String>> preSelectedItems = new ArrayList<>();
        preSelectedItems.add(createItem("2", "选项 2"));
        preSelectedItems.add(createItem("3", "选项 3"));

        BottomSheetDialogHelper.showMultiSelectBottomSheetDialog(
                this,
                "请选择多个选项",
                items,
                preSelectedItems,
                selectedItems -> {
                    // 返回选中的项集合
                    System.out.println("选中的项集合: " + selectedItems);
                }
        );

    }

    private void showDatePicker() {

//        BottomSheetDialogHelper.showBottomSheetDatePicker(
//                this,
//                BottomSheetDialogHelper.DatePickerMode.YEAR_ONLY,
//                (year, month, day) -> {
//                    System.out.println("选中的年份是: " + year);
//                }
//        );

//        BottomSheetDialogHelper.showBottomSheetDatePicker(
//                this,
//                BottomSheetDialogHelper.DatePickerMode.YEAR_MONTH,
//                (year, month, day) -> {
//                    System.out.println("选中的年月是: " + year + "-" + month );
//                }
//        );

        BottomSheetDialogHelper.showBottomSheetDatePicker(
                this,
                BottomSheetDialogHelper.DatePickerMode.YEAR_MONTH_DAY,
                (year, month, day) -> {
                    System.out.println("选中的年月日: " + year + "-" + month + "-" + day);
                }
        );
    }

    private void handleBaseInfoDisplayAndRequire() {
        // 名称


    }

    private Map<String, String> createItem(String id, String name) {
        Map<String, String> item = new HashMap<>();
        item.put("id", id);
        item.put("name", name);
        return item;
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        //注销当前Subscriber
        EventBus.getDefault().unregister(this);
        uploadCollection.removeAllCustomUpload();

    }

    private int dpToPx(int dp) {
        return (int) (dp * getResources().getDisplayMetrics().density);
    }

    private Map<String, Object> getSubmitData() {
        /*
         *  数据来源三个地方 1. storePlanData 2. dataMap 3. uploadCollection
         *  1 的数据包含基本信息
         *  2 的数据包含变动项里面非视频图片的内容
         *  3 的数据包含变动项里面的视频图片的内容以及基本信息里面非点位门头的图片
         *  **/

//        handleBaseInfoValue();


        Map<String, List<FileBean>> filesMap = new HashMap<>();
        List<FileBean> files = new ArrayList<>();
        List<String> fileIds = new ArrayList<String>() {
            {
                add("storeBrandLeft");
                add("storeBrandRight");
                add("storeBrandFront");
                add("businessDistircitEnviroment");
                add("storeBrandTop");
                add("inRoom");
            }
        };

        for (Map.Entry<String, CustomUpload> entry : uploadCollection.getCustomUploadMap().entrySet()) {
            String key = entry.getKey();

            CustomUpload customUpload = entry.getValue();

            String fieldName = customUpload.getFieldName();

            Log.i("SUBMIT_DATA", "fieldName:" + fieldName);


            List<FileBean> fileBeans = customUpload.getFiles();
            if (fileIds.contains(fieldName)) {
                files.addAll(fileBeans);
            } else {
                filesMap.put(fieldName, fileBeans);
            }

        }
        ;
        if (storeHeadFile != null) {
            files.add(storeHeadFile);
        }

        List<ContentBean> details = new ArrayList<ContentBean>();
        List<Map<String, Object>> contentDetails = new ArrayList<>();

        List<ContentBean> lastContentBeanList = storePlanData.getContent();

        Map<String, Map<String, Object>> lastContentMap = new HashMap<>();
        if (lastContentBeanList != null && lastContentBeanList instanceof List && !lastContentBeanList.isEmpty()) {
            for (ContentBean contentBean : lastContentBeanList) {
                lastContentMap.put(contentBean.getId(), contentBean.getDetails());
            }
        }

        for (int i = 0; i < changeBeanList.size(); i++) {
            ItemChangeBean itemChangeBean = changeBeanList.get(i);
            if (!itemChangeBean.isEnable()) {
                continue;
            }
            String itemName = itemChangeBean.getName();
            String id = String.valueOf(itemChangeBean.getId());
            Map<String, Object> obj = new HashMap<>();
            Map<String, Object> lastObj = new HashMap<>();

            if (lastContentMap.containsKey(id) && lastContentMap.get(id) != null) {
                lastObj = lastContentMap.get(id);
            }

            if (itemChangeBean.getDetails() != null) {
                for (ItemChangeContentDetailBean item : itemChangeBean.getDetails()) {
                    String detailId = item.getId();
                    if ("TABLE".equals(item.getComponent_type()) && dataMap.get(detailId) != null && dataMap.get(detailId) instanceof List) {

                        List<Map<String, Object>> tableList = (List<Map<String, Object>>) dataMap.get(detailId);
                        List<Map<String, Object>> lastTableList = new ArrayList<Map<String, Object>>();

                        if (lastObj != null && lastObj.containsKey(detailId) && lastObj.get(detailId) != null && lastObj.get(detailId) instanceof List) {
                            {
                                lastTableList = (List<Map<String, Object>>) lastObj.get(detailId);
                            }
                        }


                        Log.i("TABLE", "TABLE -> lastTableList: " + lastTableList);


                        List<ItemChangeContentDetailBean> curDetails = item.getDetails();
                        List<Map<String, Object>> tableResults = new ArrayList<Map<String, Object>>();
                        if (curDetails != null) {
                            for (int index = 0; index < tableList.size(); index++) {
                                Map<String, Object> resultMap = new HashMap<>();
                                // 还是需要把之前的数据补上去

                                for (ItemChangeContentDetailBean detail : curDetails) {
                                    String key = item.getId() + "." + index + "." + detail.getId();

                                    if ("LOCATION".equals(detail.getComponent_type())) {
                                        Log.i("submitCheck", "key: " + key);
                                        Log.i("submitCheck", "detailId: " + detail.getId());
                                        Log.i("submitCheck", "mapValue: " + dataMap.get(key));
                                        Log.i("submitCheck", "value: " + dataMap.get(key));

                                    }


                                    if (filesMap.containsKey(key)) {
                                        resultMap.put(detail.getId(), filesMap.get(key));

                                    } else {
                                        resultMap.put(detail.getId(), dataMap.get(key));
                                    }
                                }


                                if (index < lastTableList.size()) {
                                    Map<String, Object> lastResultMap = lastTableList.get(index);
                                    if (lastResultMap != null) {
                                        Log.i("TABLE", "TABLE -> lastResultMap: " + lastResultMap);
                                        lastResultMap.putAll(resultMap);
                                        tableResults.add(lastResultMap);

                                    } else {
                                        tableResults.add(resultMap);

                                    }

                                } else {
                                    tableResults.add(resultMap);

                                }


                            }
                        }

                        // 这里没有问题content已经复制成功但是我们还是有改一下dataMap里面的值，后面做校验能用到，当然如果修改校验的取值方式也可以解决问题
                        obj.put(detailId, tableResults);
                        dataMap.put(detailId, tableResults);
//                        Log.i("TABLE", "TABLE -> tableList: " + tableList);

                        Log.i("TABLE", "TABLE -> tableResults: " + tableResults);
                    } else if (dataMap.containsKey(detailId)) {
                        obj.put(detailId, dataMap.get(detailId));
                    } else if (filesMap.containsKey(detailId)) {
                        obj.put(detailId, filesMap.get(detailId));
                    }
                }
            }

            Log.i("TABLE", "TABLE -> obj: " + obj);

            Map<String, Object> contentDetail = new HashMap<String, Object>();
            contentDetail.put("id", id);
            contentDetail.put("name", itemName);
            if (lastObj != null) {
                lastObj.putAll(obj);
                contentDetail.put("details", lastObj);

            } else {
                contentDetail.put("details", obj);

            }
            Log.i("TABLE", "TABLE -> contentDetail: " + contentDetail);

            contentDetails.add(contentDetail);

            ContentBean contentBean = new ContentBean();
            contentBean.setId(id);
            contentBean.setName(itemName);
            if (lastObj != null) {
                lastObj.putAll(obj);
                contentBean.setDetails(lastObj);
            } else {
                contentBean.setDetails(obj);

            }
            Log.i("TABLE", "TABLE -> contentBean: details: " + contentBean.getDetails());

            details.add(contentBean);

        }

        Log.i("TABLE", "TABLE -> details: " + gson.toJson(details));
        Log.i("TABLE", "TABLE -> contentDetails: " + contentDetails);


        storePlanData.setContent(details);
        // 这里可能不会覆盖最新的content, 所以这里要手动赋值
        dataMap.put("content", contentDetails);
        storePlanData.setFiles(files);

        Log.i("SUBMIT_DATA", "dataMap: " + dataMap);


        Map<String, Object> submitData = ViewUtils.objectToMap(storePlanData);
        submitData.put("content", contentDetails);

        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (submitData.containsKey(key)) {
                Object existingValue = submitData.get(key);
                if (value != null && existingValue == null) {
                    submitData.put(key, value);
                } else {
                    submitData.put(key, existingValue);
                }
            } else {
                submitData.put(key, value);
            }
        }


//        submitData.putAll(dataMap);
        submitData.putAll(filesMap);
        Log.i("SUBMIT_DATA", "name:" + submitData.get("content"));

        Log.i("SUBMIT_DATA", "getSubmitData: " + new Gson().toJson(submitData) + "name:" + submitData.get("name") + "storePlanData" + storePlanData.getName());
        return submitData;
    }


    private void initChangeItem() {
        HashMap<String, Object> body = new HashMap<>();
        body.put("companyId", companyId);
        body.put("type", "STORE");
        body.put("store_apply", 0);
        if (dataMap.containsKey("isStartApply") && dataMap.get("isStartApply") != null) {
            body.put("approval", true);
        }
        if (receivedMap != null && receivedMap.get("org_id") != null) {
            body.put("org_id", String.valueOf(receivedMap.get("org_id")));
        }
//        if (receivedMap != null && receivedMap.get("id") != null) {
//            int id = ((Double) receivedMap.get("id")).intValue();
//            body.put("store_plan_id", id);
//        }
        body.put("store_plan_id", storePlanId);
        Log.d("TAG", "变动项传参" + body);
        mPresenter.addPointChangeItemPOST(body);
    }

    private void updateDraft() {
        Map<String, Object> data = getSubmitData();
        data.put("landlords", landlordList);
        draftUtils.update(data);
//        Log.i("DraftUtil", "contentChange"  + draftUtils.getLastRecord().toString());
//        if (draftUtils.getIsUsedDraft() || (draftUtils.getLastRecord() != null && draftUtils.getLastRecord().getInfo() != null && draftUtils.getLastRecord().getInfo().isEmpty())) {
//
//            Map<String, Object> data = getSubmitData();
//            data.put("landlords", landlordList);
//            draftUtils.update(data);
//
//        }

    }

    @Override
    public void addPointChangeItemPOST(ItemChangeReponse reponse) {
        hideLoadingDialog();
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0 && reponse.data != null) {

            Log.i("RES", "addPointChangeItemPOST: " + reponse.data);
            tabsList.clear();
            if (!changeableList.isEmpty()) {
                changeableList.clear();
            }

            if (!changeBeanList.isEmpty()) {
                changeBeanList.clear();
            }
            tabsList.add("基本信息");
            tabsList.add("房东信息");
            List<ItemChangeContentDetailBean> basicInfoList = new ArrayList<>();

            for (int i = 0; i < reponse.data.size(); i++) {
                ItemChangeBean itemChangeBean = reponse.data.get(i);
                if (!itemChangeBean.isEnable()) {
                    continue;
                }

                String itemName = itemChangeBean.getName();

                //跳过不兼容的变动项，如协同
                if (itemName.equals("协同品牌") || itemName.equals("竞品信息")) {
                    continue;
                }


                if (!itemName.equals("基本信息") && !itemName.equals("房东信息")) {
                    changeBeanList.add(itemChangeBean);
                    tabsList.add(itemName);
                }

//                处理基本信息必填显隐
                if (itemName.equals("基本信息")) {
                    basicInfoList = itemChangeBean.getDetails();
                    for (ItemChangeContentDetailBean item : basicInfoList) {
                        baseInfoRequireMap.put(item.getApi_name(), item.isRequired());
                        baseInfoShowMap.put(item.getApi_name(), true);
                    }
                }


                if (itemName.equals("房东信息")) {//判断房东信息是否必填
                    boolean isLandlordRequired = itemChangeBean.isStore_plan_required();
                    if (isLandlordRequired) {
                        isAddLandlord = true;
                    } else {
                        isAddLandlord = false;
                    }
                }


                changeableList.add(itemChangeBean);


            }


            Log.i("RES", "addPointChangeItemPOST: " + changeBeanList.size());

//            customComponentAdapter = new CustomComponentAdapter(mContext, changeBeanList, (JSONObject) JSON.toJSON(storePlanData), (view, item, value) -> {
//                //TODO 草稿回调使用
//            });

            initBaseInfoDisplayAndRequired();

            initTableInfo();


            runOnUiThread(() -> {

                        if (!changeBeanList.isEmpty()) {

                            changeableRV.setLayoutManager(new LinearLayoutManager(this));

                            if (customComponentAdapter == null) {
                                // 确保RecyclerView可见性
                                changeableRV.setVisibility(View.VISIBLE);

                                // 强制设置布局参数（防止高度异常）
                                changeableRV.getLayoutParams().height = ViewGroup.LayoutParams.MATCH_PARENT;
                                changeableRV.requestLayout();

                                if (filePickerLauncher != null) {
                                    customComponentAdapter = new CustomComponentAdapter(
                                            MapEditPointActivity.this, // 使用Activity Context
                                            changeBeanList,
                                            dataMap,
                                            filePickerLauncher,
                                            (view, item, value) -> {
                                                //TODO 草稿回调使用


                                            }
                                    );
                                }

                                changeableRV.setAdapter(customComponentAdapter);
                                changeableRV.setItemAnimator(new DefaultItemAnimator());

                            } else {
                                customComponentAdapter.updateData(changeBeanList); // 添加更新数据方法
                            }

                        }
                    }
            );
            if (tabsList.size() > 0) {
                initTabs();
            }

//            处理基本信息变动项
            if (basicInfoList != null && !basicInfoList.isEmpty()) {
                basicCheckDic.clear();

                for (ItemChangeContentDetailBean bean : basicInfoList) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("display", bean.isStore_plan_display());
                    hashMap.put("required", bean.isStore_plan_required());

                    basicCheckDic.put(bean.getApi_name(), hashMap);

                }

                Log.d("TAG", "基本信息=====" + basicCheckDic);
            }


        }
    }


    @Override
    public void addPointChangeItemPOSTFailed(Throwable e) {
        hideLoadingDialog();
        Log.i("TAG", "erroe" + e.getMessage().toString());
        showMsg("获取变动项失败");

    }

    @Override
    public void getFollowByPOST(BaseResponse reponse) {
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0 && reponse.data != null) {

//            转换返回的JSON对象
            Gson gson = new GsonBuilder()
                    .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE) // 更合理的处理方式
                    .create();
            String json = gson.toJson(reponse.data);
            Type typeToken = new TypeToken<List<Map<String, Object>>>() {
            }.getType();
            List<Map<String, Object>> list = gson.fromJson(json, typeToken);

            Log.i("MapAddPointActivity", "getFollowByPOST: " + json);

            List<Map<String, String>> items = new ArrayList();
            for (int i = 0; i < list.size(); i++) {
                Log.i("MapAddPointActivity", "getFollowByPOST: " + String.valueOf(((Double) list.get(i).get("id")).intValue()));
                items.add(createItem(String.valueOf(((Double) list.get(i).get("id")).intValue()), String.valueOf(list.get(i).get("name"))));
            }
            Map<String, String> selectedValue = new HashMap<>();

            if (storePlanData != null && storePlanData.getFollow_id() != 0) {
                selectedValue.put("id", String.valueOf(storePlanData.getFollow_id()));
            }
            if (storePlanData != null && storePlanData.getFollow_by() != null) {
                selectedValue.put("name", storePlanData.getFollow_by());
            }

            Log.i("MapAddPointActivity", "getFollowByPOST:items: " + gson.toJson(items));
            Log.i("MapAddPointActivity", "getFollowByPOST:selectedValue: " + gson.toJson(selectedValue));


            BottomSheetDialogHelper.showSingleSelectBottomSheetDialog(
                    this,
                    "请选择跟进人",
                    items,
                    selectedValue == null || (selectedValue != null && selectedValue.isEmpty()) ? null : selectedValue,
                    // 可以为 null
                    (id, name) -> {

                        Log.i("MapAddPointActivity", "follow_selected: " + id);
                        if (!TextUtils.isEmpty(name)) {

                            Map<String, Object> curData = Collections.emptyMap();
                            for (Map<String, Object> item : list) {
                                if (String.valueOf(((Double) item.get("id")).intValue()).equals(id)) {
                                    curData = item;
                                    break;
                                }

                            }

                            followByTV.setText(name);

                            storePlanData.setFollow_id(((Double) curData.get("id")).intValue());
                            storePlanData.setFollow_by(name);


                        }

                    }
            );
        }


    }

    @Override
    public void getFollowByPOSTFailed(Throwable e) {

    }

    @Override
    public void getBusinessAreaPOST(BaseResponse reponse) {

        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0 && reponse.data != null) {

//            转换返回的JSON对象
            Gson gson = new Gson();
            String json = gson.toJson(reponse.data);
            Log.i("TAG", "business" + json);
            Type typeToken = new TypeToken<List<Map<String, Object>>>() {
            }.getType();
            List<Map<String, Object>> list = gson.fromJson(json, typeToken);

            if (list.isEmpty()) {
                businessPlanNameTV.setText("根据地址匹配审批通过商圈");
            } else if (list.size() == 1) {

                Log.i("TAG", "findBusinessArea1" + list.get(0).get("name"));
                String nameText = String.valueOf(list.get(0).get("name"));
                if (!TextUtils.isEmpty(nameText)) {
                    businessPlanNameTV.setText(nameText);
                    storePlanData.setBusiness_plan_id(((Double) list.get(0).get("id")).intValue());
                    storePlanData.setBusiness_plan_name(nameText);


                }


            } else {
                if (isFirstFindBusinessFlag) {
                    String nameText = String.valueOf(list.get(0).get("name"));
                    if (!TextUtils.isEmpty(nameText)) {
                        businessPlanNameTV.setText(nameText);
                        storePlanData.setBusiness_plan_id(((Double) list.get(0).get("id")).intValue());
                        storePlanData.setBusiness_plan_name(nameText);

                    }
                } else {

                    Map<String, String> selectedValue = new HashMap<>();

                    if (storePlanData != null && storePlanData.getBusiness_plan_id() != 0) {
                        selectedValue.put("id", String.valueOf(storePlanData.getBusiness_plan_id()));
                    }
                    if (storePlanData != null && storePlanData.getBusiness_plan_name() != null) {
                        selectedValue.put("name", storePlanData.getBusiness_plan_name());
                    }
                    List<Map<String, String>> items = new ArrayList();
                    for (int i = 0; i < list.size(); i++) {
                        items.add(createItem(String.valueOf(((Double) list.get(i).get("id")).intValue()), String.valueOf(list.get(i).get("name"))));
                    }


                    BottomSheetDialogHelper.showSingleSelectBottomSheetDialog(
                            this,
                            "请选择所属商圈",
                            items,
                            selectedValue == null || (selectedValue != null && selectedValue.isEmpty()) ? null : selectedValue, // 可以为 null
                            (id, name) -> {

                                if (!TextUtils.isEmpty(name)) {

                                    Map<String, Object> curData = Collections.emptyMap();

                                    for (Map<String, Object> item : list) {
                                        if (String.valueOf(((Double) item.get("id")).intValue()).equals(id)) {
                                            curData = item;
                                            break;
                                        }


                                    }


                                    businessPlanNameTV.setText(name);
                                    Log.i("MapAddPointActivity", "business_selected: " + curData);
                                    int curId = ((Double) curData.get("id")).intValue();

                                    storePlanData.setBusiness_plan_id(curId);
                                    storePlanData.setBusiness_plan_name(name);
                                    if (curData.get("open_store") != null && !TextUtils.isEmpty(String.valueOf(curData.get("open_store")))) {
                                        int openStore = ((Double) curData.get("open_store")).intValue();
                                        storePlanData.setOpen_store(openStore);
                                    }

//                                    根据所属商圈同步
                                    HashMap<String, Object> body = new HashMap<>();
                                    body.put("companyId", companyId);
                                    body.put("store_id", storePlanId);
                                    if (storePlanData != null){
                                        body.put("id",storePlanData.getBusiness_plan_id());
                                    }
                                    Log.d("TAG", "同步数据传参" + body);
                                    mPresenter.getPointDataFromBuniessPOST(body);


                                }

                            }
                    );
                }


            }

            if (!isFirstLoad && !list.isEmpty()){//如果不是第一次进来,刷新所属商圈同步数据

                //                                    根据所属商圈同步
                HashMap<String, Object> body = new HashMap<>();
                body.put("companyId", companyId);
                body.put("store_id", storePlanId);
                if (storePlanData != null){
                    body.put("id",storePlanData.getBusiness_plan_id());
                }
                Log.d("TAG", "同步数据传参" + body);
                mPresenter.getPointDataFromBuniessPOST(body);
            }

            isFirstFindBusinessFlag = false;
            isFirstLoad = false;

        }

    }

    //    跟进所属商圈获取点位数据
    @Override
    public void getPointDataFromBuniessPOST(BaseResponse reponse) {

        if (storePlanData.getState().equals("SECOND_AUDIT")){//审批通过不支持刷新
            return;
        }

        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0 && reponse.data != null) {
            //            转换返回的JSON对象
            Gson gson = new Gson();
            String json = gson.toJson(reponse.data);
            Type typeToken = new TypeToken<MapFromBuniessReponseBean>() {
            }.getType();
            MapFromBuniessReponseBean resultData = gson.fromJson(json, typeToken);

//            更新周边环境视频
            if (resultData != null && resultData.getFiles() != null && !resultData.getFiles().isEmpty()){
                uploadCollection.getCustomUpload("businessDistircitEnviroment").setFiles(resultData.getFiles());
            }

            Log.d("TAG", "同步数据返回===" + resultData.getContent());
//           更新变动项数据
            if (resultData != null && resultData.getContent() != null && !resultData.getContent().isEmpty()){

                HashMap<String,Object> hashMap = new HashMap<>();
                for (int i = 0; i < resultData.getContent().size(); i++){
                    ContentBean bean = (ContentBean) resultData.getContent().get(i);
                    Map<String,Object> detailMap = (Map)bean.getDetails();
                    hashMap.putAll(detailMap);
                }
                if (customComponentAdapter != null) {

                    dataMap.putAll(hashMap);
                    Log.d("TAG", "同步数据返回===" + dataMap);
                    customComponentAdapter.setContentMap(dataMap);
                }

                // Update adapter data
                if (customComponentAdapter != null) {
                    customComponentAdapter.updateData(changeBeanList);
                    customComponentAdapter.notifyDataSetChanged();
                }
            }




        }
    }

    @Override
    public void getPointDataFromBuniessPOSTFailed(Throwable e) {

    }
    @Override
    public void getBusinessAreaPOSTFailed(Throwable e) {

    }

    @Override
    public void getLabelsPOST(BaseResponse reponse) {
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0 && reponse.data != null) {

//            转换返回的JSON对象
            Gson gson = new Gson();
            String json = gson.toJson(reponse.data);
            Type typeToken = new TypeToken<List<Map<String, Object>>>() {
            }.getType();
            List<Map<String, Object>> list = gson.fromJson(json, typeToken);

            Map<String, String> selectedValue = new HashMap<>();

            if (storePlanData != null && storePlanData.getBusiness_plan_id() != 0) {
                selectedValue.put("id", String.valueOf(isMainTab ? storePlanData.getStore_type_id() : storePlanData.getSub_store_type_id()));
            }
            if (storePlanData != null && storePlanData.getBusiness_plan_name() != null) {
                selectedValue.put("name", isMainTab ? storePlanData.getBusiness_plan_name() : storePlanData.getSub_store_type_name());
            }

            List<Map<String, String>> items = new ArrayList();
            for (int i = 0; i < list.size(); i++) {
                items.add(createItem(String.valueOf(((Double) list.get(i).get("id")).intValue()), String.valueOf(list.get(i).get("name"))));
            }

            BottomSheetDialogHelper.showSingleSelectBottomSheetDialog(
                    this,
                    isMainTab ? "请选择店铺类型主标签" : "请选择店铺类型副标签",
                    items,
                    selectedValue == null || (selectedValue != null && selectedValue.isEmpty()) ? null : selectedValue, // 可以为 null
                    (id, name) -> {

                        if (!TextUtils.isEmpty(name)) {
                            if (isMainTab) {
                                storeTypeTV.setText(name);
                            } else {
                                subStoreTypeTV.setText(name);
                            }
                        }

                        if (isMainTab) {
                            storePlanData.setStore_type_id((int) Double.parseDouble(id));
                            storePlanData.setStore_type_name(name);
                        } else {
                            storePlanData.setSub_store_type_id((int) Double.parseDouble(id));
                            storePlanData.setSub_store_type_name(name);
                        }

                    }
            );


        }

    }

    @Override
    public void getLabelsPOSTFailed(Throwable e) {
        Log.i("MapAddPointActivity", "getLable error" + e.getMessage().toString());
        showMsg("获取标签失败");
    }

    @Override
    public void uploadFilePOST(BaseResponse response) {


        Log.i("CustomUpload", "Upload success" + response);
        if (response != null && response.code == 0 && response.data != null) {
            FileBean uploadedFile = gson.fromJson(gson.toJson(response.data), FileBean.class);
//                curUploadInstance.add(uploadedFile);

            Glide.with(this).load(uploadedFile.getUrl()).into(pointStoreHeadIV);
            // TODO 找个地方存一下

            storeHeadFile = uploadedFile;

            if (storeHeadFile != null) {
                upload_camera_rl.setVisibility(View.GONE);
            } else {
                upload_camera_rl.setVisibility(View.VISIBLE);

            }
//            showMsg("Upload successful!");
        } else {
            showMsg("图片上传失败");
        }

    }

    @Override
    public void uploadFilePOSTFailed(Throwable e) {
//        showMsg("上传失败");
        showMsg("图片上传失败");
        Log.i("CustomUpload", "Upload error", e);
    }


    @Override
    public void initData(Bundle savedInstanceState) {

    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_map_add_point;
    }

    private void updateBaseInfoMedia(CustomUpload uploadInstance, String type) {
        List<FileBean> files = storePlanData.getFiles();
        List<FileBean> curFiles = new ArrayList<>();


        if (files != null) {

            for (FileBean file : files) {
                if (file.getRef_sub_type().equals(type)) {
                    curFiles.add(file);
                }

            }

        }

        uploadInstance.setFiles(curFiles);
    }

    private void updateViewData(Map<String, Object> record) {
        if (record != null) {
            dataMap = record;

            for (Map.Entry<String, Object> entry : record.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
//                if (!key.contains("_show")) {
//                logMap.put(key, value);
                Log.i("SUBMIT_DATA", "recordKey:" + key + "  value:" + value);
//                }

            }


            // Update basic information fields
            if (record.containsKey("name")) {
                nameET.setText(record.get("name") == null ? "" : String.valueOf(record.get("name")));
                storePlanData.setName(String.valueOf(record.get("name")));
            }
            if (record.containsKey("address")) {
                addressTV.setText(record.get("address") == null ? "" : String.valueOf(record.get("address")));
                storePlanData.setAddress(String.valueOf(record.get("address")));
            }

            if (record.containsKey("full_address")) {
                fullAddressET.setText(record.get("full_address") == null ? "" : String.valueOf(record.get("full_address")));
                storePlanData.setFull_address(String.valueOf(record.get("full_address")));
            }

            if (record.containsKey("follow_by")) {
                followByTV.setText(record.get("follow_by") == null ? "" : String.valueOf(record.get("follow_by")));
            }
            if (record.containsKey("business_plan_name")) {
                businessPlanNameTV.setText(record.get("business_plan_name") == null ? "" : String.valueOf(record.get("business_plan_name")));
                storePlanData.setBusiness_plan_name(String.valueOf(record.get("business_plan_name")));
            }

            if (record.containsKey("store_level")) {

                String value = (String) record.get("store_level");
                String text = value == null ? "" : sotreLevelValueMap.get(value);
                storeLevelTV.setText(text);
                storePlanData.setStore_level(value);
            }

            if (record.containsKey("store_type_name")) {
                storeTypeTV.setText(record.get("store_type_name") == null ? "" : String.valueOf(record.get("store_type_name")));
            }
            if (record.containsKey("sub_store_type_name")) {
                subStoreTypeTV.setText(record.get("sub_store_type_name") == null ? "" : String.valueOf(record.get("sub_store_type_name")));
            }
            if (record.containsKey("store_attributes")) {
                String value = (String) record.get("store_attributes");
                String text = value == null ? "" : value.equals("STANDARD") ? "标准店" : value.equals("COMPREHENSIVE") ? "综合店" : "旗舰店";
                storeAttributesTV.setText(record.get("store_attributes") == null ? "" : String.valueOf(text));
                storePlanData.setStore_attributes(value);
            }
            if (record.containsKey("flipped_store")) {
                flippedStoreRG.check((boolean) record.get("flipped_store") ? R.id.flipped_store_true : R.id.flipped_store_false);
            }
            if (record.containsKey("allow_look")) {
                allowLookRG.check((boolean) record.get("allow_look") ? R.id.allow_look_true : R.id.allow_look_false);
            }
            if (record.containsKey("store_area_type") && record.get("store_area_type") != null) {
                storeAreaTypeRG.check("URBAN".equals(record.get("store_area_type")) ? R.id.store_area_type_true : R.id.store_area_type_false);
            } else {
                storeAreaTypeRG.clearCheck();
            }

            // Update file uploads
            for (Map.Entry<String, CustomUpload> entry : uploadCollection.getCustomUploadMap().entrySet()) {
                String fieldName = entry.getValue().getFieldName();
                if (record.containsKey(fieldName) && record.get(fieldName) != null) {
                    List<FileBean> fileBeans = Arrays.asList(gson.fromJson(gson.toJson(record.get(fieldName)), FileBean[].class));
                    entry.getValue().setFiles(fileBeans);
                }
            }

            // Update point store head file
            if (record.containsKey("files") && record.get("files") != null) {
                List<FileBean> files = Arrays.asList(gson.fromJson(gson.toJson(record.get("files")), FileBean[].class));
                for (FileBean file : files) {
                    if ("pointStoreHead".equals(file.getRef_sub_type())) {
                        storeHeadFile = file;
                        Glide.with(this).load(file.getUrl()).into(pointStoreHeadIV);
                        upload_camera_rl.setVisibility(View.GONE);
                    }
                }
            }

            List<String> fileIds = new ArrayList<String>() {
                {
                    add("storeBrandLeft");
                    add("storeBrandRight");
                    add("storeBrandFront");
                    add("businessDistircitEnviroment");
                    add("inRoom");
                }
            };

            for (String fileId : fileIds) {
                if (fileId.equals("storeBrandLeft")) {
                    updateBaseInfoMedia(storeBrandLeftUpload, fileId);
                } else if (fileId.equals("storeBrandRight")) {
                    updateBaseInfoMedia(storeBrandRightUpload, fileId);
                } else if (fileId.equals("storeBrandFront")) {
                    updateBaseInfoMedia(storeBrandFrontUpload, fileId);
                } else if (fileId.equals("businessDistircitEnviroment")) {
                    updateBaseInfoMedia(businessDistircitUpload, fileId);
                } else if (fileId.equals("inRoom")) {
                    updateBaseInfoMedia(inRoomUpload, fileId);
                }

            }

            // Update landlord list
            if (record.containsKey("landlords")) {
                Type listType = new TypeToken<ArrayList<HashMap<String, Object>>>() {
                }.getType();
                landlordList = gson.fromJson(gson.toJson(record.get("landlords")), listType);
                refreshLandlordList();
            }

            // 如果有表格需要再处理一步
            for (ItemChangeBean item : changeBeanList) {
                List<ItemChangeContentDetailBean> details = item.getDetails();
                for (ItemChangeContentDetailBean detail : details) {
                    if ("TABLE".equals(detail.getComponent_type()) && dataMap.containsKey(detail.getId()) && dataMap.get(detail.getId()) != null && dataMap.get(detail.getId()) instanceof List) {
                        List<Map<String, Object>> tableData = (List<Map<String, Object>>) dataMap.get(detail.getId());
                        for (int i = 0; i < tableData.size(); i++) {
                            Map<String, Object> tableRow = tableData.get(i);
                            List<ItemChangeContentDetailBean> curDetails = detail.getDetails();
                            for (ItemChangeContentDetailBean curDetail : curDetails) {
                                if (tableRow.containsKey(curDetail.getId())) {
                                    dataMap.put(detail.getId() + "." + i + "." + curDetail.getId(), tableRow.get(curDetail.getId()) == null ? dataMap.get(detail.getId() + "." + i + "." + curDetail.getId()) : tableRow.get(curDetail.getId()));
                                }
                            }

                        }

                    }
                }

            }

            if (customComponentAdapter != null) {
                customComponentAdapter.setContentMap(dataMap);
            }

            // Update adapter data
            if (customComponentAdapter != null) {
                customComponentAdapter.updateData(changeBeanList);
                customComponentAdapter.notifyDataSetChanged();
            }

            // Ensure all draft values are applied to the adapter

        }
    }

    private void handleUseDraft() {
        if (!draftUtils.getIsUpdated()) {
            updateViewData(draftUtils.getLastRecord().getInfo());
        } else {
            showInfoDeleteDialog(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    updateViewData(draftUtils.getLastRecord().getInfo());
                }
            }, "打开草稿后，当前内容将被覆盖");
        }

    }

    private void handleDeleteDraft() {
        showInfoDeleteDialog(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                draftUtils.delete();
            }
        }, "确认删除草稿吗？");
    }


}
