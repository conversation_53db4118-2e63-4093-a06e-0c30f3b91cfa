import React, {FC, useMemo, useRef, useState} from 'react';
import {<PERSON><PERSON>, XIcon, XText} from '@xlb/common/src/components';
import {normalize} from '@xlb/common/src/config/theme';
import {
  View,
  StyleSheet,
  StatusBar,
  Text,
  TouchableOpacity,
  Pressable,
  Keyboard,
  TextInput,
} from 'react-native';
import Item from '../components/Item';
import {$alert} from '@xlb/common/src/utils/overlay';
import {authModel} from '../../../models/auth';
// import * as AliyunPush from 'aliyun-react-native-push'
// import { useDefined } from 'foca'
import {mineModel} from '../../../models/mine';
import useSelectStore from '../../home/<USER>/ErpHome/store';
import useStore from '@xlb/common/src/components/features/xlbStoreText/model';
import {TOKEN, XlbIconfontNew, XlbText} from '@xlb/components-rn';
import {
  <PERSON>lank,
  Button,
  Col,
  Dialog,
  Popup,
  Row,
  Space,
} from '@fruits-chain/react-native-xiaoshu';
import Loading from '@xlb/common/src/components/RootView/Loading';
import Toast from 'react-native-root-toast';
import {useRoute} from '@react-navigation/native';
import {cloneDeep} from 'lodash';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttp';

const UserInfoItem: FC<{
  title: string;
  value?: string;
  isLast: boolean;
  onPress?: (val?: string) => void;
  hideVal?: boolean;
}> = props => (
  <View
    style={StyleSheet.flatten([
      styles.card_item,
      props.isLast ? {borderBottomWidth: 0} : {},
    ])}>
    <XlbText font_size_5>{props.title}</XlbText>
    <Pressable
      style={{flex: 1, alignItems: 'flex-end'}}
      onPress={() => {
        if (props.onPress) props.onPress(props.value);
      }}>
      <Space direction="horizontal" gap={0} align="center">
        {props.value && !props.hideVal ? (
          <XlbText grey_7 font_size_5>
            {props.value}
          </XlbText>
        ) : null}
        {props.onPress ? (
          <XlbIconfontNew
            name="youjiantou"
            size={TOKEN.space_3}
            color={TOKEN.grey_25}></XlbIconfontNew>
        ) : null}
      </Space>
    </Pressable>
  </View>
);

const MinSetting: React.FC = ({navigation}: any) => {
  const model = mineModel; //useDefined(mineModel)
  const route = useRoute();
  const setStoreList = useSelectStore((state: any) => state.setStoreList);
  const setStoreLists = useStore((state: any) => state.setStoreList);
  const _logout = () => {
    // $alert('退出提示', '确定退出登录吗？', () => {
    // 清除aliyun绑定
    // AliyunPush.unbindAccount().then((result) => {
    //   console.log('----解绑', result)
    // })
    model.logout();
    authModel.setUserInfo({});
    authModel.setUserInfos({});
    authModel.setIsLoggedIn(false);
    // 解决首页切换门店问题
    setStoreList([]);
    setStoreLists([]);
    // })
  };

  const nameInputRef = useRef<TextInput>(null);
  const nameInputTempRef = useRef(authModel?.state?.userInfos?.name || '');
  const [data, setData] = useState(authModel?.state?.userInfos || {});
  const [visible, setVisible] = useState(false);
  const [changePhoneDialogShow, setChangePhoneDialogShow] = useState(false);

  const userInfos = useMemo<any>(
    () => ({
      name: data.name,
      tel: route.params?.tel || data.tel,
      company_name: authModel?.state?.userInfos?.company_name,
      business_dept: authModel?.state?.userInfos?.business_dept,
    }),
    [data, route],
  );

  const reg = /(?=(\d{4})+$)/g;

  const userInfo = [
    {
      title: '姓名',
      key: 'name',
      onPress: () => {
        setVisible(true);
      },
    },
    {
      key: 'tel',
      title: '手机号码',
      setVal: (val: string) => (val ? val.replace(reg, ' ') : ''),
      onPress: (val?: string) => {
        // navigation.navigate('ModifyPhone', { tel: val.replace(/\s+/g, '') })
        setChangePhoneDialogShow(true);
      },
    },
    {
      key: 'tel',
      title: '修改密码',
      setVal: (val: string) => (val ? val.replace(reg, ' ') : ''),
      hideVal: true,
      onPress: (val?: string) => {
        navigation.navigate('VerifyPhone', {tel: val});
      },
    },
  ];

  const handleSave = async () => {
    Loading.show();
    const res: any = await ErpHttp.post('/erp/hxl.erp.user.self.update', {
      id: authModel?.state.userInfos.id,
      name: nameInputTempRef.current,
      store_id: authModel?.state.userInfos?.store_id,
      account: authModel?.state.userInfos?.account,
      tel: authModel?.state.userInfos?.tel,
      inner_user: authModel?.state.userInfos?.inner_user,
    });
    setVisible(false);
    if (res?.code == 0) {
      Toast.show('保存成功');
      setData(res.data);
      authModel.setUserInfos({
        ...cloneDeep(authModel?.state.userInfos || {}),
        name: res.data.name,
      });
    }
  };

  return (
    <View>
      {/* <StatusBar  hidden={false} translucent={false} backgroundColor={'#fff'} barStyle={"dark-content"}/> */}

      <Header
        headerBgColor="#fff"
        centerComponent={
          <XText size16 semiBold>
            设置
          </XText>
        }
        leftComponent={
          <XIcon
            name={'back'}
            color="#000"
            onPress={() => {
              navigation.goBack();
            }}
          />
        }
      />

      <View style={[styles.card, {marginTop: normalize(12)}]}>
        <View style={styles.userInfo}>
          {userInfo.map((item, index) => (
            <UserInfoItem
              key={item.title}
              title={item.title}
              value={
                item.setVal
                  ? item.setVal(userInfos?.[item?.key])
                  : userInfos?.[item?.key]
              }
              isLast={index === userInfo.length - 1}
              hideVal={item.hideVal}
              onPress={item.onPress}
            />
          ))}
        </View>
      </View>

      <Item
        label={'消息通知'}
        onPress={() => navigation.navigate('NoticeSetting')}
        boxStyle={'miniBox'}
      />

      {/*<Item label={'账号与安全'} onPress={()=>{}}/>*/}

      <Item
        boxStyle={'miniBox'}
        label={'硬件设置'}
        onPress={() => navigation.navigate('HardwareSetting')}
      />

      <TouchableOpacity
        style={[
          styles.box,
          {marginTop: normalize(12), justifyContent: 'center'},
        ]}
        onPress={_logout}>
        <Text style={{color: 'rgba(250, 50, 50, 1)'}}>退出登录</Text>
      </TouchableOpacity>

      <Popup
        visible={visible}
        position="bottom"
        safeAreaInsetBottom
        round
        onClose={() => {
          Keyboard.dismiss();
        }}
        destroyOnClosed
        style={{paddingHorizontal: TOKEN.space_1}}
        onOpened={() => {
          if (nameInputRef.current) nameInputRef.current.focus();
        }}>
        <Popup.Header
          style={{height: normalize(56)}}
          showClose={false}
          rightExtra={
            <Pressable
              style={{
                height: '100%',
                width: normalize(50),
                justifyContent: 'center',
                alignItems: 'flex-end',
              }}
              onPress={() => {
                handleSave();
              }}>
              <XlbText font_size_4 primary_10>
                完成
              </XlbText>
            </Pressable>
          }
          leftExtra={
            <Pressable
              style={{
                height: '100%',
                width: normalize(50),
                justifyContent: 'center',
                alignItems: 'flex-start',
              }}
              onPress={() => {
                setVisible(false);
              }}>
              <XlbText font_size_4 grey_45>
                取消
              </XlbText>
            </Pressable>
          }
          titleTextStyle={{fontSize: TOKEN.space_4, fontWeight: '500'}}
          title="修改姓名"
          onClose={() => {
            setVisible(false);
          }}
        />
        <Blank top={TOKEN.space_1} bottom={TOKEN.space_2}>
          <Space gap={TOKEN.space_3}>
            <XlbText grey_45>
              {'请设置2-24个字符，不包括@<>/等无效字符'}
            </XlbText>
            <View style={styles.modifyUserName}>
              <TextInput
                ref={nameInputRef}
                maxLength={24}
                defaultValue={userInfos.name}
                placeholder="请填写姓名"
                placeholderTextColor={TOKEN.grey_45}
                onChangeText={text => {
                  nameInputTempRef.current = text;
                }}
                style={{padding: 0}}
              />
            </View>
            <View style={{height: normalize(326)}}></View>
          </Space>
        </Blank>
      </Popup>

      {/* 修改手机号确认弹框*/}
      <Dialog.Component
        title="确定修改手机号？"
        message={
          '当前手机号（+86 ' +
          authModel.state.userInfos?.tel +
          ')修改手机号需要短信验证'
        }
        visible={changePhoneDialogShow}
        showConfirmButton={false}
        showCancelButton={false}
        closeOnPressOverlay={false}
        showClose={false}
        width={300}>
        <View
          style={{
            backgroundColor: '#fff',
            padding: 20,
            paddingTop: 20,
            paddingBottom: 24,
            borderRadius: 12,
          }}>
          <Row gap={8}>
            <Col span={12}>
              <Button
                text="取消"
                color={'#1F2126'}
                type="outline"
                onPress={() => {
                  setChangePhoneDialogShow(false);
                }}
              />
            </Col>
            <Col span={12}>
              <Button
                text="确定"
                type="primary"
                onPress={async () => {
                  setChangePhoneDialogShow(false);
                  navigation.navigate('VerifyPhone', {
                    tel: userInfos?.tel,
                    routkey: 'ChangePhoneNumber',
                  });
                  // postAllRead()
                }}
              />
            </Col>
          </Row>
        </View>
      </Dialog.Component>
    </View>
  );
};

const styles = StyleSheet.create({
  view_content: {
    paddingLeft: normalize(12),
    paddingRight: normalize(12),
  },
  box: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 1)',
    paddingHorizontal: normalize(12),
    borderRadius: normalize(8),
    height: 48,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: TOKEN.space_2,
    paddingLeft: TOKEN.space_2,
  },
  card_item: {
    borderBottomColor: TOKEN.grey_15,
    borderBottomWidth: 0.5,
    paddingRight: TOKEN.space_3,
    height: normalize(48),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  userInfo: {
    paddingLeft: TOKEN.space_3,
  },
  modifyUserName: {
    flexDirection: 'row',
    backgroundColor: '#F4F5F7',
    borderRadius: TOKEN.space_2,
    height: normalize(48),
    paddingHorizontal: TOKEN.space_3,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});

export default MinSetting;
