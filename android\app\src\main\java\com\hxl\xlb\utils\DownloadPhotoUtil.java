package com.hxl.xlb.utils;

import android.Manifest;
import android.app.Activity;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.format.DateUtils;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.XXPermissions;
import com.hjq.permissions.permission.PermissionLists;
import com.hjq.permissions.permission.base.IPermission;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DownloadPhotoUtil {

    private static Activity mContext;
    private static final int REQUEST_CODE_SAVE_IMG = 0;

    /**
     * 请求读取sd卡的权限
     */
    public static void requestPermission(Activity context, Bitmap bitmap) {
        mContext = context;
        List<IPermission> p = new ArrayList<>();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            p.add(PermissionLists.getManageExternalStoragePermission());
        } else {
            p.add(PermissionLists.getReadExternalStoragePermission());
            p.add(PermissionLists.getWriteExternalStoragePermission());
        }

        XXPermissions.with(context).permissions(p).request(new OnPermissionCallback(){

            @Override
            public void onGranted(@NonNull List<IPermission> permissions, boolean allGranted) {
                // 已同意 去保存
                saveImage(context, bitmap);
            }
        });
    }

    // 是否有对应权限
    public static boolean hasPermissions(Context context, String... perms) {



        return XXPermissions.isGrantedPermission(context,PermissionLists.getManageExternalStoragePermission());

        // 判断sdk版本
//        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
//            return true;
//        }
//        for (String perm : perms) {
//            boolean hasPerm = (ContextCompat.checkSelfPermission(context, perm) ==
//                    PackageManager.PERMISSION_GRANTED);
//            if (!hasPerm) {
//                return false;
//            }
//        }
//        return true;
    }

    // 保存图片
    public static void saveImage(Context context, Bitmap bitmap) {
        if (bitmap == null) {
            return;
        }
        boolean isSaveSuccess;
        if (Build.VERSION.SDK_INT < 29) {
            isSaveSuccess = saveImageToGallery(context, bitmap);
        } else {
            isSaveSuccess = saveImageToGallery1(context, bitmap);
        }
        if (isSaveSuccess) {
            Toast.makeText(mContext, "保存图片成功", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(mContext, "保存图片失败，请稍后重试", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * android 10 以下版本
     */
    public static boolean saveImageToGallery(Context context, Bitmap image) {
        // 首先保存图片
        String storePath = Environment.getExternalStorageDirectory().getAbsolutePath() + File.separator + "dearxy";

        File appDir = new File(storePath);
        if (!appDir.exists()) {
            appDir.mkdir();
        }
        String fileName = System.currentTimeMillis() + ".jpg";
        File file = new File(appDir, fileName);
        try {
            FileOutputStream fos = new FileOutputStream(file);
            // 通过io流的方式来压缩保存图片
            boolean isSuccess = image.compress(Bitmap.CompressFormat.JPEG, 60, fos);
            fos.flush();
            fos.close();

            // 保存图片后发送广播通知更新数据库
            Uri uri = Uri.fromFile(file);
            mContext.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, uri));
            if (isSuccess) {
                return true;
            } else {
                return false;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * android 10 以上版本
     */
    public static boolean saveImageToGallery1(Context context, Bitmap image) {
        Long mImageTime = System.currentTimeMillis();
        String imageDate = new SimpleDateFormat("yyyyMMdd-HHmmss").format(new Date(mImageTime));
        String SCREENSHOT_FILE_NAME_TEMPLATE = "xlb_%s.png";//图片名称，以"winetalk"+时间戳命名
        String mImageFileName = String.format(SCREENSHOT_FILE_NAME_TEMPLATE, imageDate);

        final ContentValues values = new ContentValues();
        values.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES
                + File.separator + "xlb"); //Environment.DIRECTORY_SCREENSHOTS:截图,图库中显示的文件夹名。"dh"
        values.put(MediaStore.MediaColumns.DISPLAY_NAME, mImageFileName);
        values.put(MediaStore.MediaColumns.MIME_TYPE, "image/png");
        values.put(MediaStore.MediaColumns.DATE_ADDED, mImageTime / 1000);
        values.put(MediaStore.MediaColumns.DATE_MODIFIED, mImageTime / 1000);
        values.put(MediaStore.MediaColumns.DATE_EXPIRES, (mImageTime + DateUtils.DAY_IN_MILLIS) / 1000);
        values.put(MediaStore.MediaColumns.IS_PENDING, 1);

        ContentResolver resolver = context.getContentResolver();
        final Uri uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
        try {
            // First, write the actual data for our screenshot
            try (OutputStream out = resolver.openOutputStream(uri)) {
                if (!image.compress(Bitmap.CompressFormat.PNG, 100, out)) {
                    return false;
                }
            }
            // Everything went well above, publish it!
            values.clear();
            values.put(MediaStore.MediaColumns.IS_PENDING, 0);
            values.putNull(MediaStore.MediaColumns.DATE_EXPIRES);
            resolver.update(uri, values, null, null);
        } catch (IOException e) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R){
                resolver.delete(uri, null);
            }
            return false;
        }
        return true;
    }


    public static void requestPermissionMP4(Activity context, String videoUrl, DownloadListener listener) {
        /**
         * 添加读写权限
         *      READ_EXTERNAL_STORAGE：读外部存储的权限
         *      WRITE_EXTERNAL_STORAGE：写外部存储的权限
         */

        List<IPermission> p = new ArrayList<>();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            p.add(PermissionLists.getManageExternalStoragePermission());
        } else {
            p.add(PermissionLists.getReadExternalStoragePermission());
            p.add(PermissionLists.getWriteExternalStoragePermission());
        }

        XXPermissions.with(context).permissions(p).request(new OnPermissionCallback(){

            @Override
            public void onGranted(@NonNull List<IPermission> permissions, boolean allGranted) {
                DownloadPhotoUtil.downloadVideo(videoUrl,listener);
            }
        });


    }

    public static String getFileNameFromUrl(String urlString) {
        try {
            URL url = new URL(urlString);
            String path = url.getPath();
            return path.substring(path.lastIndexOf('/') + 1);
        } catch (Exception e) {
            return extractFileNameFromString(urlString);
        }
    }
    // 备用方法：当URL解析失败时使用正则提取
    private static String extractFileNameFromString(String url) {
        Pattern pattern = Pattern.compile("/([^/?]+\\.[^/?]+)(?:\\?|$)");
        Matcher matcher = pattern.matcher(url);
        return matcher.find() ? matcher.group(1) : null;
    }
    public static String downloadVideo(String videoUrl,  DownloadListener listener) {
        String fileName = getFileNameFromUrl(videoUrl);
        String storePath = Environment.getExternalStorageDirectory().getAbsolutePath() + File.separator + fileName;
        Log.e("onProgress","3333333333333333333");
        try {
            URL url = new URL(videoUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.connect();

            // 检查响应码
            if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
                if (listener != null) listener.onError("Server returned HTTP " + connection.getResponseCode());
                return null;
            }

            // 获取文件总大小（用于进度计算）
            int fileLength = connection.getContentLength();

            // 创建目标文件
            File outputFile = new File(storePath);
            if (outputFile.exists()) {
                outputFile.delete(); // 如果已存在则删除旧文件
            }

            // 开始下载
            InputStream input = connection.getInputStream();
            FileOutputStream output = new FileOutputStream(outputFile);

            byte[] buffer = new byte[4096];
            int downloaded = 0;
            int read;
            while ((read = input.read(buffer)) != -1) {
                output.write(buffer, 0, read);
                downloaded += read;
                // 进度回调
                if (listener != null) {
                    listener.onProgress(downloaded, fileLength);
                }
            }

            output.close();
            input.close();
            if (listener != null) listener.onComplete(storePath);
            return storePath;

        } catch (Exception e) {
            if (listener != null) listener.onError(e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    public interface DownloadListener {
        void onProgress(int downloadedBytes, int totalBytes);
        void onComplete(String filePath);
        void onError(String errorMessage);
    }

}
