import React, { useEffect, useRef, useState } from 'react'
import { Header, Switch, XIcon, XText } from '@xlb/common/src/components'
import { normalize } from '@xlb/common/src/config/theme'
import { View, StyleSheet, StatusBar, Platform, DeviceEventEmitter } from 'react-native'
import Item from '../components/Item'
import XlbRed from '@xlb/common/src/components/features/xlbRed'
import XlbTipHelp from '@xlb/common/src/components/features/xlbTipHelp'
import { RouteTabs } from '../index'
import { authModel } from '../../../models/auth'
import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'
// import useEntranceStore from '../../entrance/useEntranceStore'
import XLBStorage from '@xlb/common/src/utils/storage'
import { FONT_STATE_KEY, VOICE_STATE_KEY } from '@xlb/common/src/config/constant'

const NoticeSetting: React.FC = ({ navigation }: any) => {
  const xlbTipHelpRef = useRef<any>()
  const xlbVoiceRef = useRef<any>()
  const xlbFontRef = useRef<any>()
  // 补货单推送状态
  const [pushState, setPushState] = useState<boolean>(false)
  // 语音播报状态
  const [voiceState, setVoiceState] = useState<boolean>(false)
  // 字体设置状态
  const [fontState, setFontState] = useState<boolean>(false)
  // const system = authModel?.state?.userInfos.supplier ? 'SCM' : useEntranceStore((state: any) => state.system)

  const getPushState = async () => {
    const id = authModel?.state?.userInfos?.id
    if (!id) return
    const res = await ErpHttp.post<CommonResponse>(
      '/erp/hxl.erp.user.notice.status.read',
      {
        id,
      },
      {
        timeout: 10000,
      }
    )
    if (res.code === 0) {
      setPushState(res.data.request_order_notice_state)
    }
  }
  const setPushStates = async (state?: boolean) => {
    const id = authModel?.state?.userInfos?.id
    if (!id) return
    const res = await ErpHttp.post<CommonResponse>('/erp/hxl.erp.user.notice.status.update', {
      id,
      request_order_notice_state: state,
    })
    if (res.code == 0) {
      setPushState(!pushState)
    }
  }

  const getVoiceState = async () => {
    const res = await authModel.getVoiceState()
    setVoiceState(res)
    authModel.setVoiceState(res)
  }

  const getFontState = async () => {
    const res = await authModel.getFontState()
    setFontState(res)
    authModel.setFontState(res)
  }
  useEffect(() => {
    getPushState()
    getVoiceState()
    getFontState()
  }, [])
  return (
    <View>
      {/* <StatusBar  hidden={false} translucent={false} backgroundColor={'#fff'} barStyle={"dark-content"}/> */}
      <Header
        headerBgColor="#fff"
        centerComponent={
          <XText size16 semiBold>
            消息通知
          </XText>
        }
        leftComponent={<XIcon name={'back'} color="#000" onPress={() => navigation.goBack()} />}
      />
      <View style={styles.view_content}>
        <Item
          renderRightButton={
            <Switch
              onValueChange={() => {
                setPushStates(!pushState)
              }}
              value={pushState}
            />
          }
          boxStyle={'miniBox'}
          renderLabel={
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <XText size15 style={{ color: 'rgba(31, 33, 38, 1)' }}>
                补货单推送状态
              </XText>
              <XlbRed redBox={{ marginLeft: 2 }} redParams={{ tabKey: RouteTabs[2].key, key: 'storePush' }} isTab={false} />
              <XlbTipHelp
                ref={xlbTipHelpRef}
                style={{ marginLeft: 4 }}
                size={20}
                color={'#C9CDD4'}
                showProps={{
                  type: 'tips',
                  desc: '开启后可接收到门店补货的配送通知消息',
                  key: 'storePush',
                }}
              />
            </View>
          }
        />
        {Platform.OS === 'android' && (
          <Item
            renderRightButton={
              <Switch
                onValueChange={async () => {
                  await XLBStorage.setItem(VOICE_STATE_KEY, !voiceState)
                  authModel.setVoiceState(!voiceState)
                  setVoiceState(!voiceState)
                }}
                value={voiceState}
              />
            }
            boxStyle={'miniBox'}
            renderLabel={
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <XText size15 style={{ color: 'rgba(31, 33, 38, 1)' }}>
                  语音播报
                </XText>
                <XlbRed redBox={{ marginLeft: 2 }} redParams={{ tabKey: RouteTabs[2].key, key: 'voiceKey' }} isTab={false} />
                <XlbTipHelp
                  ref={xlbVoiceRef}
                  style={{ marginLeft: 4 }}
                  size={20}
                  color={'#C9CDD4'}
                  showProps={{
                    type: 'tips',
                    desc: '开启后拣货任务可接听语音播报',
                    key: 'voiceKey',
                  }}
                />
              </View>
            }
          />
        )}
        {Platform.OS === 'android' && (
          <Item
            renderRightButton={
              <Switch
                onValueChange={async () => {
                  await XLBStorage.setItem(FONT_STATE_KEY, !fontState)
                  authModel.setFontState(!fontState)
                  setFontState(!fontState)
                  DeviceEventEmitter.emit('WMSFontRefresh', !fontState)
                }}
                value={fontState}
              />
            }
            boxStyle={'miniBox'}
            renderLabel={
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <XText size15 style={{ color: 'rgba(31, 33, 38, 1)' }}>
                  字体大小跟随系统
                </XText>
                <XlbRed redBox={{ marginLeft: 2 }} redParams={{ tabKey: RouteTabs[2].key, key: 'fontKey' }} isTab={false} />
                <XlbTipHelp
                  ref={xlbFontRef}
                  style={{ marginLeft: 4 }}
                  size={20}
                  color={'#C9CDD4'}
                  showProps={{
                    type: 'tips',
                    desc: '开启后拣货任务字体大小跟随系统设置',
                    key: 'fontKey',
                  }}
                />
              </View>
            }
          />
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  view_content: {
    paddingLeft: normalize(12),
    paddingRight: normalize(12),
  },
  box: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 1)',
    paddingVertical: normalize(18),
    paddingHorizontal: normalize(20),
    borderRadius: normalize(8),
    height: 56,
  },
})

export default NoticeSetting
