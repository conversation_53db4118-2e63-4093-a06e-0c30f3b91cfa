import React, {use, useEffect, useMemo, useState} from 'react';
import {
  Image,
  StyleSheet,
  Pressable,
  InteractionManager,
  NativeModules,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  TouchableWithoutFeedbackComponent,
  View,
  Platform,
} from 'react-native';
import dayjs from 'dayjs';

import {
  addAccessApps,
  deleteAccessApps,
  getAccessApps,
} from '@xlb/business-base/src/screens/staging/server';
import useStore from '@xlb/business-base/src/screens/staging/store';
import Toast from 'react-native-root-toast';
import {images} from '@xlb/common/src/config/images';
import XlbTag from '@xlb/common/src/components/features/xlbTag';
import {normalize} from '@xlb/common/src/config/theme';
import {authModel} from '@xlb/business-base/src/models/auth';
import useHasAuth from '@xlb/common/src/hooks/useHasAuth';
import {CONST} from '@xlb/common/src/utils';
import Config from 'react-native-config';
import {useFlagPLant} from '../../../../hook/useFlagPlant';
// import { useFlagPLant } from '@xlb/business-kms/src/screens/newFlagPlant/hooks/useFlagPlant'

import AnalyticsUtil from '@xlb/common/src/utils/AnalyticsUtil';
import SplashScreen from 'react-native-splash-screen';
import userInfosModal from '@xlb/business-base/src/models/userInfosModal';
/**
 * 单个应用icon展示
 * @constructor
 * @param props
 */

export default function ApplicationIndex(props: any) {
  const {type, route, image, text, onPress, createTime} = props;
  const {setQuickAccessApps, quickAccessApps, isEdit} = useStore(
    state => state,
  );
  const {monthCount} = userInfosModal(state => state);

  // 获取应用
  const getApps = async () => {
    const res = await getAccessApps({});
    if (res.code === 0) {
      setQuickAccessApps(res.data || []);
    }
  };

  // 增加应用
  const addApps = async (app: string) => {
    const appList = [...quickAccessApps, app];
    if (appList?.length > 12) {
      Toast.show('我的常用最多只能添加12个！');
      return;
    }
    const res = await addAccessApps({
      names: appList,
    });
    if (res.code === 0) {
      getApps();
    }
  };

  // 删除应用
  const deleteApps = async (app: string) => {
    const res = await deleteAccessApps({
      names: [app],
    });
    if (res.code === 0) {
      getApps();
    }
  };

  //   const [flag, setFlag] = useState(operation)
  const _onPress = () => {
    // 不是编辑状态就跳转页面
    if (!isEdit) {
      if (text === '插旗系统') {
        //如果是插旗系统则跳转到原生界面
        AnalyticsUtil[Platform.OS === 'ios' ? 'onPageBegin' : 'onPageStart'](
          `插旗系统`,
        );
        const userInfos = authModel?.state?.userInfos;
        var Push = NativeModules.PushNativeMapManager;
        Push?.OpenAMap({
          token: authModel?.state?.userInfos?.access_token,
          baseUrl: `https://react-web.react-web.ali-${Config.ENV}.xlbsoft.com/`,
          companyId: userInfos?.company_id,
          userName: userInfos?.name,
          editPoint: useHasAuth(['点位规划', '编辑']),
          queryPoint: useHasAuth(['点位规划', '查询']),
          secondEditPoint: useHasAuth(['点位规划/复评编辑', '编辑']),
          sharePoint: useHasAuth(['点位规划', '分享']),
          rejectPoint: useHasAuth(['点位规划', '否决']),
          deletePoint: useHasAuth(['点位规划', '删除']),
          auditPoint: useHasAuth(['点位规划', '审核']),
          firstAuditPoint: useHasAuth(['点位规划', '初评']),
          secondAuditPoint: useHasAuth(['点位规划', '复评']),
          transformPoint: useHasAuth(['点位规划/批量转交', '编辑']),
          editBuniess: useHasAuth(['商圈规划', '编辑']),
          deleteBuniess: useHasAuth(['商圈规划', '删除']),
          firstAuditBuniess: useHasAuth(['商圈规划', '初评']),
          secondAuditBuniess: useHasAuth(['商圈规划', '复评']),
          secondEditBuniess: useHasAuth(['商圈规划/复评编辑', '编辑']),
          rejectBuniess: useHasAuth(['商圈规划', '否决']),
          environment: Config.ENV,
        });
      } else {
        onPress(route);
      }
    }
  };
  useFlagPLant();

  const isNew = useMemo(() => {
    if (!createTime) return false;
    const nowDate = dayjs(new Date());
    return nowDate.diff(createTime, 'day') <= 30;
  }, [createTime]);

  const renderContent = () => (
    <View
      style={{
        alignItems: 'center',
        // borderWidth: 1,
        position: 'relative',
        marginTop: 16,
      }}>
      {isNew && !isEdit && (
        <XlbTag
          text={'NEW!'}
          tagStyle={{position: 'absolute', zIndex: 999, right: -10, top: -10}}
          textStyle={{fontStyle: 'italic'}}
        />
      )}
      {text === '月度对账单' && monthCount > 0 && !isEdit && (
        <View style={{...styles.circle, right: type === 'access' ? -2 : 10}}>
          <Text style={styles.circleText}>{monthCount}</Text>
        </View>
      )}
      <Image
        style={{width: normalize(48), height: normalize(48)}}
        source={image}
      />
      {/* 编辑时--模块展示 */}
      {type === 'module' && isEdit && quickAccessApps.indexOf(text) === -1 && (
        <TouchableOpacity
          activeOpacity={0.5}
          onPress={() => {
            addApps(text);
          }}
          style={{position: 'absolute', top: -10, right: 10}}>
          <Image
            style={{width: 24, height: 24}}
            source={images.appAddToQuickAccess}
          />
        </TouchableOpacity>
      )}

      {/* 编辑时--我的常用展示 */}
      {type === 'access' && isEdit && (
        <TouchableOpacity
          activeOpacity={0.5}
          onPress={() => {
            deleteApps(text);
          }}
          style={{position: 'absolute', top: -10, right: -5}}>
          <Image style={{width: 24, height: 24}} source={images.appDelete} />
        </TouchableOpacity>
      )}
      <Text
        style={{
          fontSize: normalize(12),
          top: 2,
          color: 'rgba(31, 33, 38, 1)',
          marginTop: 8,
        }}>
        {text}
      </Text>
    </View>
  );

  return (
    <View>
      {isEdit ? (
        renderContent()
      ) : (
        <TouchableOpacity activeOpacity={0.5} onPress={_onPress}>
          {renderContent()}
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  circle: {
    position: 'absolute',
    zIndex: 999,
    right: 11,
    top: -8,
    width: 20, // 直径
    height: 20, // 直径
    borderRadius: 50, // 确保宽高相等且 borderRadius 为宽高的一半以形成圆形
    backgroundColor: '#F53F3F', // 背景颜色为红色
    // borderWidth: 1,
    // borderColor: '#fff',
  },
  circleText: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 20,
  },
});
