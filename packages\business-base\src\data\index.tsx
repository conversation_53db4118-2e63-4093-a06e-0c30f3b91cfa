import { erpComponent } from './component/erp'
import { globalComponent } from './component/global'
import { crmComponent } from './component/crm'
import { wmsComponent } from './component/wms'
import { tmsComponent } from './component/tms'
import { bmsComponent } from './component/bms'
import { retailComponent } from './component/retail'
import { sdsComponent } from './component/sds'
import { smsComponent } from './component/sms'
import { emsComponent } from './component/ems'

export interface Component {
  /**
   * 组件名称
   */
  label: string
  /**
   * 唯一标识
   */
  key: ComponentKey
  /**
   * 组件类型
   */
  type: string
  /**
   * 缩略图
   */
  image?: string
  /**
   * 组件配置的自定义数据集合
   */
  customerData?: Record<any, any>
  /**
   * 限制数量
   * @default 1
   */
  limit?: number
  /**
   * 不知道什么其他字段
   */
  [key: string]: any
}

export type ComponentKey =
  | 'globalNews'
  | 'globalAnnounce'
  | 'globalApplicationList'
  | 'globalFastEntrance'
  | 'globalSwiper'
  | 'memberData'
  | 'campaignsData'
  | 'couponData'
  | 'crmIntentionClientAnalysis'
  | 'crmClientAnalysis'
  | 'crmStoreApplyAnalysis'
  | 'crmStoreApplyTop'
  | 'bmsMyBalance'
  | 'bmsPayTransactions'
  | 'bmsMerchantRefund'
  | 'erpAbnormalityAlarmCard'
  | 'erpBlackListCard'
  | 'erpDeliveryAnalysisCard'
  | 'erpDistributionCenterCard'
  | 'erpHighlightedAreasCard'
  | 'erpOrderCompletionAccuracy'
  | 'erpHonorRolCard'
  | 'erpSameDayDeliveryCard'
  | 'erpBestSellerReminderCard'
  | 'erpSaleAnalysis'
  | 'wmsReceivedProgress'
  | 'wmsThroughputTrend'
  | 'wmsStockTarget'
  | 'wmsValidityWarning'
  | 'wmsHumanEffectivenessAnalysis'
  | 'wmsPerformanceAnalysis'
  | 'tmsOrderData'
  | 'tmsOrderEcharts'
  | 'tmsStorehouseAreaOrder'
  | 'tmsLoadingProgress'
  | 'tmsDeliveryTarget'
  | 'tmsElectronContract'
  | 'tmsShippingEcharts'
  | 'tmsCarrierPerformance'
  | 'sdsDevelopTarget'
  | 'sdsDevelopTop3'
  | 'sdsRent'
  | 'sdsRentRate'
  | 'sdsMarketCapacity'
  | 'sdsMarketRate'
  | 'sdsBadStoreRate'
  | 'smsInspectStoreSingle'
  | 'smsInspectStores'
  | 'smsTopProblemSingle'
  | 'smsTopProblemSum'
  | 'smsStoreRanking'
  | 'smsRegionRanking'
  | 'smsWarZoneRanking'
  | 'smsStoreTask'
  | 'sdsflagMap'
  | 'emsConstructionStage'
  | 'emsCurrentProjects'
  | 'emsDeliveredProjects'
  | 'emsMaintenanceWorkOrders'
  | 'emsMaintenanceServices'
  | 'emsMaintenanceUnits'
  | 'emsAddWorkOrder'


export let HomeModuleData = () => {
  return [...globalComponent, ...erpComponent, ...wmsComponent, ...tmsComponent, ...retailComponent, ...crmComponent, ...bmsComponent, ...sdsComponent, ...smsComponent, ...emsComponent]
}

export const HomeModuleSelectData = ({ isKMS, isEMS }: any): Component[] => {
  return [
    {
      label: '公告',
      key: 'globalAnnounce' as ComponentKey,
      type: 'announce',
      isSelect: true,
      group: 'global',
    },
    {
      type: 'swiper',
      label: '轮播图',
      key: 'globalSwiper',
      isSelect: true,
      image: require('@xlb/common/src/assets/component/global-swiper.png'),
      group: 'global',
    },
    {
      label: '消息',
      key: 'globalNews',
      type: 'news',
      group: 'global',
      isSelect: true,
    },
    {
      label: '快捷入口',
      key: 'globalFastEntrance',
      type: 'fastEntrance',
      isSelect: true,
      group: 'global',
    },
    {
      type: 'applicationList',
      label: '应用列表',
      key: 'globalApplicationList',
      isSelect: true,
      image: require('@xlb/common/src/assets/component/global-applicationList.png'),
      group: 'global',
    },
  ]
}

export const componentGroupMap: Record<string, any> = {
  global: {
    label: '全局组件',
    key: 'global',
  },
  erp: {
    label: 'ERP组件',
    key: 'erp',
  },
  wms: {
    label: 'WMS组件',
    key: 'wms',
  },
  tms: {
    label: 'TMS组件',
    key: 'tms',
  },
  mem: {
    label: '零售组件',
    key: 'mem',
  },
  crm: {
    label: 'CRM组件',
    key: 'crm',
  },
  bms: {
    label: 'BMS组件',
    key: 'bms',
  },
  sds: {
    label: 'SDS组件',
    key: 'sds',
  },
  sms: {
    label: 'SMS组件',
    key: 'sms',
  },
  ems: {
    label: 'EMS组件',
    key: 'ems',
  },
}
