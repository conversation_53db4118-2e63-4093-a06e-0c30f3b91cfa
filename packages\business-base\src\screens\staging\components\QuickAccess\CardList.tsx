import {commonStyles, EmptyBox} from '@xlb/common/src/components';
import React, {useCallback, useEffect, useMemo, useState, memo} from 'react';
import {View, Platform, Text, Pressable} from 'react-native';
import useEntranceStore from '@xlb/business-base/src/screens/entrance/useEntranceStore';
import {
  applicationFields,
  applicationsArr,
} from '@xlb/common/src/config/fields';
import useHasAuth from '@xlb/common/src/hooks/useHasAuth';
import {useNavigation} from '@react-navigation/native';
import {DraggableGrid} from 'react-native-draggable-grid';
import useStore from '@xlb/business-base/src/screens/staging/store';
import {
  addAccessApps,
  getAccessApps,
} from '@xlb/business-base/src/screens/staging/server';
import AnalyticsUtil from '@xlb/common/src/utils/AnalyticsUtil';
import {images} from '@xlb/common/src/config/images';
import ApplicationIndex from '../Application';
import {XlbIconfontNew} from '@xlb/components-rn';
import XlbIcon from '@xlb/common/src/assets/iconfont';
import XLBStorage from 'src/utils/storage';

interface AppProps {
  moduleName?: string;
  name?: string;
  route?: string;
  image?: any;
  isHasAuth?: string[];
  key?: string;
}

export interface CardProps {
  /** 应用名称图片详细信息 */
  data?: AppProps[];
  /** 应用模块名称 */
  title?: string;
  /** 点击事件 */
  onPress?: () => void;
  /** 是否是搜索 */
  isSearch?: boolean;
  /** 导航跳转 */
  onRoute?: (route: any) => Promise<void>;
}

// 优化：将应用过滤逻辑提取为独立函数，避免重复计算
const filterAuthorizedApps = (apps: any[], system: string) => {
  return apps.filter(app => useHasAuth(app.isHasAuth, false, app?.appType));
};

// 优化：将数据处理逻辑提取为独立函数
const processQuickAccessData = (quickAccessApps: string[], authorizedApps: any[]) => {
  if (!quickAccessApps?.length || !authorizedApps?.length) {
    return [];
  }
  
  // 创建应用名称到应用对象的映射，提高查找效率
  const appMap = new Map();
  authorizedApps.forEach(app => {
    appMap.set(app.name, app);
  });
  
  // 直接映射并过滤，减少循环次数
  return quickAccessApps
    .map(name => {
      const app = appMap.get(name);
      return app ? {...app, key: app.route} : null;
    })
    .filter(Boolean)
    .slice(0, 17);
};

// 优化：使用 memo 包装子组件，避免不必要的重渲染
const MemoizedApplicationIndex = memo(ApplicationIndex);

function XlbNewCommApp({readonly = false}) {
  const navigation = useNavigation<any>();
  const {
    setIsItemLongPressActive,
    quickAccessApps,
    setQuickAccessApps,
    isEdit,
    setIsEdit,
  } = useStore(state => state);

  const system = useEntranceStore((state: any) => state.system);
  const [moduleLists, setModuleLists] = useState<any>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 优化：缓存授权应用列表，避免每次重新过滤
  const authorizedApps = useMemo(() => {
    return filterAuthorizedApps(applicationsArr, system);
  }, [system]); // 只依赖 system，减少重新计算

  // 优化：缓存处理后的数据
  const processedData = useMemo(() => {
    return processQuickAccessData(quickAccessApps, authorizedApps);
  }, [quickAccessApps, authorizedApps]);

  // 优化：将导航逻辑提取并缓存
  const goTo = useCallback(async (route: any, params = null) => {
    const arr = applicationFields[system];
    let obj = {};

    if (arr) {
      obj = arr.find((item: any) => item.route === route);
    }

    navigation.navigate(route, {
      ...(params || {}),
      system: system,
      pageName: obj?.name || '',
      moduleName: obj?.moduleName || '',
    });
  }, [navigation, system]);

  const onRoute = useCallback((route: any) => goTo(route), [goTo]);

  // 优化：简化本地数据加载逻辑
  useEffect(() => {
    let isMounted = true;
    
    const loadLocalData = async () => {
      if (quickAccessApps?.length > 0) return; // 如果已有数据，不重复加载
      
      try {
        const storedApps = await XLBStorage.getItem('quickAccessApps');
        if (isMounted && storedApps?.length > 0) {
          setQuickAccessApps(storedApps);
        }
      } catch (error) {
        console.error('加载本地数据失败:', error);
      }
    };

    loadLocalData();
    
    return () => {
      isMounted = false;
    };
  }, []); // 移除依赖，只在组件挂载时执行一次

  // 优化：简化 getApps 函数
  const getApps = useCallback(async () => {
    if (isLoading) return; // 防止重复请求
    
    setIsLoading(true);
    try {
      const [localApps, serverResponse] = await Promise.allSettled([
        XLBStorage.getItem('quickAccessApps'),
        getAccessApps({})
      ]);

      // 处理本地数据
      if (localApps.status === 'fulfilled' && localApps.value?.length > 0) {
        setQuickAccessApps(localApps.value);
      }

      // 处理服务器数据
      if (serverResponse.status === 'fulfilled' && serverResponse.value?.code === 0) {
        const serverApps = serverResponse.value.data || [];
        const localData = localApps.status === 'fulfilled' ? localApps.value : [];
        
        if (JSON.stringify(localData) !== JSON.stringify(serverApps)) {
          await XLBStorage.setItem('quickAccessApps', serverApps);
          setQuickAccessApps(serverApps);
        }
      }
    } catch (error) {
      console.error('获取应用数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, setQuickAccessApps]);

  // 优化：简化 addApps 函数
  const addApps = useCallback(async (data: any) => {
    const names = data?.map(v => v.name) || [];
    try {
      const res = await addAccessApps({ names });
      if (res.code === 0) {
        await getApps();
      }
    } catch (error) {
      console.error('添加应用失败:', error);
    }
  }, [getApps]);

  // 优化：使用 processedData 直接设置 moduleLists
  useEffect(() => {
    setModuleLists(processedData);
  }, [processedData]);

  // 优化：缓存渲染项
  const renderItem = useCallback((app: any) => {
    return (
      <View key={app.key}>
        <MemoizedApplicationIndex
          type={'access'}
          text={app.name}
          image={app.image}
          route={app.route}
          onPress={onRoute}
        />
      </View>
    );
  }, [onRoute]);

  // 优化：缓存拖拽数据
  const draggableData = useMemo(() => {
    return moduleLists?.map(item => ({
      ...item,
      disabledDrag: !isEdit,
    })) || [];
  }, [moduleLists, isEdit]);

  const handleDragRelease = useCallback((data) => {
    setModuleLists(data);
    addApps(data);
  }, [addApps]);

  const handleDragItemActive = useCallback(() => {
    setIsItemLongPressActive(true);
  }, [setIsItemLongPressActive]);

  const handleAddPress = useCallback(() => {
    if (!isEdit) {
      setIsEdit(true);
    }
  }, [isEdit, setIsEdit]);

  return (
    <View
      style={{
        width: '100%',
        flexDirection: 'row',
        flexWrap: 'wrap',
      }}>
      {!moduleLists?.length && !readonly ? (
        <Pressable
          style={{
            display: 'flex',
            flexDirection: 'row',
            marginHorizontal: 12,
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 16,
            backgroundColor: '#F4F5F7',
            padding: 10,
            borderRadius: 4,
            width: '93%',
          }}
          onPress={handleAddPress}>
          <XlbIcon
            name="tianjia"
            color="#1F2126"
            size={18}
            style={{opacity: isEdit ? 0.3 : 1}}
          />
          <Text
            style={{
              color: '#1F2126',
              fontSize: 16,
              marginLeft: 2,
              opacity: isEdit ? 0.3 : 1,
            }}>
            添加常用
          </Text>
        </Pressable>
      ) : (
        <DraggableGrid
          numColumns={4}
          renderItem={renderItem}
          data={draggableData}
          onDragRelease={handleDragRelease}
          onDragItemActive={handleDragItemActive}
        />
      )}
    </View>
  );
}

export default memo(XlbNewCommApp);
