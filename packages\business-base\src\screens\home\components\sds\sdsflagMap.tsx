import { View, Image, Platform, NativeModules, InteractionManager, TouchableOpacity } from 'react-native'
import React from 'react'
import { XlbCard } from '@xlb/components-rn'
import AnalyticsUtil from '@xlb/common/src/utils/AnalyticsUtil'
import { authModel } from '@xlb/business-base/src/models/auth'
import { CONST } from '@xlb/common/src/utils'
import useHasAuth from '@xlb/common/src/hooks/useHasAuth'
import Config from 'react-native-config'
export const SdsflagMap = () => {
  const _onPress = () => {
    AnalyticsUtil[Platform.OS === 'ios' ? 'onPageBegin' : 'onPageStart'](`插旗系统`)
    const userInfos = authModel?.state?.userInfos
    var Push = NativeModules.PushNativeMapManager
    InteractionManager.runAfterInteractions(() => {
      Push.OpenAMap({
        token: authModel?.state?.userInfos?.access_token,
        baseUrl: Config.ENV === 'DEVELOP' ? CONST.NewApiService.kms : Config.ERP_URL + '/',
        companyId: userInfos?.company_id,
        userName: userInfos?.name,
        editPoint: useHasAuth(['点位规划', '编辑']),
        queryPoint: useHasAuth(['点位规划', '查询']),
        secondEditPoint: useHasAuth(['点位规划/复评编辑', '编辑']),
        sharePoint: useHasAuth(['点位规划', '分享']),
        rejectPoint: useHasAuth(['点位规划', '否决']),
        deletePoint: useHasAuth(['点位规划', '删除']),
        auditPoint: useHasAuth(['点位规划', '审核']),
        firstAuditPoint: useHasAuth(['点位规划', '初评']),
        secondAuditPoint: useHasAuth(['点位规划', '复评']),
        transformPoint: useHasAuth(['点位规划/批量转交', '编辑']),
        editBuniess: useHasAuth(['商圈规划', '编辑']),
        deleteBuniess: useHasAuth(['商圈规划', '删除']),
        firstAuditBuniess: useHasAuth(['商圈规划', '初评']),
        secondAuditBuniess: useHasAuth(['商圈规划', '复评']),
        secondEditBuniess: useHasAuth(['商圈规划/复评编辑', '编辑']),
        rejectBuniess: useHasAuth(['商圈规划', '否决']),
        environment: Config.ENV,
      })
    })
  }
  return (
    <XlbCard
      title={'插旗地图'}
      containerStyle={{
        marginTop: 0,
        marginHorizontal: 0,
      }}
    >
      <TouchableOpacity activeOpacity={0.5} onPress={_onPress}>
        <View style={{ marginTop: 10 }}>
          <Image
            source={require('@xlb/common/src/assets/component/maps.png')}
            resizeMode="stretch"
            style={{
              width: '100%',
              height: 200,
              resizeMode: 'cover',
            }}
          />
        </View>
      </TouchableOpacity>
    </XlbCard>
  )
}
