import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'

// 查询销售
const getSaleData = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.bossreport.possale.find', params, {
    timeout: 200000,
  })
}

// 查询出库
const getOutData = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.bossreport.centerout.find', params, {
    timeout: 200000,
  })
}

// 查询收货
const getReceiveData = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.bossreport.receive.find', params, {
    timeout: 200000,
  })
}

// 查询账款
const getAccountData = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.bossreport.account.find', params, {
    timeout: 200000,
  })
}

// 查询利润
const getProfitData = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.bossreport.grossprofit.find', params, {
    timeout: 200000,
  })
}

const getStoreLabel = () => {
  return ErpHttp.post<CommonResponse>('/erp/hxl.erp.storelabel.find')
}
export const bossTableApi = {
  getSaleData,
  getOutData,
  getReceiveData,
  getAccountData,
  getProfitData,
  getStoreLabel,
}
