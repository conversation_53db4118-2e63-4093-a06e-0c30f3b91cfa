const fs = require('fs');
let version = {};
try {
  version = JSON.parse(fs.readFileSync('./version.json').toString());
} catch (e) {
  version = {};
}
let packageJson = JSON.parse(fs.readFileSync('./package.json').toString());

if (
  !(version[packageJson.name] && typeof version[packageJson.name] === 'object')
) {
  version[packageJson.name] = {};
}
version[packageJson.name][process.env.CI_COMMIT_REF_NAME] =
  `${process.env.DATETIME}-${process.env.CI_COMMIT_REF_NAME}-${process.env.CI_COMMIT_SHA}`;

let branch = process.env.CI_COMMIT_REF_NAME;
let branches = branch.startsWith('prod')
  ? ['prod', 'prod-gray']
  : ['staging', 'test', 'dev'];

for (let app in version) {
  if (app.startsWith('remote-app-') || app.startsWith('host-app-')) {
    for (let branch in version[app]) {
      if (!branches.some(prefix => branch.startsWith(prefix))) {
        delete version[app][branch];
      }
    }
  } else {
    console.log(app);

    delete version[app];
  }
}
fs.writeFileSync('./version.json', stringifyJSON(version));
function stringifyJSON(obj) {
  keys = [];
  if (obj) {
    for (var key in obj) {
      keys.push(key);
    }
  }
  keys.sort();
  var tObj = {};
  var key;
  for (var index in keys) {
    key = keys[index];
    tObj[key] = obj[key];
  }
  return JSON.stringify(tObj, null, 2);
}
