import React, { useState, useEffect, useMemo, useRef } from 'react'

import { View, SafeAreaView } from 'react-native'

import { last } from 'lodash'

import { Tree } from '@fruits-chain/react-native-xiaoshu'
import { XlbButton } from '@xlb/components-rn'

import { useProRoute } from '@xlb/common/src/hooks'

import { PageWrapper } from '../PageWrapper'

import { ErpHttp } from '../../services/lib/erphttp'

const Index = () => {
  const maxLevel = useRef(1)

  const { route, navigation } = useProRoute()

  const params = route?.params

  const { title, onBack, onSelect, selectedList = [], url = '/erp/hxl.erp.org.tree', mutex } = params

  const chosedList = useRef<any[]>(selectedList || [])
  const [list, setList] = useState<any[]>(selectedList || [])
  const [value, setValue] = useState(selectedList ? selectedList.map((item) => item.id) : [])

  function arrayToTree(data: any[]) {
    const idMap: any = {} // 创建id映射表
    data.forEach((item: any) => {
      idMap[item.id] = { ...item, children: [] } // 初始化每个元素的children字段，并存储在idMap中
    })
    const tree: any[] = [] // 初始化树结构数组
    data.forEach((item: any) => {
      const { id, parent_id } = item
      if (parent_id === '' || parent_id === null || parent_id === undefined || parent_id === 0) {
        // 如果没有parentId，则作为根节点添加到树结构数组中
        tree.push(idMap[id])
      } else {
        // 否则，将当前元素添加到其父节点的children字段中
        if (idMap[parent_id] !== null && idMap[parent_id] !== undefined) {
          idMap[parent_id].children.push(idMap[id])
        }
      }
    })
    return tree // 返回构建好的树结构数组
  }

  const data = useMemo(() => arrayToTree(list.map((item: any) => ({ ...item, label: item.name, value: item.id }))), [list])

  const initData = async () => {
    const res: any = await ErpHttp.post(url, { page_number: 0, page_size: 1000 })
    if (res?.code === 0 && res?.data) {
      let arr = res?.data || []
      if (!mutex) {
        maxLevel.current = Math.max(res.data.map((item) => item.level))
        const level = selectedList.length ? selectedList[0].level : null
        arr = res.data.map((item: any) => ({ ...item, label: item.name, value: item.id, disabled: level ? level !== item.level : false }))
      }
      setList(arr)
    }
  }

  const onChange = (v, option, e) => {
    if (!mutex) {
      if (maxLevel.current === 1 || maxLevel.current === 0) {
        setValue(v.length ? [last(v)] : [])
        chosedList.current = option.length ? [last(option)] : []
      } else {
        chosedList.current = option
        setValue(v)
      }
      setList(list.map((item) => ({ ...item, disabled: option.length > 0 && item.level !== e.option.level })))
    } else {
      chosedList.current = option
      setValue(v)
    }
  }

  useEffect(() => {
    initData()
  }, [])

  return (
    <>
      <PageWrapper title={title} onBack={onBack ? () => onBack() : undefined}>
        <Tree
          value={value}
          options={data}
          defaultExpandAll
          multiple
          search
          multipleMode={Tree.MultipleMode[mutex ? 'NORMAL' : 'INDEPENDENT']}
          onChange={onChange}
        ></Tree>
        <View
          style={{
            width: '100%',
            backgroundColor: '#fff',
            paddingHorizontal: 15,
            borderTopColor: '#E5E6EA',
            borderTopWidth: 1,
            paddingVertical: 12,
            flexDirection: 'row',
          }}
        >
          <XlbButton
            style={{ flex: 1, marginRight: 16, borderRadius: 6 }}
            onPress={() => {
              setValue([])
              setList(list.map((item) => ({ ...item, disabled: false })))
              chosedList.current = []
            }}
          >
            重置
          </XlbButton>
          <XlbButton
            style={{ flex: 1, borderRadius: 6 }}
            type="primary"
            onPress={() => {
              onSelect?.(chosedList.current)
              navigation.goBack()
            }}
          >
            确定
          </XlbButton>
        </View>
      </PageWrapper>
      <SafeAreaView style={{ backgroundColor: '#fff' }}></SafeAreaView>
    </>
  )
}

export default Index
