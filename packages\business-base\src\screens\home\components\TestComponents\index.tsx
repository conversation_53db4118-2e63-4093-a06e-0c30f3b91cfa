import { useNavigation } from "@react-navigation/native";
import useSelectStore from "@xlb/common/src/components/features/xlbSelectStore/model";
import { XlbText } from "@xlb/components-rn";
import { View } from "react-native";
import useStore from './modal/useStore'
import useProductSalesAnalysis from './modal/store1';
import { XlbHeader } from "@xlb/common/src/xlb-components-new";

const TestComponents = () => {
  const navigation = useNavigation<any>();
  const selectStoreModel = useSelectStore((state: any) => state)
  const Model = useStore((state: any) => state)
  const model = useProductSalesAnalysis((state: any) => state);
  return (
    <>
      <XlbHeader
        onBack={() => {
          navigation.goBack();
        }}
        hasInputFilter={false}
        title="Demo"
      />
      <View style={{ padding: 16 }}>
        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedSelectStoreH5', {
            isMultiple: false,
            postUrl: '/erp/hxl.erp.store.short.page',
          });
        }}>选择门店-h5</XlbText>
        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedSelectStore', {
            isMultiple: true,
            //  model: {
            //     listName: storeList,
            //     setListName: updateStore
            // },
            setListName: "setListName",
            listName: "listName",
            postUrl: "/erp/hxl.erp.store.short.page",
            postParams: {
              status: true,
            }
          });
        }}>选择门店</XlbText>

        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedSelectStoreNew', {
            title: '选择门店',
            backRoute: 'goBack',
            isMultiple: false,
            postParams: {
              status: true,
              center_flag: true,
              // exclude_level_center_store: 3,
              // store_id: authModel?.state?.userInfos?.store.id,
            }
          });
        }}>选择门店-new</XlbText>

        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedSelectStoreArea', {
            model: selectStoreModel,
            listName: 'AreaList',
            setListName: 'setAreaList',
            isMultiple: true,
            postParams: {
              status: true,
            },
            postUrl: '/erp/hxl.erp.storearea.find'
          });
        }}>选择门店区域</XlbText>

        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedSelectStoreLabel', {
            listName: 'LabelList',
            setListName: 'setLabelList',
            isMultiple: false,
            model: selectStoreModel,
            postUrl: '/erp/hxl.erp.storelabel.find',
          });
        }}>选择门店标签</XlbText>

        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedSelectStoreHouse', {
            isMultiple: false,
            model: Model,
            listName: 'storehouseList',
            setListName: 'setStorehouseList',
            type: 'storeHouse',
            status: true,
            postParams: {
              store_id: 100000000838
            }
          });
        }}>选择仓库</XlbText>

        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedSelectStoreHouseNew', {
            isMultiple: false,
            model: Model,
            listName: 'storehouseList',
            setListName: 'setStorehouseList',
            type: 'storeHouse',
            status: true,
            selectedStores: [],
            postParams: {
              store_id: 100000000838
            }
          });
        }}>选择仓库-new</XlbText>

        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedSelectSupplier', {
            postParams: { status: true },
          });
        }}>选择供应商</XlbText>

        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedSelectCategory', {
            type: 'page',
            listName: 'categoryList',
            setListName: 'setCategoryList',
            isMultiple: false,
            model: Model,
            backRoute: 'goBack',
            postParams: {
              status: true,
            },
          });
        }}>选择分类</XlbText>

        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedSelectGoods', {
            isMultiple: true,
            model: model,
            listName: 'newGoodList',
            changeItem: 'changeItem',
            setListName: 'setDetails',
            postUrl: '/erp/hxl.erp.item.short.page',
            backRoute: 'productSalesAnalysis',
            chooseMode: 'checkBox',
            resetButton: true,
            isAdd: true,
            onChangeList: (list: any) => {
              model.setNewGoodList(list);
            },
          });
        }}>选择商品</XlbText>

        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedNewSelectGoods', {
            isMultiple: true,
            model: model,
            listName: 'newGoodList',
            changeItem: 'changeItem',
            setListName: 'setDetails',
            postUrl: '/erp/hxl.erp.item.short.page',
            // backRoute: 'productSalesAnalysis',
            chooseMode: 'checkBox',
            resetButton: true,
            isAdd: true,
            onChangeList: (list: any) => {
              model.setNewGoodList(list);
            },
          });
        }}>选择商品-new</XlbText>

        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedNewSelectGoodsDetail', {
            isShowPrice: true,
            // stock_state: item?.stock_state,
            // isPre: route?.params?.isPre,
            // onConfirm: (data: any) => {
            //   computeTotal(data)
            //   setCacheQuantity(data.id, data.quantity)
            // },
            // ...route?.params?.postParams,
          });
        }}>选择商品详情-Detail</XlbText>

        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.DeprecatedNewSelectGoodsDetails', {
            isShowPrice: true,
            item_id: 6779900011808,
            // stock_state: item?.stock_state,
            // isPre: route?.params?.isPre,
            // onConfirm: (data: any) => {
            //   computeTotal(data)
            //   setCacheQuantity(data.id, data.quantity)
            // },
            isEdit: true,
          });
        }}>选择商品详情-Details</XlbText>

        <XlbText onPress={() => {
          navigation.navigate('RemoteAppBase.Notice');
        }}>公告</XlbText>
      </View>
    </>
  )
}

export default TestComponents;


// route: 
// {
//   "key": "RemoteAppBase.DeprecatedNewSelectGood-bZ3fUIwqWCxBes7v88AyL",
//   "name": "RemoteAppBase.DeprecatedNewSelectGood",
//   "params": {
//       "isMultiple": true,
//       "model": {
//           "keyword": "",
//           "status": "INIT",
//           "memo": "",
//           "details": [],
//           "changeItem": {},
//           "storeDetail": {},
//           "isEditIndex": false,
//           "newGoodList": []
//       },
//       "listName": "newGoodList",
//       "changeItem": "changeItem",
//       "setListName": "setDetails",
//       "postUrl": "/erp/hxl.erp.item.short.page",
//       "chooseMode": "checkBox",
//       "resetButton": true,
//       "isAdd": true
//   }
// }

// goods_item: {
//   "id": 6779900011808,
//   "code": "1806020062",
//   "bar_code": "6921440371489",
//   "name": "东鹏特饮500ml",
//   "purchase_spec": "1*24瓶",
//   "item_type": "STANDARD",
//   "unit": "瓶",
//   "expire_type": 1,
//   "expire_type_num": 365,
//   "period": "365天",
//   "default_image_url": "",
//   "item_category_name": "312",
//   "delivery_unit": "件",
//   "delivery_ratio": 24,
//   "purchase_price": 3.3334,
//   "purchase_ratio": 24,
//   "purchase_unit": "件",
//   "stop_purchase": false,
//   "stop_sale": false,
//   "sale_unit": "瓶",
//   "sale_ratio": 1,
//   "output_tax_rate": 13,
//   "input_tax_rate": 13,
//   "item_brand_id": 6779900001612,
//   "item_brand_name": null,
//   "item_brand_level": null,
//   "item_brand_level_name": null,
//   "item_category": null,
//   "executive_standard": null,
//   "weigh": false,
//   "quantity": 0,
//   "money": 0,
//   "item_id": 6779900011808,
//   "basic_unit": "瓶",
//   "ratio": 1,
//   "use_unit": "瓶"
// }
