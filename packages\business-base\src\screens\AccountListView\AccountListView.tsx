import React, {FC, useEffect, useState} from 'react';
import {Pressable, StyleSheet, TextStyle, View, Text} from 'react-native';
import {commonStyles, XText} from '@xlb/common/src/components';
import XlbIcon from '@xlb/common/src/assets/xlbIconFont';
import {colors} from '@xlb/common/src/config/theme';
import {IconNames} from '@xlb/common/src/assets/iconfont';
import {useNavigation, useRoute} from '@react-navigation/native';
import Header from '@xlb/common/src/xlb-components-new/Header/index';
import {ScrollView} from 'native-base';
import {TOKEN, XlbCard, XlbIconfont, XlbTag, XlbText} from '@xlb/components-rn';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttpnew';
import {authModel} from '../../models/auth';
import {Flex} from '@fruits-chain/react-native-xiaoshu';
import {normalize} from '@xlb/components-rn/styles';
import {$modalAlert} from '@xlb/common/src/components/SecondModal';
import useStore from '@xlb/common/src/components/features/xlbStoreText/model';
import XLBStorage from '@xlb/common/src/utils/storage';
import useRefreshStore from '@xlb/common/src/models/refresh';
// import * as AliyunPush from 'aliyun-react-native-push';
import useAliCloudPush from '@xlb/common/src/hooks/useAliCloudPush';
const AccountListView: FC = () => {
  const route = useRoute<any>();
  const {itemData, edit} = route.params as any; //如果是编辑传递的参数里面会有值
  const userTypeMap = {
    USER: {
      text: '门店',
      color: TOKEN.yellow_10,
    },
    SUPPLIER: {
      text: '供应商',
      color: TOKEN.green_10,
    },
    CLIENT: {
      text: '批发',
      color: TOKEN.primary_10,
    },
    BRAND: {
      text: '品牌',
      color: TOKEN.red_10,
    },
    DELIVERY: {
      text: '配送',
      color: '#9F40FF',
    },
  };
  const navigation = useNavigation<any>();
  const userInfos = authModel?.state?.userInfos;
  const [accountList, setAccountList] = useState<any>([]);
  const setStoreLists = useStore((state: any) => state.setStoreList);
  const setRefresh = useRefreshStore((state: any) => state.setHomeRefresh);
  const {initPush} = useAliCloudPush();
  const getRole = async () => {
    const res = await ErpHttp.post<CommonResponse>(
      '/erp/hxl.erp.account.user.find',
      {tel: userInfos?.tel},
    );
    if (res.code === 0) {
      setAccountList(res.data.account_user_list);
    }
  };
  useEffect(() => {
    getRole();
  }, []);

  const accountChange = async (account: any, company_id: any, v: any) => {
    // 解决首页切换门店问题
    // setStoreList([])
    setStoreLists([]);
    XLBStorage.setItem('currentSelectedStore', null);

    authModel
      .onLogin(account, company_id, undefined, undefined)
      .then(async () => {
        setRefresh();
        XLBStorage.removeItem('PointPlanAdd');
        XLBStorage.removeItem('BusinessDistrictAdd');
        XLBStorage.removeItem('NewStockAdjustOrderAdd');
        // kms 多组织缓存数据
        XLBStorage.removeItem('kmsOrgListKey');
      });
    // AliyunPush.unbindAccount().then(() => initPush());
    navigation.goBack();
  };

  // console.log('AliyunPush', AliyunPush.unbindAccount);
  return (
    <View style={{flex: 1, width: '100%', height: '100%'}}>
      <Header title="切换账号" hasFilter={false} hasInputFilter={false} />
      <ScrollView
        style={{
          // minHeight: 200 - (popupProps.targetPageY + popupProps.targetHeight) - TOKEN.space_3,
          borderBottomLeftRadius: TOKEN.space_4,
          borderBottomRightRadius: TOKEN.space_4,
          zIndex: 2,
          // backgroundColor: 'red',
        }}>
        {accountList.map((e: any) => {
          let isSelect =
            userInfos?.company_id + userInfos?.account ===
            e.company_id + e.account;

          let user_type = e?.user_type;
          if (user_type == 'USER' && e.enable_delivery_center) {
            if (e?.store_name == '管理中心' || e?.enable_organization) {
              user_type = 'BRAND';
            } else {
              user_type = 'DELIVERY';
            }
          }
          return (
            <XlbCard
              key={e.company_id + e.account}
              title={
                <Flex>
                  <XlbText
                    font_size_6
                    grey_10
                    semiBold
                    style={{
                      marginRight: TOKEN.space_2,
                    }}>{`${e.company_id}｜${e.company_name}`}</XlbText>

                  {!!userTypeMap[userInfos?.user_type]?.text && (
                    <XlbTag color={userTypeMap[user_type]?.color}>
                      {userTypeMap[user_type]?.text}
                    </XlbTag>
                  )}
                </Flex>
              }
              containerStyle={{
                backgroundColor: colors.white,
                ...(isSelect && {
                  borderColor: TOKEN.primary_5,
                  borderWidth: StyleSheet.hairlineWidth,
                }),
              }}
              onPress={() => {
                if (authModel?.state?.loginMethod === 'phone') {
                  return $modalAlert(
                    '提示',
                    '账号密码登录用户无法切换账户',
                    () => {},
                    () => {},
                    '',
                    '确认',
                    true,
                  );
                }
                if (authModel?.state?.userInfos?.isTemporarily) return;
                accountChange(e.account, e.company_id, e);
              }}>
              <>
                <View>
                  <Text style={styles['item-text']}>
                    {e?.store_name}-{e?.account}
                  </Text>
                </View>
                {isSelect && (
                  <View style={styles['select-icon']}>
                    <XlbIconfont
                      name="xuanzhong"
                      size={normalize(16)}
                      color="#1A6AFF"></XlbIconfont>
                  </View>
                )}
              </>
            </XlbCard>
          );
        })}
      </ScrollView>
    </View>
  );
};
const styles = StyleSheet.create({
  'select-icon': {
    position: 'absolute',
    right: normalize(12),
    top: '50%',
    marginTop: normalize(4),
  },
  'item-text': {
    color: 'rgba(30, 33, 38, 0.45)',
    fontSize: normalize(13),
  },
});
export default AccountListView;
