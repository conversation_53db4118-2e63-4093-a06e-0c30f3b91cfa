import React, { useEffect, useMemo, useState } from 'react'
import { <PERSON><PERSON>, HeaderTitle, XLoading } from '@xlb/common/src/components'
import { useProRoute } from '@xlb/common/src/hooks'
import { tabList } from './data'
import SaleTab from './components/saleTab'
import OutStoreTab from './components/outStoreTab'
import ReceiveTab from './components/receiveTab'
import OrderMoneyTab from './components/orderMoneyTab'
import PeriodTab from './components/periodTab'
import { bossTableApi } from './api'
import CommonAreaTime from './components/commonAreaTime'
import XlbFastTimeSelectRow from '../../components/xlbFastTimeSelectRow'
import useBossTableStore from './model'
// import Tabs from '@xlb/business-kms/src/screens/pointApply/components/xlb-tab-temporarily'
import BoradWrapper from '@xlb/common/src/components/BoradWrapper'
// import XlbSelectText from '../../../components/xlbSelectText'
import XlbSelectText from '@xlb/business-base/src/components/xlbSelectText'
import useSelectCityState from '@xlb/common/src/components/BusinessAreaAndCitySelector/useSelectCityState'
import BusinessAreaAndCitySelector from '@xlb/common/src/components/BusinessAreaAndCitySelector'
import { TOKEN } from '@xlb/components-rn'

const BossTable = () => {
  const [city, setCity] = useState<any>({ data: [] })
  const { navigation } = useProRoute()
  const [tabLists, setTabLists] = useState<any>(tabList)
  const [isFirst, setIsFirst] = useState<boolean>(true)
  const [loading, setLoading] = useState<boolean>(false)
  const [parentName, setParentName] = useState(['合计'])
  const { date_range, bizday, setBizDay, setDateRange } = useBossTableStore((state: any) => state)
  const { setIsCityShow } = useSelectCityState((state: any) => state)
  const [radiusList, setRadiusList] = useState<any>([
    {
      label: '业务区域',
      value: 'BUSINESS_AREA',
      active: true,
    },
    {
      label: '地级市',
      value: 'CITY',
      active: false,
    },
    {
      label: '门店',
      value: 'STORE',
      active: false,
    },
  ])

  const renderTab = (item: any) => {
    switch (item.value) {
      // 销售
      case 'SALE':
        return (
          <SaleTab
            parentName={parentName}
            setParentName={setParentName}
            radiusList={radiusList}
            setRadiusList={setRadiusList}
            topData={item.topData}
            bottomData={item.bottomData}
          />
        )
      // 出库
      case 'OUTSTORE':
        return <OutStoreTab topData={item.topData} bottomData={item.bottomData} />
      // 收货
      case 'RECEIVE':
        return <ReceiveTab topData={item.topData} bottomData={item.bottomData} />
      // 账款
      case 'ORDERMONEY':
        return <OrderMoneyTab topData={item.topData} bottomData={item.bottomData} />
      // 利润
      case 'PERIOD':
        return <PeriodTab topData={item.topData} bottomData={item.bottomData} />
      default:
        return <></>
    }
  }

  const params = useMemo(() => {
    let data: any = {
      date_range,
      date: bizday,
    }
    if (city.type === 'city') {
      data.city_codes = city.data.map((v: any) => v.code)
    } else if (city.type === 'businessArea') {
      data.business_area_ids = city.data.map((v: any) => v.id)
    }
    return { ...data }
  }, [bizday, city])
  // 销售
  const getSaleData = async () => {
    const findItem = radiusList.find((v: any) => v.active)
    let saleRes: any = {}
    saleRes.saleTopRes = await bossTableApi.getSaleData({
      ...params,
      query_compare_details: true,
      query_last_details: true,
      store_label_id: findItem?.id,
      summary_types: ['DATE'],
    })
    saleRes.saleBottomRes = await bossTableApi.getSaleData({
      ...params,
      store_label_id: findItem?.id,
      parent_name: parentName[parentName.length - 1] === '合计' ? '' : parentName[parentName.length - 1],
      summary_types: [findItem?.value],
    })
    return saleRes
  }
  // 出库
  const getOutStoreData = async () => {
    let outRes: any = {}
    outRes.outStoreTopRes = await bossTableApi.getOutData({
      ...params,
      query_compare_details: true,
      query_last_details: true,
      summary_types: ['DATE'],
    })
    outRes.outStoreBottomRes = await bossTableApi.getOutData({
      ...params,
      summary_types: ['STORE'],
    })
    return outRes
  }
  // 收货
  const getReceiveData = async () => {
    let receiveRes: any = {}
    receiveRes.receiveTopRes = await bossTableApi.getReceiveData({
      ...params,
      query_compare_details: true,
      query_last_details: true,
      summary_types: ['DATE'],
    })
    receiveRes.receiveBottomRes = await bossTableApi.getReceiveData({
      ...params,
      summary_types: ['STORE'],
    })
    return receiveRes
  }
  // 账款
  const getOrderMoneyData = async () => {
    let orderMoneyRes: any = {}
    orderMoneyRes.orderMoneyTopRes = await bossTableApi.getAccountData({
      ...params,
      query_compare_details: true,
      query_last_details: true,
      summary_types: ['STORE'],
    })
    return orderMoneyRes
  }
  // 利润
  const getProfitData = async () => {
    let profitRes: any = {}
    profitRes.periodTopRes = await bossTableApi.getProfitData({
      ...params,
      query_compare_details: true,
      query_last_details: true,
      summary_types: ['DATE'],
    })
    profitRes.periodBottomRes = await bossTableApi.getProfitData({
      ...params,
      summary_types: ['STORE'],
    })
    return profitRes
  }
  // 获取门店标签数组
  const getStoreLabel = async () => {
    const res = await bossTableApi.getStoreLabel()
    if (res.data && res.data.length) {
      res.data.map((v: any) => {
        radiusList.push({
          label: v.store_label_name,
          value: 'STORE',
          active: false,
          id: v.id,
        })
      })
      setRadiusList(radiusList)
    }
  }
  useEffect(() => {
    setLoading(true)
    Promise.all([getSaleData(), getOutStoreData(), getReceiveData(), getOrderMoneyData(), getProfitData()])
      .then((resList) => {
        setLoading(false)
        tabLists.map((v: any, i: number) => {
          if (resList[i][v.dataName[0]]?.code === 0) {
            v.topData = {
              details: resList[i][v.dataName[0]].data?.details || [],
              top_five_details: resList[i][v.dataName[0]].data?.top_five_details || [],
              top_ten_details: resList[i][v.dataName[0]].data?.top_ten_details || [],
              totals: resList[i][v.dataName[0]].data?.totals || [],
            }
          }
          if (resList[i][v.dataName[1]]?.code === 0) {
            v.bottomData = {
              details: resList[i][v.dataName[1]].data?.details || [],
              top_five_details: resList[i][v.dataName[1]].data?.top_five_details || [],
              top_ten_details: resList[i][v.dataName[1]].data?.top_ten_details || [],
              totals: resList[i][v.dataName[1]].data?.totals || [],
            }
          }
        })
        setTabLists([...tabLists])
      })
      .catch(() => {
        setLoading(false)
      })
  }, [params])
  // 销售有个门店标签切换需要单独处理
  useEffect(() => {
    if (isFirst) return setIsFirst(false)
    setLoading(true)
    Promise.all([getSaleData()])
      .then((resList) => {
        setLoading(false)
        if (resList[0][tabList[0].dataName[0]].code === 0) {
          tabList[0].topData = {
            details: resList[0][tabList[0].dataName[0]].data?.details || [],
            top_five_details: resList[0][tabList[0].dataName[0]].data?.top_five_details || [],
            top_ten_details: resList[0][tabList[0].dataName[0]].data?.top_ten_details || [],
            totals: resList[0][tabList[0].dataName[0]].data?.totals || [],
          }
        }
        if (resList[0][tabList[0].dataName[1]].code === 0) {
          tabList[0].bottomData = {
            details: resList[0][tabList[0].dataName[1]].data?.details || [],
            top_five_details: resList[0][tabList[0].dataName[1]].data?.top_five_details || [],
            top_ten_details: resList[0][tabList[0].dataName[1]].data?.top_ten_details || [],
            totals: resList[0][tabList[0].dataName[1]].data?.totals || [],
          }
        }
        setTabLists([...tabLists])
      })
      .catch(() => {
        setLoading(false)
      })
  }, [radiusList])
  useEffect(() => {
    getStoreLabel().then()
  }, [])

  return (
    <BoradWrapper
      dateType={date_range}
      dateValue={bizday}
      onPrev={(date) => {
        setBizDay(date)
      }}
      onNext={(date) => {
        setBizDay(date)
      }}
      onDateChange={(dateValue, dateType) => {
        setBizDay(dateValue)
        setDateRange(dateType)
      }}
      title={'Boss报表'}
      renderfilter={() => {
        return (
          <XlbSelectText
            showText={city.data.length ? '部分区域' : '全部区域'}
            textColor={TOKEN.grey_10}
            iconStoreColor={TOKEN.grey_10}
            addRoute={() => setIsCityShow(true)}
            iconDropColor={'#000'}
            textStyle={{
              fontWeight: '600',
            }}
            showSuffix={false}
          />
        )
      }}
    >
      <XLoading loading={loading} />
      {/* <Header centerComponent={<HeaderTitle title={'Boss报表'} />} onBack={() => navigation.goBack()} /> */}
      {/* <CommonAreaTime city={city} setCity={setCity} /> */}
      <BusinessAreaAndCitySelector
        onChange={(val: { type: string; data: any[] }) => {
          setCity(val)
        }}
        postUrl={'/erp/hxl.erp.store.area.app.find'}
        selectedList={city.data}
      />
      {/* <XlbFastTimeSelectRow date_range={date_range} bizday={bizday} setBizDay={setBizDay} /> */}
      {/* <Tabs isSync={false} data={tabLists}>
        {tabLists.map((item: any, index: number) => {
          return (
            <Tabs.Item key={item.value} id={item.value}>
              {renderTab(item)}
            </Tabs.Item>
          )
        })}
      </Tabs> */}
    </BoradWrapper>
  )
}
export default BossTable
