diff --git a/android/src/main/java/com/swmansion/rnscreens/Screen.kt b/android/src/main/java/com/swmansion/rnscreens/Screen.kt
index 51cc2fb1d147f930716057e608effa96f22c3996..639b3a3ad13900bfd3380592de1ebfdc3e3ea195 100644
--- a/android/src/main/java/com/swmansion/rnscreens/Screen.kt
+++ b/android/src/main/java/com/swmansion/rnscreens/Screen.kt
@@ -455,7 +455,9 @@ class Screen(
     fun startRemovalTransition() {
         if (!isBeingRemoved) {
             isBeingRemoved = true
-            startTransitionRecursive(this)
+            post {
+                startTransitionRecursive(this)
+            }
         }
     }
 
