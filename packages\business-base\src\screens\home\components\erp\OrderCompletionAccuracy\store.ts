import create from 'zustand'
import { immer } from 'zustand/middleware/immer'
const useStore = create<any>(
  immer((set) => ({
    data: [],
    setData: (obj: any) =>
      set((state: any) => {
        state.data = obj
      }),
    time_data: [],
    setTime_data: (obj: any) =>
      set((state: any) => {
        state.time_data = obj
      }),
    complete_data: [],
    setComplete_data: (obj: any) =>
      set((state: any) => {
        state.complete_data = obj
      }),
  }))
)

export default useStore
