//
//  MABuniessDetailViewController.m
//  xlb
//
//  Created by 莱昂纳多·迪卡普里奥 on 2024/10/17.
//

#import "MABuniessDetailViewController.h"
#import "PushZoomScaleBuniessTranstion.h"
#import "UIImageView+WebCache.h"
#import "MALoadWaveView.h"
#import "UIView+Common.h"
#import "UIView+Toast.h"
#import "MAHttpTool.h"
#import "NSDictionary+Common.h"
#import "NSArray+Common.h"
#import <AVKit/AVKit.h>
#import "ZKPhotoBrowser.h"
#import "MALookPointViewController.h"
#import "MABuniessScrollView.h"
#import "UIButton+Common.h"
#import "MAH5ViewController.h"
#import "PushCellScaleBuniessTranstion.h"
#import "MAWriteReplyView.h"
#import "MABottomOpereateView.h"
#import "MABuniessWriteFollowViewController.h"
#import "MABottomSingleSelectAlert.h"
#import "MANativeAlert.h"
#import "MAWriteMeassageView.h"
#import "MAEditBuniessViewController.h"
#import "MATopToastView.h"
#import "MATopNotiflyView.h"
#import "MAChooseLocationViewController.h"
#import "MASearchPoiViewController.h"
#import "MAAddPointViewController.h"
#import "MAAddOpenStoreViewController.h"
#import "MAAddLandlordViewController.h"
#import "MAPointEditPoiViewController.h"
#import "MATakeLookViewController.h"
#import "MAWriteFollowUpViewController.h"
#import "MAEditPointViewController.h"
#import <AMapFoundationKit/AMapFoundationKit.h>
#import <AMapNaviKit/MAMapKit.h>
#import <AMapLocationKit/AMapLocationKit.h>
#import "MACreatBuniessPolygon.h"
#import "MACreatBuniessPolygonRender.h"
#import "MABuniessEditDrawViewController.h"

#define BCWidth   [UIScreen mainScreen].bounds.size.width
#define BCHeight  [UIScreen mainScreen].bounds.size.height
#define COLOR(R, G, B) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:1]
#define ACOLOR(R, G, B,A) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:A]
#define RandomColor  [UIColor colorWithRed:arc4random_uniform(256)/255.0 green:arc4random_uniform(256)/255.0 blue:arc4random_uniform(256)/255.0 alpha:1]

// 判断字符串是否为空
#define BCStringIsEmpty(str) ([str isKindOfClass:[NSNull class]] || str == nil || ![str isKindOfClass:[NSString class]] || [str length]< 1 ? YES : NO || [str isEqualToString:@"null"] || [str isEqualToString:@"<null>"] || [str isEqualToString:@"(null)"])
// 判断数组是否为空
#define BCArrayIsEmpty(array) (array == nil || [array isKindOfClass:[NSNull class]] || ![array isKindOfClass:[NSArray class]] || [array count] == 0)
// 判断字典是否为空
#define BCDictIsEmpty(dic) (dic == nil || [dic isKindOfClass:[NSNull class]] || ![dic isKindOfClass:[NSDictionary class]] || dic.allKeys.count == 0)

#define RealSize(value)  MAX(round(value * [UIScreen mainScreen].bounds.size.width / 400.0), value)
#define MutilFont(value)  [UIScreen mainScreen].bounds.size.width > 420 ? (value + 2) : value

#ifdef DEBUG
#define DDLog(format, ...) printf("class: <%p %s:(%d) > method: %s \n%s\n", self, [[[NSString stringWithUTF8String:__FILE__] lastPathComponent] UTF8String], __LINE__, __PRETTY_FUNCTION__, [[NSString stringWithFormat:(format), ##__VA_ARGS__] UTF8String] )
#else
#define DDLog(format, ...)
#endif

@interface MABuniessDetailViewController ()<UIGestureRecognizerDelegate,UIScrollViewDelegate,BHInfiniteScrollViewDelegate,UITableViewDelegate,UITableViewDataSource,MAMapViewDelegate>

@property(nonatomic, assign) BOOL draggingDownToDismiss;//是否开启下拉关闭
@property(nonatomic, strong) UIView *toolbar;
@property (nonatomic, strong) MALoadWaveView *loadingView;//加载
@property (nonatomic, strong) NSMutableArray *previewUrls;//预览用的
@property(nonatomic, strong) NSMutableArray *bannerImages;//轮播图数组
@property (nonatomic, assign) CGFloat heightTop;//导航栏高度
@property (nonatomic, strong) UIView *alphaV;//导航栏渐变用到的view
@property (nonatomic, strong) UIButton *followBtn;//关注按钮
@property (nonatomic, strong) NSString *isFollow;//是否关注当前点位
@property (nonatomic, strong) NSDictionary *detailDic;//商圈详情数据源
@property (nonatomic, strong) NSDictionary *itemChangeDic;//变动项返回值数据源
@property (nonatomic, strong) UILabel *indexLabel;//轮播图计数

@property (nonatomic, strong) UIScrollView *horizontalScrollView;//底部横向视图
@property (nonatomic, strong) UITableView *mainTableView;//底部视图
@property (nonatomic, strong) MABuniessScrollView *homeScrollView;//首页
@property (nonatomic, strong) MABuniessScrollView *pointPlanScrollView;//点位规划
@property (nonatomic, strong) MABuniessScrollView *buniessStateScrollView;//商圈动态
@property (nonatomic, strong) UIScrollView *topTabV;//顶部选项卡
@property (nonatomic, strong) UIView *indicateLayer;//选项卡指示器
@property (nonatomic, strong) UIButton *selectTabButton;//控制单选模板按钮
@property (nonatomic, assign) BOOL isFirstLoad;//是否首次加载
@property (nonatomic, assign) CGFloat startOffsetX;
@property (nonatomic, strong) NSArray *itemChangeArr;//变动项数据源
@property (nonatomic, strong) NSArray *pointPlanArr;//规划点位数据源
@property (nonatomic, strong) UIView *selectBuniessStateV;//商圈动态选中的view
@property (nonatomic, strong) NSArray *changeBuniessArr;//变更商圈动态数据源
@property (nonatomic, strong) NSArray *operateBuniessArr;//操作商圈动态数据源
@property (nonatomic, strong) NSArray *followBuniessArr;//跟进点位动态数据源
@property (nonatomic, strong) NSArray *transferBuniessArr;//转移记录返回值数据源
@property (nonatomic, strong) NSArray *allBuniessArr;//全部商圈动态数据源
@property (nonatomic, assign) CGFloat timeDelay;//延时

@property (nonatomic, copy) NSString *orgId;//组织id，非必传
@property (nonatomic, copy) NSString *orgName;//组织id，非必传

@property (nonatomic, strong) UIScreenEdgePanGestureRecognizer *panLeft;
@property (nonatomic, strong) UIScreenEdgePanGestureRecognizer *panRight;

@property (nonatomic, strong) UIView *bottomOpereateV;//底部操作view
@property (nonatomic, strong) MABottomOpereateView *operatV;//底部操作view
@property (nonatomic, assign) BOOL isHaveMeassage;//是否需要审批意见
@property (nonatomic, strong) MABottomSingleSelectAlert *followAlert;//跟进人弹窗
@property (nonatomic, strong) UILabel *followLabel;//跟进人

@property (nonatomic, assign) BOOL openDisplay;//可开门店是否显示
@property (nonatomic, assign) BOOL comDisplay;//同行是否显示

@property (nonatomic, strong) MATopToastView *statusView;//加载
@property (nonatomic, strong) MATopNotiflyView *notiflyView;//加载

@property (nonatomic, strong) MAMapView *mapView;//高德地图
@property (nonatomic, strong) MACreatBuniessPolygon *creatPolygon;//商圈多边形
@property (nonatomic, strong) NSArray *pointArray;//商圈经纬度数组
@end

@implementation MABuniessDetailViewController

- (void)viewDidLoad {
  [super viewDidLoad];
  if (@available(iOS 13.0, *)) {
    self.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
  }
  self.view.backgroundColor = [UIColor whiteColor];
  self.view.layer.cornerRadius = 8;
  self.view.clipsToBounds = YES;
  self.previewUrls = [NSMutableArray array];
  self.bannerImages = [NSMutableArray array];
  
  _heightTop = 54;
 
  if (@available(iOS 13.0, *)) {
    NSSet *set = [UIApplication sharedApplication].connectedScenes;
    UIWindowScene *windowScene = [set anyObject];
    if (windowScene) {
      UIWindow *window = windowScene.windows.firstObject;
    
      if (window.safeAreaInsets.top > 0) {
        _heightTop = window.safeAreaInsets.top;
       
      } else {
        _heightTop = 54;
       
      }
    }
    
  } else {
    UIWindow *window = [[UIApplication sharedApplication].windows firstObject];
    if (window.safeAreaInsets.top > 0) {
      _heightTop = window.safeAreaInsets.top;
     
    } else {
      _heightTop = 54;
     
    }
  }
  
  //  占位图片
  if (self.pushType != PushAnimationTypeNone) { //需要动画的时候
    
    _tmpIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, 307)];
    NSString *imageUrl = [self.tmpImageUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    [_tmpIM  sd_setImageWithURL:[NSURL URLWithString:imageUrl] placeholderImage:[UIImage imageNamed:@"icon_pla"]];
    [self.view addSubview:_tmpIM ];
    
    //  侧滑手势
    self.draggingDownToDismiss = YES;
    _panLeft = [[UIScreenEdgePanGestureRecognizer alloc] initWithTarget:self action:@selector(popBack:)];
    _panLeft.edges = UIRectEdgeLeft;
    _panLeft.maximumNumberOfTouches = 1;
    [self.view addGestureRecognizer:_panLeft];
  
    
    _panRight = [[UIScreenEdgePanGestureRecognizer alloc] initWithTarget:self action:@selector(popBack:)];
    _panRight.edges = UIRectEdgeRight;
    _panRight.maximumNumberOfTouches = 1;
    [self.view addGestureRecognizer:_panRight];
   
    
    self.timeDelay = 0.4;
  } else {
    
    self.timeDelay = 0;
  }
  
  //  导航栏
  [self initToolBar];
  //  转场动画执行完再去请求接口
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(self.timeDelay * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    [self.loadingView showModal];
    [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.read" Params:@{@"id":self.pointId} success:^(NSDictionary *successResult) {
      NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (![receiveData isKindOfClass:[NSDictionary class]]) {
        return;
      }
      
      if (BCDictIsEmpty(receiveData)) {
        return;
      }
      self.detailDic = receiveData;
      self.orgId = [NSString stringWithFormat:@"%@",[self.detailDic objectForKeyNil:@"org_id"]];
      self.orgName = [NSString stringWithFormat:@"%@",[self.detailDic objectForKeyNil:@"org_name"]];
      //    处理商圈范围数据
      self.pointArray = [self.detailDic objectForKeyNil:@"area_range"];
      
      [self loadMoreRequest];
      
    } failure:^(NSString *errorResult) {
     
        [self.view makeToast:errorResult duration:1 position:CSToastPositionCenter];
        [self.loadingView hideModal];
      
      
    }];
    
  });
  
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(resetView) name:@"REFRESHBUNIESSDETAILDATA" object:nil];
}

// 进行检索获取Key
- (BOOL)haveObserverKeyPath:(NSObject*)object andKey:(NSString *)key
{
    //判断keyPath有或者无来实现防止多次重复添加和删除KVO监听
    id info = object.observationInfo;
    NSArray *array = [info valueForKey:@"_observances"];
    for (id objc in array) {
        id properties = [objc valueForKeyPath:@"_property"];
        NSString *keyPath = [properties valueForKeyPath:@"_keyPath"];
        if ([key isEqualToString:keyPath]) {
            return YES;
        }
    }
    return NO;
}
- (void)resetView{
  
  self.alphaV.alpha = 0;
  
  if (self.mapView) {
    _mapView.showsUserLocation = NO;
    [_mapView.layer removeAllAnimations];
    [_mapView removeAnnotations:_mapView.annotations];
    [_mapView removeOverlays:_mapView.overlays];
    [_mapView removeFromSuperview];
    _mapView.delegate = nil;
    _mapView = nil;
  }
 
  [self.buniessStateScrollView removeFromSuperview];
  self.buniessStateScrollView = nil;
  [self.pointPlanScrollView removeFromSuperview];
  self.pointPlanScrollView = nil;
  [self.homeScrollView removeFromSuperview];
  self.homeScrollView = nil;
  [self.mainTableView setContentOffset:CGPointMake(0, 0) animated:NO];
  [self.horizontalScrollView setContentOffset:CGPointMake(0, 0) animated:NO];
  if ([self haveObserverKeyPath:self.horizontalScrollView andKey:@"contentOffset"]) {
    [self.horizontalScrollView removeObserver:self forKeyPath:@"contentOffset"];
  }
  [self.topTabV removeFromSuperview];
  self.topTabV = nil;
  [self.indicateLayer removeFromSuperview];
  self.indicateLayer = nil;
  self.bannerView.delegate = nil;
  [self.bannerView removeFromSuperview];
  self.bannerView = nil;
  [self.horizontalScrollView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  [self.horizontalScrollView removeFromSuperview];
  self.horizontalScrollView = nil;
  [self.mainTableView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  [self.mainTableView.tableHeaderView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  self.mainTableView.tableHeaderView = nil;
  [self.mainTableView.tableFooterView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  self.mainTableView.tableFooterView = nil;
  self.mainTableView.delegate = nil;
  self.mainTableView.dataSource = nil;
  [self.mainTableView removeFromSuperview];
  self.mainTableView = nil;
  
  [self.bottomOpereateV.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  [self.bottomOpereateV removeFromSuperview];
  self.bottomOpereateV = nil;
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.read" Params:@{@"id":self.pointId} success:^(NSDictionary *successResult) {
    NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
    if (![receiveData isKindOfClass:[NSDictionary class]]) {
      return;
    }
    
    if (BCDictIsEmpty(receiveData)) {
      return;
    }
    self.detailDic = receiveData;
    self.orgId = [NSString stringWithFormat:@"%@",[self.detailDic objectForKeyNil:@"org_id"]];
    self.orgName = [NSString stringWithFormat:@"%@",[self.detailDic objectForKeyNil:@"org_name"]];
    [self.loadingView showModal];
    [self loadMoreRequest];
    
  } failure:^(NSString *errorResult) {
    [self.loadingView hideModal];
    [self.view makeToast:errorResult duration:1 position:CSToastPositionCenter];
   
  }];
}

- (void)popBack:(UIScreenEdgePanGestureRecognizer *)pan{
  
  if (!self.draggingDownToDismiss) {
    return;
  }
  
  CGPoint tran = [pan translationInView:self.view];
  CGFloat prosess = tran.x/self.view.bounds.size.width;
  prosess = fabs(prosess);
  if (prosess >= 0.1) {
    NSLog(@"返回了几次");
    [[NSNotificationCenter defaultCenter] postNotificationName:@"GETPOINTDETAILINDEX" object:self userInfo:@{@"currentIndex":[NSString stringWithFormat:@"%ld",(long)self.bannerView.currentPageIndex]}];
    [self.navigationController popViewControllerAnimated:YES];
    self.draggingDownToDismiss = NO;
    return;
  }
  
  
  CGFloat targetShrinkScale = 0.6;
  CGFloat currentScale = 1 - (1 - targetShrinkScale) * prosess;
  
  
  switch (pan.state)
  {
    case UIGestureRecognizerStateBegan:
    {
      self.mainTableView.scrollEnabled = NO;
      self.horizontalScrollView.scrollEnabled = NO;
      self.homeScrollView.scrollEnabled = NO;
      self.pointPlanScrollView.scrollEnabled = NO;
      self.buniessStateScrollView.scrollEnabled = NO;
      [self.bannerView stopAutoScrollPage];
      
      break;
    }
    case UIGestureRecognizerStateChanged:
      
    {
      self.mainTableView.scrollEnabled = NO;
      self.horizontalScrollView.scrollEnabled = NO;
      self.homeScrollView.scrollEnabled = NO;
      self.pointPlanScrollView.scrollEnabled = NO;
      self.buniessStateScrollView.scrollEnabled = NO;
      [self.bannerView stopAutoScrollPage];
      self.view.transform = CGAffineTransformMakeScale(currentScale, currentScale);
      self.view.layer.cornerRadius = (50 * prosess);
      
      break;
    }
    case UIGestureRecognizerStateEnded:
    {
      
      if (prosess < 0.1) {
        [UIView animateWithDuration:0.2 animations:^{
          self.view.transform = CGAffineTransformIdentity;
          self.view.layer.cornerRadius = 0;
        }];
        
        [self.bannerView startAutoScrollPage];
        self.mainTableView.scrollEnabled = YES;
        self.horizontalScrollView.scrollEnabled = YES;
        self.homeScrollView.scrollEnabled = YES;
        self.pointPlanScrollView.scrollEnabled = YES;
        self.buniessStateScrollView.scrollEnabled = YES;
      }
      break;
    }
    default:
      break;
  }
}

- (void)viewWillDisappear:(BOOL)animated{
  [super viewWillDisappear:animated];
  self.mainTableView.scrollEnabled = NO;
  self.horizontalScrollView.scrollEnabled = NO;
  self.homeScrollView.scrollEnabled = NO;
  self.pointPlanScrollView.scrollEnabled = NO;
  self.buniessStateScrollView.scrollEnabled = NO;
  
}

- (void)viewWillAppear:(BOOL)animated{
  [super viewWillAppear:animated];
  self.mainTableView.scrollEnabled = YES;
  self.horizontalScrollView.scrollEnabled = YES;
  self.homeScrollView.scrollEnabled = YES;
  self.pointPlanScrollView.scrollEnabled = YES;
  self.buniessStateScrollView.scrollEnabled = YES;
}
- (void)viewDidAppear:(BOOL)animated
{
  [super viewDidAppear:animated];
  if (!_isFirstLoad) {
    self.isFirstLoad = YES;
  } else {
    [self.bannerView startAutoScrollPage];
  }
  
  if (self.pushType == PushAnimationTypeNone) {//不需要动画的时候走系统侧滑手势
    self.navigationController.interactivePopGestureRecognizer.delegate = (id)self;//侧滑手势
    if ([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
      self.navigationController.interactivePopGestureRecognizer.enabled = YES;
    }
  } else {
    if ([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
      self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    }
  }
  
  
  
}

- (void)viewDidDisappear:(BOOL)animated{
  [super viewDidDisappear:animated];
  
  if (_isFirstLoad) {
    [self.bannerView stopAutoScrollPage];
  }
}
#pragma mark 同时发起多个网络请求，完成之后才刷新页面
- (void)loadMoreRequest{
  
 
  __weak typeof(self)weakSelf = self;
  dispatch_group_t group = dispatch_group_create();
  
  //  是否关注
  [self request:@"kms/hxl.kms.interestuser.flag.find" group:group pamares:@{@"business_id":self.pointId,@"module":@"BUSINESS_PLAN"}];
  
 
  NSDictionary *chndic;
  if (BCStringIsEmpty(self.orgId)) {
    chndic = @{@"type":@"BUSINESS"};
  } else {
    chndic = @{@"type":@"BUSINESS",@"org_id":self.orgId};
  }
  
  NSDictionary *followDic;
  if (BCStringIsEmpty(self.orgId)) {
    followDic = @{};
  } else {
    followDic = @{@"org_id":self.orgId};
  }
  //  获取系统参数接口
  [self request:@"kms/hxl.kms.clientclueparam.read" group:group pamares:followDic];
  
  //  变更跟进人接口
  [self request:@"kms/hxl.kms.transfer.user.find" group:group pamares:followDic];
  
  //  变动项接口
  [self request:@"kms/hxl.kms.itemchange.find" group:group pamares:chndic];
  
  //  规划点位
  [self request:@"kms/hxl.kms.storeplan.flag.app.page" group:group pamares:@{@"business_plan_ids":@[self.pointId]}];
  
  //  变更记录
  [self request:@"kms/hxl.kms.businesslan.change.find" group:group pamares:@{@"id":self.pointId}];
  
  //  操作记录
  [self request:@"kms/hxl.kms.businessplan.auditrecord.find" group:group pamares:@{@"id":self.pointId}];
  
  //  获取转移记录
  [self request:@"kms/hxl.kms.transferbusiness.find" group:group pamares:@{@"ref_id":self.pointId,@"type":@"BUSINESS_PLAN"}];
  
//  跟进记录
  [self request:@"kms/hxl.kms.businessplan.followrecord.find" group:group pamares:@{@"id":self.pointId}];
  
  //  商圈全部动态
  [self request:@"kms/hxl.kms.businessplan.records.find" group:group pamares:@{@"id":self.pointId}];
  //  所有的网络请求完成,渲染界面
  dispatch_group_notify(group, dispatch_get_main_queue(), ^{
    
    weakSelf.followBtn.selected = [self.isFollow isEqualToString:@"1"] ? YES : NO;//是否关注按钮
    
    NSLog(@"请求完成2===%@",self.allBuniessArr);
    
    //    处理变动项值
    NSMutableDictionary *valueDic = [NSMutableDictionary dictionaryWithCapacity:100];
    if ([[weakSelf.detailDic objectForKeyNil:@"content"] isKindOfClass:[NSArray class]]) {
      for (NSDictionary *dic in [weakSelf.detailDic objectForKeyNil:@"content"]) {
        [valueDic addEntriesFromDictionary:[dic objectForKeyNil:@"details"]];
      }
    }
    
    weakSelf.itemChangeDic = valueDic;//变动项值
    
    //    开始加载全部界面
    UIView *headV = [weakSelf BannerHeader];
    weakSelf.mainTableView.tableHeaderView = headV;
    [weakSelf.view insertSubview:weakSelf.mainTableView atIndex:0];//加载主tableview
    
    [weakSelf.horizontalScrollView addSubview:weakSelf.homeScrollView];
    [weakSelf.horizontalScrollView addSubview:weakSelf.pointPlanScrollView];
    [weakSelf.horizontalScrollView addSubview:weakSelf.buniessStateScrollView];
    [weakSelf loadDataWithScrollView];//加载全部页面数据
    weakSelf.mainTableView.tableFooterView = weakSelf.horizontalScrollView;
    [weakSelf showBottomButton];
    //        动画渐变，完成之后加载弹窗关闭
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [weakSelf.loadingView hideModal];
      [weakSelf.bannerView scrollToPageAtIndex:weakSelf.scrollIndex Animation:NO];//轮播图滚到指定位置
      
      if (self.pushType != PushAnimationTypeNone) {
        [self.horizontalScrollView.panGestureRecognizer requireGestureRecognizerToFail:self.panLeft];
        [self.topTabV.panGestureRecognizer requireGestureRecognizerToFail:self.panLeft];
        [self.horizontalScrollView.panGestureRecognizer requireGestureRecognizerToFail:self.panRight];
        [self.topTabV.panGestureRecognizer requireGestureRecognizerToFail:self.panRight];
      } else {
        [self.horizontalScrollView.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
        [self.topTabV.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
      }
      
    });
    
    //  动画渐变
    [UIView animateWithDuration:0.25 animations:^{
      weakSelf.toolbar.alpha = 1;//导航栏
      weakSelf.mainTableView.alpha = 1;
      
    } completion:^(BOOL finished) {
      weakSelf.tmpIM.hidden = YES;
      [weakSelf.bannerView startAutoScrollPage];
    }];
    
  });
}

- (void)request:(NSString *)url group:(dispatch_group_t)group pamares:(NSDictionary *)para{
  
  dispatch_group_enter(group);
  [[MAHttpTool defaultManagerTool] postWithURL:url Params:para success:^(NSDictionary * _Nonnull successResult) {
    dispatch_group_leave(group);
    
    if ([url isEqualToString:@"kms/hxl.kms.interestuser.flag.find"]){//是否关注接口
      NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (![receiveData isKindOfClass:[NSDictionary class]]) {
        return;
      }
      
      if (BCDictIsEmpty(receiveData)) {
        return;
      }
      self.isFollow = [NSString stringWithFormat:@"%@",[receiveData objectNilForKey:@"flag"]];
      
    } else if ([url isEqualToString: @"kms/hxl.kms.businessplan.read"]) {//商圈详情接口
      NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (![receiveData isKindOfClass:[NSDictionary class]]) {
        return;
      }
      
      if (BCDictIsEmpty(receiveData)) {
        return;
      }
      self.detailDic = receiveData;
      
    } else if ([url isEqualToString:@"kms/hxl.kms.itemchange.find"]){//变动项接口
      
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      for (NSDictionary *dic in receiveData) {
        if ([[dic objectNilForKey:@"name"] isEqualToString:@"基本信息"]) {//商圈现场变动项
          NSArray *details = [[dic objectForKeyNil:@"content"] objectForKeyNil:@"details"];
          if (!BCArrayIsEmpty(details)) {
            for (NSDictionary *detailDic in details) {
              if ([[detailDic objectForKeyNil:@"api_name"] isEqualToString:@"basic_info_open_store"]) {
                self.openDisplay = [[detailDic objectNilForKey:@"business_plan_display"] integerValue] == 1 ? YES :NO;
              
                
              } else if ([[detailDic objectForKeyNil:@"api_name"] isEqualToString:@"basic_info_competitor"]){
                
                self.comDisplay = [[detailDic objectNilForKey:@"business_plan_display"] integerValue] == 1 ? YES :NO;
                
              }
            }
          }
        }
      }
      
      self.itemChangeArr = receiveData;
      
    } else if ([url isEqualToString:@"kms/hxl.kms.storeplan.flag.app.page"]){//规划点位接口
      
      NSArray *receiveData = [[NSDictionary cleanNull:[successResult objectNilForKey:@"data"]] objectForKeyNil:@"content"];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      self.pointPlanArr = receiveData;
      
    } else if ([url isEqualToString:@"kms/hxl.kms.businesslan.change.find"]){//变更记录接口
      
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      NSMutableArray *arr = [NSMutableArray arrayWithCapacity:1000];
      for (NSDictionary *dic in receiveData) {
        NSArray *tmpArr = [dic objectForKeyNil:@"detail_changes"];
        if ([tmpArr isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(tmpArr)) {
          [arr addObjectsFromArray:tmpArr];
        }
        
      }
      
      self.changeBuniessArr = arr;
      
    } else if ([url isEqualToString:@"kms/hxl.kms.businessplan.auditrecord.find"]){//获取操作记录
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      self.operateBuniessArr = receiveData;
      
    } else if ([url isEqualToString:@"kms/hxl.kms.businessplan.followrecord.find"]){//获取跟进记录
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      self.followBuniessArr = receiveData;
      
    } else if ([url isEqualToString:@"kms/hxl.kms.transferbusiness.find"]){//获取转移记录
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      

      self.transferBuniessArr = receiveData;
      
    } else if ([url isEqualToString:@"kms/hxl.kms.businessplan.records.find"]){//获取商圈全部动态
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      self.allBuniessArr = receiveData;
      DDLog(@"===全部动态==%@",self.allBuniessArr);
      
    } else if ([url isEqualToString:@"kms/hxl.kms.clientclueparam.read"]){//系统参数
      
      NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (BCDictIsEmpty(receiveData)) {
        return;
      }
    
      self.isHaveMeassage = [[receiveData objectForKeyNil:@"target_audit_stock"] isEqual:@(1)] ? YES : NO;
   
    } else if ([url isEqualToString:@"kms/hxl.kms.transfer.user.find"]){//变更跟进人
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      BOOL showS = receiveData.count < 15 ? NO : YES;
      self.followAlert = [[MABottomSingleSelectAlert alloc] initWithTitle:@"选择跟进人" showKey:@"name" showSearch:showS maskClose:YES andData:receiveData endChooseItem:@{}];
      
    }
    
  } failure:^(NSString * _Nonnull errorResult) {
    dispatch_group_leave(group);
  }];
  
}

#pragma mark 底部操作按钮
- (void)showBottomButton{
  
  self.bottomOpereateV = [[UIView alloc] initWithFrame:CGRectMake(0, BCHeight - 90 , BCWidth, 90)];
  self.bottomOpereateV.backgroundColor = [UIColor whiteColor];
  [self.view addSubview:self.bottomOpereateV];
  
  NSString *stateStr = [self changeState:[self.detailDic objectNilForKey:@"state"]];
  NSArray *titleArr = @[];
  NSArray *imageArr = @[];
  
  if ([stateStr isEqualToString:@"制单"]) {
    titleArr = @[@"编辑",@"发起审批",@"添加跟进",@"变更跟进人",@"更多"];
    imageArr = @[@"bottom_edit",@"bottom_approve",@"bottom_follow",@"bottom_change",@"bottom_more"];
  } else if ([stateStr isEqualToString:@"待审批"]) {
    
    titleArr = @[@"审批通过",@"添加跟进",@"变更跟进人",@"撤销审批"];
    imageArr = @[@"bottom_agree",@"bottom_follow",@"bottom_change",@"bottom_revoke"];
    
  } else if ([stateStr isEqualToString:@"审批通过"]) {
    
    NSString *transPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"SECOND_EDIT_BUNIESS"];
    if (![transPoint isEqualToString:@"1"]) {
      titleArr = @[@"添加跟进",@"否决",@"变更跟进人"];
      imageArr = @[@"bottom_follow",@"bottom_reject",@"bottom_change"];
    } else {
      titleArr = @[@"编辑",@"添加跟进",@"否决",@"变更跟进人"];
      imageArr = @[@"bottom_edit",@"bottom_follow",@"bottom_reject",@"bottom_change"];
    }
   
    
  } else if ([stateStr isEqualToString:@"否决"]) {
    titleArr = @[@"重新打开",@"添加跟进",@"变更跟进人",@"删除"];
    imageArr = @[@"bottom_reopen",@"bottom_follow",@"bottom_change",@"bottom_delete"];
  }
  
  CGFloat width = BCWidth/titleArr.count;
  for (int i = 0; i < titleArr.count; i ++) {
    
    UIButton *btn = [[UIButton alloc] initWithFrame:CGRectMake(i * width, 0, width, 56)];
    [self.bottomOpereateV addSubview:btn];
    [btn addTarget:self action:@selector(clickBottomOpereate:) forControlEvents:UIControlEventTouchUpInside];
    
    UIImageView *leftIM = [[UIImageView alloc] initWithFrame:CGRectMake((width - 20)/2, 8, 20, 20)];
    leftIM.image = [UIImage imageNamed:[imageArr objectAtIndexCheck:i]];
    leftIM.tag = 444;
    [btn addSubview:leftIM];
    
    UILabel *timeStartL = [[UILabel alloc] initWithFrame:CGRectMake(0, leftIM.bottom + 2, width, 17)];
    timeStartL.text = [titleArr objectAtIndexCheck:i];
    timeStartL.textAlignment = NSTextAlignmentCenter;
    timeStartL.font = [UIFont systemFontOfSize:MutilFont(12)];
    timeStartL.tag = 333;
    timeStartL.textColor = COLOR(29, 33, 41);
    [btn addSubview:timeStartL];
  }
  
  
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(0,0, BCWidth , 1)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [self.bottomOpereateV addSubview:lineV];
  
  if (titleArr.count == 0) {
    self.bottomOpereateV.hidden = YES;
  }
  
}

- (void)clickBottomOpereate:(UIButton *)sender{
  
  __weak typeof(self)weakSelf = self;
  
  UILabel *leftL = [sender viewWithTag:333];
  UIImageView *leftIM = [sender viewWithTag:444];
  
  
  if ([leftL.text isEqualToString:@"添加跟进"]) {
    [self.operatV hideModal];
    [self clickToFollowUp];
    
  } else  if ([leftL.text isEqualToString:@"变更跟进人"]) {
    
   
    [self.operatV hideModal];
    [self showFollowView];
    
  } else if ([leftL.text isEqualToString:@"否决"]) {
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"REJECT_BUNIESS"];
    if (![editPoint isEqualToString:@"1"]) {
//      [[UIApplication sharedApplication].keyWindow makeToast:@"您没有商圈否决权限" duration:1 position:CSToastPositionCenter];
      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有商圈否决权限"];
      return;
    }
   
    [self.operatV hideModal];
    
    if (self.isHaveMeassage) {//需要审批意见
      MAWriteMeassageView *meassgaeV = [[MAWriteMeassageView alloc] initWithName:@"审批意见"];
      [meassgaeV showModal];
      meassgaeV.okBlock = ^(NSString *dataString) {
        [weakSelf clickRejectButton:dataString];
      };
    } else {//不需要
      [self clickRejectButton:@""];
    }
    
  } else if ([leftL.text isEqualToString:@"更多"]) {
    
    leftL.text = @"收起";
    [UIView animateWithDuration:0.25 animations:^{
      leftIM.transform = CGAffineTransformMakeRotation(M_PI);
    } completion:nil];
    [self.operatV showModal];
    
    self.operatV.hideBlock = ^(NSString *dataString) {
      leftL.text = @"更多";
      [UIView animateWithDuration:0.25 animations:^{
        leftIM.transform = CGAffineTransformIdentity;
      } completion:nil];
    };
    
    self.operatV.okBlock = ^(NSString *dataString) {
      if ([dataString isEqualToString:@"删除"]) {
        
        NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"DELETE_BUNIESS"];
        if (![editPoint isEqualToString:@"1"]) {
          [weakSelf.notiflyView showModal:NotiflyFail andTitle:@"您没有商圈删除权限"];
          return;
        }
        
       
        [weakSelf.operatV hideModal];
        [weakSelf clickDeletePoint];
        
      }
      
    };
    
  } else if ([leftL.text isEqualToString:@"删除"]) {
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"DELETE_BUNIESS"];
    if (![editPoint isEqualToString:@"1"]) {
     
      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有商圈删除权限"];
      return;
    }
    
    
    [weakSelf.operatV hideModal];
    [weakSelf clickDeletePoint];
    
  } else if ([leftL.text isEqualToString:@"收起"]) {
    
    [weakSelf.operatV hideModal];
    
  } else if ([leftL.text isEqualToString:@"审批通过"]) {
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"SECOND_AUDIT_BUNIESS"];
    NSString *editBuniess = [[NSUserDefaults standardUserDefaults] objectForKey:@"FRIST_AUDIT_BUNIESS"];
    if (![editPoint isEqualToString:@"1"] && ![editBuniess isEqualToString:@"1"]) {
      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有商圈审批通过权限"];
      return;
    }
    
    [self.operatV hideModal];
    
    if (self.isHaveMeassage) {//需要审批意见
      MAWriteMeassageView *meassgaeV = [[MAWriteMeassageView alloc] initWithName:@"审批意见"];
      [meassgaeV showModal];
      meassgaeV.okBlock = ^(NSString *dataString) {
        [weakSelf clickSecondAudit:dataString];
      };
    } else {//不需要
      [self clickSecondAudit:@""];
    }
    
  }else  if ([leftL.text isEqualToString:@"撤销审批"]) {
    
    NSString *userName = [[NSUserDefaults standardUserDefaults] objectForKey:@"MAP_USERNAME"];
    if (![userName isEqualToString:[self.detailDic objectForKeyNil:@"update_by"]]) {
      [self.notiflyView showModal:NotiflyFail andTitle:@"您不是当前操作人，无法撤销审批"];
      return;
    }
    
    [self.operatV hideModal];
    [self clickRevoke];
    
  } else if ([leftL.text isEqualToString:@"重新打开"]) {
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"EDIT_BUNIESS"];
    if (![editPoint isEqualToString:@"1"]) {
      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有商圈编辑权限"];
      return;
    }
    
    [self.operatV hideModal];
    
    if (self.isHaveMeassage) {//需要审批意见
      MAWriteMeassageView *meassgaeV = [[MAWriteMeassageView alloc] initWithName:@"审批意见"];
      [meassgaeV showModal];
      meassgaeV.okBlock = ^(NSString *dataString) {
        [weakSelf clickReopen:dataString];
      };
    } else {//不需要
      [self clickReopen:@""];
    }
    
    
  } else if ([leftL.text isEqualToString:@"发起审批"]) {
    
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"EDIT_BUNIESS"];
    if (![editPoint isEqualToString:@"1"]) {

      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有商圈编辑权限"];
      return;
    }
    
    [self.operatV hideModal];
    
    [self clickStartApply];
    
  } else if ([leftL.text isEqualToString:@"编辑"]) {
  
    NSString *editPoint = [[NSUserDefaults standardUserDefaults] objectForKey:@"EDIT_BUNIESS"];
    if (![editPoint isEqualToString:@"1"]) {

      [self.notiflyView showModal:NotiflyFail andTitle:@"您没有商圈编辑权限"];
      return;
    }
    
    [self.operatV hideModal];
    
    NSString *pointState = [NSString stringWithFormat:@"%@",[self changeState:[self.detailDic objectNilForKey:@"state"]]];
    MAEditBuniessViewController *vc = [[MAEditBuniessViewController alloc] init];
    vc.dataDic = self.detailDic;
    vc.pointId = self.pointId;
    vc.orgId = self.orgId;
    vc.orgName = self.orgName;
    vc.state = pointState;
    vc.draftitemChangeDic = self.itemChangeDic;
    [self.navigationController pushViewController:vc animated:YES];
    
  } 
  
}

- (void)clickStartApply{
  
 
  MAEditBuniessViewController *vc = [[MAEditBuniessViewController alloc] init];
  vc.dataDic = self.detailDic;
  vc.pointId = self.pointId;
  vc.orgId = self.orgId;
  vc.state = @"发起审批";
  vc.draftitemChangeDic = self.itemChangeDic;
  [self.navigationController pushViewController:vc animated:YES];
 
}

- (void)requestStartApply{
  
  NSDictionary *dic = @{@"id":self.pointId,@"memo":@""};
  
  [self.loadingView showModal];
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.initiateapproval" Params:dic success:^(NSDictionary *successResult) {
    
    [self.loadingView hideModal];
    
    [self.statusView showModal:ToastSuccess andTitle:@"发起审批成功"];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHMAPDATA" object:nil];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self resetView];
    });
    
    
  } failure:^(NSString *errorResult) {
    [self.loadingView hideModal];
    
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
}

//重新打开
- (void)clickReopen:(NSString *)reson{
  __weak typeof(self)weakSelf = self;
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定要重新打开?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf getReopen:reson];
  };
}
- (void)getReopen:(NSString *)reson{
  [self.loadingView showModal];
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.reopen" Params:@{@"id":self.pointId,@"memo":reson} success:^(NSDictionary * _Nonnull successResult) {
    [self.loadingView hideModal];
    
    [self.statusView showModal:ToastSuccess andTitle:@"重新打开成功"];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHMAPDATA" object:nil];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self resetView];
    });
    
  } failure:^(NSString * _Nonnull errorResult) {
    [self.loadingView hideModal];
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
}

//撤销审批
- (void)clickRevoke{
  __weak typeof(self)weakSelf = self;
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定要撤销审批?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf getRevoke];
  };
  
}

- (void)getRevoke{
  
  [self.loadingView showModal];
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.revoke" Params:@{@"memo":@"",@"id":self.pointId} success:^(NSDictionary * _Nonnull successResult) {
    [self.loadingView hideModal];
    
    [self.statusView showModal:ToastSuccess andTitle:@"撤销审批成功"];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHMAPDATA" object:nil];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self resetView];
    });
    
  } failure:^(NSString * _Nonnull errorResult) {
    [self.loadingView hideModal];
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}

- (void)clickSecondAudit:(NSString *)reason{
  
  __weak typeof(self)weakSelf = self;
  
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定要审批通过吗?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf requestSecondAudit:reason];
  };
  
}

- (void)requestSecondAudit:(NSString *)reason{
  
  
  NSDictionary *dic = @{@"id":self.pointId,@"memo":reason};
  
  [self.loadingView showModal];
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.secondaudit" Params:dic success:^(NSDictionary *successResult) {
    
    [self.loadingView hideModal];
    
    [self.statusView showModal:ToastSuccess andTitle:@"审批通过成功"];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHMAPDATA" object:nil];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self resetView];
    });
    
    
  } failure:^(NSString *errorResult) {
    [self.loadingView hideModal];
    
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}


- (void)clickDeletePoint{
  
  __weak typeof(self)weakSelf = self;
  
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定要删除点位吗?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf requesDeletePoint];
  };
}

- (void)requesDeletePoint{
  
  NSDictionary *dic = @{@"ids":@[self.pointId]};
  
  [self.loadingView showModal];
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.delete" Params:dic success:^(NSDictionary *successResult) {
    
    [self.loadingView hideModal];
    
    [self.statusView showModal:ToastSuccess andTitle:@"商圈删除成功"];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHMAPDATA" object:nil];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self.navigationController popViewControllerAnimated:YES];
    });
    
    
  } failure:^(NSString *errorResult) {
    [self.loadingView hideModal];
    
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}

- (void)clickRejectButton:(NSString *)reason{
  __weak typeof(self)weakSelf = self;
  
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定要否决吗?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf requestRejectButton:reason];
  };
}

- (void)requestRejectButton:(NSString *)reason{
  
  NSDictionary *dic = @{@"id":self.pointId,@"memo":reason};
  
  [self.loadingView showModal];
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.reject" Params:dic success:^(NSDictionary *successResult) {
    
    [self.loadingView hideModal];
    
    [self.statusView showModal:ToastSuccess andTitle:@"否决成功"];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHMAPDATA" object:nil];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self resetView];
    });
    
    
  } failure:^(NSString *errorResult) {
    [self.loadingView hideModal];
    
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}

#pragma mark 去写跟进
- (void)clickToFollowUp{
  
  MABuniessWriteFollowViewController *toVC = [[MABuniessWriteFollowViewController alloc] init];
  toVC.pointId = self.pointId;
  [self.navigationController pushViewController:toVC animated:YES];
}

- (void)showFollowView{
  
  __weak typeof(self)weakSelf = self;
  
  if (self.followAlert) {
    [self.followAlert showModal];
     self.followAlert.chooseBlock = ^(NSDictionary *chooseDic) {
     
      MAWriteMeassageView *meassgaeV = [[MAWriteMeassageView alloc] initWithName:@"转交原因"];
      meassgaeV.okBlock = ^(NSString *dataString) {
        NSMutableDictionary *reasonDic = [NSMutableDictionary dictionaryWithDictionary:chooseDic];
        [reasonDic setObject:dataString forKey:@"reason"];
        [weakSelf changeFollow:reasonDic];

      };
      dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [meassgaeV showModal];
      });
      
    };
    
  } else {
    
//    [self.view makeToast:@"获取跟进人失败" duration:1 position:CSToastPositionCenter];
    [self.notiflyView showModal:NotiflyFail andTitle:@"获取跟进人失败"];
  }
    
}

//修改跟进人
- (void)changeFollow:(NSDictionary *)params{
  
  NSDictionary *dic = @{@"change_reason":[params objectNilForKey:@"reason"],@"type":@"BUSINESS_PLAN",@"ids":@[self.pointId],@"after_change_link":[NSString stringWithFormat:@"%@",[params objectForKeyNil:@"phone"]],@"user_id":[params objectForKeyNil:@"id"]};
  
  [self.loadingView showModal];
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.user.business.transfer" Params:dic success:^(NSDictionary * _Nonnull successResult) {
    [self.loadingView hideModal];
    
    NSString *followStr = [NSString stringWithFormat:@"%@",[params objectForKeyNil:@"name"]];
    NSString *followResult = [NSString stringWithFormat:@"跟进人：%@",followStr];
    NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
    [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:134/255.0 green:144/255.0 blue:156/255.0 alpha:1.000000]} range:NSMakeRange(0, 4 + followStr.length)];
    
    
    self.followLabel.attributedText = string;
    [self.statusView showModal:ToastSuccess andTitle:@"变更成功"];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHLISTDATA" object:nil];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self resetView];
    });
    
  } failure:^(NSString * _Nonnull errorResult) {
    [self.loadingView hideModal];
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
}
#pragma mark 所有网络请求结束之后初始化左右滚动scrollview界面
- (void)loadDataWithScrollView{
  

  //  基本信息
  UIView *homeBasicV = [self loadBasicInfo:self.detailDic];
  [self.homeScrollView addSubview:homeBasicV];
  
  
  UIView *homeMapV = [self loadMapArea:homeBasicV.bottom];
  [self.homeScrollView addSubview:homeMapV];
  
  //  变动项
  UIView *changeV = [self loadChangeItem:homeMapV.bottom];
  [self.homeScrollView addSubview:changeV];
  
  //  规划点位
  UIView *pointV = [self loadPointPlan:changeV.bottom];
  [self.homeScrollView addSubview:pointV];
  
  //  商圈动态
  UIView *operateV = [self loadBuniessState:pointV.bottom];
  [self.homeScrollView addSubview:operateV];
  
  self.homeScrollView.contentSize = CGSizeMake(0, operateV.bottom  + 40);
  
  
  //  tab下的规划点位
  [self loadPointPlanRecord];
  
  //  tab下的商圈动态
  [self loadBuniessStateRecord];
  
}
#pragma mark 所有网络请求回来加载首页基础信息界面
- (UIView *)loadBasicInfo:(NSDictionary *)receiveData{
  
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,12,BCWidth,205)];
  contentView.backgroundColor = [UIColor whiteColor];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,100,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = @"基本信息";
  [contentView addSubview:nameLabel];
  
  //    分割线
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,49, BCWidth - 16, 1)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [contentView addSubview:lineV];
  
  NSMutableArray *leftArr = [NSMutableArray arrayWithArray:@[@"所属组织",@"商圈区域"]];
  NSMutableArray *rightArr = [NSMutableArray array];
  NSString *orgStr = [NSString stringWithFormat:@"%@",[receiveData objectNilForKey:@"org_name"]];
  [rightArr addObject:BCStringIsEmpty(orgStr) ? @"-" : orgStr];
  
  NSString *areaStr = [NSString stringWithFormat:@"%@",[receiveData objectNilForKey:@"area_name"]];
  [rightArr addObject:BCStringIsEmpty(areaStr) ? @"-" : areaStr];
  
  
  if (self.openDisplay) {
    [leftArr addObject:@"可开门店数量"];
    NSString *tmpStr = [NSString stringWithFormat:@"%@",[receiveData objectNilForKey:@"open_store"]];
    [rightArr addObject:BCStringIsEmpty(tmpStr) ? @"-" : tmpStr];
  }
  
  if (self.comDisplay) {
    [leftArr addObject:@"同行品牌"];
    NSString *tmpStr = [NSString stringWithFormat:@"%@",[receiveData objectNilForKey:@"competitor"]];
    [rightArr addObject:BCStringIsEmpty(tmpStr) ? @"-" : tmpStr];
  }
  
  UIView *bottomV = [[UIView alloc]initWithFrame:CGRectMake(16,  lineV.bottom + 14 , BCWidth - 32, 35 * leftArr.count)];
  [contentView addSubview:bottomV];
  for (int i = 0; i < leftArr.count; i ++) {
    UILabel *leftL = [[UILabel alloc] initWithFrame:CGRectMake(0, (21 + 14) *i, RealSize(104), 21)];
    leftL.text = [leftArr objectAtIndex:i];
    leftL.font = [UIFont systemFontOfSize:MutilFont(15)];
    leftL.textColor = ACOLOR(30, 33, 38, 0.45);
    [bottomV addSubview:leftL];
    
    
    UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(104, (21 + 14) *i, CGRectGetWidth(bottomV.bounds) - RealSize(104), 21)];
    rightL.text = [rightArr objectAtIndexCheck:i];
    rightL.textAlignment = NSTextAlignmentRight;
    rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
    rightL.textColor = [UIColor colorWithRed:29/255.0 green:33/255.0 blue:41/255.0 alpha:1];
    [bottomV addSubview:rightL];
    
   
  }
  contentView.height = leftArr.count * 35 + 14 + 50;
  return contentView;
}


#pragma mark 所有网络请求回来加载首页商圈范围界面
- (UIView *)loadMapArea:(CGFloat)top{
  
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,top + 12,BCWidth,64 + RealSize(200))];
  contentView.backgroundColor = [UIColor whiteColor];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,100,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = @"商圈范围";
  [contentView addSubview:nameLabel];
  
  
//  地图
  UIButton *newButton = [[UIButton alloc] initWithFrame:CGRectMake(16, 50, BCWidth - 32, RealSize(200))];
  newButton.layer.cornerRadius = 6;
  newButton.clipsToBounds = YES;
  [newButton addTarget:self action:@selector(clickEditDraw) forControlEvents:UIControlEventTouchUpInside];
  [contentView addSubview:newButton];
  
  
  _mapView = [[MAMapView alloc] initWithFrame:newButton.bounds];
  _mapView.rotateCameraEnabled = NO;
  _mapView.zoomEnabled = NO;
  _mapView.showsScale = NO;
  _mapView.scrollEnabled = NO;
  _mapView.showsCompass = NO;
  _mapView.delegate = self;
  _mapView.rotateEnabled = NO;
  _mapView.mapType = MAMapTypeStandard;
  _mapView.zoomingInPivotsAroundAnchorPoint = YES;
  _mapView.customizeUserLocationAccuracyCircleRepresentation = YES;
  _mapView.userTrackingMode = MAUserTrackingModeFollow;
  _mapView.zoomLevel = 15.5;
  _mapView.userInteractionEnabled = NO;
  _mapView.showsUserLocation = NO;
  [newButton addSubview:self.mapView];
  
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    CLLocationCoordinate2D commonPolylineCoords[self.pointArray.count];
    for (int i = 0; i < self.pointArray.count; i ++) {
      
      NSDictionary *dic = [self.pointArray objectAtIndexCheck:i];
      CLLocationCoordinate2D coor = CLLocationCoordinate2DMake([[dic objectForKeyNil:@"latitude"] floatValue], [[dic objectForKeyNil:@"longitude"] floatValue]);
      commonPolylineCoords[i] = coor;
    }
    
    self.creatPolygon = [MACreatBuniessPolygon polygonWithCoordinates:commonPolylineCoords count:self.pointArray.count];
    [self.mapView addOverlay:self.creatPolygon];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self.mapView showOverlays:@[self.creatPolygon] edgePadding:UIEdgeInsetsMake(40, 40, 40, 40) animated:NO];
    });
    
  });
  
  return contentView;
  
}

#pragma mark 点击重新绘制商圈
- (void)clickEditDraw{
  
  MABuniessEditDrawViewController *editVC = [[MABuniessEditDrawViewController alloc] init];
  editVC.pointArray = self.pointArray;
  editVC.buniessId = self.pointId;
  editVC.orgId = self.orgId;
  editVC.isFromBuniessDetail = YES;
  [self.navigationController pushViewController:editVC animated:YES];
}

#pragma mark 所有网络请求回来加载首页变动项界面
- (UIView *)loadChangeItem:(CGFloat)top{
  __weak typeof(self)weakSelf = self;
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,top + 12,BCWidth,0)];
  if (BCArrayIsEmpty(self.itemChangeArr)) {
    
    return contentView;;
  }
  
  CGFloat itemH = 0;
  
  for (int i = 0; i < self.itemChangeArr.count; i ++) {
    
    NSDictionary *dic = [self.itemChangeArr objectAtIndexCheck:i];
    if ([[dic objectForKeyNil:@"enable"] isEqual:@(0)]) {//不启用跳过
      continue;
    }
    
    if ([[dic objectForKeyNil:@"acquiesce"] isEqual:@(1)]) {//跳过系统预设的变动项，基本信息，房东信息等等
      continue;
    }
    
    //    每一个变动项
    UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH , BCWidth , 0)];
    bottomV.backgroundColor = [UIColor whiteColor];
    [contentView addSubview:bottomV];
    
    //    变动项标题
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.frame = CGRectMake(16,0,BCWidth - 32,50);
    nameLabel.textColor = COLOR(31, 33, 38);
    nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
    nameLabel.text = [NSString stringWithFormat:@"%@",[dic objectNilForKey:@"name"]];
    [bottomV addSubview:nameLabel];
    
    //    分割线
    UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,50, BCWidth - 16, 1)];
    lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
    [bottomV addSubview:lineV];
    
    //    变动项
    NSArray *items = [[dic objectForKeyNil:@"content"] objectForKeyNil:@"details"];
    if (BCArrayIsEmpty(items)) {
      continue;
    }
    
    CGFloat LabelH = 50 + 14;
    for (int j = 0; j < items.count; j ++) {
      
      NSDictionary *itemDic = [items objectAtIndexCheck:j];
      
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
      NSString *titleNameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"title"]];
      CGFloat height = [titleNameStr boundingRectWithSize:CGSizeMake(120, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      height = height < 21 ? 21 : height;
      UILabel *leftL = [[UILabel alloc] initWithFrame:CGRectMake(16, LabelH, 120, height)];
      leftL.text = titleNameStr;
      leftL.numberOfLines = 0;
      leftL.font = [UIFont systemFontOfSize:MutilFont(15)];
      leftL.textColor = ACOLOR(30, 33, 38, 0.45);
      [bottomV addSubview:leftL];
      
      if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"IMAGE"]) {//如果是图片
        
        NSArray *imageArr = [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
        
        if (BCArrayIsEmpty(imageArr)) {//如果没有图片，展示-
          
          UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(130,LabelH, CGRectGetWidth(bottomV.bounds) - 146, 21)];
          rightL.textAlignment = NSTextAlignmentRight;
          rightL.text = @"-";
          rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
          rightL.textColor = COLOR(31, 33, 38);
          [bottomV addSubview:rightL];
          if (height > 21) {
            LabelH += height + 14;
          } else {
            LabelH += 35;
          }
          
        } else {//如果有图片
          
          UIView *imageV = [[UIView alloc] initWithFrame:CGRectMake(16, leftL.bottom, BCWidth - 32, 0)];
          [bottomV addSubview:imageV];
          CGFloat imageH = (BCWidth - 32 - 24)/4;
          CGFloat paddingL = 8.0; //button 间距
          CGFloat paddingT = 10; //button 间距
          CGFloat pointX = 0; //button X坐标
          CGFloat pointY = 10; //button Y坐标
          
          NSMutableArray *previewUrls = [NSMutableArray arrayWithCapacity:100];
          for (NSDictionary *previewDic in imageArr) {
            [previewUrls addObject:[previewDic objectNilForKey:@"url"]];
          }
          
          for (int k = 0; k < imageArr.count; k ++) {
            
            NSDictionary *imageDic = [imageArr objectAtIndexCheck:k];
            if (pointX + imageH > (BCWidth - 32)) {//换行
              pointX = 0;//X从新开始
              pointY += (imageH + paddingT);//换行后Y+
            }
            
            UIButton *imageBtn = [[UIButton alloc] initWithFrame:CGRectMake(pointX, pointY, imageH, imageH)];
            imageBtn.layer.cornerRadius = 4;
            imageBtn.clipsToBounds = YES;
            UIImageView *imageI = [[UIImageView alloc] initWithFrame:CGRectMake(0,0, imageH, imageH)];
            [imageI sd_setImageWithURL:[NSURL URLWithString:[[imageDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
            [imageBtn addSubview:imageI];
            [imageV addSubview:imageBtn];
            [imageBtn addtargetBlock:^(UIButton *button) {//预览图片
              [ZKPhotoBrowser showWithImageUrls:previewUrls currentPhotoIndex:k sourceSuperView:button];
            }];
            
            pointX += (imageH + paddingL);
          }
          
          imageV.height = pointY + imageH ;
          if (height > 21) {
            LabelH += height + 14 + imageV.height;
          } else {
            LabelH +=  imageV.height + 14 + 21;
          }
          
        }
        
        
      } else if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"VIDEO"]) {//如果是视频
        
        NSArray *videoArr = [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
        
        if (BCArrayIsEmpty(videoArr)) {//如果没有视频，展示-
          
          UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(130,LabelH, CGRectGetWidth(bottomV.bounds) - 146, 21)];
          rightL.textAlignment = NSTextAlignmentRight;
          rightL.text = @"-";
          rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
          rightL.textColor = COLOR(31, 33, 38);
          [bottomV addSubview:rightL];
          if (height > 21) {
            LabelH += height + 14;
          } else {
            LabelH += 35;
          }
          
        } else {//如果有视频
          
          UIView *imageV = [[UIView alloc] initWithFrame:CGRectMake(16, leftL.bottom, BCWidth - 32, 0)];
          [bottomV addSubview:imageV];
          CGFloat imageH = (BCWidth - 32 - 24)/4;
          CGFloat paddingL = 8.0; //button 间距
          CGFloat paddingT = 10; //button 间距
          CGFloat pointX = 0; //button X坐标
          CGFloat pointY = 10; //button Y坐标
          
          for (int k = 0; k < videoArr.count; k ++) {
            
            NSDictionary *imageDic = [videoArr objectAtIndexCheck:k];
            if (pointX + imageH > (BCWidth - 32)) {//换行
              pointX = 0;//X从新开始
              pointY += (imageH + paddingT);//换行后Y+
            }
            
            UIButton *imageBtn = [[UIButton alloc] initWithFrame:CGRectMake(pointX, pointY, imageH, imageH)];
            imageBtn.layer.cornerRadius = 4;
            imageBtn.clipsToBounds = YES;
            NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[imageDic objectNilForKey:@"url"]];
            UIImageView *imageI = [[UIImageView alloc] initWithFrame:CGRectMake(0,0, imageH, imageH)];
            [imageI sd_setImageWithURL:[NSURL URLWithString:[videoUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
            [imageBtn addSubview:imageI];
            
            UIImageView *playIM = [[UIImageView alloc] initWithFrame:CGRectMake((imageH - 25)/2, (imageH - 25)/2, 25, 25)];
            playIM.image = [UIImage imageNamed:@"play_video"];
            [imageBtn addSubview:playIM];
            
            [imageV addSubview:imageBtn];
            [imageBtn addtargetBlock:^(UIButton *button) {
              NSURL * url = [NSURL URLWithString:[[imageDic objectForKeyNil:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
              AVPlayerViewController * pVC = [[AVPlayerViewController alloc] init];
              pVC.player = [AVPlayer playerWithURL:url];
              [weakSelf presentViewController:pVC animated:YES completion:nil];
              [pVC.player play];
            }];
            
            
            pointX += (imageH + paddingL);
          }
          
          imageV.height = pointY + imageH ;
          if (height > 21) {
            LabelH += height + 14 + imageV.height;
          } else {
            LabelH +=  imageV.height + 14 + 21;
          }
          
          
        }
        
      } else if([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"TABLE"]){
        
        NSArray *arr = @[];
        if (!BCArrayIsEmpty([self.itemChangeDic objectForKeyNil:[itemDic objectNilForKey:@"id"]])) {
          arr = [self.itemChangeDic objectForKeyNil:[itemDic objectNilForKey:@"id"]];
        }
       
        UIView *tableV = [self getTableView:itemDic andRightArr:arr endTop:leftL.top];
        [bottomV addSubview:tableV];
        LabelH +=  tableV.height;
      
        
      }else if( [[itemDic objectNilForKey:@"component_type"] isEqualToString:@"LOCATION"]){
        
        NSDictionary *dic = [self.itemChangeDic objectForKeyNil:[itemDic objectNilForKey:@"id"]];
        NSString *rightStr = @"-";
        if (!BCDictIsEmpty(dic)) {
          rightStr = [NSString stringWithFormat:@"%@",[dic objectNilForKey:@"address"]];
        }
        
        CGFloat rightHeight = [rightStr boundingRectWithSize:CGSizeMake(CGRectGetWidth(bottomV.bounds) - 146, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        rightHeight = rightHeight < 21 ? 21 : rightHeight;
        
        UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,LabelH, CGRectGetWidth(bottomV.bounds) - leftL.right - 22,rightHeight)];
        rightL.textAlignment = NSTextAlignmentRight;
        rightL.text = rightStr;
        rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
        rightL.textColor =  COLOR(26, 106, 255);
        rightL.numberOfLines = 0;
        [bottomV addSubview:rightL];
        
        if (rightHeight > height) {
          LabelH += rightHeight + 14;
        } else {
          if (height > 21) {
            LabelH += height + 14;
          } else {
            LabelH += 35;
          }
        }
        
        [rightL addTapGestureWithBlock:^{
          
          if ([rightStr isEqualToString:@"-"]) {
            [weakSelf.notiflyView showModal:NotiflyFail andTitle:@"定位地址为空，无法查看定位"];
            return;
          }
          
          MALookPointViewController *lookVC = [[MALookPointViewController alloc] init];
          lookVC.pointName = @"查看定位";
          lookVC.pointAddress = [dic objectNilForKey:@"address"];
          lookVC.pointLatitude = [dic objectNilForKey:@"latitude"];
          lookVC.pointLongitude = [dic objectNilForKey:@"longitude"];
          [weakSelf.navigationController pushViewController:lookVC animated:YES];
        }];
        
      } else if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"DATA_ASSOCIATION"]){//数据关联
        
        CGFloat rightHeight = 21;
        UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,LabelH, CGRectGetWidth(bottomV.bounds) - leftL.right - 22,rightHeight)];
        rightL.textAlignment = NSTextAlignmentRight;
        rightL.text = @"暂不支持该类型数据展示";
        rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
        rightL.textColor = COLOR(31, 33, 38);
        rightL.numberOfLines = 0;
        [bottomV addSubview:rightL];
        
        if (rightHeight > height) {
          LabelH += rightHeight + 14;
        } else {
          if (height > 21) {
            LabelH += height + 14;
          } else {
            LabelH += 35;
          }
        }
        
      } else {//剩下的全是文本类型
        NSString *rightStr = @"";
        if ([[self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] isKindOfClass:[NSArray class]]) {//如果是多选
          NSMutableArray *rightStrArr = [[NSMutableArray alloc] initWithCapacity:100];
          for (NSDictionary *rightStrDic in [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]) {
            [rightStrArr addObject:[rightStrDic objectNilForKey:@"label"]];
          }
          rightStr = [rightStrArr componentsJoinedByString:@","];
        } else {//正常字符串
          rightStr =  [NSString stringWithFormat:@"%@",[self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]];
        }
        CGFloat rightHeight = [rightStr boundingRectWithSize:CGSizeMake(CGRectGetWidth(bottomV.bounds) - 146, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        rightHeight = rightHeight < 21 ? 21 : rightHeight;
        
        UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(130,LabelH, CGRectGetWidth(bottomV.bounds) - 146,rightHeight)];
        rightL.textAlignment = NSTextAlignmentRight;
        rightL.text = [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] ? [NSString stringWithFormat:@"%@",rightStr] : @"-";
        rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
        rightL.textColor = COLOR(31, 33, 38);
        rightL.numberOfLines = 0;
        [bottomV addSubview:rightL];
        
        if (rightHeight > height) {
          LabelH += rightHeight + 14;
        } else {
          if (height > 21) {
            LabelH += height + 14;
          } else {
            LabelH += 35;
          }
        }
        
      }
      
      if (j == items.count - 1) {
        bottomV.height = LabelH ;
        itemH += bottomV.height + 12 ;
      }
    }
  }
  
  contentView.height = itemH;
  
  return contentView;
}

#pragma mark 根据数据源返回表格
- (UIView *)getTableView:(NSDictionary *)leftDic andRightArr:(NSArray *)rightArr endTop:(CGFloat)top{
  
  UIView *mainV = [[UIView alloc] initWithFrame:CGRectMake(0, top, BCWidth, 50)];
  mainV.backgroundColor = [UIColor whiteColor];
  
  if (BCArrayIsEmpty(rightArr)) {//如果表格没有数据
    
    UIView *topV = [[UIView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, 20)];
    [mainV addSubview:topV];
    
    NSString *titleStr = [NSString stringWithFormat:@"%@",[leftDic objectNilForKey:@"title"]];
    UILabel *leftTL = [[UILabel alloc] initWithFrame:CGRectMake(16, 0, BCWidth - 32, 20)];
    leftTL.text = titleStr;
    leftTL.numberOfLines = 1;
    leftTL.textColor = COLOR(31, 33, 38);
    leftTL.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
    [topV addSubview:leftTL];
    
    NSArray *items = [leftDic objectForKeyNil:@"details"];
    if (BCArrayIsEmpty(items)) {
      return mainV;
    }
    
    UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(16, topV.bottom + 8, BCWidth - 32, 50)];
    bottomV.layer.cornerRadius = 8;
    bottomV.layer.borderWidth = 1;
    bottomV.layer.borderColor = ACOLOR(31, 33, 38, 0.1).CGColor;
    [mainV addSubview:bottomV];
    
    __weak typeof(self)weakSelf = self;
    UIView *lastInV;//里面的每一小项选项
    for (int j = 0; j < items.count; j ++) {
      
      NSDictionary *itemDic = [items objectAtIndexCheck:j];
      if (BCDictIsEmpty(itemDic)) {
        continue;
      }
      
      //      右边的根据类型返回不同的组件
      NSString *rightStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"component_type"]];

      //      每一小项的id
      NSString *itemId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
      
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
      NSString *titleNameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"title"]];
      CGFloat height = [titleNameStr boundingRectWithSize:CGSizeMake(120, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      height = height < 22 ? 22 : height;
      
      
      UIView *itemV = [[UIView alloc] initWithFrame:CGRectMake(0, lastInV ? lastInV.bottom + 14: 0, bottomV.width, height + 14)];
      itemV.clipsToBounds = YES;
      [bottomV addSubview:itemV];
      
//      左侧标题
      UILabel *leftL = [[UILabel alloc] initWithFrame:CGRectMake(16, 14, 120, height)];
      leftL.text = titleNameStr;
      leftL.numberOfLines = 0;
      leftL.font = [UIFont systemFontOfSize:MutilFont(15)];
      leftL.textColor = ACOLOR(30, 33, 38, 0.45);
      [itemV addSubview:leftL];
      
//      右侧
      UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6, leftL.top, itemV.width - leftL.right - 22, height)];
      rightL.text = @"-";
      rightL.textAlignment = NSTextAlignmentRight;
      rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rightL.textColor = COLOR(31, 33, 38);
      [itemV addSubview:rightL];
      
      if (j > 0) {
        UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, itemV.width - 16, 1)];
        lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
        [itemV addSubview:lineV];
      }
      
      lastInV = itemV;
      
    }
    
    bottomV.height = lastInV.bottom + 14;
    mainV.height = bottomV.bottom + 14;
    
  } else {//表格有数据
    
    
    UIView *lastOutV;
    for (int i = 0; i < rightArr.count; i ++) {
      
      NSDictionary *dataDic = [rightArr objectAtIndexCheck:i];//表格里面的值
      
      
      UIView *contentV = [[UIView alloc] initWithFrame:CGRectMake(0, lastOutV?lastOutV.bottom + 14 : 0, BCWidth, 50)];
      [mainV addSubview:contentV];
      
      UIView *topV = [[UIView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, 20)];
      [contentV addSubview:topV];
      
      NSString *titleStr = [NSString stringWithFormat:@"%@",[leftDic objectNilForKey:@"title"]];
      UILabel *leftTL = [[UILabel alloc] initWithFrame:CGRectMake(16, 0, BCWidth - 32, 20)];
      leftTL.text = rightArr.count == 1? titleStr : [NSString stringWithFormat:@"%@%d",titleStr,i + 1];
      leftTL.numberOfLines = 1;
      leftTL.textColor = COLOR(31, 33, 38);
      leftTL.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
      [topV addSubview:leftTL];
      
      NSArray *items = [leftDic objectForKeyNil:@"details"];
      if (BCArrayIsEmpty(items)) {
        return mainV;
      }
      
      UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(16, topV.bottom + 8, BCWidth - 32, 50)];
      bottomV.layer.cornerRadius = 8;
      bottomV.layer.borderWidth = 1;
      bottomV.layer.borderColor = ACOLOR(31, 33, 38, 0.1).CGColor;
      [contentV addSubview:bottomV];
      
      __weak typeof(self)weakSelf = self;
      UIView *lastInV;//里面的每一小项选项
      for (int j = 0; j < items.count; j ++) {
        
        NSDictionary *itemDic = [items objectAtIndexCheck:j];
        if (BCDictIsEmpty(itemDic)) {
          continue;
        }
        
        //      右边的根据类型返回不同的组件
        NSString *rightStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"component_type"]];

        //      每一小项的id
        NSString *itemId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
        
        NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
        NSString *titleNameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"title"]];
        CGFloat height = [titleNameStr boundingRectWithSize:CGSizeMake(120, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        height = ceil(height);
        height = height < 22 ? 22 : height;
        
        
        UIView *itemV = [[UIView alloc] initWithFrame:CGRectMake(0, lastInV ? lastInV.bottom + 14: 0, bottomV.width, height + 14)];
        itemV.clipsToBounds = YES;
        [bottomV addSubview:itemV];
        
  //      左侧标题
        UILabel *leftL = [[UILabel alloc] initWithFrame:CGRectMake(16, 14, 120, height)];
        leftL.text = titleNameStr;
        leftL.numberOfLines = 0;
        leftL.font = [UIFont systemFontOfSize:MutilFont(15)];
        leftL.textColor = ACOLOR(30, 33, 38, 0.45);
        [itemV addSubview:leftL];
        
  //      右侧
        if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"IMAGE"]) {//如果是图片
          
          NSArray *imageArr = [dataDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
          
          if (BCArrayIsEmpty(imageArr)) {//如果没有图片，展示-
            
            UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,leftL.top, CGRectGetWidth(bottomV.bounds) - leftL.right - 22, height)];
            rightL.textAlignment = NSTextAlignmentRight;
            rightL.text = [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] ? [NSString stringWithFormat:@"%@",[self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]] : @"-";
            rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
            rightL.textColor = COLOR(31, 33, 38);
            [itemV addSubview:rightL];
            
            
          } else {//如果有图片
            
            UIView *imageV = [[UIView alloc] initWithFrame:CGRectMake(16, leftL.bottom, BCWidth - 32, 0)];
            [itemV addSubview:imageV];
            CGFloat imageH = (BCWidth - 32 - 24)/4;
            CGFloat paddingL = 8.0; //button 间距
            CGFloat paddingT = 10; //button 间距
            CGFloat pointX = 0; //button X坐标
            CGFloat pointY = 10; //button Y坐标
            
            NSMutableArray *previewUrls = [NSMutableArray arrayWithCapacity:100];
            for (NSDictionary *previewDic in imageArr) {
              [previewUrls addObject:[previewDic objectNilForKey:@"url"]];
            }
            
            for (int k = 0; k < imageArr.count; k ++) {
              
              NSDictionary *imageDic = [imageArr objectAtIndexCheck:k];
              if (pointX + imageH > (BCWidth - 32)) {//换行
                pointX = 0;//X从新开始
                pointY += (imageH + paddingT);//换行后Y+
              }
              
              UIButton *imageBtn = [[UIButton alloc] initWithFrame:CGRectMake(pointX, pointY, imageH, imageH)];
              imageBtn.layer.cornerRadius = 4;
              imageBtn.clipsToBounds = YES;
              UIImageView *imageI = [[UIImageView alloc] initWithFrame:CGRectMake(0,0, imageH, imageH)];
              [imageI sd_setImageWithURL:[NSURL URLWithString:[[imageDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
              [imageBtn addSubview:imageI];
              [imageV addSubview:imageBtn];
              [imageBtn addtargetBlock:^(UIButton *button) {//预览图片
                [ZKPhotoBrowser showWithImageUrls:previewUrls currentPhotoIndex:k sourceSuperView:button];
              }];
              
              pointX += (imageH + paddingL);
            }
            
            imageV.height = pointY + imageH ;
            itemV.height = imageV.bottom;
            
          }
          
          
        } else if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"VIDEO"]) {//如果是视频
          
          NSArray *videoArr = [dataDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
          
          if (BCArrayIsEmpty(videoArr)) {//如果没有视频，展示-
            
            UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,leftL.top, CGRectGetWidth(bottomV.bounds) - leftL.right - 22, height)];
            rightL.textAlignment = NSTextAlignmentRight;
            rightL.text = [self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] ? [NSString stringWithFormat:@"%@",[self.itemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]] : @"-";
            rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
            rightL.textColor = COLOR(31, 33, 38);
            [itemV addSubview:rightL];
            
            
          } else {//如果有视频
            
            UIView *imageV = [[UIView alloc] initWithFrame:CGRectMake(16, leftL.bottom, itemV.width - 32, 0)];
            [itemV addSubview:imageV];
            CGFloat imageH = (BCWidth - 32 - 24)/4;
            CGFloat paddingL = 8.0; //button 间距
            CGFloat paddingT = 10; //button 间距
            CGFloat pointX = 0; //button X坐标
            CGFloat pointY = 10; //button Y坐标
            
            for (int k = 0; k < videoArr.count; k ++) {
              
              NSDictionary *imageDic = [videoArr objectAtIndexCheck:k];
              if (pointX + imageH > (BCWidth - 32)) {//换行
                pointX = 0;//X从新开始
                pointY += (imageH + paddingT);//换行后Y+
              }
              
              UIButton *imageBtn = [[UIButton alloc] initWithFrame:CGRectMake(pointX, pointY, imageH, imageH)];
              imageBtn.layer.cornerRadius = 4;
              imageBtn.clipsToBounds = YES;
              NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[imageDic objectNilForKey:@"url"]];
              UIImageView *imageI = [[UIImageView alloc] initWithFrame:CGRectMake(0,0, imageH, imageH)];
              [imageI sd_setImageWithURL:[NSURL URLWithString:[videoUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
              [imageBtn addSubview:imageI];
              
              UIImageView *playIM = [[UIImageView alloc] initWithFrame:CGRectMake((imageH - 25)/2, (imageH - 25)/2, 25, 25)];
              playIM.image = [UIImage imageNamed:@"play_video"];
              [imageBtn addSubview:playIM];
              
              [imageV addSubview:imageBtn];
              [imageBtn addtargetBlock:^(UIButton *button) {
                NSURL * url = [NSURL URLWithString:[[imageDic objectForKeyNil:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
                AVPlayerViewController * pVC = [[AVPlayerViewController alloc] init];
                pVC.player = [AVPlayer playerWithURL:url];
                [weakSelf presentViewController:pVC animated:YES completion:nil];
                [pVC.player play];
              }];
              
              
              pointX += (imageH + paddingL);
            }
            
            imageV.height = pointY + imageH ;
            itemV.height = imageV.bottom;
            
          }
          
        } else if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"LOCATION"]) {//如果是定位组件
          
          NSDictionary *dic = [dataDic objectForKeyNil:[itemDic objectNilForKey:@"id"]];
          NSString *rightStr = @"-";
          if (!BCDictIsEmpty(dic)) {
            rightStr = [NSString stringWithFormat:@"%@",[dic objectNilForKey:@"address"]];
          }
          
          CGFloat rightHeight = [rightStr boundingRectWithSize:CGSizeMake(CGRectGetWidth(bottomV.bounds) - 146, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
          rightHeight = rightHeight < 22 ? 22 : rightHeight;
          
          UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,leftL.top, CGRectGetWidth(bottomV.bounds) - leftL.right - 22,rightHeight)];
          rightL.textAlignment = NSTextAlignmentRight;
          rightL.text = rightStr;
          rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
          rightL.textColor =  COLOR(26, 106, 255);
          rightL.numberOfLines = 0;
          [itemV addSubview:rightL];
          
          if (rightHeight > height) {
            itemV.height += rightHeight - height;
          }
          [rightL addTapGestureWithBlock:^{
            
            if ([rightStr isEqualToString:@"-"]) {
              [weakSelf.notiflyView showModal:NotiflyFail andTitle:@"定位地址为空，无法查看定位"];
              return;
            }
            
            MALookPointViewController *lookVC = [[MALookPointViewController alloc] init];
            lookVC.pointName = @"查看定位";
            lookVC.pointAddress = [dic objectNilForKey:@"address"];
            lookVC.pointLatitude = [dic objectNilForKey:@"latitude"];
            lookVC.pointLongitude = [dic objectNilForKey:@"longitude"];
            [weakSelf.navigationController pushViewController:lookVC animated:YES];
          }];
          
        } else {//其他的全是文本
          
          NSString *rightStr = @"";
          if ([[dataDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]] isKindOfClass:[NSArray class]]) {//如果是多选
            NSMutableArray *rightStrArr = [[NSMutableArray alloc] initWithCapacity:100];
            for (NSDictionary *rightStrDic in [dataDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]) {
              [rightStrArr addObject:[rightStrDic objectNilForKey:@"label"]];
            }
            rightStr = [rightStrArr componentsJoinedByString:@","];
          } else {//正常字符串
            rightStr =  [NSString stringWithFormat:@"%@",[dataDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]];
          }
          CGFloat rightHeight = [rightStr boundingRectWithSize:CGSizeMake(CGRectGetWidth(bottomV.bounds) - 146, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
          rightHeight = rightHeight < 21 ? 21 : rightHeight;
          
          UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(leftL.right + 6,leftL.top, CGRectGetWidth(bottomV.bounds) - leftL.right - 22,rightHeight)];
          rightL.textAlignment = NSTextAlignmentRight;
          rightL.text = BCStringIsEmpty(rightStr)?  @"-" : rightStr;
          rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
          rightL.textColor = COLOR(31, 33, 38);
          rightL.numberOfLines = 0;
          [itemV addSubview:rightL];
          
          if (rightHeight > height) {
            itemV.height = rightHeight;
            
          }
          
        }
       
        
        if (j > 0) {
          UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, itemV.width - 16, 1)];
          lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
          [itemV addSubview:lineV];
        }
        
        lastInV = itemV;
        
      }
      
      bottomV.height = lastInV.bottom + 14;
      contentV.height = bottomV.bottom;
     
      
      if (i == rightArr.count - 1) {
        contentV.height += 14;
      }
      
      lastOutV = contentV;
    }
    
    mainV.height = lastOutV.bottom;
    
    
  }
  
  return mainV;
  
}
#pragma mark 所有网络请求回来加载首页规划点位界面
- (UIView *)loadPointPlan:(CGFloat)top{
  
  __weak typeof(self)weakSelf = self;
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,top,BCWidth,50)];
  contentView.backgroundColor = [UIColor whiteColor];
  
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,200,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = [NSString stringWithFormat:@"规划点位（%lu）",(unsigned long)((![self.pointPlanArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.pointPlanArr)) ? 0:self.pointPlanArr.count)];
  [contentView addSubview:nameLabel];
  
  
  if (![self.pointPlanArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.pointPlanArr)) {
    return contentView;
  }
  
  for (int i = 0; i < (self.pointPlanArr.count > 3 ? 3 :self.pointPlanArr.count); i ++) {
    
    NSDictionary *itemDic = [self.pointPlanArr objectAtIndexCheck:i];
    
    UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(16, 50 + 100 * i, BCWidth - 32, 100)];
    bottomV.tag = 10010 + [[itemDic objectForKeyNil:@"id"] integerValue];
    [contentView addSubview:bottomV];
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(clickToPoint:)];
    [bottomV addGestureRecognizer:tap];
    bottomV.userInteractionEnabled = YES;
    //    分割线
    UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,50 + 100 * i, BCWidth - 16, 0.5)];
    lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
    [contentView addSubview:lineV];
    
    UIImageView *headIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 14, 72, 72)];
    headIM.layer.cornerRadius = 6;
    headIM.clipsToBounds = YES;
    NSString *imageUrl = [[[itemDic objectForKeyNil:@"files"] firstObject] objectNilForKey:@"url"];
    [headIM sd_setImageWithURL:[NSURL URLWithString:[imageUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] placeholderImage:[UIImage imageNamed:@"icon_pla"]];
    [bottomV addSubview:headIM];
    
    //    点位状态
    NSString *pointStateName = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"state"]];
    pointStateName = [self changeState:pointStateName];
    NSDictionary *attributes1 = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)]};
    CGFloat length = [pointStateName boundingRectWithSize:CGSizeMake(MAXFLOAT, 20) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes1 context:nil].size.width;
    length = ceil(length);
    
    UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(bottomV.width - length,14, length, 20)];
    rightL.text = pointStateName;
    rightL.font = [UIFont systemFontOfSize:MutilFont(14)];
    [bottomV addSubview:rightL];
    if ([rightL.text isEqualToString:@"制单"]) {
      rightL.textColor =  [UIColor colorWithRed:26/255.0 green:106/255.0 blue:255/255.0 alpha:1];
    } else if ([rightL.text isEqualToString:@"审批通过"]) {
      rightL.textColor = [UIColor colorWithRed:0/255.0 green:180/255.0 blue:43/255.0 alpha:1];
    } else if ([rightL.text isEqualToString:@"否决"] || [rightL.text isEqualToString:@"无效"]) {
      rightL.textColor = [UIColor colorWithRed:134/255.0 green:144/255.0 blue:156/255.0 alpha:1];
    } else {
      rightL.textColor =  [UIColor colorWithRed:255/255.0 green:125/255.0 blue:1/255.0 alpha:1];
    }
    
    //   点位名称
    NSString *pointName = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"name"]];
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    [paragraphStyle setLineSpacing:4];
    NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium],NSParagraphStyleAttributeName:paragraphStyle};
    CGFloat height = [pointName boundingRectWithSize:CGSizeMake(bottomV.width - 98 - length, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
    height = ceil(height);
    UILabel *leftL = [[UILabel alloc] initWithFrame:CGRectMake(headIM.right + 12, 14, bottomV.width - 98 - length, height < 20 ? 20 : height)];
    leftL.text = pointName;
    leftL.font = [UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium];
    leftL.textColor = ACOLOR(31, 33, 38, 1);
    leftL.numberOfLines = 2;
    [bottomV addSubview:leftL];
    
    //    跟进人
    NSString *followStr = [itemDic objectNilForKey:@"follow_by"];
    NSString *followState = [[itemDic objectNilForKey:@"follow_state"] isEqualToString:@"CLOSE"] ? @"关闭" : [[itemDic objectNilForKey:@"follow_state"] isEqualToString:@"FINISH"] ? @"完成":@"跟进中";
    
    NSString *followResult = [NSString stringWithFormat:@"跟进人：%@ %@",followStr,followState];
    NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
    [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:134/255.0 green:144/255.0 blue:156/255.0 alpha:1.000000]} range:NSMakeRange(0, 4 + followStr.length)];
    
    if ([followState isEqualToString:@"跟进中"]) {
      [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:255/255.0 green:125/255.0 blue:1/255.0 alpha:1.000000]} range:NSMakeRange(5 + followStr.length, followState.length)];
      
    } else if ([followState isEqualToString:@"完成"]) {
      [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:0/255.0 green:180/255.0 blue:43/255.0 alpha:1.000000]} range:NSMakeRange(5 + followStr.length, followState.length)];
      
    } else {
      [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:245/255.0 green:63/255.0 blue:63/255.0 alpha:1.000000]} range:NSMakeRange(5 + followStr.length, followState.length)];
    }
    
    UILabel *followLabel = [[UILabel alloc] init];
    followLabel.frame = CGRectMake(headIM.right + 13 ,62, BCWidth - 114, 18.5);
    followLabel.font = [UIFont systemFontOfSize:MutilFont(13)];
    followLabel.attributedText = string;
    [bottomV addSubview:followLabel];
  }
  //  查看全部按钮
  UIButton *allBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  allBtn.frame = CGRectMake(0, 50 + 100 * (self.pointPlanArr.count > 3 ? 3 :self.pointPlanArr.count), BCWidth , 48);
  [contentView addSubview:allBtn];
  [allBtn addtargetBlock:^(UIButton *button) {
    UIButton *tabBtn = [weakSelf.topTabV viewWithTag:1015];
    [tabBtn sendActionsForControlEvents:UIControlEventTouchUpInside];
  }];
  
  
  UILabel *switchlabel1 = [[UILabel alloc] init];
  switchlabel1.frame = CGRectMake((BCWidth - RealSize(58) - 18)/2,0,RealSize(58),48);
  switchlabel1.text = @"显示全部";
  switchlabel1.textColor = COLOR(26, 106, 255);
  switchlabel1.font = [UIFont systemFontOfSize:MutilFont(14)];
  [allBtn addSubview:switchlabel1];
  
  
  UIImageView *leftIM1 = [[UIImageView alloc] initWithFrame:CGRectMake(switchlabel1.right + 4, 17, 14, 14)];
  leftIM1.image = [UIImage imageNamed:@"look_landlord"];
  [allBtn addSubview:leftIM1];
  
  UIView *lineV1 = [[UIView alloc] initWithFrame:CGRectMake(0,50 + 100 * (self.pointPlanArr.count > 3 ? 3 :self.pointPlanArr.count), BCWidth , 0.5)];
  lineV1.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [contentView addSubview:lineV1];
  
  
  contentView.height = 50 + 100 * (self.pointPlanArr.count > 3 ? 3 :self.pointPlanArr.count) + 48;
  return contentView;
}

#pragma mark 跳转到对应的规划点位
- (void)clickToPoint:(UITapGestureRecognizer *)tap{
  UIView *bottomV = tap.view;
  
 NSString *pointID = [NSString stringWithFormat:@"%ld",(long)(bottomV.tag - 10010)];
  
  [self.loadingView showModal];
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.storeplan.read" Params:@{@"id":pointID} success:^(NSDictionary *successResult) {
    [self.loadingView hideModal];
    
    MAPointDetailViewController *sec = [[MAPointDetailViewController alloc] init];
    sec.pushType = PushAnimationTypeNone;
    sec.pointId = pointID;
    sec.scrollIndex = 0;
    [self.navigationController pushViewController:sec animated:YES];
    
  } failure:^(NSString *errorResult) {
    [self.loadingView hideModal];
    [self.view makeToast:errorResult duration:1 position:CSToastPositionCenter];
    
  }];
  
}
#pragma mark 所有网络请求回来加载首页全部动态界面
- (UIView *)loadBuniessState:(CGFloat )top{
  __weak typeof(self)weakSelf = self;
  NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
  NSArray *colorArr = @[@[(__bridge id)COLOR(158, 171, 255).CGColor,(__bridge id)COLOR(45, 78, 238).CGColor],
                        @[(__bridge id)COLOR(27, 236, 212).CGColor,(__bridge id)COLOR(0, 196, 170).CGColor],
                        @[(__bridge id)COLOR(178, 132, 255).CGColor,(__bridge id)COLOR(127, 60, 237).CGColor],
                        @[(__bridge id)COLOR(158, 225, 255).CGColor,(__bridge id)COLOR(36, 78, 241).CGColor],
                        @[(__bridge id)COLOR(255, 174, 132).CGColor,(__bridge id)COLOR(237, 60, 60).CGColor],
                        @[(__bridge id)COLOR(255, 183, 100).CGColor,(__bridge id)COLOR(240, 130, 3).CGColor],
  ];
  
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,top + 12,BCWidth,50)];
  contentView.backgroundColor = [UIColor whiteColor];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,200,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = [NSString stringWithFormat:@"商圈动态（%lu）",(unsigned long)((![self.allBuniessArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.allBuniessArr)) ? 0:self.allBuniessArr.count)];
  [contentView addSubview:nameLabel];
  
  UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
  switchMap.frame = CGRectMake(BCWidth - 67 - 15, 0, 82, 50);
  //  [contentView addSubview:switchMap];
  
  UIImageView *leftIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 16, 18, 18)];
  leftIM.image = [UIImage imageNamed:@"point_add"];
  [switchMap addSubview:leftIM];
  
  UILabel *switchlabel = [[UILabel alloc] init];
  switchlabel.frame = CGRectMake(20,0,47,50);
  switchlabel.text = @"写跟进";
  switchlabel.textColor = COLOR(26, 106, 255);
  switchlabel.font = [UIFont systemFontOfSize:15];
  [switchMap addSubview:switchlabel];
  
  if (![self.allBuniessArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.allBuniessArr)) {
    return contentView;
  }
  
  //    分割线
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,49, BCWidth - 16, 1)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [contentView addSubview:lineV];
  
  CGFloat itemH = 50 + 14;
  for (int i = 0; i < (self.allBuniessArr.count > 3 ? 3 :self.allBuniessArr.count); i ++) {
    NSDictionary *pointDic = [self.allBuniessArr objectAtIndexCheck:i];
    
    UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth, 178)];
    bottomV.backgroundColor = [UIColor whiteColor];
    [contentView addSubview:bottomV];
    
    NSString *typeStr = [pointDic objectNilForKey:@"type"];
    NSDictionary *itemDic = [pointDic objectForKeyNil:@"data"];
    if ([typeStr isEqualToString:@"TRANSFER_RECORD"]) {
      
      NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
      [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
      [dateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
      NSString *creatTimeStr = [NSString stringWithFormat:@"%@",[pointDic objectNilForKey:@"create_time"]];
      NSDate *creatDate = [dateFormatter dateFromString:creatTimeStr];
      
      NSDateFormatter *newdateFormatter = [[NSDateFormatter alloc] init];
      [newdateFormatter setDateFormat:@"yyyy-MM-dd HH:mm"];
      [newdateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
      NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
      NSString *followResult = [NSString stringWithFormat:@"%@ · 转移 · %@",nameStr,[newdateFormatter stringFromDate:creatDate]];
      NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
      [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
      
      NSInteger randomNum = arc4random_uniform(6);
      UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
      headImageV.layer.cornerRadius = 12;
      headImageV.clipsToBounds = YES;
      [bottomV addSubview:headImageV];
      
      CAGradientLayer *gl = [CAGradientLayer layer];
      gl.frame = headImageV.bounds;
      gl.startPoint = CGPointMake(0.0, 0);
      gl.endPoint = CGPointMake(1, 1);
      gl.colors = [colorArr objectAtIndexCheck:randomNum];
      gl.locations = @[@(0), @(1.0f)];
      [headImageV.layer addSublayer:gl];
      
      UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
      headL.textColor = [UIColor whiteColor];
      headL.textAlignment = NSTextAlignmentCenter;
      headL.font = [UIFont systemFontOfSize:10];
      headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
      [headImageV addSubview:headL];
      
      UILabel *switchlabel = [[UILabel alloc] init];
      switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - headImageV.right - 22,20);
      switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
      switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
      [bottomV addSubview:switchlabel];
      switchlabel.attributedText = string;
      
      //        变更字段
      NSString *beforeStr = @"跟进人";
      NSString *beforeResult = [NSString stringWithFormat:@"变更字段：%@",beforeStr];
      NSMutableAttributedString *beforeString = [[NSMutableAttributedString alloc] initWithString:beforeResult];
      [beforeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, beforeStr.length)];
      
      UILabel *beforelabel = [[UILabel alloc] init];
      beforelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 6,BCWidth - 80,20);
      beforelabel.textColor = ACOLOR(30, 33, 38, 0.45);
      beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:beforelabel];
      beforelabel.attributedText = beforeString;
      
      //        原负责人
      NSString *beforePeopleStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"before_change"]];
      NSString *beforePeopleResult = [NSString stringWithFormat:@"原跟进人：%@",BCStringIsEmpty(beforePeopleStr) ? @"-" : beforePeopleStr];
      NSMutableAttributedString *resultString = [[NSMutableAttributedString alloc] initWithString:beforePeopleResult];
      [resultString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5,BCStringIsEmpty(beforePeopleStr) ? 1 : beforePeopleStr.length)];
      
      UILabel *resultlabel = [[UILabel alloc] init];
      resultlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 80,20);
      resultlabel.textColor = ACOLOR(30, 33, 38, 0.45);
      resultlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:resultlabel];
      resultlabel.attributedText = resultString;
      
      //      新负责人
      NSString *timeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"after_change"]];
      NSString *timeResult = [NSString stringWithFormat:@"新跟进人：%@",timeStr];
      NSMutableAttributedString *timeString = [[NSMutableAttributedString alloc] initWithString:timeResult];
      [timeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, timeStr.length)];
      
      UILabel *timelabel = [[UILabel alloc] init];
      timelabel.frame = CGRectMake(headImageV.right + 6,resultlabel.bottom + 4,BCWidth - 80,20);
      timelabel.textColor = ACOLOR(30, 33, 38, 0.45);
      timelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:timelabel];
      timelabel.attributedText = timeString;
      
      //        转交原因
      NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"change_reason"]];
      NSString *memoResult = [NSString stringWithFormat:@"转交原因：%@",!BCStringIsEmpty(memoStr) ? memoStr : @"-"];
      NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:memoResult];
      NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
      [paragraphStyle setLineSpacing:4];//调整行间距
      paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
      [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, memoResult.length)];
      [opereateResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 5)];
      
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:paragraphStyle};
      CGFloat height = [memoResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      height = height < 20 ? 20 : height;
      
      UILabel *memolabel = [[UILabel alloc] init];
      memolabel.frame = CGRectMake(headImageV.right + 6 , timelabel.bottom + 4,BCWidth - 62,height);
      memolabel.textColor = ACOLOR(31, 33, 38, 1);
      memolabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      memolabel.numberOfLines = 0;
      [bottomV addSubview:memolabel];
      memolabel.attributedText = opereateResultStr;
      
      //      分割线
      if (i < 2 && self.self.allBuniessArr.count > 1 ) {
        UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, memolabel.bottom + 14, BCWidth - 16, 0.5)];
        lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
        [bottomV addSubview:lineV];
      }
      
      bottomV.height = memolabel.bottom + 14 + 14;
      if (i == (self.self.allBuniessArr.count > 3 ? 3 : self.allBuniessArr.count) - 1) {
        bottomV.height = memolabel.bottom + 14;
      }
      itemH += bottomV.height;
      
    } else if ([typeStr isEqualToString:@"AUDIT_RECORD"]) {//操作
      
      NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
      NSString *followResult = [NSString stringWithFormat:@"%@ · 操作 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
      NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
      [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
      
      NSInteger randomNum = arc4random_uniform(6);
      UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
      headImageV.layer.cornerRadius = 12;
      headImageV.clipsToBounds = YES;
      [bottomV addSubview:headImageV];
      
      CAGradientLayer *gl = [CAGradientLayer layer];
      gl.frame = headImageV.bounds;
      gl.startPoint = CGPointMake(0.0, 0);
      gl.endPoint = CGPointMake(1, 1);
      gl.colors = [colorArr objectAtIndexCheck:randomNum];
      gl.locations = @[@(0), @(1.0f)];
      [headImageV.layer addSublayer:gl];
      
      UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
      headL.textColor = [UIColor whiteColor];
      headL.textAlignment = NSTextAlignmentCenter;
      headL.font = [UIFont systemFontOfSize:10];
      headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
      [headImageV addSubview:headL];
      
      UILabel *switchlabel = [[UILabel alloc] init];
      switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
      switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
      switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
      [bottomV addSubview:switchlabel];
      switchlabel.attributedText = string;
      
      NSString *operateStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"model_state"]];
      NSString *operateResult = [NSString stringWithFormat:@"操作：%@",operateStr];
      NSMutableAttributedString *operateString = [[NSMutableAttributedString alloc] initWithString:operateResult];
      [operateString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(3, operateStr.length)];
      
      UILabel *operatelabel = [[UILabel alloc] init];
      operatelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 8,BCWidth - 80,20);
      operatelabel.textColor = ACOLOR(30, 33, 38, 0.45);
      operatelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:operatelabel];
      operatelabel.attributedText = operateString;
      
      
      NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
      NSString *memoResult = [NSString stringWithFormat:@"审批意见：%@",!BCStringIsEmpty(memoStr) ? memoStr : @"-"];
      NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:memoResult];
      NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
      [paragraphStyle setLineSpacing:4];//调整行间距
      paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
      [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, memoResult.length)];
      [opereateResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 5)];
      
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:paragraphStyle};
      CGFloat height = [memoResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      height = height < 20 ? 20 : height;
      
      UILabel *memolabel = [[UILabel alloc] init];
      memolabel.frame = CGRectMake(headImageV.right + 6 , operatelabel.bottom + 4,BCWidth - 62,height);
      memolabel.textColor = ACOLOR(31, 33, 38, 1);
      memolabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      memolabel.numberOfLines = 0;
      [bottomV addSubview:memolabel];
      memolabel.attributedText = opereateResultStr;
      //      分割线
      if (i < 2 && self.allBuniessArr.count > 1 ) {
        UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, memolabel.bottom + 14, BCWidth - 16, 0.5)];
        lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
        [bottomV addSubview:lineV];
      }
      
      bottomV.height = memolabel.bottom + 14 + 14;
      if (i == (self.allBuniessArr.count > 3 ? 3 :self.allBuniessArr.count) - 1) {
        bottomV.height = memolabel.bottom + 14;
      }
      itemH += bottomV.height;
    } else if ([typeStr isEqualToString:@"CHANGE_RECORD"]) {//变更
      
      NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
      NSString *followResult = [NSString stringWithFormat:@"%@ · 变更 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
      NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
      [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
      
      NSInteger randomNum = arc4random_uniform(6);
      UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
      headImageV.layer.cornerRadius = 12;
      headImageV.clipsToBounds = YES;
      [bottomV addSubview:headImageV];
      
      CAGradientLayer *gl = [CAGradientLayer layer];
      gl.frame = headImageV.bounds;
      gl.startPoint = CGPointMake(0.0, 0);
      gl.endPoint = CGPointMake(1, 1);
      gl.colors = [colorArr objectAtIndexCheck:randomNum];
      gl.locations = @[@(0), @(1.0f)];
      [headImageV.layer addSublayer:gl];
      
      UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
      headL.textColor = [UIColor whiteColor];
      headL.textAlignment = NSTextAlignmentCenter;
      headL.font = [UIFont systemFontOfSize:10];
      headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
      [headImageV addSubview:headL];
      
      UILabel *switchlabel = [[UILabel alloc] init];
      switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
      switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
      switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
      [bottomV addSubview:switchlabel];
      switchlabel.attributedText = string;
      
      
      NSString *operateStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"field_name"]];
      NSString *operateResult = [NSString stringWithFormat:@"变更字段：%@",operateStr];
      NSMutableAttributedString *operateString = [[NSMutableAttributedString alloc] initWithString:operateResult];
      [operateString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, operateStr.length)];
      
      UILabel *operatelabel = [[UILabel alloc] init];
      operatelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 8,BCWidth - 80,20);
      operatelabel.textColor = ACOLOR(30, 33, 38, 0.45);
      operatelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:operatelabel];
      operatelabel.attributedText = operateString;
      
      //       原值
      NSString *beforeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"before_change"]];
      if ([beforeStr containsString:@"https"] || [beforeStr containsString:@"http"]  || BCStringIsEmpty(beforeStr)) {
        beforeStr = @"-";
      }
      
      NSString *beforeResult = [NSString stringWithFormat:@"原值：%@", beforeStr];
      NSMutableAttributedString *beforeResultStr = [[NSMutableAttributedString alloc] initWithString:beforeResult];
      NSMutableParagraphStyle *beforeParagraphStyle = [[NSMutableParagraphStyle alloc] init];
      [beforeParagraphStyle setLineSpacing:4];//调整行间距
      beforeParagraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
      [beforeResultStr addAttribute:NSParagraphStyleAttributeName value:beforeParagraphStyle range:NSMakeRange(0, beforeResult.length)];
      [beforeResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 3)];
      
      NSDictionary *beforeAttributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:beforeParagraphStyle};
      CGFloat beforeHeight = [beforeResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:beforeAttributes context:nil].size.height;
      beforeHeight= ceil(beforeHeight);
      beforeHeight = beforeHeight < 20 ? 20 : beforeHeight;
      
      UILabel *beforelabel = [[UILabel alloc] init];
      beforelabel.frame = CGRectMake(headImageV.right + 6 , operatelabel.bottom + 4,BCWidth - 62,beforeHeight);
      beforelabel.textColor = COLOR(31, 33, 38);
      beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      beforelabel.numberOfLines = 0;
      [bottomV addSubview:beforelabel];
      beforelabel.attributedText = beforeResultStr;
      
      //        新值
      NSString *afterStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"after_change"]];
      if ([afterStr containsString:@"https"] || [afterStr containsString:@"http"]  || BCStringIsEmpty(beforeStr)) {
        afterStr = @"-";
      }
      
      NSString *afterResult = [NSString stringWithFormat:@"新值：%@", afterStr];
      NSMutableAttributedString *afterResultStr = [[NSMutableAttributedString alloc] initWithString:afterResult];
      NSMutableParagraphStyle *afterParagraphStyle = [[NSMutableParagraphStyle alloc] init];
      [afterParagraphStyle setLineSpacing:4];//调整行间距
      afterParagraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
      [afterResultStr addAttribute:NSParagraphStyleAttributeName value:afterParagraphStyle range:NSMakeRange(0, afterResult.length)];
      [afterResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 3)];
      
      NSDictionary *afterAttributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:afterParagraphStyle};
      CGFloat afterHeight = [afterResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:afterAttributes context:nil].size.height;
      afterHeight = ceil(afterHeight);
      afterHeight = afterHeight < 20 ? 20 : afterHeight;
      
      UILabel *afterlabel = [[UILabel alloc] init];
      afterlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 62,afterHeight);
      afterlabel.textColor = COLOR(31, 33, 38);
      afterlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
      afterlabel.numberOfLines = 0;
      [bottomV addSubview:afterlabel];
      afterlabel.attributedText = afterResultStr;
      
      //      分割线
      if (i < 2 && self.allBuniessArr.count > 1 ) {
        UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, afterlabel.bottom + 14, BCWidth - 16, 0.5)];
        lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
        [bottomV addSubview:lineV];
      }
      
      bottomV.height = afterlabel.bottom + 14 + 14;
      if (i == (self.allBuniessArr.count > 3 ? 3 :self.allBuniessArr.count) - 1) {
        bottomV.height = afterlabel.bottom + 14;
      }
      itemH += bottomV.height;
   
    } else { //跟进记录
      NSDictionary *itemDic = [pointDic objectForKeyNil:@"data"];
      NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
      NSString *followResult = [NSString stringWithFormat:@"%@ · 跟进 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
      NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
      [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
      
      NSInteger randomNum = arc4random_uniform(6);
      UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
      headImageV.layer.cornerRadius = 12;
      headImageV.clipsToBounds = YES;
      [bottomV addSubview:headImageV];
      
      CAGradientLayer *gl = [CAGradientLayer layer];
      gl.frame = headImageV.bounds;
      gl.startPoint = CGPointMake(0.0, 0);
      gl.endPoint = CGPointMake(1, 1);
      gl.colors = [colorArr objectAtIndexCheck:randomNum];
      gl.locations = @[@(0), @(1.0f)];
      [headImageV.layer addSublayer:gl];
      
      UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
      headL.textColor = [UIColor whiteColor];
      headL.textAlignment = NSTextAlignmentCenter;
      headL.font = [UIFont systemFontOfSize:10];
      headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
      [headImageV addSubview:headL];
      
      UILabel *switchlabel = [[UILabel alloc] init];
      switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
      switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
      switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
      [bottomV addSubview:switchlabel];
      switchlabel.attributedText = string;
      
      //        备注
      NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
      CGFloat height = [memoStr boundingRectWithSize:CGSizeMake(BCWidth - 92, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      UILabel *memoLabel= [[UILabel alloc] initWithFrame:CGRectMake(headImageV.right + 6, headImageV.bottom + 6, BCWidth - 92,BCStringIsEmpty(memoStr) ? 0 : height)];
      memoLabel.textColor = ACOLOR(29, 33, 41, 1);
      memoLabel.font = [UIFont systemFontOfSize:MutilFont(15)];
      memoLabel.text = memoStr;
      memoLabel.numberOfLines = 0;
      [bottomV addSubview:memoLabel];
      
      //        附件信息
      NSArray *fileArr = [itemDic objectForKeyNil:@"files"];
      if ([fileArr isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(fileArr)) {
        
        
        for (int j = 0; j <fileArr.count; j ++) {
          
          NSDictionary *deedDic = [fileArr objectAtIndexCheck:j];
          UIButton *leaseImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(headImageV.right + 6, memoLabel.bottom + 8 + (44 + 10) * j, BCWidth - 62, 44)];
          leaseImageBtn.layer.cornerRadius = 4;
          leaseImageBtn.clipsToBounds = YES;
          leaseImageBtn.backgroundColor = COLOR(244, 245, 247);
          [bottomV addSubview:leaseImageBtn];
          
          UIImageView *leaseIM = [[UIImageView alloc] initWithFrame:CGRectMake(16, 10 ,24, 24)];
          [leaseImageBtn addSubview:leaseIM];
          
          UILabel *leaseL = [[UILabel alloc] initWithFrame:CGRectMake(leaseIM.right + 10,0, leaseImageBtn.width - leaseIM.right - 26, 44)];
          leaseL.text = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"name"]];
          leaseL.font = [UIFont systemFontOfSize:MutilFont(14)];
          leaseL.textColor = ACOLOR(31, 33, 38,1);
          leaseL.lineBreakMode = NSLineBreakByTruncatingMiddle;
          [leaseImageBtn addSubview:leaseL];
          
          if ([temArr containsObject:[deedDic objectNilForKey:@"suffix_type"]]) {//如果是图片
            
            leaseIM.image = [UIImage imageNamed:@"file_img"];
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
              [ZKPhotoBrowser showWithImageUrls:@[[[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] currentPhotoIndex:0 sourceSuperView:button];
            }];
          } else {//是其他文本 pdf  word
            
            NSString *fileStr = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"suffix_type"] ];
            if ([fileStr containsString:@"pdf"]) {
              leaseIM.image = [UIImage imageNamed:@"file_pdf"];
            } else  if ([fileStr containsString:@"doc"] || [fileStr containsString:@"docx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_word"];
            } else  if ([fileStr containsString:@"xls"] || [fileStr containsString:@"xls"]) {
              leaseIM.image = [UIImage imageNamed:@"file_excel"];
            }else  if ([fileStr containsString:@"ppt"] || [fileStr containsString:@"pptx"]) {
              leaseIM.image = [UIImage imageNamed:@"file_ppt"];
            }else {
              leaseIM.image = [UIImage imageNamed:@"file_no"];
            }
            
            [leaseImageBtn addtargetBlock:^(UIButton *button) {
             
              NSString *url = [[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
              [weakSelf openSFDoc:url];
            }];
          }
          
        }
      }
      
      //        计算高度
      CGFloat bottomH = 0;
      if (![fileArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(fileArr)) {//没有附件
        bottomH  = memoLabel.bottom  + 14;
      } else {
        bottomH  = memoLabel.bottom + 8 + (44 + 10) * fileArr .count - 8 + 14;
      }
      
      bottomV.height = bottomH + 14;
      
      //      分割线
      if (i < 2 && self.allBuniessArr.count > 1 ) {
        UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, bottomH, BCWidth - 16, 0.5)];
        lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
        [bottomV addSubview:lineV];
      }
      
      if (i == (self.allBuniessArr.count > 3 ? 3 :self.allBuniessArr.count) - 1) {
        bottomV.height = bottomH;
      }
      
      itemH += bottomV.height ;
    }
    
  }
  
  //  查看全部按钮
  UIButton *allBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  allBtn.frame = CGRectMake(0, itemH, BCWidth , 48);
  [contentView addSubview:allBtn];
  [allBtn addtargetBlock:^(UIButton *button) {
    UIButton *tabBtn = [weakSelf.topTabV viewWithTag:1016];
    [tabBtn sendActionsForControlEvents:UIControlEventTouchUpInside];
  }];
  
  
  UILabel *switchlabel1 = [[UILabel alloc] init];
  switchlabel1.frame = CGRectMake((BCWidth - RealSize(58) - 18)/2,0,RealSize(58),48);
  switchlabel1.text = @"显示全部";
  switchlabel1.textColor = COLOR(26, 106, 255);
  switchlabel1.font = [UIFont systemFontOfSize:MutilFont(14)];
  [allBtn addSubview:switchlabel1];
  
  
  UIImageView *leftIM1 = [[UIImageView alloc] initWithFrame:CGRectMake(switchlabel1.right + 4, 17, 14, 14)];
  leftIM1.image = [UIImage imageNamed:@"look_landlord"];
  [allBtn addSubview:leftIM1];
  
  UIView *lineV1 = [[UIView alloc] initWithFrame:CGRectMake(0,itemH, BCWidth , 1)];
  lineV1.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [contentView addSubview:lineV1];
  
  contentView.height = itemH + 48;
  
  return contentView;
}

#pragma mark 打开附件
- (void)openSFDoc:(NSString *)url{
  
  if (BCStringIsEmpty(url)) {
    [self.notiflyView showModal:NotiflyFail andTitle:@"附件地址出错"];
    return;
  }
  
//  NSURL *openUrl = [NSURL URLWithString:url];
//  if (url) {
//    SFSafariViewController *safariVC = [[SFSafariViewController alloc] initWithURL:openUrl];
//    safariVC.modalPresentationStyle = UIModalPresentationPopover;
//    [self presentViewController:safariVC animated:YES completion:nil];
//  }
  
  MAH5ViewController *webVC = [[MAH5ViewController alloc] init];
  webVC.url = url;
  webVC.titleStr = @"预览附件";
  webVC.modalPresentationStyle = UIModalPresentationPopover;
  [self presentViewController:webVC animated:YES completion:^{
    
  }];
  
}
#pragma mark 所有网络请求回来加载tab下的规划点位界面
- (void)loadPointPlanRecord{
  if (![self.pointPlanArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.pointPlanArr)) {//没数据
    
    UIImageView *emptyView = [[UIImageView alloc] initWithFrame:CGRectMake((BCWidth - 160)/2, self.heightTop + 144, 160, 180)];
    emptyView.image = [UIImage imageNamed:@"tab_nodata"];
    [self.pointPlanScrollView addSubview:emptyView];
    
    UILabel *emptyL = [[UILabel alloc] initWithFrame:CGRectMake(0, emptyView.bottom - 40, BCWidth, 20)];
    emptyL.textColor = ACOLOR(30, 33, 38, 0.45);
    emptyL.font = [UIFont systemFontOfSize:MutilFont(14)];
    emptyL.text = @"暂无规划点位";
    emptyL.textAlignment = NSTextAlignmentCenter;
    [self.pointPlanScrollView addSubview:emptyL];
    
  } else {
    __weak typeof(self)weakSelf = self;
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 12,BCWidth,50)];
    contentView.backgroundColor = [UIColor whiteColor];
    [self.pointPlanScrollView addSubview:contentView];
    
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.frame = CGRectMake(16,0,200,50);
    nameLabel.textColor = COLOR(31, 33, 38);
    nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
    nameLabel.text = [NSString stringWithFormat:@"规划点位（%lu）",(unsigned long)((![self.pointPlanArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.pointPlanArr)) ? 0:self.pointPlanArr.count)];
    [contentView addSubview:nameLabel];
    
    
    for (int i = 0; i < self.pointPlanArr.count; i ++) {
      
      NSDictionary *itemDic = [self.pointPlanArr objectAtIndexCheck:i];
      
      UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(16, 50 + 100 * i, BCWidth - 32, 100)];
      bottomV.tag = 10010 + [[itemDic objectForKeyNil:@"id"] integerValue];
      [contentView addSubview:bottomV];
      UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(clickToPoint:)];
      [bottomV addGestureRecognizer:tap];
      bottomV.userInteractionEnabled = YES;
      
      //    分割线
      UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,50 + 100 * i, BCWidth - 16, 1)];
      lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
      [contentView addSubview:lineV];
      
      UIImageView *headIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 14, 72, 72)];
      headIM.layer.cornerRadius = 6;
      headIM.clipsToBounds = YES;
      NSString *imageUrl = [[[itemDic objectForKeyNil:@"files"] firstObject] objectNilForKey:@"url"];
      [headIM sd_setImageWithURL:[NSURL URLWithString:[imageUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] placeholderImage:[UIImage imageNamed:@"icon_pla"]];
      [bottomV addSubview:headIM];
      
      //    点位状态
      NSString *pointStateName = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"state"]];
      pointStateName = [self changeState:pointStateName];
      NSDictionary *attributes1 = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)]};
      CGFloat length = [pointStateName boundingRectWithSize:CGSizeMake(MAXFLOAT, 20) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes1 context:nil].size.width;
      length = ceil(length);
      
      UILabel *rightL = [[UILabel alloc] initWithFrame:CGRectMake(bottomV.width - length,14, length, 20)];
      rightL.text = pointStateName;
      rightL.font = [UIFont systemFontOfSize:MutilFont(14)];
      [bottomV addSubview:rightL];
      if ([rightL.text isEqualToString:@"制单"]) {
        rightL.textColor =  [UIColor colorWithRed:26/255.0 green:106/255.0 blue:255/255.0 alpha:1];
      } else if ([rightL.text isEqualToString:@"审批通过"]) {
        rightL.textColor = [UIColor colorWithRed:0/255.0 green:180/255.0 blue:43/255.0 alpha:1];
      } else if ([rightL.text isEqualToString:@"否决"] || [rightL.text isEqualToString:@"无效"]) {
        rightL.textColor = [UIColor colorWithRed:134/255.0 green:144/255.0 blue:156/255.0 alpha:1];
      } else {
        rightL.textColor =  [UIColor colorWithRed:255/255.0 green:125/255.0 blue:1/255.0 alpha:1];
      }
      
      //   点位名称
      NSString *pointName = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"name"]];
      NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
      [paragraphStyle setLineSpacing:4];
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium],NSParagraphStyleAttributeName:paragraphStyle};
      CGFloat height = [pointName boundingRectWithSize:CGSizeMake(bottomV.width - 98 - length, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      UILabel *leftL = [[UILabel alloc] initWithFrame:CGRectMake(headIM.right + 12, 14, bottomV.width - 98 - length, height < 20 ? 20 : height)];
      leftL.text = pointName;
      leftL.font = [UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium];
      leftL.textColor = ACOLOR(31, 33, 38, 1);
      leftL.numberOfLines = 2;
      [bottomV addSubview:leftL];
      
      //    跟进人
      NSString *followStr = [itemDic objectNilForKey:@"follow_by"];
      NSString *followState = [[itemDic objectNilForKey:@"follow_state"] isEqualToString:@"CLOSE"] ? @"关闭" : [[itemDic objectNilForKey:@"follow_state"] isEqualToString:@"FINISH"] ? @"完成":@"跟进中";
      
      NSString *followResult = [NSString stringWithFormat:@"跟进人：%@ %@",followStr,followState];
      NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
      [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:134/255.0 green:144/255.0 blue:156/255.0 alpha:1.000000]} range:NSMakeRange(0, 4 + followStr.length)];
      
      if ([followState isEqualToString:@"跟进中"]) {
        [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:255/255.0 green:125/255.0 blue:1/255.0 alpha:1.000000]} range:NSMakeRange(5 + followStr.length, followState.length)];
        
      } else if ([followState isEqualToString:@"完成"]) {
        [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:0/255.0 green:180/255.0 blue:43/255.0 alpha:1.000000]} range:NSMakeRange(5 + followStr.length, followState.length)];
        
      } else {
        [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:245/255.0 green:63/255.0 blue:63/255.0 alpha:1.000000]} range:NSMakeRange(5 + followStr.length, followState.length)];
      }
      
      UILabel *followLabel = [[UILabel alloc] init];
      followLabel.frame = CGRectMake(headIM.right + 13 ,62, BCWidth - 114, 18.5);
      followLabel.font = [UIFont systemFontOfSize:MutilFont(13)];
      followLabel.attributedText = string;
      [bottomV addSubview:followLabel];
    }
    
    contentView.height = 50 + 100 * self.pointPlanArr.count;
    if (contentView.bottom  + 114 > self.pointPlanScrollView.height) {
      self.pointPlanScrollView.contentSize = CGSizeMake(0, contentView.bottom + 40);
    }
    
  }
}

#pragma mark 所有网络请求回来加载tab下的商圈动态页面
- (void)loadBuniessStateRecord {
  __weak typeof(self)weakSelf = self;
  UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0,12,BCWidth,114)];
  contentView.backgroundColor = [UIColor whiteColor];
  
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,100,50);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = @"商圈动态";
  [contentView addSubview:nameLabel];
  
  UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
  switchMap.frame = CGRectMake(BCWidth - 82 - 15, 0, 82, 50);
  //  [contentView addSubview:switchMap];
  
  UIImageView *leftIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 16, 18, 18)];
  leftIM.image = [UIImage imageNamed:@"point_add"];
  [switchMap addSubview:leftIM];
  
  UILabel *switchlabel = [[UILabel alloc] init];
  switchlabel.frame = CGRectMake(20,0,47,50);
  switchlabel.text = @"写跟进";
  switchlabel.textColor = COLOR(26, 106, 255);
  switchlabel.font = [UIFont systemFontOfSize:MutilFont(15)];
  [switchMap addSubview:switchlabel];
  [self.buniessStateScrollView addSubview:contentView];
  
  //    分割线
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,49, BCWidth - 16, 1)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [contentView addSubview:lineV];
  
  
  //    3个操作按钮
  NSArray *opeationArr = @[@"全部",@"跟进",@"变更",@"操作",@"转移"];
  UISegmentedControl *segment = [[UISegmentedControl alloc]initWithItems:opeationArr];
  segment.selectedSegmentIndex = 0;
  segment.frame = CGRectMake(16, lineV.bottom + 14, BCWidth - 32, 36);
  [segment addTarget:self action:@selector(changeSegment:) forControlEvents:UIControlEventValueChanged];
  [contentView addSubview:segment];
  [segment setTitleTextAttributes:@{NSFontAttributeName :[UIFont systemFontOfSize:MutilFont(14)],NSForegroundColorAttributeName:COLOR(29, 33, 41)}forState:UIControlStateNormal];
  [segment setTitleTextAttributes:@{NSFontAttributeName :[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium],NSForegroundColorAttributeName:COLOR(26, 106, 255)}forState:UIControlStateSelected];
  
  _selectBuniessStateV= [[UIView alloc] initWithFrame:CGRectMake(0, contentView.bottom, BCWidth, BCHeight - self.heightTop - 44 - 70 - 12 - 114)];
  [self.buniessStateScrollView addSubview:_selectBuniessStateV];
  [self reloadSelectSegment:0];
}
// 点击分段视图
- (void)changeSegment:(UISegmentedControl *)segment {
  NSInteger Index = segment.selectedSegmentIndex;
  self.buniessStateScrollView.contentSize = CGSizeMake(0, 0);
  [self reloadSelectSegment:Index];
}
- (void)reloadSelectSegment:(NSInteger)segeIndex{
  
  __weak typeof(self)weakSelf = self;
  NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
  NSArray *arr = @[];
  
  switch (segeIndex) {
    case 0:
      arr = _allBuniessArr;
      break;
    case 1:
      arr = _followBuniessArr;
      break;
    case 2:
      arr = _changeBuniessArr;
      break;
      
    case 3:
      arr = _operateBuniessArr;
      break;
      
    case 4:
      arr = _transferBuniessArr;
      break;
    default:
      break;
  }
  
  [self.selectBuniessStateV.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  
  if (![arr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(arr)) {//空视图
    
    UIImageView *emptyView = [[UIImageView alloc] initWithFrame:CGRectMake((BCWidth - 160)/2, 100, 160, 180)];
    emptyView.image = [UIImage imageNamed:@"tab_nodata"];
    [self.selectBuniessStateV addSubview:emptyView];
    
    UILabel *emptyL = [[UILabel alloc] initWithFrame:CGRectMake(0, emptyView.bottom - 40, BCWidth, 20)];
    emptyL.textColor = ACOLOR(30, 33, 38, 0.45);
    emptyL.font = [UIFont systemFontOfSize:MutilFont(14)];
    emptyL.text = @"暂无商圈动态";
    emptyL.textAlignment = NSTextAlignmentCenter;
    [self.selectBuniessStateV addSubview:emptyL];
    
    self.selectBuniessStateV.height =  BCHeight - self.heightTop - 44 - 70 - 12 - 114;
    self.buniessStateScrollView.contentSize = CGSizeMake(0, 0);
    
  } else {//有点位动态
    NSArray *colorArr = @[@[(__bridge id)COLOR(158, 171, 255).CGColor,(__bridge id)COLOR(45, 78, 238).CGColor],
                          @[(__bridge id)COLOR(27, 236, 212).CGColor,(__bridge id)COLOR(0, 196, 170).CGColor],
                          @[(__bridge id)COLOR(178, 132, 255).CGColor,(__bridge id)COLOR(127, 60, 237).CGColor],
                          @[(__bridge id)COLOR(158, 225, 255).CGColor,(__bridge id)COLOR(36, 78, 241).CGColor],
                          @[(__bridge id)COLOR(255, 174, 132).CGColor,(__bridge id)COLOR(237, 60, 60).CGColor],
                          @[(__bridge id)COLOR(255, 183, 100).CGColor,(__bridge id)COLOR(240, 130, 3).CGColor],
    ];
    
    CGFloat itemH = 0;
    CGFloat changeH = 0;
    CGFloat operationH = 0;
    
    for (int i = 0; i < arr.count; i ++) {
      
      if (segeIndex == 4) {//转移记录
        
        NSDictionary *itemDic = [arr objectAtIndexCheck:i];
        UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth, 126)];
        bottomV.backgroundColor = [UIColor whiteColor];
        [self.selectBuniessStateV addSubview:bottomV];
        
        NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
        [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
        [dateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
        NSString *creatTimeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_time"]];
        NSDate *creatDate = [dateFormatter dateFromString:creatTimeStr];
        
        NSDateFormatter *newdateFormatter = [[NSDateFormatter alloc] init];
        [newdateFormatter setDateFormat:@"yyyy-MM-dd HH:mm"];
        [newdateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
        
        NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
        NSString *followResult = [NSString stringWithFormat:@"%@ · 转移 · %@",nameStr,[newdateFormatter stringFromDate:creatDate]];
        NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
        [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
        
        NSInteger randomNum = arc4random_uniform(6);
        UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
        headImageV.layer.cornerRadius = 12;
        headImageV.clipsToBounds = YES;
        [bottomV addSubview:headImageV];
        
        CAGradientLayer *gl = [CAGradientLayer layer];
        gl.frame = headImageV.bounds;
        gl.startPoint = CGPointMake(0.0, 0);
        gl.endPoint = CGPointMake(1, 1);
        gl.colors = [colorArr objectAtIndexCheck:randomNum];
        gl.locations = @[@(0), @(1.0f)];
        [headImageV.layer addSublayer:gl];
        
        UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        headL.textColor = [UIColor whiteColor];
        headL.textAlignment = NSTextAlignmentCenter;
        headL.font = [UIFont systemFontOfSize:10];
        headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
        [headImageV addSubview:headL];
        
        UILabel *switchlabel = [[UILabel alloc] init];
        switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - headImageV.right - 22,20);
        switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
        switchlabel.font = [UIFont systemFontOfSize:13];
        [bottomV addSubview:switchlabel];
        switchlabel.attributedText = string;
        
        //        变更字段
        NSString *beforeStr = @"跟进人";
        NSString *beforeResult = [NSString stringWithFormat:@"变更字段：%@",beforeStr];
        NSMutableAttributedString *beforeString = [[NSMutableAttributedString alloc] initWithString:beforeResult];
        [beforeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, beforeStr.length)];
        
        UILabel *beforelabel = [[UILabel alloc] init];
        beforelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 6,BCWidth - 80,20);
        beforelabel.textColor = ACOLOR(30, 33, 38, 0.45);
        beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:beforelabel];
        beforelabel.attributedText = beforeString;
        
        //        原负责人
        NSString *beforePeopleStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"before_change"]];
        NSString *beforePeopleResult = [NSString stringWithFormat:@"原跟进人：%@",BCStringIsEmpty(beforePeopleStr) ? @"-" : beforePeopleStr];
        NSMutableAttributedString *resultString = [[NSMutableAttributedString alloc] initWithString:beforePeopleResult];
        [resultString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5,BCStringIsEmpty(beforePeopleStr) ? 1 : beforePeopleStr.length)];
        
        UILabel *resultlabel = [[UILabel alloc] init];
        resultlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 80,20);
        resultlabel.textColor = ACOLOR(30, 33, 38, 0.45);
        resultlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:resultlabel];
        resultlabel.attributedText = resultString;
        
//      新负责人
        NSString *timeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"after_change"]];
        NSString *timeResult = [NSString stringWithFormat:@"新跟进人：%@",timeStr];
        NSMutableAttributedString *timeString = [[NSMutableAttributedString alloc] initWithString:timeResult];
        [timeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, timeStr.length)];
        
        UILabel *timelabel = [[UILabel alloc] init];
        timelabel.frame = CGRectMake(headImageV.right + 6,resultlabel.bottom + 4,BCWidth - 80,20);
        timelabel.textColor = ACOLOR(30, 33, 38, 0.45);
        timelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:timelabel];
        timelabel.attributedText = timeString;
        
//        转交原因
        NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"change_reason"]];
        NSString *memoResult = [NSString stringWithFormat:@"转交原因：%@",!BCStringIsEmpty(memoStr) ? memoStr : @"-"];
        NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:memoResult];
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        [paragraphStyle setLineSpacing:4];//调整行间距
        paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
        [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, memoResult.length)];
        [opereateResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 5)];
        
        NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:paragraphStyle};
        CGFloat height = [memoResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        height = ceil(height);
        height = height < 20 ? 20 : height;
        
        UILabel *memolabel = [[UILabel alloc] init];
        memolabel.frame = CGRectMake(headImageV.right + 6 , timelabel.bottom + 4,BCWidth - 62,height);
        memolabel.textColor = ACOLOR(31, 33, 38, 1);
        memolabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        memolabel.numberOfLines = 0;
        [bottomV addSubview:memolabel];
        memolabel.attributedText = opereateResultStr;
        
        //      分割线
        if (i < arr.count - 1) {
          UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, memolabel.bottom + 14, BCWidth - 16, 0.5)];
          lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
          [bottomV addSubview:lineV];
        }
        
        bottomV.height = memolabel.bottom + 14 + 14;
        itemH += memolabel.bottom + 14 + 14;
        if (i == arr.count - 1) {
          bottomV.height = memolabel.bottom + 14 ;
          self.selectBuniessStateV.height = itemH - 14;
          if (self.selectBuniessStateV.bottom  + 114 > self.buniessStateScrollView.height) {
            self.buniessStateScrollView.contentSize = CGSizeMake(0, self.selectBuniessStateV.bottom + 40);
          }
        }
        
        
      } else if (segeIndex == 3) {//操作记录
        NSDictionary *itemDic = [arr objectAtIndexCheck:i];
        UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, operationH, BCWidth, 102)];
        bottomV.backgroundColor = [UIColor whiteColor];
        [self.selectBuniessStateV addSubview:bottomV];
        
        NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
        NSString *followResult = [NSString stringWithFormat:@"%@ · 操作 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
        NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
        [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
        
        NSInteger randomNum = arc4random_uniform(6);
        UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
        headImageV.layer.cornerRadius = 12;
        headImageV.clipsToBounds = YES;
        [bottomV addSubview:headImageV];
        
        CAGradientLayer *gl = [CAGradientLayer layer];
        gl.frame = headImageV.bounds;
        gl.startPoint = CGPointMake(0.0, 0);
        gl.endPoint = CGPointMake(1, 1);
        gl.colors = [colorArr objectAtIndexCheck:randomNum];
        gl.locations = @[@(0), @(1.0f)];
        [headImageV.layer addSublayer:gl];
        
        UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        headL.textColor = [UIColor whiteColor];
        headL.textAlignment = NSTextAlignmentCenter;
        headL.font = [UIFont systemFontOfSize:10];
        headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
        [headImageV addSubview:headL];
        
        UILabel *switchlabel = [[UILabel alloc] init];
        switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
        switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
        switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
        [bottomV addSubview:switchlabel];
        switchlabel.attributedText = string;
        
        NSString *operateStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"model_state"]];
        NSString *operateResult = [NSString stringWithFormat:@"操作：%@",operateStr];
        NSMutableAttributedString *operateString = [[NSMutableAttributedString alloc] initWithString:operateResult];
        [operateString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(3, operateStr.length)];
        
        UILabel *operatelabel = [[UILabel alloc] init];
        operatelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 8,BCWidth - 80,20);
        operatelabel.textColor = ACOLOR(30, 33, 38, 0.45);
        operatelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:operatelabel];
        operatelabel.attributedText = operateString;
        
        
        NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
        NSString *memoResult = [NSString stringWithFormat:@"审批意见：%@",!BCStringIsEmpty(memoStr) ? memoStr : @"-"];
        NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:memoResult];
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        [paragraphStyle setLineSpacing:4];//调整行间距
        paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
        [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, memoResult.length)];
        [opereateResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 5)];
        
        NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:paragraphStyle};
        CGFloat height = [memoResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        height = ceil(height);
        height = height < 20 ? 20 : height;
        
        UILabel *memolabel = [[UILabel alloc] init];
        memolabel.frame = CGRectMake(headImageV.right + 6 , operatelabel.bottom + 4,BCWidth - 62,height);
        memolabel.textColor = ACOLOR(31, 33, 38, 1);
        memolabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        memolabel.numberOfLines = 0;
        [bottomV addSubview:memolabel];
        memolabel.attributedText = opereateResultStr;
        
        //      分割线
        if (i < arr.count - 1) {
          UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, memolabel.bottom + 13, BCWidth - 16, 1)];
          lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
          [bottomV addSubview:lineV];
        }
        
        bottomV.height = memolabel.bottom + 14 + 14;
        operationH += memolabel.bottom + 14 + 14;
        if (i == arr.count - 1) {
          bottomV.height = memolabel.bottom + 14 ;
          self.selectBuniessStateV.height = operationH - 14;
          if ( self.selectBuniessStateV.bottom  + 114 > self.buniessStateScrollView.height) {
            self.buniessStateScrollView.contentSize = CGSizeMake(0, self.selectBuniessStateV.bottom + 40);
          }
        }
        
      } else if (segeIndex == 2){//变更
        NSDictionary *itemDic = [arr objectAtIndexCheck:i];
        
        UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, changeH, BCWidth, 126)];
        bottomV.backgroundColor = [UIColor whiteColor];
        [self.selectBuniessStateV addSubview:bottomV];
        
        
        NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
        NSString *followResult = [NSString stringWithFormat:@"%@ · 变更 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
        NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
        [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
        
        NSInteger randomNum = arc4random_uniform(6);
        UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
        headImageV.layer.cornerRadius = 12;
        headImageV.clipsToBounds = YES;
        [bottomV addSubview:headImageV];
        
        CAGradientLayer *gl = [CAGradientLayer layer];
        gl.frame = headImageV.bounds;
        gl.startPoint = CGPointMake(0.0, 0);
        gl.endPoint = CGPointMake(1, 1);
        gl.colors = [colorArr objectAtIndexCheck:randomNum];
        gl.locations = @[@(0), @(1.0f)];
        [headImageV.layer addSublayer:gl];
        
        UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        headL.textColor = [UIColor whiteColor];
        headL.textAlignment = NSTextAlignmentCenter;
        headL.font = [UIFont systemFontOfSize:10];
        headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
        [headImageV addSubview:headL];
        
        UILabel *switchlabel = [[UILabel alloc] init];
        switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
        switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
        switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
        [bottomV addSubview:switchlabel];
        switchlabel.attributedText = string;
        
        
        NSString *operateStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"field_name"]];
        NSString *operateResult = [NSString stringWithFormat:@"变更字段：%@",operateStr];
        NSMutableAttributedString *operateString = [[NSMutableAttributedString alloc] initWithString:operateResult];
        [operateString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, operateStr.length)];
        
        UILabel *operatelabel = [[UILabel alloc] init];
        operatelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 8,BCWidth - 80,20);
        operatelabel.textColor = ACOLOR(30, 33, 38, 0.45);
        operatelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        [bottomV addSubview:operatelabel];
        operatelabel.attributedText = operateString;
        
        //       原值
        NSString *beforeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"before_change"]];
        if ([beforeStr containsString:@"https"] || [beforeStr containsString:@"http"]  || BCStringIsEmpty(beforeStr)) {
          beforeStr = @"-";
        }
        
        NSString *beforeResult = [NSString stringWithFormat:@"原值：%@", beforeStr];
        NSMutableAttributedString *beforeResultStr = [[NSMutableAttributedString alloc] initWithString:beforeResult];
        NSMutableParagraphStyle *beforeParagraphStyle = [[NSMutableParagraphStyle alloc] init];
        [beforeParagraphStyle setLineSpacing:4];//调整行间距
        beforeParagraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
        [beforeResultStr addAttribute:NSParagraphStyleAttributeName value:beforeParagraphStyle range:NSMakeRange(0, beforeResult.length)];
        [beforeResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 3)];
        
        NSDictionary *beforeAttributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:beforeParagraphStyle};
        CGFloat beforeHeight = [beforeResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:beforeAttributes context:nil].size.height;
        beforeHeight= ceil(beforeHeight);
        beforeHeight = beforeHeight < 20 ? 20 : beforeHeight;
        
        UILabel *beforelabel = [[UILabel alloc] init];
        beforelabel.frame = CGRectMake(headImageV.right + 6 , operatelabel.bottom + 4,BCWidth - 62,beforeHeight);
        beforelabel.textColor = COLOR(31, 33, 38);
        beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        beforelabel.numberOfLines = 0;
        [bottomV addSubview:beforelabel];
        beforelabel.attributedText = beforeResultStr;
        
        //        新值
        NSString *afterStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"after_change"]];
        if ([afterStr containsString:@"https"] || [afterStr containsString:@"http"] || BCStringIsEmpty(beforeStr)) {
          afterStr = @"-";
        }
        
        NSString *afterResult = [NSString stringWithFormat:@"新值：%@", afterStr];
        NSMutableAttributedString *afterResultStr = [[NSMutableAttributedString alloc] initWithString:afterResult];
        NSMutableParagraphStyle *afterParagraphStyle = [[NSMutableParagraphStyle alloc] init];
        [afterParagraphStyle setLineSpacing:4];//调整行间距
        afterParagraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
        [afterResultStr addAttribute:NSParagraphStyleAttributeName value:afterParagraphStyle range:NSMakeRange(0, afterResult.length)];
        [afterResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 3)];
        
        NSDictionary *afterAttributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:afterParagraphStyle};
        CGFloat afterHeight = [afterResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:afterAttributes context:nil].size.height;
        afterHeight = ceil(afterHeight);
        afterHeight = afterHeight < 20 ? 20 : afterHeight;
        
        UILabel *afterlabel = [[UILabel alloc] init];
        afterlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 62,afterHeight);
        afterlabel.textColor = COLOR(31, 33, 38);
        afterlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
        afterlabel.numberOfLines = 0;
        [bottomV addSubview:afterlabel];
        afterlabel.attributedText = afterResultStr;
        
        //      分割线
        if (i < arr.count - 1 ) {
          UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, afterlabel.bottom + 13, BCWidth - 16, 1)];
          lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
          [bottomV addSubview:lineV];
        }
        bottomV.height = afterlabel.bottom + 14 + 14;
        changeH += afterlabel.bottom + 14 + 14;
        if (i == arr.count - 1) {
          bottomV.height = afterlabel.bottom + 14 ;
          self.selectBuniessStateV.height = changeH - 14;
          if (self.selectBuniessStateV.bottom  + 114 > self.buniessStateScrollView.height) {
            self.buniessStateScrollView.contentSize = CGSizeMake(0, self.selectBuniessStateV.bottom + 40);
          }
        }
        
      } else if (segeIndex == 1){//跟进
        
        NSDictionary *itemDic = [arr objectAtIndexCheck:i];
        UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth, 126)];
        bottomV.backgroundColor = [UIColor whiteColor];
        [self.selectBuniessStateV addSubview:bottomV];
        
        NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
        NSString *followResult = [NSString stringWithFormat:@"%@ · 跟进 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
        NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
        [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
        
        NSInteger randomNum = arc4random_uniform(6);
        UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
        headImageV.layer.cornerRadius = 12;
        headImageV.clipsToBounds = YES;
        [bottomV addSubview:headImageV];
        
        CAGradientLayer *gl = [CAGradientLayer layer];
        gl.frame = headImageV.bounds;
        gl.startPoint = CGPointMake(0.0, 0);
        gl.endPoint = CGPointMake(1, 1);
        gl.colors = [colorArr objectAtIndexCheck:randomNum];
        gl.locations = @[@(0), @(1.0f)];
        [headImageV.layer addSublayer:gl];
        
        UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        headL.textColor = [UIColor whiteColor];
        headL.textAlignment = NSTextAlignmentCenter;
        headL.font = [UIFont systemFontOfSize:10];
        headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
        [headImageV addSubview:headL];
        
        UILabel *switchlabel = [[UILabel alloc] init];
        switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 116,20);
        switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
        switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
        [bottomV addSubview:switchlabel];
        switchlabel.attributedText = string;
        
//        评论
        UIButton *commentBtn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - 60, switchlabel.top, 60, 20)];
        [bottomV addSubview:commentBtn];
        [commentBtn addtargetBlock:^(UIButton *button) {
          
          MAWriteReplyView *replyVV = [[MAWriteReplyView alloc] initWithName:@"评论" andPla:[NSString stringWithFormat:@"回复%@：",nameStr]];
          [replyVV showModal];
          replyVV.okBlock = ^(NSString *dataString) {
            [weakSelf replyString:[itemDic objectNilForKey:@"id"] andMemo:dataString andIndex:1];
          };
        }];
        
        UIImageView *commentIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 2, 16, 16)];
        commentIM.image = [UIImage imageNamed:@"detail_comment"];
        [commentBtn addSubview:commentIM];
        
        UILabel *commentL = [[UILabel alloc] initWithFrame:CGRectMake(commentIM.right + 4,0,30,20)];
        commentL.text = @"评论";
        commentL.font = [UIFont systemFontOfSize:MutilFont(12)];
        commentL.textColor = COLOR(134, 144, 156);
        [commentBtn addSubview:commentL];
        
        
        //        备注
        NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
        NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
        CGFloat height = [memoStr boundingRectWithSize:CGSizeMake(BCWidth - 92, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        height = ceil(height);
        UILabel *memoLabel= [[UILabel alloc] initWithFrame:CGRectMake(headImageV.right + 6, headImageV.bottom + 6, BCWidth - 92,BCStringIsEmpty(memoStr) ? 0 : height)];
        memoLabel.textColor = ACOLOR(29, 33, 41, 1);
        memoLabel.font = [UIFont systemFontOfSize:MutilFont(15)];
        memoLabel.text = memoStr;
        memoLabel.numberOfLines = 0;
        [bottomV addSubview:memoLabel];
        
        //        附件信息
        NSArray *fileArr = [itemDic objectForKeyNil:@"files"];
        if ([fileArr isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(fileArr)) {
          
          
          for (int j = 0; j <fileArr.count; j ++) {
            
            NSDictionary *deedDic = [fileArr objectAtIndexCheck:j];
            UIButton *leaseImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(headImageV.right + 6, memoLabel.bottom + 8 + (44 + 10) * j, BCWidth - 62, 44)];
            leaseImageBtn.layer.cornerRadius = 4;
            leaseImageBtn.clipsToBounds = YES;
            leaseImageBtn.backgroundColor = COLOR(244, 245, 247);
            [bottomV addSubview:leaseImageBtn];
            
            UIImageView *leaseIM = [[UIImageView alloc] initWithFrame:CGRectMake(16, 10 ,24, 24)];
            [leaseImageBtn addSubview:leaseIM];
            
            UILabel *leaseL = [[UILabel alloc] initWithFrame:CGRectMake(leaseIM.right + 10,0, leaseImageBtn.width - leaseIM.right - 26, 44)];
            leaseL.text = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"name"]];
            leaseL.font = [UIFont systemFontOfSize:MutilFont(14)];
            leaseL.textColor = ACOLOR(31, 33, 38,1);
            leaseL.lineBreakMode = NSLineBreakByTruncatingMiddle;
            [leaseImageBtn addSubview:leaseL];
            
            if ([temArr containsObject:[deedDic objectNilForKey:@"suffix_type"]]) {//如果是图片
              
              leaseIM.image = [UIImage imageNamed:@"file_img"];
              [leaseImageBtn addtargetBlock:^(UIButton *button) {
                [ZKPhotoBrowser showWithImageUrls:@[[[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] currentPhotoIndex:0 sourceSuperView:button];
              }];
            } else {//是其他文本 pdf  word
              
              NSString *fileStr = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"suffix_type"] ];
              if ([fileStr containsString:@"pdf"]) {
                leaseIM.image = [UIImage imageNamed:@"file_pdf"];
              } else  if ([fileStr containsString:@"doc"] || [fileStr containsString:@"docx"]) {
                leaseIM.image = [UIImage imageNamed:@"file_word"];
              } else  if ([fileStr containsString:@"xls"] || [fileStr containsString:@"xls"]) {
                leaseIM.image = [UIImage imageNamed:@"file_excel"];
              }else  if ([fileStr containsString:@"ppt"] || [fileStr containsString:@"pptx"]) {
                leaseIM.image = [UIImage imageNamed:@"file_ppt"];
              }else {
                leaseIM.image = [UIImage imageNamed:@"file_no"];
              }
              
              [leaseImageBtn addtargetBlock:^(UIButton *button) {
               
                NSString *url = [[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
                [weakSelf openSFDoc:url];
                
              }];
            }
            
          }
        }
        
//        评论明细
        CGFloat commentTop = 0;
        if (![fileArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(fileArr)) {//没有附件
          commentTop  = memoLabel.bottom  + 14;
        } else {
          commentTop = memoLabel.bottom + 8 + (44 + 10) * fileArr.count - 8 + 14;
        }
        
        CGFloat commentHeight = 0;
        NSArray *commentArr = [itemDic objectForKeyNil:@"details"];
        if (!BCArrayIsEmpty(commentArr)) {
         
          for (int k = 0; k <commentArr.count; k ++) {
            NSDictionary *replyDic = [commentArr objectAtIndexCheck:k];
            
            NSString *replyNameStr = [NSString stringWithFormat:@"%@",[replyDic objectNilForKey:@"create_by"]];
            NSString *replyResult = [NSString stringWithFormat:@"%@ · %@",replyNameStr,[replyDic objectNilForKey:@"create_time"]];
            NSMutableAttributedString *replystring = [[NSMutableAttributedString alloc] initWithString:replyResult];
            [replystring addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, replyNameStr.length)];
            
            //  回复人
            NSInteger randomNum = arc4random_uniform(6);
            UIView *replyheadV = [[UIView alloc] initWithFrame:CGRectMake(headImageV.right + 6, commentTop + commentHeight, 24, 24)];
            replyheadV.layer.cornerRadius = 12;
            replyheadV.clipsToBounds = YES;
            [bottomV addSubview:replyheadV];
            
            CAGradientLayer *gl = [CAGradientLayer layer];
            gl.frame = replyheadV.bounds;
            gl.startPoint = CGPointMake(0.0, 0);
            gl.endPoint = CGPointMake(1, 1);
            gl.colors = [colorArr objectAtIndexCheck:randomNum];
            gl.locations = @[@(0), @(1.0f)];
            [replyheadV.layer addSublayer:gl];
            
            UILabel *replyL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
            replyL.textColor = [UIColor whiteColor];
            replyL.textAlignment = NSTextAlignmentCenter;
            replyL.font = [UIFont systemFontOfSize:10];
            replyL.text = replyNameStr.length > 2 ? [replyNameStr substringWithRange:NSMakeRange(replyNameStr.length - 2, 2)] : replyNameStr;
            [replyheadV addSubview:replyL];
            
            UILabel *replyTitleL = [[UILabel alloc] init];
            replyTitleL.frame = CGRectMake(replyheadV.right + 6,replyheadV.top + 1.5,BCWidth - replyheadV.right - 76,20);
            replyTitleL.textColor = ACOLOR(30, 33, 38, 0.45);
            replyTitleL.font = [UIFont systemFontOfSize:MutilFont(13)];
            [bottomV addSubview:replyTitleL];
            replyTitleL.attributedText = replystring;
            
//            评论按钮
            UIButton *replyBtn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - 60, replyTitleL.top, 60, 20)];
            [bottomV addSubview:replyBtn];
            
            UIImageView *replyIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 2, 16, 16)];
            replyIM.image = [UIImage imageNamed:@"detail_comment"];
            [replyBtn addSubview:replyIM];
            
            UILabel *replyRL = [[UILabel alloc] initWithFrame:CGRectMake(replyIM.right + 4,0,30,20)];
            replyRL.text = @"评论";
            replyRL.font = [UIFont systemFontOfSize:MutilFont(12)];
            replyRL.textColor = COLOR(134, 144, 156);
            [replyBtn addSubview:replyRL];
            [replyBtn addtargetBlock:^(UIButton *button) {
              
              MAWriteReplyView *replyVV = [[MAWriteReplyView alloc] initWithName:@"评论" andPla:[NSString stringWithFormat:@"回复%@：",replyNameStr]];
              [replyVV showModal];
              replyVV.okBlock = ^(NSString *dataString) {
                [weakSelf replyString:[replyDic objectNilForKey:@"id"] andMemo:dataString andIndex:1];
              };
            }];
            
//            回复内容
            NSString *anwserStr = [NSString stringWithFormat:@"%@",[replyDic objectNilForKey:@"memo"]];
            NSString *anwserNameStr = [NSString stringWithFormat:@"%@",[replyDic objectNilForKey:@"reply_by"]];
            NSString *anwserResult = [NSString stringWithFormat:@"回复 %@：%@",anwserNameStr,!BCStringIsEmpty(anwserStr) ? anwserStr: @"-"];
            
            NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:anwserResult];
            NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
            [paragraphStyle setLineSpacing:4];//调整行间距
            paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
            [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, anwserResult.length)];
            [opereateResultStr addAttribute:NSForegroundColorAttributeName value:COLOR(26, 106, 255) range:NSMakeRange(2, anwserNameStr.length + 1)];
            
            NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)],NSParagraphStyleAttributeName:paragraphStyle};
            CGFloat height = [anwserResult boundingRectWithSize:CGSizeMake(BCWidth - replyheadV.right - 22, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
            height = ceil(height);
            height = height < 20 ? 20 : height;
            
            UILabel *anwserL = [[UILabel alloc] init];
            anwserL.frame = CGRectMake(replyheadV.right + 6 , replyheadV.bottom + 6,BCWidth - replyheadV.right - 22,height);
            anwserL.textColor = ACOLOR(31, 33, 38, 1);
            anwserL.font = [UIFont systemFontOfSize:MutilFont(15)];
            anwserL.numberOfLines = 0;
            [bottomV addSubview:anwserL];
            anwserL.attributedText = opereateResultStr;
            
            
            commentHeight += 30 + 14 + height;
          
          }
          
        }
        
        //        计算高度
        CGFloat bottomH = commentTop + commentHeight;
        bottomV.height = bottomH + 14;
        itemH += bottomV.height ;
        //      分割线
        if (i < arr.count - 1 ) {
          UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, bottomH - 1, BCWidth - 16, 1)];
          lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
          [bottomV addSubview:lineV];
        }
        
        if (i == arr.count - 1) {
          
          bottomV.height = bottomH;
          self.selectBuniessStateV.height = itemH - 14;
          if ( self.selectBuniessStateV.bottom  + 114 > self.buniessStateScrollView.height) {
            self.buniessStateScrollView.contentSize = CGSizeMake(0, self.selectBuniessStateV.bottom + 40);
          }
          
         
        }
        
      } else {//全部
        
        NSDictionary *pointDic = [arr objectAtIndexCheck:i];
        UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, itemH, BCWidth, 102)];
        bottomV.backgroundColor = [UIColor whiteColor];
        [self.selectBuniessStateV addSubview:bottomV];
        
        NSString *typeStr = [pointDic objectNilForKey:@"type"];
        NSDictionary *itemDic = [pointDic objectForKeyNil:@"data"];
        
        if ([typeStr isEqualToString:@"TRANSFER_RECORD"]) {
  
          NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
          [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
          [dateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
          NSString *creatTimeStr = [NSString stringWithFormat:@"%@",[pointDic objectNilForKey:@"create_time"]];
          NSDate *creatDate = [dateFormatter dateFromString:creatTimeStr];
          
          NSDateFormatter *newdateFormatter = [[NSDateFormatter alloc] init];
          [newdateFormatter setDateFormat:@"yyyy-MM-dd HH:mm"];
          [newdateFormatter setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"zh_CN"]];
          NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
          NSString *followResult = [NSString stringWithFormat:@"%@ · 转移 · %@",nameStr,[newdateFormatter stringFromDate:creatDate]];
          NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
          [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
          
          NSInteger randomNum = arc4random_uniform(6);
          UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
          headImageV.layer.cornerRadius = 12;
          headImageV.clipsToBounds = YES;
          [bottomV addSubview:headImageV];
          
          CAGradientLayer *gl = [CAGradientLayer layer];
          gl.frame = headImageV.bounds;
          gl.startPoint = CGPointMake(0.0, 0);
          gl.endPoint = CGPointMake(1, 1);
          gl.colors = [colorArr objectAtIndexCheck:randomNum];
          gl.locations = @[@(0), @(1.0f)];
          [headImageV.layer addSublayer:gl];
          
          UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
          headL.textColor = [UIColor whiteColor];
          headL.textAlignment = NSTextAlignmentCenter;
          headL.font = [UIFont systemFontOfSize:10];
          headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
          [headImageV addSubview:headL];
          
          UILabel *switchlabel = [[UILabel alloc] init];
          switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - headImageV.right - 22,20);
          switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
          switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
          [bottomV addSubview:switchlabel];
          switchlabel.attributedText = string;
          
          //        变更字段
          NSString *beforeStr = @"跟进人";
          NSString *beforeResult = [NSString stringWithFormat:@"变更字段：%@",beforeStr];
          NSMutableAttributedString *beforeString = [[NSMutableAttributedString alloc] initWithString:beforeResult];
          [beforeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, beforeStr.length)];
          
          UILabel *beforelabel = [[UILabel alloc] init];
          beforelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 6,BCWidth - 80,20);
          beforelabel.textColor = ACOLOR(30, 33, 38, 0.45);
          beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:beforelabel];
          beforelabel.attributedText = beforeString;
          
          //        原负责人
          NSString *beforePeopleStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"before_change"]];
          NSString *beforePeopleResult = [NSString stringWithFormat:@"原跟进人：%@",BCStringIsEmpty(beforePeopleStr) ? @"-" : beforePeopleStr];
          NSMutableAttributedString *resultString = [[NSMutableAttributedString alloc] initWithString:beforePeopleResult];
          [resultString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5,BCStringIsEmpty(beforePeopleStr) ? 1 : beforePeopleStr.length)];
          
          UILabel *resultlabel = [[UILabel alloc] init];
          resultlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 80,20);
          resultlabel.textColor = ACOLOR(30, 33, 38, 0.45);
          resultlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:resultlabel];
          resultlabel.attributedText = resultString;
          
  //      新负责人
          NSString *timeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"after_change"]];
          NSString *timeResult = [NSString stringWithFormat:@"新跟进人：%@",timeStr];
          NSMutableAttributedString *timeString = [[NSMutableAttributedString alloc] initWithString:timeResult];
          [timeString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, timeStr.length)];
          
          UILabel *timelabel = [[UILabel alloc] init];
          timelabel.frame = CGRectMake(headImageV.right + 6,resultlabel.bottom + 4,BCWidth - 80,20);
          timelabel.textColor = ACOLOR(30, 33, 38, 0.45);
          timelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:timelabel];
          timelabel.attributedText = timeString;
          
  //        转交原因
          NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"change_reason"]];
          NSString *memoResult = [NSString stringWithFormat:@"转交原因：%@",!BCStringIsEmpty(memoStr) ? memoStr : @"-"];
          NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:memoResult];
          NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
          [paragraphStyle setLineSpacing:4];//调整行间距
          paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
          [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, memoResult.length)];
          [opereateResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 5)];
          
          NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:paragraphStyle};
          CGFloat height = [memoResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
          height = ceil(height);
          height = height < 20 ? 20 : height;
          
          UILabel *memolabel = [[UILabel alloc] init];
          memolabel.frame = CGRectMake(headImageV.right + 6 , timelabel.bottom + 4,BCWidth - 62,height);
          memolabel.textColor = ACOLOR(31, 33, 38, 1);
          memolabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          memolabel.numberOfLines = 0;
          [bottomV addSubview:memolabel];
          memolabel.attributedText = opereateResultStr;
          
          //      分割线
          if (i < arr.count - 1 ) {
            UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, memolabel.bottom + 14, BCWidth - 16, 0.5)];
            lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
            [bottomV addSubview:lineV];
          }
          bottomV.height = memolabel.bottom + 14 + 14;
          
          if (i == arr.count - 1) {
            bottomV.height = memolabel.bottom + 14 ;
          }
          
          itemH += bottomV.height ;
          
        } else if ([typeStr isEqualToString:@"AUDIT_RECORD"]) {//操作
          
          NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
          NSString *followResult = [NSString stringWithFormat:@"%@ · 操作 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
          NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
          [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
          
          NSInteger randomNum = arc4random_uniform(6);
          UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
          headImageV.layer.cornerRadius = 12;
          headImageV.clipsToBounds = YES;
          [bottomV addSubview:headImageV];
          
          CAGradientLayer *gl = [CAGradientLayer layer];
          gl.frame = headImageV.bounds;
          gl.startPoint = CGPointMake(0.0, 0);
          gl.endPoint = CGPointMake(1, 1);
          gl.colors = [colorArr objectAtIndexCheck:randomNum];
          gl.locations = @[@(0), @(1.0f)];
          [headImageV.layer addSublayer:gl];
          
          UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
          headL.textColor = [UIColor whiteColor];
          headL.textAlignment = NSTextAlignmentCenter;
          headL.font = [UIFont systemFontOfSize:10];
          headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
          [headImageV addSubview:headL];
          
          UILabel *switchlabel = [[UILabel alloc] init];
          switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
          switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
          switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
          [bottomV addSubview:switchlabel];
          switchlabel.attributedText = string;
          
          NSString *operateStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"model_state"]];
          NSString *operateResult = [NSString stringWithFormat:@"操作：%@",operateStr];
          NSMutableAttributedString *operateString = [[NSMutableAttributedString alloc] initWithString:operateResult];
          [operateString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(3, operateStr.length)];
          
          UILabel *operatelabel = [[UILabel alloc] init];
          operatelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 8,BCWidth - 80,20);
          operatelabel.textColor = ACOLOR(30, 33, 38, 0.45);
          operatelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:operatelabel];
          operatelabel.attributedText = operateString;
          
          
          NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
          NSString *memoResult = [NSString stringWithFormat:@"审批意见：%@",!BCStringIsEmpty(memoStr) ? memoStr : @"-"];
          NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:memoResult];
          NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
          [paragraphStyle setLineSpacing:4];//调整行间距
          paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
          [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, memoResult.length)];
          [opereateResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 5)];
          
          NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:paragraphStyle};
          CGFloat height = [memoResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
          height = ceil(height);
          height = height < 20 ? 20 : height;
          
          UILabel *memolabel = [[UILabel alloc] init];
          memolabel.frame = CGRectMake(headImageV.right + 6 , operatelabel.bottom + 4,BCWidth - 62,height);
          memolabel.textColor = ACOLOR(31, 33, 38, 1);
          memolabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          memolabel.numberOfLines = 0;
          [bottomV addSubview:memolabel];
          memolabel.attributedText = opereateResultStr;
        
          
          //      分割线
          if (i < arr.count - 1 ) {
            UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, memolabel.bottom + 13, BCWidth - 16, 1)];
            lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
            [bottomV addSubview:lineV];
          }
          
          bottomV.height = memolabel.bottom + 14 + 14;
          
          if (i == arr.count - 1) {
            bottomV.height = memolabel.bottom + 14 ;
          }
          itemH += bottomV.height ;
        } else if ([typeStr isEqualToString:@"CHANGE_RECORD"]) {//变更
          
          NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
          NSString *followResult = [NSString stringWithFormat:@"%@ · 变更 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
          NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
          [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
          
          NSInteger randomNum = arc4random_uniform(6);
          UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
          headImageV.layer.cornerRadius = 12;
          headImageV.clipsToBounds = YES;
          [bottomV addSubview:headImageV];
          
          CAGradientLayer *gl = [CAGradientLayer layer];
          gl.frame = headImageV.bounds;
          gl.startPoint = CGPointMake(0.0, 0);
          gl.endPoint = CGPointMake(1, 1);
          gl.colors = [colorArr objectAtIndexCheck:randomNum];
          gl.locations = @[@(0), @(1.0f)];
          [headImageV.layer addSublayer:gl];
          
          UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
          headL.textColor = [UIColor whiteColor];
          headL.textAlignment = NSTextAlignmentCenter;
          headL.font = [UIFont systemFontOfSize:10];
          headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
          [headImageV addSubview:headL];
          
          UILabel *switchlabel = [[UILabel alloc] init];
          switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 32,20);
          switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
          switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
          [bottomV addSubview:switchlabel];
          switchlabel.attributedText = string;
          
          
          NSString *operateStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"field_name"]];
          NSString *operateResult = [NSString stringWithFormat:@"变更字段：%@",operateStr];
          NSMutableAttributedString *operateString = [[NSMutableAttributedString alloc] initWithString:operateResult];
          [operateString addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38)} range:NSMakeRange(5, operateStr.length)];
          
          UILabel *operatelabel = [[UILabel alloc] init];
          operatelabel.frame = CGRectMake(headImageV.right + 6,switchlabel.bottom + 8,BCWidth - 80,20);
          operatelabel.textColor = ACOLOR(30, 33, 38, 0.45);
          operatelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          [bottomV addSubview:operatelabel];
          operatelabel.attributedText = operateString;
          
          //       原值
          NSString *beforeStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"before_change"]];
          if ([beforeStr containsString:@"https"] || [beforeStr containsString:@"http"]  || BCStringIsEmpty(beforeStr)) {
            beforeStr = @"-";
          }
          
          NSString *beforeResult = [NSString stringWithFormat:@"原值：%@", beforeStr];
          NSMutableAttributedString *beforeResultStr = [[NSMutableAttributedString alloc] initWithString:beforeResult];
          NSMutableParagraphStyle *beforeParagraphStyle = [[NSMutableParagraphStyle alloc] init];
          [beforeParagraphStyle setLineSpacing:4];//调整行间距
          beforeParagraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
          [beforeResultStr addAttribute:NSParagraphStyleAttributeName value:beforeParagraphStyle range:NSMakeRange(0, beforeResult.length)];
          [beforeResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 3)];
          
          NSDictionary *beforeAttributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:beforeParagraphStyle};
          CGFloat beforeHeight = [beforeResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:beforeAttributes context:nil].size.height;
          beforeHeight= ceil(beforeHeight);
          beforeHeight = beforeHeight < 20 ? 20 : beforeHeight;
          
          UILabel *beforelabel = [[UILabel alloc] init];
          beforelabel.frame = CGRectMake(headImageV.right + 6 , operatelabel.bottom + 4,BCWidth - 62,beforeHeight);
          beforelabel.textColor = COLOR(31, 33, 38);
          beforelabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          beforelabel.numberOfLines = 0;
          [bottomV addSubview:beforelabel];
          beforelabel.attributedText = beforeResultStr;
          
          //        新值
          NSString *afterStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"after_change"]];
          if ([afterStr containsString:@"https"] || [afterStr containsString:@"http"] || BCStringIsEmpty(beforeStr)) {
            afterStr = @"-";
          }
          
          NSString *afterResult = [NSString stringWithFormat:@"新值：%@", afterStr];
          NSMutableAttributedString *afterResultStr = [[NSMutableAttributedString alloc] initWithString:afterResult];
          NSMutableParagraphStyle *afterParagraphStyle = [[NSMutableParagraphStyle alloc] init];
          [afterParagraphStyle setLineSpacing:4];//调整行间距
          afterParagraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
          [afterResultStr addAttribute:NSParagraphStyleAttributeName value:afterParagraphStyle range:NSMakeRange(0, afterResult.length)];
          [afterResultStr addAttribute:NSForegroundColorAttributeName value:ACOLOR(30, 33, 38, 0.45) range:NSMakeRange(0, 3)];
          
          NSDictionary *afterAttributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14)],NSParagraphStyleAttributeName:afterParagraphStyle};
          CGFloat afterHeight = [afterResult boundingRectWithSize:CGSizeMake(BCWidth - 62, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:afterAttributes context:nil].size.height;
          afterHeight = ceil(afterHeight);
          afterHeight = afterHeight < 20 ? 20 : afterHeight;
          
          UILabel *afterlabel = [[UILabel alloc] init];
          afterlabel.frame = CGRectMake(headImageV.right + 6,beforelabel.bottom + 4,BCWidth - 62,afterHeight);
          afterlabel.textColor = COLOR(31, 33, 38);
          afterlabel.font = [UIFont systemFontOfSize:MutilFont(14)];
          afterlabel.numberOfLines = 0;
          [bottomV addSubview:afterlabel];
          afterlabel.attributedText = afterResultStr;
          
          //      分割线
          if (i < arr.count - 1 ) {
            UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,  afterlabel.bottom + 13, BCWidth - 16, 1)];
            lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
            [bottomV addSubview:lineV];
          }
          bottomV.height = afterlabel.bottom + 14 + 14;
          
          if (i == arr.count - 1) {
            bottomV.height = afterlabel.bottom + 14 ;
          }
          
          itemH += bottomV.height ;
          
        } else {//跟进
          
          NSDictionary *itemDic = [pointDic objectForKeyNil:@"data"];
          NSString *nameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"create_by"]];
          NSString *followResult = [NSString stringWithFormat:@"%@ · 跟进 · %@",nameStr,[itemDic objectNilForKey:@"create_time"]];
          NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
          [string addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, nameStr.length)];
          
          NSInteger randomNum = arc4random_uniform(6);
          UIView *headImageV = [[UIView alloc] initWithFrame:CGRectMake(16, 0, 24, 24)];
          headImageV.layer.cornerRadius = 12;
          headImageV.clipsToBounds = YES;
          [bottomV addSubview:headImageV];
          
          CAGradientLayer *gl = [CAGradientLayer layer];
          gl.frame = headImageV.bounds;
          gl.startPoint = CGPointMake(0.0, 0);
          gl.endPoint = CGPointMake(1, 1);
          gl.colors = [colorArr objectAtIndexCheck:randomNum];
          gl.locations = @[@(0), @(1.0f)];
          [headImageV.layer addSublayer:gl];
          
          UILabel *headL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
          headL.textColor = [UIColor whiteColor];
          headL.textAlignment = NSTextAlignmentCenter;
          headL.font = [UIFont systemFontOfSize:10];
          headL.text = nameStr.length > 2 ? [nameStr substringWithRange:NSMakeRange(nameStr.length - 2, 2)] : nameStr;
          [headImageV addSubview:headL];
          
          UILabel *switchlabel = [[UILabel alloc] init];
          switchlabel.frame = CGRectMake(headImageV.right + 6,1.5,BCWidth - 116,20);
          switchlabel.textColor = ACOLOR(30, 33, 38, 0.45);
          switchlabel.font = [UIFont systemFontOfSize:MutilFont(13)];
          [bottomV addSubview:switchlabel];
          switchlabel.attributedText = string;
          
  //        评论
          UIButton *commentBtn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - 60, switchlabel.top, 60, 20)];
          [bottomV addSubview:commentBtn];
          [commentBtn addtargetBlock:^(UIButton *button) {
            
            MAWriteReplyView *replyVV = [[MAWriteReplyView alloc] initWithName:@"评论" andPla:[NSString stringWithFormat:@"回复%@：",nameStr]];
            [replyVV showModal];
            replyVV.okBlock = ^(NSString *dataString) {
              [weakSelf replyString:[itemDic objectNilForKey:@"id"] andMemo:dataString andIndex:0];
            };
          }];
          
          UIImageView *commentIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 2, 16, 16)];
          commentIM.image = [UIImage imageNamed:@"detail_comment"];
          [commentBtn addSubview:commentIM];
          
          UILabel *commentL = [[UILabel alloc] initWithFrame:CGRectMake(commentIM.right + 4,0,30,20)];
          commentL.text = @"评论";
          commentL.font = [UIFont systemFontOfSize:MutilFont(12)];
          commentL.textColor = COLOR(134, 144, 156);
          [commentBtn addSubview:commentL];
          
          //        备注
          NSString *memoStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"memo"]];
          NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
          CGFloat height = [memoStr boundingRectWithSize:CGSizeMake(BCWidth - 92, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
          height = ceil(height);
          UILabel *memoLabel= [[UILabel alloc] initWithFrame:CGRectMake(headImageV.right + 6, headImageV.bottom + 6, BCWidth - 92,BCStringIsEmpty(memoStr) ? 0 : height)];
          memoLabel.textColor = ACOLOR(29, 33, 41, 1);
          memoLabel.font = [UIFont systemFontOfSize:15];
          memoLabel.text = memoStr;
          memoLabel.numberOfLines = 0;
          [bottomV addSubview:memoLabel];
          
          
          
          //        附件信息
          NSArray *fileArr = [itemDic objectForKeyNil:@"files"];
          if ([fileArr isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(fileArr)) {
            
            
            for (int j = 0; j <fileArr.count; j ++) {
              
              NSDictionary *deedDic = [fileArr objectAtIndexCheck:j];
              UIButton *leaseImageBtn = [[UIButton alloc] initWithFrame:CGRectMake(headImageV.right + 6, memoLabel.bottom + 8 + (44 + 10) * j, BCWidth - 62, 44)];
              leaseImageBtn.layer.cornerRadius = 4;
              leaseImageBtn.clipsToBounds = YES;
              leaseImageBtn.backgroundColor = COLOR(244, 245, 247);
              [bottomV addSubview:leaseImageBtn];
              
              UIImageView *leaseIM = [[UIImageView alloc] initWithFrame:CGRectMake(16, 10 ,24, 24)];
              [leaseImageBtn addSubview:leaseIM];
              
              UILabel *leaseL = [[UILabel alloc] initWithFrame:CGRectMake(leaseIM.right + 10,0, leaseImageBtn.width - leaseIM.right - 26, 44)];
              leaseL.text = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"name"]];
              leaseL.font = [UIFont systemFontOfSize:MutilFont(14)];
              leaseL.textColor = ACOLOR(31, 33, 38,1);
              leaseL.lineBreakMode = NSLineBreakByTruncatingMiddle;
              [leaseImageBtn addSubview:leaseL];
              
              if ([temArr containsObject:[deedDic objectNilForKey:@"suffix_type"]]) {//如果是图片
                
                leaseIM.image = [UIImage imageNamed:@"file_img"];
                [leaseImageBtn addtargetBlock:^(UIButton *button) {
                  [ZKPhotoBrowser showWithImageUrls:@[[[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] currentPhotoIndex:0 sourceSuperView:button];
                }];
              } else {//是其他文本 pdf  word
                
                NSString *fileStr = [NSString stringWithFormat:@"%@",[deedDic objectNilForKey:@"suffix_type"] ];
                if ([fileStr containsString:@"pdf"]) {
                  leaseIM.image = [UIImage imageNamed:@"file_pdf"];
                } else  if ([fileStr containsString:@"doc"] || [fileStr containsString:@"docx"]) {
                  leaseIM.image = [UIImage imageNamed:@"file_word"];
                } else  if ([fileStr containsString:@"xls"] || [fileStr containsString:@"xls"]) {
                  leaseIM.image = [UIImage imageNamed:@"file_excel"];
                }else  if ([fileStr containsString:@"ppt"] || [fileStr containsString:@"pptx"]) {
                  leaseIM.image = [UIImage imageNamed:@"file_ppt"];
                }else {
                  leaseIM.image = [UIImage imageNamed:@"file_no"];
                }
                
                [leaseImageBtn addtargetBlock:^(UIButton *button) {
                 
                  NSString *url = [[deedDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
                  [weakSelf openSFDoc:url];
                  
                }];
              }
              
            }
          }
          
          //                 评论明细
          CGFloat commentTop = 0;
          if (![fileArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(fileArr)) {//没有附件
            commentTop  = memoLabel.bottom  + 14;
          } else {
            commentTop = memoLabel.bottom + 8 + (44 + 10) * fileArr.count - 8 + 14;
          }
          
          CGFloat commentHeight = 0;
          NSArray *commentArr = [itemDic objectForKeyNil:@"details"];
          if (!BCArrayIsEmpty(commentArr)) {
            
            for (int k = 0; k <commentArr.count; k ++) {
              NSDictionary *replyDic = [commentArr objectAtIndexCheck:k];
              
              NSString *replyNameStr = [NSString stringWithFormat:@"%@",[replyDic objectNilForKey:@"create_by"]];
              NSString *replyResult = [NSString stringWithFormat:@"%@ · %@",replyNameStr,[replyDic objectNilForKey:@"create_time"]];
              NSMutableAttributedString *replystring = [[NSMutableAttributedString alloc] initWithString:replyResult];
              [replystring addAttributes:@{NSForegroundColorAttributeName:COLOR(31, 33, 38),NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium]} range:NSMakeRange(0, replyNameStr.length)];
              
              //  回复人
              NSInteger randomNum = arc4random_uniform(6);
              UIView *replyheadV = [[UIView alloc] initWithFrame:CGRectMake(headImageV.right + 6, commentTop + commentHeight, 24, 24)];
              replyheadV.layer.cornerRadius = 12;
              replyheadV.clipsToBounds = YES;
              [bottomV addSubview:replyheadV];
              
              CAGradientLayer *gl = [CAGradientLayer layer];
              gl.frame = replyheadV.bounds;
              gl.startPoint = CGPointMake(0.0, 0);
              gl.endPoint = CGPointMake(1, 1);
              gl.colors = [colorArr objectAtIndexCheck:randomNum];
              gl.locations = @[@(0), @(1.0f)];
              [replyheadV.layer addSublayer:gl];
              
              UILabel *replyL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
              replyL.textColor = [UIColor whiteColor];
              replyL.textAlignment = NSTextAlignmentCenter;
              replyL.font = [UIFont systemFontOfSize:10];
              replyL.text = replyNameStr.length > 2 ? [replyNameStr substringWithRange:NSMakeRange(replyNameStr.length - 2, 2)] : replyNameStr;
              [replyheadV addSubview:replyL];
              
              UILabel *replyTitleL = [[UILabel alloc] init];
              replyTitleL.frame = CGRectMake(replyheadV.right + 6,replyheadV.top + 1.5,BCWidth - replyheadV.right - 76,20);
              replyTitleL.textColor = ACOLOR(30, 33, 38, 0.45);
              replyTitleL.font = [UIFont systemFontOfSize:MutilFont(13)];
              [bottomV addSubview:replyTitleL];
              replyTitleL.attributedText = replystring;
              
              //            评论按钮
              UIButton *replyBtn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - 60, replyTitleL.top, 60, 20)];
              [bottomV addSubview:replyBtn];
              
              UIImageView *replyIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 2, 16, 16)];
              replyIM.image = [UIImage imageNamed:@"detail_comment"];
              [replyBtn addSubview:replyIM];
              
              UILabel *replyRL = [[UILabel alloc] initWithFrame:CGRectMake(replyIM.right + 4,0,30,20)];
              replyRL.text = @"评论";
              replyRL.font = [UIFont systemFontOfSize:MutilFont(12)];
              replyRL.textColor = COLOR(134, 144, 156);
              [replyBtn addSubview:replyRL];
              [replyBtn addtargetBlock:^(UIButton *button) {
                
                MAWriteReplyView *replyVV = [[MAWriteReplyView alloc] initWithName:@"评论" andPla:[NSString stringWithFormat:@"回复%@：",replyNameStr]];
                [replyVV showModal];
                replyVV.okBlock = ^(NSString *dataString) {
                  [weakSelf replyString:[replyDic objectNilForKey:@"id"] andMemo:dataString andIndex:0];
                };
              }];
              
              //            回复内容
              NSString *anwserStr = [NSString stringWithFormat:@"%@",[replyDic objectNilForKey:@"memo"]];
              NSString *anwserNameStr = [NSString stringWithFormat:@"%@",[replyDic objectNilForKey:@"reply_by"]];
              NSString *anwserResult = [NSString stringWithFormat:@"回复 %@：%@",anwserNameStr,!BCStringIsEmpty(anwserStr) ? anwserStr: @"-"];
              
              NSMutableAttributedString *opereateResultStr = [[NSMutableAttributedString alloc] initWithString:anwserResult];
              NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
              [paragraphStyle setLineSpacing:4];//调整行间距
              paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
              [opereateResultStr addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, anwserResult.length)];
              [opereateResultStr addAttribute:NSForegroundColorAttributeName value:COLOR(26, 106, 255) range:NSMakeRange(2, anwserNameStr.length + 1)];
              
              NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)],NSParagraphStyleAttributeName:paragraphStyle};
              CGFloat height = [anwserResult boundingRectWithSize:CGSizeMake(BCWidth - replyheadV.right - 22, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
              height = ceil(height);
              height = height < 20 ? 20 : height;
              
              UILabel *anwserL = [[UILabel alloc] init];
              anwserL.frame = CGRectMake(replyheadV.right + 6 , replyheadV.bottom + 6,BCWidth - replyheadV.right - 22,height);
              anwserL.textColor = ACOLOR(31, 33, 38, 1);
              anwserL.font = [UIFont systemFontOfSize:MutilFont(15)];
              anwserL.numberOfLines = 0;
              [bottomV addSubview:anwserL];
              anwserL.attributedText = opereateResultStr;
              
              
              commentHeight += 30 + 14 + height;
              
            }
            
          }
          
          //        计算高度
          CGFloat bottomH = commentTop + commentHeight;
          bottomV.height = bottomH + 14;
          
          //      分割线
          if (i < arr.count - 1 ) {
            UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16, bottomH - 1, BCWidth - 16, 1)];
            lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
            [bottomV addSubview:lineV];
          }
          
          if (i == arr.count - 1) {
            bottomV.height = bottomH;
          }
          itemH += bottomV.height ;
          
        }
        
        if (i == arr.count - 1) {
          self.selectBuniessStateV.height = itemH;
          if ( self.selectBuniessStateV.bottom  + 114 > self.buniessStateScrollView.height) {
            self.buniessStateScrollView.contentSize = CGSizeMake(0, self.selectBuniessStateV.bottom + 40);
          }
        }
      }
      
    }
    
  }
  
}

#pragma mark 回复评论功能
- (void)replyString:(NSString *)replyId andMemo:(NSString *)memo andIndex:(NSInteger)index{
  
  NSDictionary *params = @{@"id":self.pointId,@"reply_id":replyId,@"memo":memo};
  
  [self.loadingView showModal];
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.followrecord.save" Params:params success:^(NSDictionary *successResult) {
   
    if (index == 0) {//全部
      
      [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.records.find" Params:@{@"id":self.pointId} success:^(NSDictionary *successResult) {
        [self.loadingView hideModal];
        NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
        
        if (![receiveData isKindOfClass:[NSArray class]]) {
          return;
        }
        
        if (BCArrayIsEmpty(receiveData)) {
          return;
        }
        
        self.allBuniessArr = receiveData;
        
        [self.statusView showModal:ToastSuccess andTitle:@"评论成功"];
        
        [self reloadSelectSegment:0];
        
      } failure:^(NSString *errorResult) {
        
        [self.loadingView hideModal];
        [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
      }];
      
      
      [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.followrecord.find" Params:@{@"id":self.pointId} success:^(NSDictionary *successResult) {
        
       
        NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
        
        if (![receiveData isKindOfClass:[NSArray class]]) {
          return;
        }
        
        if (BCArrayIsEmpty(receiveData)) {
          return;
        }
        
        self.followBuniessArr = receiveData;
      
      } failure:^(NSString *errorResult) {
        
        [self.loadingView hideModal];
        [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
      }];
      
      
    } else {//跟进
      
      
      [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.records.find" Params:@{@"id":self.pointId} success:^(NSDictionary *successResult) {
        
        NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
        
        if (![receiveData isKindOfClass:[NSArray class]]) {
          return;
        }
        
        if (BCArrayIsEmpty(receiveData)) {
          return;
        }
        
        self.allBuniessArr = receiveData;
        
      } failure:^(NSString *errorResult) {
        
       
        [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
      }];
      
        
      [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.followrecord.find" Params:@{@"id":self.pointId} success:^(NSDictionary *successResult) {
        
        [self.loadingView hideModal];
        NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
        
        if (![receiveData isKindOfClass:[NSArray class]]) {
          return;
        }
        
        if (BCArrayIsEmpty(receiveData)) {
          return;
        }
        
        self.followBuniessArr = receiveData;
        
        [self.statusView showModal:ToastSuccess andTitle:@"评论成功"];
        
        [self reloadSelectSegment:1];
        
        
      } failure:^(NSString *errorResult) {
        
        [self.loadingView hideModal];
        [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
      }];
      
      
      
    }
     
  } failure:^(NSString *errorResult) {
    
    [self.loadingView hideModal];
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
}
#pragma mark 加载轮播图
- (UIView *)BannerHeader{
  
  [self.previewUrls removeAllObjects];
  [self.bannerImages removeAllObjects];
  
  NSArray *files = [self.detailDic objectForKeyNil:@"flag_all_files"];
  NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
  NSArray *videArr = @[@"mp4",@"mov",@"MP4",@"MOV",@"WMV",@"AVI",@"MKV",@"wmv",@"avi",@"mkv"];
  if ([files isKindOfClass:[NSArray class]] && !BCArrayIsEmpty(files)) {
    
    for (NSDictionary *urlDic in files) {
      
      if ([temArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {//图片
        if ([[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"svg"] || [[urlDic objectNilForKey:@"suffix_type"] isEqualToString:@"SVG"]) {
          [self.bannerImages addObject:[urlDic objectNilForKey:@"ref_img"]];
          [self.previewUrls addObject:[[urlDic objectNilForKey:@"ref_img"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
        } else {
          [self.bannerImages addObject:[urlDic objectNilForKey:@"url"]];
          [self.previewUrls addObject:[[urlDic objectNilForKey:@"url"] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
        }
        
        
      } else {//是视频，需要转换生成一下封面
        if ([videArr containsObject:[urlDic objectNilForKey:@"suffix_type"]]) {
          NSString *videoUrl = [NSString stringWithFormat:@"%@?spm=qipa250&x-oss-process=video/snapshot,t_300,f_jpg,ar_auto,m_fast",[urlDic objectNilForKey:@"url"]];
          [self.bannerImages addObject:videoUrl];
        }
        
      }
    }
  } else {
  
    [self.bannerImages addObject:@"https://hxl-applet.oss-cn-hangzhou.aliyuncs.com/插旗系統/img_v3_02g0_6c7950c5-c78f-40e3-997a-332bba8d1f9g.png"];
  }
  
  UIView *headV = [[UIView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, 460)];
  headV.backgroundColor = [UIColor whiteColor];
  
  self.bannerView = [BHInfiniteScrollView infiniteScrollViewWithFrame:CGRectMake(0, 0, self.view.bounds.size.width ,307) Delegate:self ImagesArray:self.bannerImages PlageHolderImage:[UIImage imageNamed:@"icon_pla"]];
  self.bannerView.dotSize = 6;
  self.bannerView.showPlay = YES;
  self.bannerView.dotSpacing = 5;
  self.bannerView.scrollTimeInterval = 5;
  self.bannerView.pageControlHidden = YES;
  [self.bannerView stopAutoScrollPage];
  [headV addSubview:self.bannerView];
  
  if (![self.detailDic isKindOfClass:[NSDictionary class]] || BCDictIsEmpty(self.detailDic)) {
    return headV;
  }
  
  __weak typeof(self)weakSelf = self;
  
  UIView *countV = [[UIView alloc] initWithFrame:CGRectMake(BCWidth - 50, 307 - 24 - 7, RealSize(42), 24)];
  countV.backgroundColor = ACOLOR(0, 0, 0, 0.5);
  countV.layer.cornerRadius = 12;
  [self.bannerView addSubview:countV];
  
  _indexLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, RealSize(42), 24)];
  _indexLabel.textColor = [UIColor whiteColor];
  _indexLabel.textAlignment = NSTextAlignmentCenter;
  _indexLabel.font = [UIFont systemFontOfSize:MutilFont(12) weight:UIFontWeightMedium];
  _indexLabel.text = [NSString stringWithFormat:@"%ld/%lu",(long)(self.scrollIndex + 1),(unsigned long)self.bannerImages.count];
  [countV addSubview:_indexLabel];
  
  NSString *name = [NSString stringWithFormat:@"%@",[self.detailDic objectNilForKey:@"name"]];
  NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
  [paragraphStyle setLineSpacing:4];
  paragraphStyle.lineBreakMode = NSLineBreakByCharWrapping;
  NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:18 weight:UIFontWeightMedium],NSParagraphStyleAttributeName:paragraphStyle};
  CGFloat height = [name boundingRectWithSize:CGSizeMake(BCWidth - 32, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
  height = ceil(height);
  
  NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:name];
  [attributedString addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, [name length])];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,307 + 12,BCWidth - 32 - 10 - 44,height);
  nameLabel.textColor = COLOR(31, 33, 38);
  nameLabel.font = [UIFont systemFontOfSize:18 weight:UIFontWeightMedium];
  nameLabel.numberOfLines = 0;
  nameLabel.attributedText = attributedString;
  [headV addSubview:nameLabel];
  [nameLabel addTapGestureWithBlock:^{
    UIPasteboard *pp = [UIPasteboard generalPasteboard];
    pp.string = name;
    [weakSelf.notiflyView showModal:NotiflySuccess andTitle:@"复制成功"];
  }];
  
  //  复制按钮
  UIButton *copyBtn = [[UIButton alloc] initWithFrame:CGRectMake(nameLabel.right + 10, nameLabel.top,44, 24)];
  UIImageView *copyIM = [[UIImageView alloc] initWithFrame:CGRectMake(20, 2, 24, 24)];
  copyIM.image = [UIImage imageNamed:@"icon_copyname"];
  [copyBtn addSubview:copyIM];
  [headV addSubview:copyBtn];
  [copyBtn addtargetBlock:^(UIButton *button) {
    UIPasteboard *pp = [UIPasteboard generalPasteboard];
    pp.string = name;
    [weakSelf.notiflyView showModal:NotiflySuccess andTitle:@"复制成功"];
  }];
  
  //  多个标签
  NSMutableArray *arr = [NSMutableArray arrayWithCapacity:10];
  if (![[self.detailDic objectNilForKey:@"state"] isEqualToString:@""]) {//商圈状态
    [arr addObject:[self changeState:[self.detailDic  objectNilForKey:@"state"]]];
  }
  
  if (![[self.detailDic objectNilForKey:@"level_name"] isEqualToString:@""]) {//商圈等级
    [arr addObject:[self.detailDic objectNilForKey:@"level_name"]];
  }
  
  if (![[self.detailDic objectNilForKey:@"schedule"] isEqualToString:@""]) {//开发进度
    NSString *schedule = [[self.detailDic objectNilForKey:@"schedule"] isEqualToString:@"INIT"] ? @"待开发" :[[self.detailDic  objectNilForKey:@"schedule"] isEqualToString:@"DEVELOPING"] ? @"开发中" : @"开发完成";
    [arr addObject:schedule];
  }
  
  if (!BCStringIsEmpty([self.detailDic objectForKeyNil:@"main_label_name"])) {
    [arr addObject:[self.detailDic objectForKeyNil:@"main_label_name"]];
  }
  if (!BCStringIsEmpty([self.detailDic objectForKeyNil:@"sub_label_name"])) {
    [arr addObject:[self.detailDic objectForKeyNil:@"sub_label_name"]];
  }
  
  CGFloat w = 16;
  for (int i = 0; i < arr.count; i++) {
    
    NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:11]};
    CGFloat length = [arr[i] boundingRectWithSize:CGSizeMake(MAXFLOAT, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.width;
    length = ceil(length);
    UILabel *label = [[UILabel alloc] init];
    label.layer.cornerRadius = 3;
    label.clipsToBounds = YES;
    label.textAlignment = NSTextAlignmentCenter;
    label.frame = CGRectMake(w , nameLabel.bottom + 6, length + 6, 17);
    label.font = [UIFont systemFontOfSize:11];
    label.text = [arr objectAtIndex:i];
    [headV addSubview:label];
    
    if (i == 0) {//点位状态
      label.textColor = [UIColor whiteColor];
      if ([label.text isEqualToString:@"制单"]) {
        label.backgroundColor =  [UIColor colorWithRed:26/255.0 green:106/255.0 blue:255/255.0 alpha:1];
      } else if ([label.text isEqualToString:@"审批通过"]) {
        label.backgroundColor = [UIColor colorWithRed:0/255.0 green:180/255.0 blue:43/255.0 alpha:1];
      } else if ([label.text isEqualToString:@"否决"] || [label.text isEqualToString:@"无效"]) {
        label.backgroundColor = [UIColor colorWithRed:134/255.0 green:144/255.0 blue:156/255.0 alpha:1];
      } else {
        label.backgroundColor =  [UIColor colorWithRed:255/255.0 green:125/255.0 blue:1/255.0 alpha:1];
      }
      
      
    } else if (i == 1){//等级
      label.textColor = [UIColor whiteColor];
      label.backgroundColor = [UIColor colorWithRed:245/255.0 green:63/255.0 blue:63/255.0 alpha:1];
    }else if (i == 2){//商圈开发状态
      label.textColor = [UIColor whiteColor];
      if ([label.text isEqualToString:@"待开发"]) {
        label.backgroundColor =  [UIColor colorWithRed:26/255.0 green:106/255.0 blue:255/255.0 alpha:1];
      } else if ([label.text isEqualToString:@"开发中"] ) {
        label.backgroundColor =  [UIColor colorWithRed:255/255.0 green:125/255.0 blue:1/255.0 alpha:1];
      } else {
        label.backgroundColor = [UIColor colorWithRed:0/255.0 green:180/255.0 blue:43/255.0 alpha:1];
      }
    }else {//商圈标签
      label.textColor = [UIColor colorWithRed:78/255.0 green:89/255.0 blue:105/255.0 alpha:1];
      label.backgroundColor = [UIColor colorWithRed:229/255.0 green:230/255.0 blue:234/255.0 alpha:1];
    }
    
    w = label.frame.size.width + label.frame.origin.x + 4;
  }
  
  
  // 跟进人
  NSString *followStr = [self.detailDic objectNilForKey:@"head_by"];
  NSString *followResult = [NSString stringWithFormat:@"跟进人：%@",followStr];
  NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:followResult];
  [string addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithRed:134/255.0 green:144/255.0 blue:156/255.0 alpha:1.000000]} range:NSMakeRange(0, 4 + followStr.length)];
  
  self.followLabel = [[UILabel alloc] init];
  self.followLabel.frame = CGRectMake(16 ,nameLabel.bottom + 27, BCWidth, 18.5);
  self.followLabel.font = [UIFont systemFontOfSize:13];
  self.followLabel.attributedText = string;
  [headV addSubview:self.followLabel];
  
  
  
  headV.height = self.followLabel.bottom + 2;
  return headV;
}
#pragma mark 初始化导航栏
- (void)initToolBar{
  
  //  导航栏
  _toolbar = [[UIView alloc] initWithFrame: CGRectMake(0, 0, BCWidth, self.heightTop + 44)];
  if (self.pushType == PushAnimationTypeNone) {
    _toolbar.alpha = 1;
  } else {
    _toolbar.alpha = 0;
  }
  [self.view addSubview:_toolbar];
  
  _alphaV = [[UIView alloc] initWithFrame:_toolbar.bounds];
  _alphaV.backgroundColor = [UIColor whiteColor];
  _alphaV.alpha = 0;
  [_toolbar insertSubview:_alphaV atIndex:0];
  
  //  返回按钮
  UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
  switchMap.frame = CGRectMake(14,self.heightTop + 4, RealSize(32), RealSize(32));
  switchMap.backgroundColor = [UIColor whiteColor];
  [switchMap addTarget:self action:@selector(clickBack) forControlEvents:UIControlEventTouchUpInside];
  UIImageView *backIM = [[UIImageView alloc] initWithFrame:CGRectMake((RealSize(32) - 18)/2, (RealSize(32) - 18)/2, 18, 18)];
  backIM.image = [UIImage imageNamed:@"nav_back"];
  [switchMap addSubview:backIM];
  switchMap.layer.cornerRadius = RealSize(32)/2;
  [_toolbar addSubview:switchMap];
  
  //    省略号按钮
  UIButton *moreBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  moreBtn.frame = CGRectMake(BCWidth - 46,self.heightTop + 4, 32, 32);
  moreBtn.backgroundColor = [UIColor whiteColor];
//  [moreBtn addTarget:self action:@selector(showBottomSheet) forControlEvents:UIControlEventTouchUpInside];
  moreBtn.layer.cornerRadius = 16;
  UIImageView *moreIM = [[UIImageView alloc] initWithFrame:CGRectMake(7, 7, 18, 18)];
  moreIM.image = [UIImage imageNamed:@"icon_more"];
  [moreBtn addSubview:moreIM];
  //  [_toolbar addSubview:moreBtn];
  
  //  关注按钮
  _followBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  _followBtn.frame = CGRectMake(BCWidth - RealSize(32) - 14,self.heightTop + 4, RealSize(32), RealSize(32));
  _followBtn.backgroundColor = [UIColor whiteColor];
  [_followBtn addTarget:self action:@selector(clickFollow:) forControlEvents:UIControlEventTouchUpInside];
  _followBtn.layer.cornerRadius = RealSize(32)/2;
  [_followBtn setImage:[UIImage imageNamed:@"point_follow"] forState:UIControlStateNormal];
  [_followBtn setImage:[UIImage imageNamed:@"point_follows"] forState:UIControlStateSelected];
  _followBtn.imageEdgeInsets = UIEdgeInsetsMake(7, 7, 7, 7);
  [_toolbar addSubview: _followBtn];
  
  
  //    分割线
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(0, self.heightTop + 43,BCWidth , 1)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [_alphaV addSubview:lineV];
}
// 点击顶部关注按钮
- (void)clickFollow:(UIButton *)sender{
  sender.selected = !sender.selected;
  sender.userInteractionEnabled = NO;
  
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    sender.userInteractionEnabled = YES;
  });
  [sender.layer removeAllAnimations];
  
  if (sender.selected) {//去关注
    
    CAKeyframeAnimation * animation = [CAKeyframeAnimation animationWithKeyPath:@"transform"];
    animation.duration = 0.5;
    animation.removedOnCompletion = YES;
    animation.fillMode = kCAFillModeForwards;
    NSMutableArray *values = [NSMutableArray array];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(0.1, 0.1, 1.0)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(1.1, 1.1, 1.0)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(0.9, 0.9, 0.9)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(1.0, 1.0, 1.0)]];
    animation.values = values;
    
    [sender.layer addAnimation:animation forKey:nil];
    
    [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.interestuser.save" Params:@{@"business_id":self.pointId,@"module":@"BUSINESS_PLAN"} success:^(NSDictionary * _Nonnull successResult) {
      [self.statusView showModal:ToastSuccess andTitle:@"关注成功"];
    } failure:^(NSString * _Nonnull errorResult) {
      sender.selected = YES;
      [self.statusView showModal:ToastFail andTitle:@"关注失败"];
    }];
    
  } else {//取消关注
    
    CAKeyframeAnimation * scaleAnimation = [CAKeyframeAnimation animationWithKeyPath:@"transform.scale"];
    scaleAnimation.duration = 0.5;
    scaleAnimation.values = @[@0.6, @1, @0.8, @1];
    [sender.layer addAnimation:scaleAnimation forKey:@"transform.scale"];
    
    [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.interestuser.delete" Params:@{@"business_id":self.pointId,@"module":@"BUSINESS_PLAN"} success:^(NSDictionary * _Nonnull successResult) {
      [self.statusView showModal:ToastSuccess andTitle:@"取消关注成功"];
    } failure:^(NSString * _Nonnull errorResult) {
      sender.selected = YES;
      [self.statusView showModal:ToastFail andTitle:@"取消关注失败"];
    }];
  }
}

// 点击返回上一页
- (void)clickBack{
  
  [self.bannerView stopAutoScrollPage];
  [[NSNotificationCenter defaultCenter] postNotificationName:@"GETPOINTDETAILINDEX" object:self userInfo:@{@"currentIndex":[NSString stringWithFormat:@"%ld",(long)self.bannerView.currentPageIndex]}];
  [self.navigationController popViewControllerAnimated:YES];
}
- (NSString *)changeState:(NSString *)state{
  if ([state isEqualToString:@"INIT_PRE"]) {
    return @"制单";
  } else if ([state isEqualToString:@"INIT"]) {
    return @"待审批";
  } else if ([state isEqualToString:@"SECOND_AUDIT"]) {
    return @"审批通过";
  } else if ([state isEqualToString:@"REJECT"]) {
    return @"否决";
  } else if ([state isEqualToString:@"INVALID"]) {
    return @"无效";
  }
  
  return @"";
}


#pragma mark 跳转动画
- (id<UIViewControllerAnimatedTransitioning>)navigationController:(UINavigationController *)navigationController animationControllerForOperation:(UINavigationControllerOperation)operation fromViewController:(UIViewController *)fromVC toViewController:(UIViewController *)toVC
{
  if (operation == UINavigationControllerOperationNone ) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MAEditPointViewController class]] || [fromVC isKindOfClass:[MAEditPointViewController class]]) {
    return nil;
  }
  if ([toVC isKindOfClass:[MAAddLandlordViewController class]] || [fromVC isKindOfClass:[MAAddLandlordViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MATakeLookViewController class]] || [fromVC isKindOfClass:[MATakeLookViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MAWriteFollowUpViewController class]] || [fromVC isKindOfClass:[MAWriteFollowUpViewController class]]) {
    return nil;
  }
  if ([toVC isKindOfClass:[MAPointEditPoiViewController class]] || [fromVC isKindOfClass:[MAPointEditPoiViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MAAddLandlordViewController class]] || [fromVC isKindOfClass:[MAAddLandlordViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MAAddPointViewController class]] || [fromVC isKindOfClass:[MAAddPointViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MAAddOpenStoreViewController class]] || [fromVC isKindOfClass:[MAAddOpenStoreViewController class]]) {
    return nil;
  }
  
  //  去查看点位地图时不执行动画
  if ([toVC isKindOfClass:[MALookPointViewController class]] || [fromVC isKindOfClass:[MALookPointViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MAPointDetailViewController class]] || [fromVC isKindOfClass:[MAPointDetailViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MABuniessWriteFollowViewController class]] || [fromVC isKindOfClass:[MABuniessWriteFollowViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MAEditBuniessViewController class]] || [fromVC isKindOfClass:[MAEditBuniessViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MAChooseLocationViewController class]] || [fromVC isKindOfClass:[MAChooseLocationViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MASearchPoiViewController class]] || [fromVC isKindOfClass:[MASearchPoiViewController class]]) {
    return nil;
  }
  
  if ([toVC isKindOfClass:[MABuniessEditDrawViewController class]] || [fromVC isKindOfClass:[MABuniessEditDrawViewController class]]) {
    return nil;
  }
  
  if (self.pushType == PushAnimationTypeModal) {
    
    return [[PushZoomScaleBuniessTranstion alloc] initWithTransitionType:operation == UINavigationControllerOperationPush ?  MoveTransitionTypePush: MoveTransitionTypePop];
    
  } else if (self.pushType == PushAnimationTypeCell) {
    
    return [[PushCellScaleBuniessTranstion alloc] initWithTransitionType:operation == UINavigationControllerOperationPush ?  CellTransitionTypePush: CellTransitionTypePop];
    
  } else {
    
    return nil;
  }
}

#pragma mark 轮播图代理
- (void)infiniteScrollView:(BHInfiniteScrollView *)infiniteScrollView didScrollToIndex:(NSInteger)index{
  _indexLabel.text = [NSString stringWithFormat:@"%ld/%lu",(long)(index + 1),(unsigned long)self.bannerImages.count];
  
  //  处理图片
  if (self.pushType == PushAnimationTypeModal) {
    if (infiniteScrollView.cellShowImage) {
      self.showImage = infiniteScrollView.cellShowImage;
    } else {
      if (index == self.scrollIndex) {
        self.showImage = self.tmpIM.image;
      } else {
        NSString *imageUrl = [self.bannerImages objectAtIndexCheck:index];
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
          NSData *data = [NSData dataWithContentsOfURL: [NSURL URLWithString:[imageUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]]];
          self.showImage = [UIImage imageWithData:data];
        });
      }
      
    }
    
  }
  
}

#pragma mark 点击轮播图
- (void)infiniteScrollView:(BHInfiniteScrollView *)infiniteScrollView didSelectItemAtIndex:(NSInteger)index{
  
  //   视频播放
  
  NSString *fileUrl = [self.bannerImages objectAtIndexCheck:index];
  NSArray *temArr = @[@"png",@"jpg",@"PNG",@"JPG",@"jpeg",@"JPEG",@"bmp",@"BMP",@"svg",@"SVG",@"webp",@"WEBP",@"gif",@"GIF"];
  if ([temArr containsObject:[fileUrl pathExtension]]) {
    NSString *imageUrl = [[self.bannerImages objectAtIndexCheck:index] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    NSInteger imageIndex  = [self.previewUrls indexOfObject:imageUrl];
    
    if (imageIndex != NSNotFound) {
      [ZKPhotoBrowser showWithImageUrls:self.previewUrls currentPhotoIndex:imageIndex sourceSuperView:infiniteScrollView];
    }
  } else {
    NSString *videoUrl =  [fileUrl substringToIndex:[fileUrl rangeOfString:@"?spm"].location];
    if (BCStringIsEmpty(videoUrl)) {
      return;
    }
    NSArray *temArr = @[@"mp4",@"mov",@"MP4",@"MOV",@"WMV",@"AVI",@"MKV",@"wmv",@"avi",@"mkv"];
    NSURL * url = [NSURL URLWithString:[videoUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
    AVPlayerViewController * pVC = [[AVPlayerViewController alloc] init];
    pVC.player = [AVPlayer playerWithURL:url];
    [self presentViewController:pVC animated:YES completion:nil];
    [pVC.player play];
  }
  
}

#pragma mark 地图商圈显示代理
- (MAOverlayRenderer *)mapView:(MAMapView *)mapView rendererForOverlay:(id<MAOverlay>)overlay{
  if ([overlay isKindOfClass:[MACreatBuniessPolygon class]])
  {
    
    MACreatBuniessPolygonRender *polygonRenderer = [[MACreatBuniessPolygonRender alloc] initWithPolygon:overlay];
    polygonRenderer.lineWidth   = 2;
    polygonRenderer.strokeColor = [UIColor colorWithRed:255/255.0 green:33/255.0 blue:33/255.0 alpha:1];
    polygonRenderer.fillColor   = [UIColor colorWithRed:255/255.0 green:33/255.0 blue:33/255.0 alpha:0.2];
    
    return polygonRenderer;
  }
  
  return nil;
}
- (void)dealloc{
  NSLog(@"商圈详情释放了");
  [[NSNotificationCenter defaultCenter] removeObserver:self];
  _mapView.showsUserLocation = NO;
  [_mapView.layer removeAllAnimations];
  [_mapView removeAnnotations:_mapView.annotations];
  [_mapView removeOverlays:_mapView.overlays];
  [_mapView removeFromSuperview];
  _mapView.delegate = nil;
  _mapView = nil;
}
// 点击顶部选项卡
- (void)titleClick:(UIButton *)sender{
  
  if (sender != _selectTabButton) {
    
    UIImageView *iconIm = [_selectTabButton viewWithTag:100];
    if (iconIm) {
      iconIm.tintColor =  COLOR(31, 33, 38);
    }
    
    
    UILabel *titleL = [_selectTabButton viewWithTag:200];
    titleL.font = [UIFont systemFontOfSize:MutilFont(14)];
    titleL.textColor = COLOR(78, 89, 105);
    
    UILabel *numL = [_selectTabButton viewWithTag:300];
    if (numL) {
      numL.textColor = COLOR(31, 33, 38);
    }
    
    _selectTabButton.selected = NO;
    sender.selected = YES;
    
    UIImageView *iconIms = [sender viewWithTag:100];
    if (iconIms) {
      iconIms.tintColor =  COLOR(26, 106, 255);
    }
    
    UILabel *titleLs = [sender viewWithTag:200];
    titleLs.font = [UIFont systemFontOfSize:MutilFont(14) weight:UIFontWeightMedium];
    titleLs.textColor = COLOR(26, 106, 255);
    
    UILabel *numLs = [sender viewWithTag:300];
    if (numLs) {
      numLs.textColor = COLOR(26, 106, 255);
    }
    
    _selectTabButton = sender;
    
    //    指示条动画
    [UIView animateWithDuration:0.2 animations:^{
      self.indicateLayer.frame = CGRectMake(sender.left, 67, BCWidth/3, 3);
    }];
    
    [UIView animateWithDuration:0.3 animations:^{
      sender.transform = CGAffineTransformMakeScale(1.05, 1.05);
    } completion:^(BOOL finished) {
      [UIView animateWithDuration:0.3 animations:^{
        sender.transform = CGAffineTransformIdentity;
      }];
    }];
    
    //   计算选项卡滚动位置
    [self scrollSelectedViewToCenter:sender];
    
    //    计算横向滚动scrollview滚动位置
    [self.horizontalScrollView setContentOffset:CGPointMake(BCWidth * (sender.tag - 1014), 0) animated:NO];
  }
  
}
// 设置标题滚动区域的偏移量
- (void)scrollSelectedViewToCenter:(UIButton *)subView {
  
  
  CGFloat offsetX = subView.center.x - BCWidth * 0.5;
  
  if (offsetX < 0) {
    offsetX = 0;
  }
  
  CGFloat maxOffsetX = self.topTabV.contentSize.width - BCWidth;
  
  if (maxOffsetX < 0) {
    maxOffsetX = 0;
  }
  
  if (offsetX > maxOffsetX) {
    offsetX = maxOffsetX;
  }
  
  [self.topTabV setContentOffset:CGPointMake(offsetX, 0) animated:YES];
}
#pragma mark *** UITableViewDataSource ***
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
  
  return 0;
  
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
  return 0.01;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
  return 70;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
  
  _indicateLayer = [[UIView alloc] init];
  _indicateLayer.layer.cornerRadius = 1.5;
  _indicateLayer.backgroundColor = COLOR(26, 106, 255);
  [self.topTabV addSubview:_indicateLayer];
  
  NSArray *titleArr = @[@"首页",@"规划点位",@"商圈动态"];
  NSArray *iconArr = @[@"point_home",@"point_state"];
  
  CGFloat w = BCWidth/3;
  for (int i = 0; i < titleArr.count; i ++) {
    
    
    
    UIButton *titleButton = [UIButton buttonWithType:UIButtonTypeCustom];
    titleButton.frame = CGRectMake(w * i, 10, w , 52);
    titleButton.tag = 1014 + i;
    
    if (i == 0 || i == 2) {//图标
      UIImageView *iconIm = [[UIImageView alloc] initWithFrame:CGRectMake((w - 20)/2, 3, 20, 20)];
      if (i == 2) {
        iconIm.image = [[UIImage imageNamed:iconArr[1]] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
      } else {
        iconIm.image = [[UIImage imageNamed:iconArr[i]] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
      }
      iconIm.tag = 100;
      iconIm.tintColor = COLOR(31, 33, 38);
      [titleButton addSubview:iconIm];
      
      UILabel *titleL = [[UILabel alloc] initWithFrame:CGRectMake(0, iconIm.bottom + 2, w, 20)];
      titleL.textColor  = COLOR(78, 89, 105);
      titleL.text = titleArr[i];
      titleL.tag = 200;
      titleL.textAlignment = NSTextAlignmentCenter;
      titleL.font = [UIFont systemFontOfSize:MutilFont(14)];
      [titleButton addSubview:titleL];
      
    } else {//数字
      
      UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, w, 25)];
      label.textColor  = COLOR(31, 33, 38);
      label.font = [UIFont systemFontOfSize:MutilFont(18)];
      label.text = [NSString stringWithFormat:@"%lu",(unsigned long)((![self.pointPlanArr isKindOfClass:[NSArray class]] || BCArrayIsEmpty(self.pointPlanArr)) ? 0:self.pointPlanArr.count)];
      label.tag = 300;
      label.textAlignment = NSTextAlignmentCenter;
      [titleButton addSubview:label];
      
      UILabel *titleL = [[UILabel alloc] initWithFrame:CGRectMake(0, label.bottom, w, 20)];
      titleL.textColor  = COLOR(78, 89, 105);
      titleL.font = [UIFont systemFontOfSize:MutilFont(14)];
      titleL.text = titleArr[i];
      titleL.tag = 200;
      titleL.textAlignment = NSTextAlignmentCenter;
      [titleButton addSubview:titleL];
    }
    
    
    
    if (i == 0) {
      titleButton.selected = YES;
      UIImageView *iconIm = [titleButton viewWithTag:100];
      iconIm.tintColor = COLOR(26, 106, 255);
      
      UILabel *titleL = [titleButton viewWithTag:200];
      titleL.font = [UIFont systemFontOfSize:MutilFont(14) weight:UIFontWeightMedium];
      titleL.textColor = COLOR(26, 106, 255);
      _selectTabButton = titleButton;
      _indicateLayer.frame = CGRectMake(0, 67, BCWidth/3, 3);
    }
    [titleButton addTarget:self action:@selector(titleClick:) forControlEvents:UIControlEventTouchUpInside];
    [self.topTabV addSubview:titleButton];
    
    
  }
  self.topTabV.contentSize = CGSizeMake(w - 8 , 70);
  
  return self.topTabV;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  
  
  UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"nullCell1"];
  if (!cell) {
    cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:@"nullCell1"];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.backgroundColor = [UIColor greenColor];
    
  }
  
  return cell;
  
}

#pragma mark 滑动手势代理
- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
  
  
  if (scrollView == self.mainTableView) {//如果是竖向滑动的主scrollview滑动，则导航栏渐变
    
    CGFloat tmpHeight = 307 - self.heightTop - 44 ;
    CGFloat alphaValue = MIN(1, scrollView.contentOffset.y/tmpHeight);
    
    
    self.alphaV.alpha = alphaValue;
    if (scrollView.contentOffset.y>=307) {
      scrollView.contentInset = UIEdgeInsetsMake(self.heightTop + 44, 0, 0, 0);
      [self.bannerView stopAutoScrollPage];
    } else {
      scrollView.contentInset = UIEdgeInsetsMake(0, 0, 0, 0);
      [self.bannerView startAutoScrollPage];
    }
    
    
    if (scrollView.contentOffset.y >= (scrollView.contentSize.height-scrollView.height)) {
      self.offsetType = OffsetTypeMax;
    } else if (scrollView.contentOffset.y <= 0) {
      self.offsetType = OffsetTypeMin;
    } else {
      self.offsetType = OffsetTypeCenter;
    }
    
    NSInteger selectedIndex = self.selectTabButton.tag - 1014;
    
    if (selectedIndex == 0) {
      
      if (self.homeScrollView.offsetType == OffsetTypeCenter) {
        scrollView.contentOffset = CGPointMake(scrollView.contentOffset.x, scrollView.contentSize.height-scrollView.height);
      } else {
        self.pointPlanScrollView.contentOffset = CGPointZero;
        self.buniessStateScrollView.contentOffset = CGPointZero;
        
      }
      
    } else  if (selectedIndex == 1) {
      
      if (self.pointPlanScrollView.offsetType == OffsetTypeCenter) {
        scrollView.contentOffset = CGPointMake(scrollView.contentOffset.x, scrollView.contentSize.height-scrollView.height);
      } else {
        self.buniessStateScrollView.contentOffset = CGPointZero;
        self.homeScrollView.contentOffset = CGPointZero;
      }
      
    }else  if (selectedIndex == 2) {
      
      if (self.buniessStateScrollView.offsetType == OffsetTypeCenter) {
        scrollView.contentOffset = CGPointMake(scrollView.contentOffset.x, scrollView.contentSize.height-scrollView.height);
      } else {
        self.homeScrollView.contentOffset = CGPointZero;
        self.pointPlanScrollView.contentOffset = CGPointZero;
      }
      
    }
    
  }
  
  if (scrollView == self.horizontalScrollView) {
    if (!scrollView.isDragging && !scrollView.isDecelerating) {return;}
    // 当滑到边界时，继续通过scrollView的bouces效果滑动时，直接return
    if (scrollView.contentOffset.x < 0 || scrollView.contentOffset.x > scrollView.contentSize.width-scrollView.bounds.size.width) {
      return;
    }
    
    CGFloat movingOffsetX = scrollView.contentOffset.x - _startOffsetX;
    NSInteger selectIndex = scrollView.contentOffset.x/scrollView.bounds.size.width;
    
    [self moveSelectedLineByScrollWithOffsetX:movingOffsetX andIndex:selectIndex];
  }
  
}
- (void)moveSelectedLineByScrollWithOffsetX:(CGFloat)offsetX andIndex:(NSInteger)index{
  
  if (fabs(offsetX) < BCWidth) {
    if (offsetX >= 0) {//向右
      CGFloat movedFloat =  (offsetX * BCWidth/3 ) / [UIScreen mainScreen].bounds.size.width;
      self.indicateLayer.left = movedFloat + BCWidth/3 *index;
    } else {//向左
      CGFloat movedFloat =  (offsetX * BCWidth/3 ) / [UIScreen mainScreen].bounds.size.width;
      self.indicateLayer.left = movedFloat + BCWidth/3 *(index + 1);
    }
  }
}
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
  if ([scrollView isEqual:self.horizontalScrollView]) {
    self.homeScrollView.scrollEnabled = NO;
    self.pointPlanScrollView.scrollEnabled = NO;
    self.buniessStateScrollView.scrollEnabled = NO;
    _startOffsetX = scrollView.contentOffset.x;
  }
}
- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
  if ([scrollView isEqual:self.horizontalScrollView]) {
    self.homeScrollView.scrollEnabled = YES;
    self.pointPlanScrollView.scrollEnabled = YES;
    self.buniessStateScrollView.scrollEnabled = YES;
  }
}
- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView withVelocity:(CGPoint)velocity targetContentOffset:(inout CGPoint *)targetContentOffset
{
  
  
  if (scrollView == self.horizontalScrollView) {//如果是横向左右滚动的scrollview,控制选项卡按钮位置
    
    NSInteger selectIndex = targetContentOffset->x/BCWidth;
    
    UIButton *sender = [self.topTabV viewWithTag:1014 + selectIndex];
    if (sender != _selectTabButton) {
      
      
      UIImageView *iconIm = [_selectTabButton viewWithTag:100];
      if (iconIm) {
        iconIm.tintColor =  COLOR(31, 33, 38);
      }
      
      
      UILabel *titleL = [_selectTabButton viewWithTag:200];
      titleL.font = [UIFont systemFontOfSize:MutilFont(14)];
      titleL.textColor = COLOR(78, 89, 105);
      
      UILabel *numL = [_selectTabButton viewWithTag:300];
      if (numL) {
        numL.textColor = COLOR(31, 33, 38);
      }
      
      _selectTabButton.selected = NO;
      sender.selected = YES;
      
      UIImageView *iconIms = [sender viewWithTag:100];
      if (iconIms) {
        iconIms.tintColor =  COLOR(26, 106, 255);
      }
      
      UILabel *titleLs = [sender viewWithTag:200];
      titleLs.font = [UIFont systemFontOfSize:MutilFont(14) weight:UIFontWeightMedium];
      titleLs.textColor = COLOR(26, 106, 255);
      
      UILabel *numLs = [sender viewWithTag:300];
      if (numLs) {
        numLs.textColor = COLOR(26, 106, 255);
      }
      
      _selectTabButton = sender;
      
      [UIView animateWithDuration:0.3 animations:^{
        sender.transform = CGAffineTransformMakeScale(1.05, 1.05);
      } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.3 animations:^{
          sender.transform = CGAffineTransformIdentity;
        }];
      }];
      
      //   计算选项卡滚动位置
      [self scrollSelectedViewToCenter:sender];
      
    }
    
  }
}
#pragma mark 懒加载
- (MALoadWaveView *)loadingView{
  if (!_loadingView) {
    _loadingView = [[MALoadWaveView alloc] initWithFrame:[UIScreen mainScreen].bounds];
  }
  return _loadingView;
}
- (UITableView *)mainTableView{
  if (!_mainTableView) {
    _mainTableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, BCHeight) style:UITableViewStylePlain];
    _mainTableView.showsVerticalScrollIndicator = NO;
    _mainTableView.backgroundColor = [UIColor whiteColor];
    _mainTableView.delegate = self;
    _mainTableView.dataSource = self;
    _mainTableView.bounces = NO;
    _mainTableView.alpha = 0;
    _mainTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    if (@available(iOS 15.0, *)) {
      _mainTableView.sectionHeaderTopPadding = 0;
    }
    _mainTableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    
  }
  
  return _mainTableView;
}
- (UIScrollView*)horizontalScrollView{
  if (!_horizontalScrollView) {
    _horizontalScrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, BCHeight - (self.heightTop + 44 + 70))];
    _horizontalScrollView.contentSize = CGSizeMake(BCWidth * 3,  0);
    _horizontalScrollView.showsHorizontalScrollIndicator = NO;
    _horizontalScrollView.backgroundColor = COLOR(242, 243, 245);
    _horizontalScrollView.delegate = self;
    _horizontalScrollView.bounces = NO;
    _horizontalScrollView.pagingEnabled = YES;
    
  }
  return _horizontalScrollView;
}
- (UIScrollView*)topTabV{
  if (!_topTabV) {
    _topTabV = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, 70)];
    _topTabV.backgroundColor = [UIColor whiteColor];
    _topTabV.showsHorizontalScrollIndicator=NO;
    _topTabV.showsVerticalScrollIndicator=NO;
    
  }
  
  return _topTabV;
}
- (MABuniessScrollView *)homeScrollView{
  if (!_homeScrollView) {
    _homeScrollView = [[MABuniessScrollView alloc] initWithFrame: CGRectMake(0, 0, BCWidth, BCHeight - (self.heightTop + 44 + 70 + 90))];
    _homeScrollView.showsVerticalScrollIndicator = NO;
    _homeScrollView.backgroundColor = COLOR(242, 243, 245);
    _homeScrollView.mainVC = self;
    _homeScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
  }
  
  return _homeScrollView;
}

- (MABuniessScrollView *)pointPlanScrollView{
  if (!_pointPlanScrollView) {
    _pointPlanScrollView = [[MABuniessScrollView alloc] initWithFrame: CGRectMake(BCWidth , 0, BCWidth, BCHeight - (self.heightTop + 44 + 70 + 90))];
    _pointPlanScrollView.showsVerticalScrollIndicator = NO;
    _pointPlanScrollView.backgroundColor =  COLOR(242, 243, 245);
    _pointPlanScrollView.mainVC = self;
    _pointPlanScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
  }
  
  return _pointPlanScrollView;
}

- (MABuniessScrollView *)buniessStateScrollView{
  if (!_buniessStateScrollView) {
    _buniessStateScrollView = [[MABuniessScrollView alloc] initWithFrame: CGRectMake(BCWidth * 2, 0, BCWidth, BCHeight - (self.heightTop + 44 + 70 + 90))];
    _buniessStateScrollView.showsVerticalScrollIndicator = NO;
    _buniessStateScrollView.backgroundColor = COLOR(242, 243, 245);
    _buniessStateScrollView.mainVC = self;
    _buniessStateScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
  }
  
  return _buniessStateScrollView;
}
- (UIStatusBarStyle)preferredStatusBarStyle{
  if (@available(iOS 13.0, *)) {
    return  UIStatusBarStyleDarkContent;
  } else {
    return UIStatusBarStyleDefault;
  }
}

- (NSString *)removeSpaceAndNewline:(NSString *)str{
   
    NSString *temp = [str stringByReplacingOccurrencesOfString:@" " withString:@""];
    temp = [temp stringByReplacingOccurrencesOfString:@"\r" withString:@""];
    temp = [temp stringByReplacingOccurrencesOfString:@"\n" withString:@""];
    return temp;
}

- (MABottomOpereateView *)operatV{
  if (!_operatV) {
    _operatV = [[MABottomOpereateView alloc]initWithArray:@[@"删除"]];
  }
  return _operatV;
}

- (MATopToastView *)statusView{
  if (!_statusView) {
    _statusView = [[MATopToastView alloc] initCustomViewWithDuration:1];
  }
  
  return _statusView;
}

- (MATopNotiflyView *)notiflyView{
  if (!_notiflyView) {
    _notiflyView = [[MATopNotiflyView alloc] initCustomViewWithDuration:1];
  }
  return _notiflyView;
}
@end
