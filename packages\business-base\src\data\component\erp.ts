export let erpComponent = [
  {
    label: '当天要货',
    key: 'erpSameDayDeliveryCard',
    type: 'erpSameDayDeliveryCard',
    image: require('@xlb/common/src/assets/component/erpSameDayDeliveryCard.png'),
    isSelect: false,
    group: 'erp',
  },
  {
    label: '异常预警',
    key: 'erpAbnormalityAlarmCard',
    type: 'erpAbnormalityAlarmCard',
    image: require('@xlb/common/src/assets/component/erpAbnormalityAlarmCard.png'),
    isSelect: false,
    group: 'erp',
  },
  {
    label: '重点关注',
    key: 'erpHighlightedAreasCard',
    type: 'erpHighlightedAreasCard',
    image: require('@xlb/common/src/assets/component/erpHighlightedAreasCard.png'),
    isSelect: false,
    group: 'erp',
  },
   {
    label: '采购关注',
    key: 'erpOrderCompletionAccuracy',
    type: 'erpOrderCompletionAccuracy',
    image: require('@xlb/common/src/assets/component/erpOrderCompletionAccuracy.png'),
    isSelect: false,
    group: 'erp',
  },
  {
    label: '红榜',
    key: 'erpHonorRolCard',
    type: 'erpHonorRolCard',
    image: require('@xlb/common/src/assets/component/erpHonorRolCard.png'),
    isSelect: false,
    group: 'erp',
  },
  {
    label: '黑榜',
    key: 'erpBlackListCard',
    type: 'erpBlackListCard',
    image: require('@xlb/common/src/assets/component/erpBlackListCard.png'),
    isSelect: false,
    group: 'erp',
  },
  {
    label: '配送分析',
    key: 'erpDeliveryAnalysisCard',
    type: 'erpDeliveryAnalysisCard',
    image: require('@xlb/common/src/assets/component/erpDeliveryAnalysisCard.png'),
    isSelect: false,
    group: 'erp',
  },
  {
    label: '商品统计',
    key: 'erpDistributionCenterCard',
    type: 'erpDistributionCenterCard',
    image: require('@xlb/common/src/assets/component/erpDistributionCenterCard.png'),
    isSelect: false,
    group: 'erp',
  },
  {
    label: '销售分析',
    key: 'erpSaleAnalysis',
    type: 'erpSaleAnalysis',
    image: require('@xlb/common/src/assets/component/erp-saleAnalysis.png'),
    isSelect: false,
    group: 'erp',
    auth: ['营业收款报表', '查询'],
  },
  {
    label: '畅销品提醒',
    key: 'erpBestSellerReminderCard',
    type: 'erpBestSellerReminderCard',
    image: require('@xlb/common/src/assets/component/erpBestSellerReminderCard.png'),
    isSelect: false,
    group: 'erp',
  },
]
