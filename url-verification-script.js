#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 配置
const CONFIG = {
  apiMappingFile: './api-mapping.json',
  outputLogFile: './url-verification-log.json',
  fileExtensions: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx', '**/*.json'],
  excludePatterns: [
    'node_modules/**',
    'dist/**',
    'build/**',
    '.git/**',
    'coverage/**',
    '*.log',
    'url-replacement-*.js',
    'url-verification-*.js',
    'url-replacement-*.json',
    'url-verification-*.json'
  ]
};

class URLVerifier {
  constructor() {
    this.apiMapping = {};
    this.verificationLog = {
      summary: {
        totalFiles: 0,
        filesWithOldPaths: 0,
        filesWithNewPaths: 0,
        totalOldPathsFound: 0,
        totalNewPathsFound: 0,
        unmappedErpPaths: [],
        startTime: new Date().toISOString(),
        endTime: null
      },
      oldPathsFound: [],
      newPathsFound: [],
      unmappedErpPaths: [],
      errors: []
    };
  }

  // 加载API映射
  loadApiMapping() {
    try {
      const content = fs.readFileSync(CONFIG.apiMappingFile, 'utf8');
      this.apiMapping = JSON.parse(content);
      console.log(`✅ 加载了 ${Object.keys(this.apiMapping).length} 个API映射`);
    } catch (error) {
      console.error('❌ 加载API映射失败:', error.message);
      process.exit(1);
    }
  }

  // 获取所有需要处理的文件
  getAllFiles() {
    const allFiles = [];
    
    CONFIG.fileExtensions.forEach(pattern => {
      const files = glob.sync(pattern, {
        ignore: CONFIG.excludePatterns,
        nodir: true
      });
      allFiles.push(...files);
    });

    const uniqueFiles = [...new Set(allFiles)];
    console.log(`📁 找到 ${uniqueFiles.length} 个文件需要验证`);
    return uniqueFiles;
  }

  // 创建搜索模式
  createSearchPatterns() {
    const oldPaths = Object.keys(this.apiMapping);
    const newPaths = Object.values(this.apiMapping);
    
    return {
      // 搜索旧路径的模式
      oldPathPatterns: oldPaths.map(path => {
        const escapedPath = path.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        return {
          path,
          regex: new RegExp(`(['"\`]|\\$\\{[^}]+\\}|:\\s*|^|\\s)(${escapedPath})(\\?[^\\s'"\`}]*)?`, 'g')
        };
      }),
      
      // 搜索新路径的模式
      newPathPatterns: newPaths.map(path => {
        const escapedPath = path.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        return {
          path,
          regex: new RegExp(`(['"\`]|\\$\\{[^}]+\\}|:\\s*|^|\\s)(${escapedPath})(\\?[^\\s'"\`}]*)?`, 'g')
        };
      }),
      
      // 搜索未映射的/erp路径
      unmappedErpPattern: new RegExp(`(['"\`]|\\$\\{[^}]+\\}|:\\s*|^|\\s)(/erp/[^\\s'"\`}?]*)(\\?[^\\s'"\`}]*)?`, 'g')
    };
  }

  // 验证单个文件
  verifyFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const patterns = this.createSearchPatterns();
      
      let fileHasOldPaths = false;
      let fileHasNewPaths = false;
      let fileOldPathCount = 0;
      let fileNewPathCount = 0;

      // 检查旧路径
      patterns.oldPathPatterns.forEach(({ path, regex }) => {
        const matches = [...content.matchAll(regex)];
        if (matches.length > 0) {
          fileHasOldPaths = true;
          fileOldPathCount += matches.length;
          
          matches.forEach(match => {
            this.verificationLog.oldPathsFound.push({
              filePath,
              path,
              match: match[0],
              line: this.getLineNumber(content, match.index)
            });
          });
        }
      });

      // 检查新路径
      patterns.newPathPatterns.forEach(({ path, regex }) => {
        const matches = [...content.matchAll(regex)];
        if (matches.length > 0) {
          fileHasNewPaths = true;
          fileNewPathCount += matches.length;
          
          matches.forEach(match => {
            this.verificationLog.newPathsFound.push({
              filePath,
              path,
              match: match[0],
              line: this.getLineNumber(content, match.index)
            });
          });
        }
      });

      // 检查未映射的/erp路径
      const unmappedMatches = [...content.matchAll(patterns.unmappedErpPattern)];
      unmappedMatches.forEach(match => {
        const erpPath = match[2]; // 第二个捕获组是/erp路径
        if (!this.apiMapping[erpPath]) {
          this.verificationLog.unmappedErpPaths.push({
            filePath,
            path: erpPath,
            match: match[0],
            line: this.getLineNumber(content, match.index)
          });
        }
      });

      // 更新统计
      this.verificationLog.summary.totalFiles++;
      if (fileHasOldPaths) {
        this.verificationLog.summary.filesWithOldPaths++;
        this.verificationLog.summary.totalOldPathsFound += fileOldPathCount;
      }
      if (fileHasNewPaths) {
        this.verificationLog.summary.filesWithNewPaths++;
        this.verificationLog.summary.totalNewPathsFound += fileNewPathCount;
      }

      // 输出进度
      if (fileHasOldPaths || fileHasNewPaths || unmappedMatches.length > 0) {
        console.log(`📄 ${filePath}: 旧路径=${fileOldPathCount}, 新路径=${fileNewPathCount}, 未映射=${unmappedMatches.length}`);
      }

    } catch (error) {
      const errorMsg = `验证文件 ${filePath} 时出错: ${error.message}`;
      console.error(`❌ ${errorMsg}`);
      this.verificationLog.errors.push(errorMsg);
    }
  }

  // 获取行号
  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  // 主验证函数
  async run() {
    console.log('🔍 开始URL路径验证任务...\n');
    
    // 加载API映射
    this.loadApiMapping();
    
    // 获取所有文件
    const allFiles = this.getAllFiles();
    
    console.log('🔄 开始验证文件...\n');
    
    // 验证每个文件
    allFiles.forEach(file => {
      this.verifyFile(file);
    });
    
    // 完成验证
    this.verificationLog.summary.endTime = new Date().toISOString();
    
    // 统计未映射的路径
    const uniqueUnmappedPaths = [...new Set(this.verificationLog.unmappedErpPaths.map(item => item.path))];
    this.verificationLog.summary.unmappedErpPaths = uniqueUnmappedPaths;
    
    // 保存日志
    fs.writeFileSync(CONFIG.outputLogFile, JSON.stringify(this.verificationLog, null, 2));
    
    // 输出总结
    this.printSummary();
  }

  // 打印总结
  printSummary() {
    const { summary } = this.verificationLog;
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 URL路径验证完成总结');
    console.log('='.repeat(60));
    console.log(`📁 总文件数: ${summary.totalFiles}`);
    console.log(`❌ 仍有旧路径的文件: ${summary.filesWithOldPaths}`);
    console.log(`✅ 已有新路径的文件: ${summary.filesWithNewPaths}`);
    console.log(`🔢 旧路径总数: ${summary.totalOldPathsFound}`);
    console.log(`🔢 新路径总数: ${summary.totalNewPathsFound}`);
    console.log(`❓ 未映射的/erp路径数: ${summary.unmappedErpPaths.length}`);
    console.log(`⏱️  开始时间: ${summary.startTime}`);
    console.log(`⏱️  结束时间: ${summary.endTime}`);
    console.log(`❌ 错误数量: ${this.verificationLog.errors.length}`);
    console.log(`📄 详细日志: ${CONFIG.outputLogFile}`);
    console.log('='.repeat(60));
    
    // 显示替换完成率
    const totalMappedPaths = Object.keys(this.apiMapping).length;
    const completionRate = totalMappedPaths > 0 ? 
      ((totalMappedPaths * summary.totalNewPathsFound) / (totalMappedPaths * summary.totalNewPathsFound + summary.totalOldPathsFound)) * 100 : 100;
    
    console.log(`\n📈 替换完成率: ${completionRate.toFixed(2)}%`);
    
    if (summary.totalOldPathsFound > 0) {
      console.log('\n❌ 仍需处理的旧路径:');
      const oldPathsByFile = {};
      this.verificationLog.oldPathsFound.forEach(item => {
        if (!oldPathsByFile[item.filePath]) {
          oldPathsByFile[item.filePath] = [];
        }
        oldPathsByFile[item.filePath].push(`${item.path} (行${item.line})`);
      });
      
      Object.entries(oldPathsByFile).forEach(([file, paths]) => {
        console.log(`  📄 ${file}:`);
        paths.forEach(path => console.log(`    - ${path}`));
      });
    }
    
    if (summary.unmappedErpPaths.length > 0) {
      console.log('\n❓ 发现未映射的/erp路径:');
      summary.unmappedErpPaths.forEach(path => {
        console.log(`  - ${path}`);
      });
    }
    
    if (this.verificationLog.errors.length > 0) {
      console.log('\n❌ 错误列表:');
      this.verificationLog.errors.forEach(error => console.log(`  - ${error}`));
    }
  }
}

// 运行脚本
if (require.main === module) {
  const verifier = new URLVerifier();
  verifier.run().catch(error => {
    console.error('❌ 验证脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = URLVerifier;
