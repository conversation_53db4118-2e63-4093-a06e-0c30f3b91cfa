import React, {useEffect, useRef, useState} from 'react';
import useEntranceStore from '../entrance/useEntranceStore';
import {
  DeviceEventEmitter,
  ImageBackground,
  NativeModules,
  Platform,
  StatusBar,
  Text,
  View,
  StyleSheet,
  Image,
} from 'react-native';
import {authModel, roleAuth} from '@xlb/business-base/src/models/auth';
import {
  commonStyles,
  Header,
  Item,
  XIcon,
  XText,
} from '@xlb/common/src/components';
import {useNavigation} from '@react-navigation/native';
// import * as AliyunPush from 'aliyun-react-native-push'
import {colors, normalize} from '@xlb/common/src/config/theme';
import {XlbIconfont, XlbCard} from '@xlb/components-rn';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttp';
import XLBStorage from '@xlb/common/src/utils/storage';
import useRefreshStore from '@xlb/common/src/models/refresh';
import useAliCloudPush from '@xlb/common/src/hooks/useAliCloudPush';

/**
 * 首页
 * @constructor
 */
const Index: React.FC = () => {
  const navigation = useNavigation();
  const {initPush} = useAliCloudPush();
  const system = useEntranceStore((state: any) => state.system);
  const userInfos = authModel?.state?.userInfos;
  const [accountList, setAccountList] = useState<any>([]);
  const setRefresh = useRefreshStore((state: any) => state.setHomeRefresh);
  const getRole = async () => {
    const res = await ErpHttp.post<CommonResponse>(
      '/erp/hxl.erp.account.user.find',
      {tel: userInfos?.tel},
    );
    if (res.code === 0) {
      setAccountList(res.data.account_user_list);
    }
  };

  useEffect(() => {
    getRole();
  }, []);

  const accountChange = async (account: any, company_id: any, v: any) => {
    navigation.goBack();
    // 解决首页切换门店问题
    authModel
      .onLogin(account, company_id, undefined, undefined)
      .then(async () => {
        setRefresh();
        XLBStorage.removeItem('PointPlanAdd');
        XLBStorage.removeItem('BusinessDistrictAdd');
        XLBStorage.removeItem('NewStockAdjustOrderAdd');
      });
    // AliyunPush.unbindAccount().then(() => initPush())
  };
  return (
    <View>
      {/* <StatusBar  hidden={false} translucent={false} backgroundColor={'#fff'} /> */}
      <Header
        headerBgColor="#fff"
        centerComponent={
          <XText size16 semiBold>
            切换账户
          </XText>
        }
        leftComponent={
          <XIcon
            name={'back'}
            color="#000"
            onPress={() => {
              navigation.goBack();
            }}
          />
        }></Header>
      <View>
        {accountList.map((e: any) => {
          console.log('accountList', e);
          let isSelect =
            userInfos?.company_id + userInfos?.account ===
            e.company_id + e.account;
          return (
            <XlbCard
              title={`${e.company_id}｜${e.company_name}`}
              containerStyle={
                isSelect && {
                  borderColor: '#1A6AFF',
                  borderWidth: 1,
                }
              }
              onPress={() => {
                accountChange(e.account, e.company_id, e);
              }}>
              <>
                <View>
                  <Text style={styles['item-text']}>
                    {e?.store_name}-{e?.account}
                  </Text>
                </View>
                {isSelect && (
                  <View style={styles['select-icon']}>
                    <XlbIconfont
                      name="xuanzhong"
                      size={normalize(16)}
                      color="#1A6AFF"></XlbIconfont>
                  </View>
                )}
              </>
            </XlbCard>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  'select-icon': {
    position: 'absolute',
    right: normalize(12),
    top: '50%',
    marginTop: normalize(4),
  },
  'item-text': {
    color: 'rgba(30, 33, 38, 0.45)',
    fontSize: normalize(13),
  },
});

export default Index;
