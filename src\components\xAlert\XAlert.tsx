import React from 'react'
import { Overlay, XText } from '@xlb/common/src/components'
import XButtonGroup from './XButtonGroup'
import { View } from 'react-native'
import XAlertStyle from './XAlertStyle'

export default function XAlert(props: any) {
  const { show, title, content, onCancel, onConfirm, onClose, children, showButton = true, cancelText, confirmText, contanerStyle = {} } = props

  const _radius = 15

  const _cancel = () => {
    onCancel ? onCancel() : onClose && onClose(false)
  }

  const _confirm = () => {
    onConfirm ? onConfirm() : onClose && onClose(false)
  }

  return (
    <Overlay isVisible={show} onBackdropPress={_cancel}>
      <View>
        <View style={[XAlertStyle.Alerystyle, contanerStyle]}>
          <XText
            style={{
              color: '#111',
              fontSize: 16,
              fontWeight: 'bold',
              height: 40,
              top: 20,
            }}
          >
            {title}
          </XText>

          {children ? (
            children
          ) : (
            <View style={XAlertStyle.childrenStyle}>
              <XText style={{ textAlign: 'center', color: '#4E5969' }}>{content}</XText>
            </View>
          )}

          {showButton && <XButtonGroup
            borderBottomLeftRadius={_radius}
            borderBottomRightRadius={_radius}
            cancelText={cancelText}
            confirmText={confirmText}
            onCancel={_cancel}
            onConfirm={_confirm}
          />}
        </View>
      </View>
    </Overlay>
  )
}
