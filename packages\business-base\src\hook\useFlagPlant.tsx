import {useEffect} from 'react';
import {
  BackHandler,
  DeviceEventEmitter,
  NativeModules,
  Platform,
} from 'react-native';

// import {useProRoute} from '@xlb/common/src/hooks';
import {CommonActions} from '@react-navigation/native';
import useProRoute from './useProRoute';

export const LANDLORD_KEY = 'landlords';

export enum FLagPlantAction {
  // 商圈跟进
  BUSINESS_DISTRICT_FOLLOW_RECORD = 'BUSINESS_DISTRICT_FOLLOW_RECORD',
}

interface BaseOrgParams {
  org_id: number;
  org_name: string;
}

const FLAG_PLANT_MODULE_KEY = 'flagPlantMoudleKey__';

export const useFlagPLant = () => {
  const {navigation} = useProRoute();

  // 点位跟进
  const onBusinessDistrictFollowRecordEmit = (obj: {
    data: PointPlan & BaseOrgParams;
  }) => {
    const data =
      typeof obj.data === 'string' ? JSON.parse(obj.data) : obj?.data || {};

    // 传分享记录整条数据
    navigation.dispatch(
      CommonActions.navigate('RemoteAppSds.BusinessDistrictAddFollow', {
        ...data,
        isFromAndroid: true,
        [FLAG_PLANT_MODULE_KEY]:
          FLagPlantAction.BUSINESS_DISTRICT_FOLLOW_RECORD,
        getLists: () => {
          NativeModules?.FlagPlantManager?.goBack?.(
            FLagPlantAction.BUSINESS_DISTRICT_FOLLOW_RECORD,
            {
              ...data,
            },
          );
        },
      }),
    );
    // navigation.navigate(SdsRoutes[PointPlanRoute.EXPLORATION_CLOCK], { ...data, shareId: item?.id, refresh: getShard })
  };

  useEffect(() => {
    // 点位新增编辑

    // 商圈跟进
    const businessDistrictFlowRecordListener = DeviceEventEmitter.addListener(
      FLagPlantAction.BUSINESS_DISTRICT_FOLLOW_RECORD,
      onBusinessDistrictFollowRecordEmit,
    );

    return () => {
      businessDistrictFlowRecordListener.remove();
    };
  }, []);
};
