import Loading from '@xlb/common/src/components/RootView/Loading';
import {uploadFiles as uploadImageFile} from './upload';

import {
  types as DocumentPickerType,
  pick,
} from '@react-native-documents/picker';
import PermissionHandler from '@xlb/common/src/utils/PermissionHandler';
import {Platform, ToastAndroid} from 'react-native';
import Toast from 'react-native-root-toast';
import ImagePicker, {Options} from 'react-native-image-crop-picker';
// import { filesItem } from '@xlb/business-kms/src/screens/lookApplication/ as DocumentPickerType'
import {getDocmentTypes, getUrlMineType, videoSuffix} from './media';
import {isObject} from 'lodash';
const ONE_MB = 1024 * 1024;

const MAX_SIZE = 100;

const baseConfig = {
  cropping: false,
  compressImageMaxWidth: 1000,
  compressImageQuality: 0.7,
  cropperChooseText: '确认',
  cropperCancelText: '取消',
  includeBase64: true,
  maxFiles: 9,
};

// const MAX_MB = MAX_SIZE * ONE_MB

// ! 由于安卓的文件名不能包含特殊字符，所以需要进行编码，后端又不给处理只能自己处理
export const encodeFileName = (filename: string): string => {
  try {
    // 使用 Base64 编码完整文件名，保留原始信息
    const extension = filename.split('.').pop() || '';
    const nameWithoutExt = filename.slice(0, filename.lastIndexOf('.'));

    // 使用 Base64 编码原始文件名（不包含扩展名）
    const encodedName = Buffer.from(nameWithoutExt)
      .toString('base64')
      .replace(/\+/g, '-') // 替换 URL 不安全的字符
      .replace(/\//g, '_')
      .replace(/=/g, '');

    // 添加时间戳和原始扩展名
    return `${encodedName}.${extension}`;
  } catch (error) {
    console.error('Encode filename error:', error);
    const timestamp = new Date().getTime();
    return `file_${timestamp}.${filename.split('.').pop() || ''}`;
  }
};

export const decodeFileName = (encodedName: string): string => {
  try {
    const [baseName, extension] = encodedName.split('.');
    // 还原被替换的 Base64 字符
    const normalizedBase64 = baseName.replace(/-/g, '+').replace(/_/g, '/');

    // 添加可能缺失的 Base64 填充
    const padding = '='.repeat((4 - (normalizedBase64.length % 4)) % 4);
    const originalName = Buffer.from(
      normalizedBase64 + padding,
      'base64',
    ).toString();

    return `${originalName}.${extension}`;
  } catch {
    return encodedName;
  }
};

export const isEncodedFileName = (s: string): boolean => {
  if (s.indexOf('.') === -1) {
    return false;
  }

  const lastDotIndex = s.lastIndexOf('.');
  const prefix = s.slice(0, lastDotIndex);
  const suffix = s.slice(lastDotIndex + 1);

  // 新增校验：编码部分必须仅含 Base64 URL 安全字符（字母、数字、-、_）
  const isNormalCasePrefix = /^[A-Za-z0-9\-_]+$/.test(prefix);
  if (isNormalCasePrefix) {
    // 尝试还原为原始 Base64 并解码
    let base64 = prefix.replace(/-/g, '+').replace(/_/g, '/');
    const padLength = (4 - (base64.length % 4)) % 4;
    base64 += '='.repeat(padLength);

    try {
      const decodedBuffer = Buffer.from(base64, 'base64');
      // 关键优化：检查解码后的内容是否为有效的 UTF-8 字符串
      if (!isValidUTF8(decodedBuffer)) {
        return false;
      }
      return true;
    } catch (e) {
      // 解码失败则继续检查错误处理分支
    }
  }

  // 检查错误处理分支：前缀为 file_ 后接数字
  if (/^file_\d+$/.test(prefix)) {
    return true;
  }

  return false;
};

// 辅助函数：检查 Buffer 是否为有效 UTF-8 编码
const isValidUTF8 = (buffer: Buffer): boolean => {
  try {
    new TextDecoder('utf-8', {fatal: true}).decode(buffer);
    return true;
  } catch {
    return false;
  }
};

export type UploadConfig = Options & {
  suffix?: string[];
  maxSize?: number;
  customToast?: (msg: string) => void;
};

interface BaseUploadParams {
  url?: string;
  params?: Record<string, any>;
  uploadConfig?: UploadConfig;
  store_name: string;
  watermark: boolean;
}

interface UploadFileParams extends BaseUploadParams {
  // 类型来自 DocumentPicker.types 比如： DocumentPicker.types.images
  types?: string | string[];
}

const defaultToast = (msg = '最多上传十二个附件') => {
  Loading.hide();
  if (Platform.OS === 'android') {
    ToastAndroid.show(msg, ToastAndroid.LONG);
  } else {
    Toast.show(msg, {
      position: Toast.positions.TOP + 20,
    });
  }
};

const handleUploadImage = async ({
  url = '/erp/hxl.erp.file.upload',
  params,
  uploadConfig,
}: BaseUploadParams) => {
  const defaultSuffix = ['.jpg', '.jpeg', '.png', '.bmp'];
  const defaultConfig: UploadConfig = {
    multiple: true,
    mediaType: 'photo',
    includeBase64: true,
    cropping: false,
    maxFiles: 200,
    forceJpg: true,
    compressImageMaxWidth: 1000,
    compressImageQuality: 0.7,
    suffix: defaultSuffix,
  };

  const {
    suffix = defaultSuffix,
    maxSize = MAX_SIZE,
    customToast,
    ...rest
  } = uploadConfig || defaultConfig;
  const toast = customToast || defaultToast;
  const res: any =
    Platform.OS === 'ios'
      ? await ImagePicker.openPicker({
          ...rest,
        }).catch(() => {
          return null;
        })
      : await pick({
          type: getDocmentTypes(suffix) || [DocumentPickerType.images],
          allowMultiSelection: true,
        });

  if (!res) return;

  Loading.show();

  console.log(res, 'res===>', suffix);

  const finalSuffix = suffix?.map(item =>
    item?.startsWith('.') ? item : '.' + item,
  );

  const fileList = Array.isArray(res) ? res : isObject(res) ? [res] : [];

  const maxMb = maxSize * ONE_MB;

  const result = await Promise.all(
    fileList?.map(async (image: any) => {
      const mimeType = image?.mime || image?.type;
      const fileType = mimeType?.replace(/.*\//, '.');
      console.log(fileType, 'fileType');

      if (!finalSuffix.includes(fileType)) {
        Loading.hide();
        toast(`请上传 ${suffix.join('、')} 格式的文件`);
        return null;
      }
      if (image.size > maxMb) {
        // setLoading(false)
        Loading.hide();

        toast(`最大上传支持${maxSize}MB文件`);
        return null;
      }

      const filename =
        image?.name ||
        image?.filename ||
        new Date().getTime() + `.${mimeType.split('/')[1]}`;

      image.filename = encodeFileName(filename);
      // const url: any = await XLBUpload.uploadImage(image, Date.now())

      const uploadResult = await uploadImageFile(
        image,
        {refType: 'ITEM_Image', refId: Date.now(), ...(params || {})},
        Date.now(),
        url,
      ).catch(() => null);

      if (uploadResult) {
        return {
          ...uploadResult,
          decodeName: decodeFileName(uploadResult?.name),
        } as filesItem;
      } else {
        Loading.hide();
        toast('上传失败');
        return uploadResult;
      }
    }),
  );

  const urls = result.filter(Boolean);

  Loading.hide();
  return urls;

  // setLoading(false)
};

const getCameraSuffix = (uploadConfig: UploadConfig) => {
  const defaultSuffix = ['.jpg', '.jpeg', '.png', '.bmp'];

  const customUploadConfig = {
    ...baseConfig,
    ...uploadConfig,

    suffix: defaultSuffix,
  };

  if (uploadConfig?.suffix) {
    customUploadConfig.suffix = uploadConfig?.suffix;
  } else if (uploadConfig?.mediaType === 'video') {
    customUploadConfig.suffix = videoSuffix;
  } else if (uploadConfig?.mediaType === 'any') {
    customUploadConfig.suffix = videoSuffix.concat(defaultSuffix);
  }

  return customUploadConfig;
};

// 时间紧任务重，先C一份
const handleUploadImageCamera = async ({
  url = '/erp/hxl.erp.file.upload',
  params,
  uploadConfig,
  ...restParams
}: BaseUploadParams) => {
  const defaultSuffix = [
    '.jpg',
    '.jpeg',
    '.png',
    '.bmp',
    '.PNG',
    '.JPG',
    '.JPEG',
  ];

  const defaultConfig: UploadConfig = {
    ...baseConfig,
    multiple: true,
    mediaType: 'photo',
    suffix: defaultSuffix,
  };

  const {
    suffix = defaultSuffix,
    maxSize = MAX_SIZE,
    customToast,
    ...rest
  } = uploadConfig ? getCameraSuffix(uploadConfig) : defaultConfig;
  const toast = customToast || defaultToast;
  const maxMb = maxSize * ONE_MB;

  let res: any = await ImagePicker.openCamera(rest).catch(() => {
    return [];
  });

  res = !Array.isArray(res) && !!res ? [res] : res;

  Loading.show();

  const finalSuffix = suffix?.map(item =>
    item?.startsWith('.') ? item : '.' + item,
  );

  const result = await Promise.all(
    res?.map(async (image: any) => {
      const mimeType = image?.mime || image?.type;
      const fileType = mimeType?.startsWith('application')
        ? getUrlMineType(image?.name || image?.filename)
        : mimeType?.replace(/.*\//, '.');
      console.log(fileType, 'fileType');

      if (!finalSuffix.includes(fileType)) {
        Loading.hide();
        toast(`请上传 ${suffix.join('、')} 格式的文件`);
        return null;
      }

      console.log(image.size, 'imageSize');

      if (image.size > maxMb) {
        // setLoading(false)
        Loading.hide();

        toast(`最大上传支持${maxSize}MB文件`);
        return null;
      }

      // ios上传视频的时候存在filename 跟mimeType类型不一致的情况，所以需要单独处理一下文件名称

      const origiFileName = image?.name || image?.filename;

      const formatFileName = !!origiFileName
        ? origiFileName.slice(0, origiFileName.lastIndexOf('.')) + fileType
        : origiFileName;

      const filename =
        formatFileName || new Date().getTime() + `.${mimeType.split('/')[1]}`;
      image.filename = encodeFileName(filename);
      // const url: any = await XLBUpload.uploadImage(image, Date.now())
      const {store_name, watermark} = restParams;
      // if (watermark) {
      //   image.path = await addWatermark(image.path, {
      //     store_name,
      //     imgWidth: image.width,
      //   })
      // }
      const uploadResult = await uploadImageFile(
        image,
        {
          refType: 'ITEM_Image',
          refId: Date.now(),
          ref_type: 'ITEM_Image',
          ...(params || {}),
          refId: Date.now(),
        },
        Date.now(),
        url,
      ).catch(err => {
        console.log(err, 'err');
        return null;
      });

      console.log(uploadResult, 'uploadResult');
      if (!uploadResult) {
        Loading.hide();
        toast('上传失败');
        return null;
      }

      // setLoading(false

      if (!uploadResult) {
        Loading.hide();
        toast('上传失败');
        return uploadResult;
      }

      return {
        ...uploadResult,
        decodeName: decodeFileName(uploadResult?.name),
      } as filesItem;
    }),
  );

  console.log(result, 'resut===>');

  const urls = result.filter(Boolean);

  Loading.hide();
  return urls;

  // setLoading(false)
};

/**
 *
 *如果ios调用有问题，请加个setTimeout
 * @param {UploadFileParams} params
 * @return {*}
 */
const uploadFiles = async (params: UploadFileParams) =>
  handleUploadFiles(params);
export const uploadImages = async (params: BaseUploadParams) =>
  handleUploadImage(params);
export const uploadImagesByCamera = async (params: BaseUploadParams) =>
  handleUploadImageCamera(params);

export const XlbUpload = {
  uploadFiles,
  uploadImages,
  uploadImagesByCamera,
};

const handleUploadFiles = async ({
  types = [
    DocumentPickerType.images,
    DocumentPickerType.docx,
    DocumentPickerType.doc,
    DocumentPickerType.pdf,
  ],
  url = '/erp/hxl.erp.file.upload',
  params,
  uploadConfig,
}: UploadFileParams) => {
  const toast = uploadConfig?.customToast || defaultToast;
  const maxSize = uploadConfig?.maxSize || MAX_SIZE;
  const maxMb = maxSize * ONE_MB;

  const res = await pick({
    type: uploadConfig?.suffix ? getDocmentTypes(uploadConfig.suffix) : types,
    allowMultiSelection: true,
    // mode: 'import',

    // copyTo: 'cachesDirectory',
  }).catch(() => {
    return null;
  });

  if (!res) return;

  Loading.show();

  const result = await Promise.all(
    res?.map(async (image: any) => {
      console.log(res, 'res===>');
      // 兼容：如果cdr文件是 图片类型，转换成 文件类型
      const mimeType =
        image?.type == 'image/x-coreldraw'
          ? 'application/x-coreldraw'
          : image?.mime || image?.type;

      const fileType = mimeType?.startsWith('application')
        ? getUrlMineType(image?.name || image?.filename)
        : mimeType?.replace(/.*\//, '.');

      const filename =
        image?.name ||
        image?.filename ||
        new Date().getTime() + `.${mimeType.split('/')[1]}`;

      image.filename = encodeFileName(filename);

      // const url: any = await XLBUpload.uploadIparamsmage(image, Date.now())
      if (uploadConfig?.suffix) {
        const finalSuffix = uploadConfig?.suffix?.map(item =>
          item?.startsWith('.') ? item : '.' + item,
        );
        if (!finalSuffix.includes(fileType)) {
          Loading.hide();
          toast(`请上传 ${uploadConfig?.suffix.join('、')} 格式的文件`);
          return null;
        }
      }
      if (image.size > maxMb) {
        // setLoading(false)
        Loading.hide();

        toast(`最大上传支持${maxSize}M文件`);
        return null;
      }

      const uploadResult = await uploadImageFile(
        image,
        {refType: 'ITEM_Image', refId: Date.now(), ...(params || {})},
        Date.now(),
        url,
      ).catch(() => null);

      console.log(uploadResult, 'uploadResult');

      return {
        ...uploadResult,
        decodeName: decodeFileName(uploadResult?.name),
      } as filesItem;
    }),
  ).finally(() => {
    Loading.hide();
  });

  const urls = result.filter(Boolean);

  Loading.hide();
  return urls;
};

/**
 * @desc 支持上传图片和文件
 * @desc 支持自定义toast提示
 * @desc 支持自定义上传文件类型
 * @desc 支持自定义上传文件大小
 * @class XlbUploadKit
 * @example
 * const uploadKit = new XlbUploadKit()
 * uploadKit.uploadFiles({ url: '/erp/hxl.erp.file.upload', params: { refType: 'ITEM_Image', refId: Date.now() } })
 *
 *
 *
 */
export class XlbUploadKit {
  private maxSize: number = 100;
  private customToast?: (msg: string) => void;

  constructor(config?: {maxSize?: number; toast?: (msg: string) => void}) {
    if (config?.maxSize) this.maxSize = config.maxSize;
    if (config?.toast) this.customToast = config.toast;
  }

  setMaxSize(sizeMB: number) {
    this.maxSize = sizeMB;
    return this;
  }

  setToast(toastFn: (msg: string) => void) {
    this.customToast = toastFn;
    return this;
  }

  private wrapParams<T extends BaseUploadParams>(params: T): T {
    return {
      ...params,
      uploadConfig: {
        ...params.uploadConfig,
        maxSize: this.maxSize,
        customToast: this.customToast,
      },
    };
  }

  async uploadFiles(params: UploadFileParams) {
    try {
      return await uploadFiles(this.wrapParams(params));
    } catch {
      return [];
    } finally {
      Loading.hide();
    }
  }

  async uploadImages(params: BaseUploadParams) {
    try {
      return await uploadImages(this.wrapParams(params));
    } catch {
      return [];
    } finally {
      Loading.hide();
    }
  }

  async uploadImagesByCamera(params: BaseUploadParams) {
    try {
      return await uploadImagesByCamera(this.wrapParams(params));
    } catch {
      return [];
    } finally {
      Loading.hide();
    }
  }
}
