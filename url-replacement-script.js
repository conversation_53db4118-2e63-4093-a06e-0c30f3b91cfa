#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 配置
const CONFIG = {
  apiMappingFile: './api-mapping.json',
  outputLogFile: './url-replacement-log.json',
  batchSize: 100,
  fileExtensions: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx', '**/*.json'],
  excludePatterns: [
    'node_modules/**',
    'dist/**',
    'build/**',
    '.git/**',
    'coverage/**',
    '*.log',
    'url-replacement-*.js',
    'url-replacement-*.json'
  ]
};

class URLReplacer {
  constructor() {
    this.apiMapping = {};
    this.replacementLog = {
      summary: {
        totalFiles: 0,
        modifiedFiles: 0,
        totalReplacements: 0,
        startTime: new Date().toISOString(),
        endTime: null
      },
      fileDetails: [],
      errors: []
    };
  }

  // 加载API映射
  loadApiMapping() {
    try {
      const content = fs.readFileSync(CONFIG.apiMappingFile, 'utf8');
      this.apiMapping = JSON.parse(content);
      console.log(`✅ 加载了 ${Object.keys(this.apiMapping).length} 个API映射`);
    } catch (error) {
      console.error('❌ 加载API映射失败:', error.message);
      process.exit(1);
    }
  }

  // 获取所有需要处理的文件
  getAllFiles() {
    const allFiles = [];
    
    CONFIG.fileExtensions.forEach(pattern => {
      const files = glob.sync(pattern, {
        ignore: CONFIG.excludePatterns,
        nodir: true
      });
      allFiles.push(...files);
    });

    // 去重
    const uniqueFiles = [...new Set(allFiles)];
    console.log(`📁 找到 ${uniqueFiles.length} 个文件需要处理`);
    return uniqueFiles;
  }

  // 创建正则表达式模式
  createRegexPatterns(originalPath) {
    const escapedPath = originalPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    
    return [
      // 1. 单引号字符串 '/erp/path' 或 '/erp/path?query'
      new RegExp(`'(${escapedPath})(\\?[^']*)?'`, 'g'),
      
      // 2. 双引号字符串 "/erp/path" 或 "/erp/path?query"
      new RegExp(`"(${escapedPath})(\\?[^"]*)?\"`, 'g'),
      
      // 3. 模板字符串 `/erp/path` 或 `/erp/path?query`
      new RegExp(`\`([^}]*)(${escapedPath})(\\?[^\`]*)?\``, 'g'),
      
      // 4. 变量拼接 ${var}/erp/path 或 ${var}/erp/path?query
      new RegExp(`(\\$\\{[^}]+\\})(${escapedPath})(\\?[^\\s'"}\`]*)?`, 'g'),
      
      // 5. 对象属性值 url: /erp/path 或 path: /erp/path
      new RegExp(`(:\\s*)(${escapedPath})(\\?[^\\s,}]*)?`, 'g'),
      
      // 6. 不在引号中的路径片段
      new RegExp(`(^|\\s|=|:)(${escapedPath})(\\?[^\\s'"}\`]*)?(?=\\s|$|,|})`, 'gm')
    ];
  }

  // 处理单个文件
  processFile(filePath) {
    try {
      const originalContent = fs.readFileSync(filePath, 'utf8');
      let modifiedContent = originalContent;
      let fileReplacements = [];
      let hasChanges = false;

      // 遍历所有API映射
      Object.entries(this.apiMapping).forEach(([originalPath, newPath]) => {
        const patterns = this.createRegexPatterns(originalPath);
        
        patterns.forEach((pattern, patternIndex) => {
          const matches = [...modifiedContent.matchAll(pattern)];
          
          matches.forEach(match => {
            const fullMatch = match[0];
            let replacement;
            
            switch (patternIndex) {
              case 0: // 单引号
                replacement = fullMatch.replace(originalPath, newPath);
                break;
              case 1: // 双引号
                replacement = fullMatch.replace(originalPath, newPath);
                break;
              case 2: // 模板字符串
                replacement = fullMatch.replace(originalPath, newPath);
                break;
              case 3: // 变量拼接
                replacement = fullMatch.replace(originalPath, newPath);
                break;
              case 4: // 对象属性
                replacement = fullMatch.replace(originalPath, newPath);
                break;
              case 5: // 路径片段
                replacement = fullMatch.replace(originalPath, newPath);
                break;
            }
            
            if (replacement && replacement !== fullMatch) {
              modifiedContent = modifiedContent.replace(fullMatch, replacement);
              hasChanges = true;
              
              fileReplacements.push({
                pattern: `Pattern ${patternIndex + 1}`,
                original: fullMatch,
                replacement: replacement,
                line: this.getLineNumber(originalContent, match.index)
              });
            }
          });
        });
      });

      // 如果有修改，写入文件
      if (hasChanges) {
        fs.writeFileSync(filePath, modifiedContent, 'utf8');
        this.replacementLog.summary.modifiedFiles++;
        this.replacementLog.summary.totalReplacements += fileReplacements.length;
        
        this.replacementLog.fileDetails.push({
          filePath,
          replacementCount: fileReplacements.length,
          replacements: fileReplacements
        });
        
        console.log(`✅ ${filePath}: ${fileReplacements.length} 个替换`);
      }

      this.replacementLog.summary.totalFiles++;
      return hasChanges;
      
    } catch (error) {
      const errorMsg = `处理文件 ${filePath} 时出错: ${error.message}`;
      console.error(`❌ ${errorMsg}`);
      this.replacementLog.errors.push(errorMsg);
      return false;
    }
  }

  // 获取行号
  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  // 分批处理文件
  async processBatch(files, batchIndex, totalBatches) {
    console.log(`\n🔄 处理批次 ${batchIndex + 1}/${totalBatches} (${files.length} 个文件)`);
    
    let batchModified = 0;
    for (const file of files) {
      if (this.processFile(file)) {
        batchModified++;
      }
    }
    
    console.log(`✅ 批次 ${batchIndex + 1} 完成: ${batchModified} 个文件被修改`);
    
    // 添加小延迟避免内存压力
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // 主处理函数
  async run() {
    console.log('🚀 开始URL路径替换任务...\n');
    
    // 加载API映射
    this.loadApiMapping();
    
    // 获取所有文件
    const allFiles = this.getAllFiles();
    
    // 分批处理
    const batches = [];
    for (let i = 0; i < allFiles.length; i += CONFIG.batchSize) {
      batches.push(allFiles.slice(i, i + CONFIG.batchSize));
    }
    
    console.log(`📦 将处理 ${batches.length} 个批次\n`);
    
    // 处理每个批次
    for (let i = 0; i < batches.length; i++) {
      await this.processBatch(batches[i], i, batches.length);
    }
    
    // 完成处理
    this.replacementLog.summary.endTime = new Date().toISOString();
    
    // 保存日志
    fs.writeFileSync(CONFIG.outputLogFile, JSON.stringify(this.replacementLog, null, 2));
    
    // 输出总结
    this.printSummary();
  }

  // 打印总结
  printSummary() {
    const { summary } = this.replacementLog;
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 URL替换任务完成总结');
    console.log('='.repeat(60));
    console.log(`📁 总文件数: ${summary.totalFiles}`);
    console.log(`✏️  修改文件数: ${summary.modifiedFiles}`);
    console.log(`🔄 总替换次数: ${summary.totalReplacements}`);
    console.log(`⏱️  开始时间: ${summary.startTime}`);
    console.log(`⏱️  结束时间: ${summary.endTime}`);
    console.log(`❌ 错误数量: ${this.replacementLog.errors.length}`);
    console.log(`📄 详细日志: ${CONFIG.outputLogFile}`);
    console.log('='.repeat(60));
    
    if (this.replacementLog.errors.length > 0) {
      console.log('\n❌ 错误列表:');
      this.replacementLog.errors.forEach(error => console.log(`  - ${error}`));
    }
  }
}

// 运行脚本
if (require.main === module) {
  const replacer = new URLReplacer();
  replacer.run().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = URLReplacer;
