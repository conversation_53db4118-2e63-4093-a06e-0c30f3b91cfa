import React, {useState, useRef, useEffect} from 'react';
import type {FC} from 'react';

import {
  View,
  TextInput,
  StyleSheet,
  Pressable,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import {useRoute, useNavigation} from '@react-navigation/native';

import Toast from 'react-native-root-toast';
import trim from 'lodash/trim';

import {XlbText, TOKEN} from '@xlb/components-rn';

import Loading from '@xlb/common/src/components/RootView/Loading';
import {XlbHeader} from '@xlb/common/src/xlb-components-new';
import {normalize} from '@xlb/components-rn/styles';

import {authModel} from '@xlb/business-base/src/models/auth';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttp';

import {numReg} from './define';

import {modifyPhoneStyle, ComponentStyle} from './style';

const Index: FC = () => {
  const route = useRoute();
  const navigation = useNavigation();

  const phoneNumber = route.params?.tel
    ? route.params?.tel?.replace(/\s+/g, '')
    : '';
  const routkey = route.params?.routkey ? route.params?.routkey : '';

  const [loading, setLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60); // 时间
  const [captcha, setCaptcha] = useState(''); // 验证码

  const inputRef = useRef<TextInput>(null);

  const getCaptcha = async () => {
    if (loading) return;

    inputRef.current?.focus();
    setLoading(true);
    Loading.show();

    const res = await ErpHttp.post<CommonResponse>(
      routkey == 'ChangePhoneNumber'
        ? '/erp/hxl.erp.user.changetelcodechenk.send'
        : '/erp/hxl.erp.user.resetcode.send',
      {
        account: authModel?.state?.userInfos?.account,
        tel: phoneNumber,
      },
    );

    if (res?.code === 0) {
      let time = 59;
      const interval = setInterval(() => {
        setTimeLeft(time);
        time -= 1;
        if (time < 0) {
          clearInterval(interval);
          setLoading(false);
          setTimeLeft(60);
        }
      }, 1000);

      Toast.show(
        `验证码已发送到尾号为${phoneNumber.substring(phoneNumber.length - 4)}的手机上`,
        {position: Toast.positions.CENTER},
      );
    } else {
      Toast.show(`验证码发送失败`, {position: Toast.positions.CENTER});
    }
  };

  const nextStep = async () => {
    if (routkey == 'ChangePhoneNumber' && captcha.length) {
      const res = await ErpHttp.post<CommonResponse>(
        '/erp/hxl.erp.user.changetelcodechenk.confirm',
        {
          code: captcha,
        },
      );
      if (res?.code === 0) {
        navigation.navigate('ModifyPhone', {tel: phoneNumber});
      } else {
        // Toast.show(`验证码错误`, { position: Toast.positions.CENTER })
      }
    } else if (captcha.length) {
      navigation.navigate('ModifyPassword', {tel: phoneNumber, code: captcha});
    }
  };

  useEffect(() => {
    getCaptcha();
  }, []);

  return (
    <TouchableWithoutFeedback
      onPress={() => {
        Keyboard.dismiss();
      }}>
      <View style={modifyPhoneStyle.body}>
        <XlbHeader
          title="验证手机号"
          hasInputFilter={false}
          rightContent={
            <XlbText
              font_size_5
              style={{color: captcha.length ? TOKEN.primary_10 : '#96C5FF'}}
              onPress={nextStep}>
              下一步
            </XlbText>
          }
        />

        <View style={modifyPhoneStyle.content}>
          <XlbText font_size_4 grey_7 style={{marginBottom: TOKEN.space_3}}>
            短信验证码已发送，请填写4位数验证码
          </XlbText>

          <View
            style={StyleSheet.flatten([
              ComponentStyle.card,
              {marginBottom: normalize(48)},
            ])}>
            <View style={{paddingLeft: TOKEN.space_3}}>
              <View style={ComponentStyle.card_item}>
                <XlbText
                  font_size_5
                  style={{marginRight: TOKEN.space_1, color: TOKEN.grey_10}}>
                  +86 {route.params?.tel}
                </XlbText>
              </View>
              <View
                style={StyleSheet.flatten([
                  ComponentStyle.card_item,
                  {borderBottomWidth: 0},
                ])}>
                <TextInput
                  keyboardType="numeric"
                  ref={inputRef}
                  placeholder="验证码"
                  placeholderTextColor={TOKEN.grey_25}
                  style={{
                    fontSize: normalize(15),
                    padding: 0,
                    flex: 1,
                    color: TOKEN.grey_10,
                  }}
                  onChangeText={text => {
                    setCaptcha(trim(text));
                  }}
                />
                <Pressable
                  style={{height: '100%', justifyContent: 'center'}}
                  onPress={getCaptcha}>
                  <XlbText
                    font_size_5
                    style={{color: loading ? TOKEN.grey_25 : TOKEN.primary_10}}>
                    {loading ? `${timeLeft}秒后重试` : '获取验证码'}
                  </XlbText>
                </Pressable>
              </View>
            </View>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default Index;
