import React, {useEffect, useMemo} from 'react';
import {
  // AppState,
  // NativeEventEmitter,
  NativeModules,
  Platform,
  StatusBar,
  StyleSheet,
  UIManager,
  View,
  // DeviceEventEmitter,
  LogBox,
  // View,
  // Text,
  // DevSettings,
  // ToastAndroid,
  // Alert,
} from 'react-native';

import '../packages/common/src/models/store';
import '@/store/deprecatedNewSelectGoodsStore.tsx';
import '@/store/deprecatedSelectGoodsStore.tsx';
import {FocaProvider} from 'foca';
import {NativeBaseProvider} from 'native-base';
import {baseTheme} from '@xlb/common/src/config/baseTheme';
import Config from 'react-native-config';
import Navigation from '@xlb/common/src/navigation';
import SplashScreen from 'react-native-splash-screen';
import {RootSiblingParent} from 'react-native-root-siblings';
import {SafeAreaProvider} from 'react-native-safe-area-context';
// import { isFirstTime, isRolledBack, markSuccess } from 'react-native-update'
import NP from 'number-precision';
// import Toast from 'react-native-root-toast';
import '@xlb/common/src/components/RootView';
import {useFlagPLant} from './hook/useFlagPlant';
import {
  disabledFontScale,
  setCustomText,
} from '@xlb/common/src/utils/textStyle';
// import dayjs from 'dayjs';
// import weekday from 'dayjs/plugin/weekday';
// import customParseFormat from 'dayjs/plugin/customParseFormat';
// import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
// import weekOfYear from 'dayjs/plugin/weekOfYear';
// import isoWeeksInYear from 'dayjs/plugin/isoWeeksInYear';
// import isLeapYear from 'dayjs/plugin/isLeapYear';
// import isoWeek from 'dayjs/plugin/isoWeek';
// import weekYear from 'dayjs/plugin/weekYear';
// import advancedFormat from 'dayjs/plugin/advancedFormat';
// import quarterOfYear from 'dayjs/plugin/quarterOfYear';
// import 'dayjs/locale/zh-cn'; // 配置中文本地化
// import useInitMap from './useInitMap'
// import XlbUpdate from '@xlb/common/src/components/features/xlbUpdate';
// import useInitStorage from '@xlb/common/src/hooks/useInitStorage';
import useLogOut from './useLogOut';
// import useBack from './useBack'
// import XlbAllTips from '@xlb/business-base/src/components/xlbAllTips';
// import {authModel} from '@xlb/business-base/src/models/auth';
import {Provider as AntProvieder} from '@ant-design/react-native';
import {Provider} from '@fruits-chain/react-native-xiaoshu';
import 'react-native-reanimated';

// import XLBStorage from '@xlb/common/src/utils/storage';
// import useBlueSetting from '@xlb/business-base/src/models/useBlueSetting';
//
// import {getToken} from '@xlb/common/src/services/auth';

// import useInitEmas from "@xlb/common/src/hooks/useInitEmas";
import {useSentry} from './useSentry';
import '@sentry/react-native';
// // import useInitEmas from "@xlb/common/src/hooks/useInitEmas";

// Sentry.init({
//   dsn: 'https://<EMAIL>/4507122562957312',
//   tracesSampleRate: 1.0,
// })
// import BluetoothClassic from 'react-native-bluetooth-classic'
// import useClearStorage from './useClearStorage'
// import useAppState from 'react-native-appstate-hook';

import DeviceInfo from 'react-native-device-info';
import {TOKEN, XlbAppProvider, themeObject} from '@xlb/components-rn';
import {normalize} from '@xlb/common/src/config/theme';
import useSystemStore from '@xlb/common/src/models/system';
import Loading from '@xlb/common/src/components/RootView/Loading';
import XlbAllTips from '@xlb/business-base/src/components/xlbAllTips';
import {ScreenShotCtr} from './ScreenShotCtr';
import {useInitMap} from '@xlb/common/src/hooks/useInitMap.tsx';
import XlbUpdate from '@/components/xlbUpdate';
// import { useFlagPLant } from '@xlb/business-kms/src/screens/newFlagPlant/hooks/useFlagPlant'
// import FastImage from 'react-native-fast-image';
// dayjs.extend(quarterOfYear);
// dayjs.extend(advancedFormat);
// dayjs.extend(weekYear);
// dayjs.extend(isoWeeksInYear);
// dayjs.extend(isoWeek);
// dayjs.extend(isLeapYear);
// dayjs.extend(weekOfYear);
// dayjs.extend(isSameOrAfter);
// dayjs.extend(weekday); // 使用Weekday插件
// dayjs.extend(customParseFormat);
// dayjs.locale('zh-cn'); // 设置本地化为中文
const brand = DeviceInfo.getBrand(); // 手机品牌

//日志打印
// FileLogger.configure({
//   maximumFileSize: 1024 * 1024 * 5, //5MB,
//   maximumNumberOfFiles: 3,
// }).then(() => console.log('File-logger configured'));

// Android 上面使用此动画
if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
// 关闭数字溢出提示 https://github.com/nefe/number-precision
NP.enableBoundaryChecking(false);

//适配深色模式
setCustomText();
disabledFontScale();

// 在现有导入后添加
// import '../ReactotronConfig';
setTimeout(() => {
  LogBox.ignoreAllLogs();
});
const App: React.FC = () => {
  console.log(Config.ENV, '----------------config.APP_NAME');

  // 添加 Reactotron 调试信息
  if (__DEV__) {
    require('../ReactotronConfig');
    console.tron.log('App component mounted');
  }
  // const ScanFeasyBlue = NativeModules.ScanFeasyBlue;
  // const {connectItem, setConnectItem, setIsPrint, printType, setIsPowerOff} =
  //   useBlueSetting((state: any) => state);
  const {theme, setTheme, themeName, setThemeName} = useSystemStore(
    (state: any) => state,
  );
  // const [imageLoaded, setImageLoaded] = useState(true);
  // const [isRouterLoaded, setIsRouterLoaded] = useState(true);
  // if (isFirstTime) {
  //   markSuccess()
  //   XLBStorage.getItem('refreshAuth').then((res) => {
  //     if (res) {
  //       const obj = JSON.parse(res)
  //       if (authModel?.state?.userInfos?.access_token) authModel.setUserInfos({ ...authModel?.state?.userInfos, authorities: obj })
  //       XLBStorage.removeItem('refreshAuth') // 设置完成后删除缓存
  //     }
  //   })
  //   getToken().then((res) => {
  //     XlbUpdate.uploadAppVersion('HOT', res?.currentToken || authModel?.state?.userInfos?.access_token)
  //   })
  // }
  console.disableYellowBox = true;
  useEffect(() => {
    //   let subscription: any = null;

    //   let subscription: any = null;

    //   if (Platform.OS === 'android') {
    //     subscription = BluetoothClassic.onDeviceDisconnected(e => {
    //       XLBStorage.getItem('blueConnect').then(res => {
    //         if (
    //           res &&
    //           printType !== null &&
    //           printType !== undefined &&
    //           e.id === res[printType].id
    //         ) {
    //           Toast.show('打印机已断开连接，请检查打印机状态');
    //           setIsPowerOff(true);
    //           // setConnectItem({})
    //         }
    //       });
    //     });
    //   }

    //   if (Platform.OS === 'android') {
    //   } else {
    //     // ios得提前注册扫描事件
    //     // const emitter = new NativeEventEmitter(ScanFeasyBlue)
    //     // emitter.addListener('onEvent', () => {})
    //   }

    //   // XlbUpdate.updateVersion('home').then()

    //   const eventListener = AppState.addEventListener('change', state => {
    //     if (state === 'active') {
    //       // 从前台进入时请求一次, 获取kms是否还有合同未签署
    //       XlbAllTips.getIsNeedDialog(authModel.state.userInfos.tel);
    //     }
    //   });

    //   setTimeout(() => {
    //     // 杀后台时请求一次
    //     XlbAllTips.getIsNeedDialog(authModel.state.userInfos.tel);
    //   }, 6000);

    //   // DevSettings.addMenuItem('切换主题', () => {
    //   //   setTheme()
    //   // })
    //   console.log('app启动完成开始');
    let splashScreenTimer = setTimeout(() => {
      console.log('app启动完成，调用隐藏方法');
      SplashScreen.hide();
      console.log('app启动完成,已调用隐藏方法');
    }, 5);

    //   const timer = setTimeout(() => {
    //     isRouterLoaded || setIsRouterLoaded(true);
    //   }, 8000);

    //   const SplashScreenMessage = DeviceEventEmitter.addListener(
    //     'SplashScreen',
    //     () => {
    //       console.log('app路由加载完成');
    //       setIsRouterLoaded(true);
    //     },
    //   );

    return () => {
      //     clearTimeout(timer);
      //     SplashScreenMessage.remove();
      splashScreenTimer && clearTimeout(splashScreenTimer);
      //     if (subscription) {
      //       subscription.remove();
      //     }
      //     eventListener.remove();
    };
  }, []);

  // 地图组件初始化
  // useInitMap()

  // 小红点显示逻辑
  // useInitStorage();

  // 过期时自动退出登录
  useLogOut();
  useInitMap();

  // 阿里崩溃日志分析
  // useInitEmas()
  // //监听返回事件
  // useBack()

  // 存储当前的位置信息
  // useGetPosition()
  // useSentry();

  // 存储控制截屏信息
  ScreenShotCtr.useScreenShotCtrServer();
  // useClearStorage()
  // useAppState({
  //   onChange: newAppState => {
  //     if (
  //       newAppState.match(/inactive|background/) &&
  //       Platform.OS === 'android'
  //     ) {
  //       // App进入后台运行
  //       ToastAndroid.show('新零帮已切换到后台运行', ToastAndroid.SHORT);
  //     }
  //     if (Platform.OS == 'android' && brand && brand == 'Honeywell') {
  //       // if (Platform.OS == 'android') {
  //       if (newAppState.match(/inactive|background/)) {
  //         ScanService.onPause();
  //       } else {
  //         ScanService.onResume();
  //       }
  //     }
  //   },
  // });

  const themeNameState = useMemo(
    () => ({
      theme: themeName ? themeObject[themeName] : themeObject?.default,
      themeName,
    }),
    [themeName],
  );

  // useEffect(() => {
  //   StatusBar.setHidden(false);
  // }, []);

  return (
    <View style={styles.container}>
      <StatusBar
        translucent
        hidden={false}
        animated={true}
        backgroundColor={'transparent'}
      />
      {/* {!isRouterLoaded && (
        <View
          style={{
            width: '100%',
            height: '100%',
            backgroundColor: '#fff',
            display: 'flex',
          }}>
          <FastImage
            resizeMode="contain"
            source={
              imageLoaded
                ? {
                    cache: FastImage.cacheControl.web,
                    uri: `https://hzlapp.oss-cn-hangzhou.aliyuncs.com/jieqi/${dayjs().format(
                      'YYYYMMDD',
                    )}.jpg`,
                  }
                : require('@xlb/common/src/assets/images/3.png')
            }
            // onLoad={() => setImageLoaded(true)} // 当图片成功加载时调用
            onError={() => {
              setImageLoaded(false);
            }}
            // defaultSource={require('../assets/images/3.png')} // or use require('./path/to/local/image.png') for local images
            style={{width: '100%', height: '100%'}}></FastImage>
        </View>
      )} */}

      <NativeBaseProvider theme={baseTheme}>
        <XlbAppProvider.Provider value={themeNameState}>
          <FocaProvider>
            <AntProvieder>
              <Provider
                theme={{
                  cell_icon_color: '#C7C7C9',
                  checkbox_icon_color: '#C9CDD4',
                  checkbox_icon_disabled_color: '#C9CDD4',
                  action_sheet_text_color: '#1D2129',
                  toast_border_radius: TOKEN.space_1,
                  toast_inner_width: 'auto',
                  toast_inner_min_height: normalize(100),
                  dialog_confirm_button_text_color:
                    themeObject[themeName]?.primary6,
                  badge_dot_size: normalize(6),
                  text_input_color: '#1D2129',
                  text_input_s_font_size: 15,
                }}>
                <RootSiblingParent>
                  <SafeAreaProvider>
                    <Navigation />
                  </SafeAreaProvider>
                </RootSiblingParent>
              </Provider>
            </AntProvieder>
          </FocaProvider>
        </XlbAppProvider.Provider>
      </NativeBaseProvider>
      <Loading />
      <XlbAllTips />
      <XlbUpdate />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
});

export default App;
