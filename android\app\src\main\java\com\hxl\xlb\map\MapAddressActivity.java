package com.hxl.xlb.map;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.PointF;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.VectorDrawable;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.animation.LinearInterpolator;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.amap.api.maps.AMap;
import com.amap.api.maps.AMapUtils;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.LocationSource;
import com.amap.api.maps.MapView;
import com.amap.api.maps.Projection;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.maps.model.MyLocationStyle;
import com.amap.api.maps.model.Polygon;
import com.amap.api.maps.model.PolygonOptions;
import com.amap.api.maps.model.Polyline;
import com.amap.api.maps.model.PolylineOptions;
import com.amap.api.maps.model.VisibleRegion;
import com.amap.api.maps.model.animation.Animation;
import com.amap.api.maps.model.animation.ScaleAnimation;
import com.amap.api.services.core.AMapException;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.core.PoiItemV2;
import com.amap.api.services.poisearch.PoiResultV2;
import com.amap.api.services.poisearch.PoiSearchV2;
import com.bumptech.glide.Glide;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.gson.Gson;
import com.hxl.xlb.R;
import com.hxl.xlb.adapter.MapOrganizationAdapter;
import com.hxl.xlb.adapter.PointSearchContentAdapter;
import com.hxl.xlb.bean.AreaBean;
import com.hxl.xlb.bean.BusinessPlanShortDetail;
import com.hxl.xlb.bean.CompetitorMarkShortDetail;
import com.hxl.xlb.bean.ErpStoreShortDetail;
import com.hxl.xlb.bean.FileBean;
import com.hxl.xlb.bean.FindOrganizationBean;
import com.hxl.xlb.bean.FlagPlantInfoShortBean;
import com.hxl.xlb.bean.StorePlanShortDetail;
import com.hxl.xlb.bean.TemplateAddRequestDetail;
import com.hxl.xlb.bean.TemplateBrandReponseBean;
import com.hxl.xlb.bean.TemplateQueryReponseBean;
import com.hxl.xlb.bean.WarpmapLocationFlagBean;
import com.hxl.xlb.bean.eventbus.AddBusinessEvent;
import com.hxl.xlb.bean.eventbus.MessageEvent;
import com.hxl.xlb.bean.eventbus.PoiItemChangeEvent;
import com.hxl.xlb.bean.eventbus.SearchMessageEvent;
import com.hxl.xlb.contract.MapContract;
import com.hxl.xlb.requestbody.FlagPlantInfoShortRequest;
import com.hxl.xlb.requestbody.MapPointDetailRequest;
import com.hxl.xlb.requestbody.MapPointErpStoreDetailRequest;
import com.hxl.xlb.requestbody.NullRequest;
import com.hxl.xlb.requestbody.StorePlanInitailRequest;
import com.hxl.xlb.requestbody.TemplateAddRequest;
import com.hxl.xlb.requestbody.TemplateBrandRequest;
import com.hxl.xlb.requestbody.TemplateQueryRequest;
import com.hxl.xlb.requestbody.TemplateReadRequest;
import com.hxl.xlb.responsebody.FindOrganizationReponse;
import com.hxl.xlb.responsebody.FlagPlantInfoShortReponse;
import com.hxl.xlb.responsebody.KmsUserInfoResponse;
import com.hxl.xlb.responsebody.MapPointBusinessDetailReponse;
import com.hxl.xlb.responsebody.MapPointCollaborativeDetailReponse;
import com.hxl.xlb.responsebody.MapPointCompetitorDetailReponse;
import com.hxl.xlb.responsebody.MapPointDetailReponse;
import com.hxl.xlb.responsebody.MapPointErpStoreDetailReponse;
import com.hxl.xlb.responsebody.StorePlanInitailReponse;
import com.hxl.xlb.responsebody.TemplateAddReponse;
import com.hxl.xlb.responsebody.TemplateBrandsReponse;
import com.hxl.xlb.responsebody.TemplateQueryReponse;
import com.hxl.xlb.responsebody.TemplateReadReponse;
import com.hxl.xlb.utils.AreaOverlapUtil;
import com.hxl.xlb.utils.BaseBottomSheetDialog;
import com.hxl.xlb.utils.MySPTool;
import com.hxl.xlb.utils.ScreenUtils;
import com.hxl.xlb.utils.ViewUtils;
import com.hxl.xlb.widget.DialogPoiSearchView;
import com.hxl.xlb.widget.DialogTitleContentView;
import com.hxl.xlb.widget.DividerItemDecoration;
import com.hxl.xlb.widget.DropDownEditText;
import com.hxl.xlb.widget.FlexBoxLayout;
import com.hxl.xlb.widget.TransparentDrawingView;
import com.umeng.analytics.MobclickAgent;
import com.xlb.mvplibrary.mvp.MvpActivity;
import com.xlb.mvplibrary.network.errorhandler.ExceptionHandle;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * AMapV2地图中介绍如何显示一个基本地图
 * 继承MvpActivity适用于访问网络的Activity 否则直接继承BaseActivity
 */
public class MapAddressActivity extends MvpActivity<MapContract.MapPresenter> implements MapContract.IMainView, OnClickListener, LocationSource,
        AMapLocationListener, AMap.OnMarkerClickListener, AMap.OnMapTouchListener, AMap.OnCameraChangeListener, SensorEventListener, PoiSearchV2.OnPoiSearchListener {
    private int companyId;

    final int REQUEST_CODE = 2000;

    private final Handler searchHandler = new Handler();
    private Runnable searchRunnable;

    final int POI_DIALOG_REQUEST_CODE = 2001;
    private MapView mapView;
    private AMap aMap;
    private HorizontalScrollView mHorizontalScrollView;
    private LinearLayout container;
    private RadioGroup mRadioGroup, drawRg;
    private ImageView addIv;
    private OnLocationChangedListener mListener;
    private AMapLocationClient mlocationClient;
    private AMapLocationClientOption mLocationOption;
    private MyLocationStyle myLocationStyle;

    private LatLng latLngLocation;

    private LatLng centerlatLng;


    boolean useMoveToLocationWithMapMode = true;

    private MarkerOptions locationMarkerOption;

    private String eventBusinessId;

    // poi 搜索弹窗
    private DialogPoiSearchView poiSearchDialog;

    //自定义定位小蓝点的Marker
    private Marker locationMarker;

    //坐标和经纬度转换工具
    Projection projection;

    private SensorManager sensorManager;
    private Sensor orientationSensor;
    private float currentDegree = 0f;

    private MarkerOptions markerOption;
    // private Marker storeMarker;
    // 存储所有标记的列表
    //private List<Marker> storeMarkerList = new ArrayList<>();
    //标记名称是否显示
    private boolean isMarkerNameShow = true;

    //是否为添加模版 true:不重新调用获取插旗数据接口
    private boolean isAddTemplateFlag = false;
    private int markerSize = 2;
    private int markerWidth = 32, markerHeight = 36;

    private RelativeLayout backBtn;
    private RelativeLayout location_ll;
    private BottomSheetDialog bottomSheetDialog;

    private BaseBottomSheetDialog templatebottomSheetDialog;


    // 点位弹窗打开弹窗标记
    private boolean isPointSearchSheetDialogOpen = false;


    //选择模版Id
    private int templateId;

    private static MapAddressActivity instance;

    //选择模版位置
    private int radioBtTemplatePosition = -1;
    //选择模版名称
    private String radioBtTemplateName;

    //点击模版选择 时为true
    //  private boolean radioBtTemplateFlg = false;

    //请求插旗数据接口bean
    private TemplateQueryReponseBean templateQueryReponseBean;

    //请求插旗数据接口bean
    private TemplateAddRequestDetail templateAddRequestDetail;

    private List<TemplateQueryReponseBean> templateQueryList = new ArrayList<>();

    private ArrayList<MarkerOptions> markerOptionAllList = new ArrayList<>();
    private ArrayList<MarkerOptions> markerOptionNewList = new ArrayList<>();

    private ArrayList<MarkerOptions> pointAnntionList = new ArrayList<>();//点位大头针当次网络请求数据源

    private ArrayList<Marker> pointMarkerList = new ArrayList<>();//当前地图全部的大头针

    private ArrayList<PolygonOptions> buniessList = new ArrayList<>();//商圈当次网络请求数据源

    private ArrayList<Polygon> buniessAllList = new ArrayList<>();//当前地图全部的商圈


    private boolean isFirstLoad = false;

    //点位列表 每次加载获取数据总和
    private List<StorePlanShortDetail> storePlanShortDetailAllList = new ArrayList<>();

    //点位列表 每次加载获取的数据
    private List<StorePlanShortDetail> storePlanShortDetailNewList = new ArrayList<>();

    //商圈列表
    private List<BusinessPlanShortDetail> businessPlanDetailsAllList = new ArrayList<>();
    //商圈列表
    private List<BusinessPlanShortDetail> businessPlanDetailsNewList = new ArrayList<>();

    /**
     * 门店列表
     */
    private List<ErpStoreShortDetail> erpStoreDetailsAllList = new ArrayList<>();
    private List<ErpStoreShortDetail> erpStoreDetailsNewList = new ArrayList<>();
    /**
     * 协同品牌列表
     */
    private List<CompetitorMarkShortDetail> collaborativeDetailsAllList = new ArrayList<>();
    private List<CompetitorMarkShortDetail> collaborativeDetailsNewList = new ArrayList<>();
    private List<Integer> collaborativeIdsAllList = new ArrayList<>();
    private List<Integer> mCollaborativeSelectedList = new ArrayList<>();
    private List<CheckBox> collaborativeCheckBoxs = new ArrayList<CheckBox>();
    /**
     * 竞品品牌列表
     */
    private List<CompetitorMarkShortDetail> competitorDetailsAllList = new ArrayList<>();
    private List<CompetitorMarkShortDetail> competitorDetailsNewList = new ArrayList<>();
    private List<Integer> competitorIdsAllList = new ArrayList<>();
    private List<Integer> mCompetitorSelectedList = new ArrayList<>();
    private List<CheckBox> competitorCheckBoxs = new ArrayList<CheckBox>();

    //新增模版Dialog

    private DropDownEditText templateNameEt;
    private CheckBox templateDefaultCB;

    private CheckBox pointDocumentPreparationCB, pointWaitAssessCB, pointReAssessPassCB, pointVetoCB, pointBuildingCB, pointCounterSignCB;
    private CheckBox businessDocumentPreparationCB, businessWaitAssessCB, businessReAssessPassCB, businessVetoCB;
    private CheckBox storeWaitDeliverCB, storeConstructionCB, storeWaitBusinessCB, storeInBusinessCB, storeClosedStoreCB, storeCancelledCB, storeWaitCloseBusinessCB;
    private FlexBoxLayout competitorFbl;
    private LinearLayout competitorLl;

    private FlexBoxLayout collaborativeFbl;
    private LinearLayout collaborativeLl;
    //设为默认
    private boolean cbTemplateDefault;
    //点位制单
    private boolean cbPointDocumentPreparation;
    //点位待审批
    private boolean cbPointWaitAssess;
    //点位审批通过
    private boolean cbPointReAssessPass = true;
    //点位否决
    private boolean cbPointVeto;
    //点位建店中
    private boolean cbBuilding;
    //点位已双签
    private boolean cbCounterSign;

    // 选中的点位数据
    private PoiItemV2 selectedPoiItem;

    //商圈制单
    private boolean cbBusinessDocumentPreparation;
    //商圈待评估
    private boolean cbBusinessWaitAssess;
    //商圈初评通过
    private boolean cbBusinessInitialAssessPass;
    //商圈复评通过
    private boolean cbBusinessReAssessPass = true;
    //商圈否决
    private boolean cbBusinessVeto;

    private boolean isMapAddPointJumpFlag;//从点位新增页跳转过来


    //门店待交付
    private boolean cbStoreWaitDeliver;
    //门店营建中
    private boolean cbStoreConstruction;
    //门店待营业
    private boolean cbStoreWaitBusiness;
    //门店营业中
    private boolean cbStoreInBusiness = true;
    //门店已闭店
    private boolean cbStoreClosedStore;
    //门店已取消
    private boolean cbStoreCancelled;
    //门店待停业
    private boolean cbStoreWaitCloseBusiness;
    //协同品牌
    private List<TemplateBrandReponseBean> collaborativeList = new ArrayList<>();
    //竞品品牌
    private List<TemplateBrandReponseBean> competitorList = new ArrayList<>();
    //搜索界面item选择跳转进入
    private boolean isSearchSelected = false;

    private List<FindOrganizationBean> organizationList = new ArrayList<>();
    private BaseBottomSheetDialog findOrganizationDialog;

    private MapOrganizationAdapter mMapOrganizationAdapter;

    private long orgId;
    private String orgName;
    private boolean flagAddStorePlan;
    //点位，商圈编辑权限
    private boolean editPointFlag, editBuniessFlag;

    private boolean mapOnTouchUp = false;
    private boolean markerOnTouch = false;

    private final Handler uiHandler = new Handler(Looper.getMainLooper());

    ExecutorService executor = Executors.newSingleThreadExecutor();

    private Marker centerMarker;
//    private SpringAnimation springAnimation;

    private RelativeLayout manageRl, searchRl;
    private LinearLayout addBottomLl, drawBottomLl, templateLl, rightLl, moveMapBtnLl;
    private TextView drawCenterTv, centerFreeTv, cancelTv, finishTv;
    private ImageView drawBottomLeftIv, drawBottomRightIv;
    // false 代表back返回按钮正常，true时点击返回按钮显示已隐藏的所有按钮
    private boolean backFlag;

    // 表示是否从新增或者编辑进入二次修改地址进入当前页面 选择点位的时候也要支持了
    private boolean poiSearchChangeFlag = false;

    // 判断地图滑动时点击搜索的点位还是滑动地图,搜索的点位不滑动
    private boolean poiSearchPointClick = false;

    // 是否编辑和第一次选择地址
    private boolean isFisrtSelectFlag = false;


    private MarkerOptions drawMarkerOption;

    //自定义定位小蓝点的Marker
    private Marker drawMarker;

    // POi搜索定点marker

    private Marker poiMarker;

    // 是否开启poi搜索
    private boolean poiSearchFlag = false;

    private LatLng mapCameraCenterLatLng;
    private List<LatLng> mapCameraCenterLatLngList = new ArrayList<>();
    private List<Marker> mapCameraCenterMarkerList = new ArrayList<>();
    //商圈创建 绘制方式 true:自由绘制，false:定点绘制
    private boolean isDrawWayFlag;
    //商圈创建 绘制 撤销，完成按钮是否可点击
    private boolean isDrawBtnFlag;
    //定点绘制,添加的线集合
    private List<Polyline> mapPolylineList = new ArrayList<>();
    //定点绘制,虚线
    private Polyline PolygonDashedLine;
    private Polygon drawPolygon;// 用于保存当前绘制的多边形对象

    private List<LatLng> drawFreePoints = new ArrayList<>(); // 存储自由绘制区域的顶点
    //  private Polygon drawFreePolygon; // 用于保存当前自由绘制的多边形对象
    private RadioButton drawFixedRb, drawFreeRb;
    private TransparentDrawingView drawView;
    private DialogTitleContentView drawDialog;
    private View pointSelectViewSheet;

    private TextView searchEt;
    private RecyclerView contentRv;
    private View loadingView;
    private FrameLayout listContainer;
    private PointSearchContentAdapter adapter;
    private ArrayList<PoiItemV2> allList = new ArrayList<>();
    private String searchContent = "";
    private int pageNumber = 0;

    private boolean isMapJumpFlag;
    private HashMap<String, Object> saveDataMap;

    @Override
    public void initData(Bundle savedInstanceState) {
        mapView = (MapView) findViewById(R.id.map);
        mapView.onCreate(savedInstanceState);// 此方法必须重写
        mapView.setClickable(true);
        mapView.setFocusable(true);
        mapView.setFocusableInTouchMode(true);
        companyId = MySPTool.getInt(this, "app_companyId");
        editPointFlag = MySPTool.getBoolean(this, "editPoint");
        editBuniessFlag = MySPTool.getBoolean(this, "editBuniess");
//        showLoadingDialog();
        instance = this;


        init();


    }

//    private void initCenterMarker() {
//        // 创建弹簧动画（需要引入androidx.dynamicanimation库）
//        springAnimation = new SpringAnimation(findViewById(R.id.fake_view), SpringAnimation.TRANSLATION_Y)
//                .setSpring(new SpringForce()
//                        .setFinalPosition(0)
//                        .setDampingRatio(SpringForce.DAMPING_RATIO_MEDIUM_BOUNCY)
//                        .setStiffness(SpringForce.STIFFNESS_LOW));
//    }

    @Override
    public int getLayoutId() {
        return R.layout.basicmap_activity;
    }


    /**
     * 初始化AMap对象
     */
    private void init() {
        //获取默认的EventBus对象(单例)，并把当前对象注册为Subscriber。
        //注意：当销毁当前实例的时候必须注销这个Subscriber。一般与Activity的生命周期绑定
        EventBus.getDefault().register(this);


        if (aMap == null) {
            aMap = mapView.getMap();
            aMap.setMapType(AMap.MAP_TYPE_NORMAL);
            setUpMap();
        }
        backBtn = (RelativeLayout) findViewById(R.id.back_ll);

        listContainer = findViewById(R.id.point_search_list_container); // 需要先在布局中添加这个容器
        loadingView = LayoutInflater.from(context).inflate(com.xlb.mvplibrary.R.layout.dialog_loading, listContainer, false);
        listContainer.addView(loadingView);
        loadingView.setVisibility(View.GONE);

        // 初始化控件
        contentRv = findViewById(R.id.point_search_list);
        searchEt = findViewById(R.id.search_poi_et);
        backBtn.setOnClickListener(this);
        location_ll = (RelativeLayout) findViewById(R.id.location_ll);
        location_ll.setOnClickListener(this);
        // findViewById(R.id.add_ll).setOnClickListener(this);
        findViewById(R.id.list_ll).setOnClickListener(this);
        findViewById(R.id.layer_ll).setOnClickListener(this);
        findViewById(R.id.manage_ll).setOnClickListener(this);
        findViewById(R.id.search_ll).setOnClickListener(this);
        findViewById(R.id.add_store_plan_ll).setOnClickListener(this);
        findViewById(R.id.add_business_ll).setOnClickListener(this);
        addBottomLl = findViewById(R.id.add_bottom_ll);
        drawBottomLl = findViewById(R.id.draw_bottom_ll);
        moveMapBtnLl = findViewById(R.id.move_map_btn_ll);
        moveMapBtnLl.setOnClickListener(this);
        manageRl = findViewById(R.id.manage_ll);
        searchRl = findViewById(R.id.search_ll);
        templateLl = findViewById(R.id.template_ll);
        rightLl = findViewById(R.id.right_ll);
        drawCenterTv = findViewById(R.id.center_tv);
        centerFreeTv = findViewById(R.id.center_free_tv);
        drawCenterTv.setOnClickListener(this);
        centerFreeTv.setOnClickListener(this);
        cancelTv = findViewById(R.id.cancel_tv);
        finishTv = findViewById(R.id.finish_tv);
        drawView = findViewById(R.id.drawing_view);
        drawRg = findViewById(R.id.draw_rg);
        drawFixedRb = findViewById(R.id.draw_fixed_rb);
        drawFreeRb = findViewById(R.id.draw_free_rb);
        drawBottomLeftIv = findViewById(R.id.draw_bottom_left_iv);
        drawBottomRightIv = findViewById(R.id.draw_bottom_right_iv);
        pointSelectViewSheet = findViewById(R.id.point_select_view);
        manageRl.setVisibility(View.GONE);
        searchRl.setVisibility(View.GONE);
        rightLl.setVisibility(View.GONE);
        templateLl.setVisibility(View.GONE);

//        poiSearchDialog = findViewById(R.id.point_select_view);

//        poiSearchDialog.setOnPoiSelectedListener(poiItem -> {
//            // 处理选中的POI结果
////            handleSelectedPoi(poiItem);
//            selectedPoiItem = poiItem;
//            if (poiSearchChangeFlag) {
//                handleChangePoiItem();
//            } else {
//                findOrganization();
//
//            }
//        });
//        poiSearchDialog.setOnCloseListener(dialog -> {
//            // 处理对话框取消事件
////            poiSearchDialog.dismiss();
//            dialog.dismiss();
//            isPointSearchSheetDialogOpen = false;
//            if (poiSearchChangeFlag) {
//                finish();
//            }
//            poiSearchChangeFlag = false;
//
//        });

        findViewById(R.id.close_rl).setOnClickListener(v -> {
            hideBottomSheet();
            isPointSearchSheetDialogOpen = false;
            if (poiSearchChangeFlag) {
                finish();
            }
            poiSearchChangeFlag = false;

        });


        findViewById(R.id.cancel_ll).setOnClickListener(this);
        findViewById(R.id.finish_ll).setOnClickListener(this);
        //获取mRadioGroup horizontalscrollview布局
        LayoutInflater inflater = (LayoutInflater) getSystemService(LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(R.layout.item_template_horizontal, null);

        mRadioGroup = (RadioGroup) view.findViewById(R.id.template_rg);
//        poiSearchDialog = findViewById(R.id.point_select_view);

//        poiSearchDialog = new DialogPoiSearchView.Builder(this).create();
        //mRadioGroup.setOnCheckedChangeListener(this);
        mHorizontalScrollView = (HorizontalScrollView) findViewById(R.id.horizontal_sv);
        container = findViewById(R.id.container);

        contentRv.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                int lastVisibleItem = layoutManager.findLastVisibleItemPosition();
                int totalItemCount = layoutManager.getItemCount();

                if (totalItemCount > 0 && lastVisibleItem >= totalItemCount - 2) {
                    // 触发加载更多
                    startSearch(TextUtils.isEmpty(searchContent) ? "" : searchContent);
                }
            }
        });

        contentRv.addOnItemTouchListener(new RecyclerView.SimpleOnItemTouchListener() {
            @Override
            public boolean onInterceptTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {
                // 仅拦截列表区域的触摸事件
                return false;
            }
        });
        ImageView etClearIv = findViewById(R.id.et_clear_iv);
//        searchEt.addTextChangedListener(new TextWatcher() {
//            @Override
//            public void afterTextChanged(Editable s) {
//            }
//
//            @Override
//            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
//            }
//
//            @Override
//            public void onTextChanged(CharSequence s, int start, int before, int count) {
//                etClearIv.setVisibility(s.length() > 0 ? View.VISIBLE : View.GONE);
//            }
//        });
        searchEt.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(context,MapLocationFlagActivity.class);
                intent.putExtra("centerlatLng", centerlatLng);
                startActivity(intent);


            }
        });

//        searchEt.setOnEditorActionListener((v, actionId, event) -> {
//            searchContent = searchEt.getText().toString();
//            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
//                performSearch();
//                return true;
//            }
//            return false;
//        });

        // 在布局加载后添加


        etClearIv.setOnClickListener(v -> searchEt.setText(""));


        drawRg.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                //创建商圈，绘制方式按钮切换
                if (checkedId == R.id.draw_fixed_rb) {
                    // 定点绘制
                    if (isDrawBtnFlag) {
                        showDrawChangeDialog();
                    } else {
                        isDrawWayFlag = false;
                        drawView.setVisibility(View.GONE);
                        moveMapBtnLl.setVisibility(View.GONE);
                        drawCenterTv.setVisibility(View.VISIBLE);
                        centerFreeTv.setVisibility(View.GONE);
                        cancelTv.setText("撤销");
                        addDrawMarkerToMap(aMap.getCameraPosition().target);
                    }
                } else if (checkedId == R.id.draw_free_rb) {
                    // 自由绘制
                    if (isDrawBtnFlag) {
                        showDrawChangeDialog();
                    } else {
                        isDrawWayFlag = true;
                        drawView.setVisibility(View.GONE);
                        drawCenterTv.setVisibility(View.GONE);
                        centerFreeTv.setVisibility(View.VISIBLE);
                        cancelTv.setText("重画");
                        if (drawMarker != null) {
                            drawMarker.remove(); // 移除定点位置
                            drawMarker = null; // 清空reference
                        }
                    }
                }
            }
        });

        drawView.setOnDrawEndListener((List<PointF> pointList) -> {
            // 在此处理手势抬起后的业务逻辑
            if (null != pointList && !pointList.isEmpty()) {
                if (null != drawFreePoints && !drawFreePoints.isEmpty()) {
                    drawFreePoints.clear();
                }
                Projection projection = aMap.getProjection();
                for (PointF bean : pointList) {
                    LatLng latLng = projection.fromScreenLocation(new Point((int) bean.x, (int) bean.y));
                    drawFreePoints.add(latLng);
                }
                if (drawFreePoints.size() >= 3) {
                    setDrawPolygonDrawable(drawFreePoints); // 当点数≥3时绘制多边形
                    drawView.clearCanvas();
                    drawView.setVisibility(View.GONE);
                    isDrawBtnFlag = true;//让重画，完成按钮可点击
                    moveMapBtnLl.setVisibility(View.GONE);
                    drawBottomLeftIv.setImageResource(R.drawable.map_draw_bottom_left_back_iv);
                    cancelTv.setTextColor(getResources().getColor(R.color.color_map_template_cb_text_normal));
                    drawBottomRightIv.setImageResource(R.drawable.map_draw_bottom_right_finish_iv);
                    finishTv.setTextColor(getResources().getColor(R.color.color_map_template_cb_text_normal));
                }
            }


        });

//        aMap.setOnMapClickListener(new AMap.OnMapClickListener() {
//            @Override
//            public void onMapClick(LatLng latLng) {
//                drawFreePoints.add(latLng); // 添加点击坐标到列表
//                if (drawFreePoints.size() >= 3) {
//                    drawFreePolygon(drawFreePoints); // 当点数≥3时绘制多边形
//                }
//            }
//        });
        //获取数据
        TemplateQueryRequest templateBody = new TemplateQueryRequest();

        templateBody.setCompany_id(companyId);
        templateBody.setType("FLAG");
//        mPresenter.findTemplatePOST(templateBody);

        //获取 新增插旗模版弹框中协品，竞品品牌数据
        TemplateBrandRequest brandBody = new TemplateBrandRequest();
        Log.i("TAG", "----TemplateBrandRequest---companyId--------" + companyId);
        brandBody.setCompany_id(companyId);
        List<String> brandList = new ArrayList<>();
        //品牌类型COLLABORATIVE（协同品牌)，COMPETITOR(竞品品牌)
        brandList.add("COLLABORATIVE");
        brandList.add("COMPETITOR");
        brandBody.setTypes(brandList);
        brandBody.setEnable(true);
//        mPresenter.templateBrandPOST(brandBody);


        startAddressChange();

    }

    /**
     * 新增点位先调用此接口 生成id
     */
    private void getStorePlanInitial() {
        //显示加载弹框
        showLoadingDialog();
        StorePlanInitailRequest body = new StorePlanInitailRequest();
        body.setCompany_id(companyId);
        mPresenter.storePlanInitialPOST(body);
    }


    /**
     * 新增商圈先调用此接口
     */
    private void getBusinessPlanInitial() {
        //显示加载弹框
        showLoadingDialog();
        StorePlanInitailRequest body = new StorePlanInitailRequest();
        body.setCompany_id(companyId);
        mPresenter.businessPlanInitialPOST(body);
    }


    private void findOrganization() {

        NullRequest body = new NullRequest();
        body.setCompany_id(companyId);
        mPresenter.findOrganizationPOST(body);

    }

    /**
     * 设置一些amap的属性
     */
    private void setUpMap() {
        aMap.setLocationSource(this);// 设置定位监听
        myLocationStyle = new MyLocationStyle();//初始化定位蓝点样式类
        myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE);//连续定位、且将视角移动到地图中心点，定位点依照设备方向旋转，并且会跟随设备移动。（1秒1次定位）如果不设置myLocationType，默认也会执行此种模式。
        myLocationStyle.interval(2000); //设置连续定位模式下的定位间隔，只在连续定位模式下生效，单次定位模式下不会生效。单位为毫秒。
        aMap.setMyLocationStyle(myLocationStyle);//设置定位蓝点的Style
        aMap.getUiSettings().setZoomControlsEnabled(false);//隐藏缩放地图按钮
        aMap.getUiSettings().setTiltGesturesEnabled(false);//关闭倾斜手势 效果
        //aMap.getUiSettings().setMyLocationButtonEnabled(true);//设置默认定位按钮是否显示，非必需设置。
        aMap.setMyLocationEnabled(true);// 设置为true表示启动显示定位蓝点，false表示隐藏定位蓝点并不进行定位，默认是false。
        aMap.setOnMapTouchListener(this);
        aMap.setOnCameraChangeListener(this);//监听地图的移动和缩放变化
        // 绑定 Marker 被点击事件
        aMap.setOnMarkerClickListener(this);
        //aMap.setOnMarkerClickListener(markerClickListener);
        sensorManager = (SensorManager) getSystemService(Context.SENSOR_SERVICE);
        orientationSensor = sensorManager.getDefaultSensor(Sensor.TYPE_ORIENTATION);
//        aMap.setOnMapLoadedListener(
//                new AMap.OnMapLoadedListener() {
//                    @Override
//                    public void onMapLoaded() {
//                        startAddressChange();
//                    }
//
//
//                }
//        );
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onResume() {
        super.onResume();
        MobclickAgent.onResume(this);
        mapView.onResume();
        sensorManager.registerListener(this, orientationSensor, SensorManager.SENSOR_DELAY_UI);
        useMoveToLocationWithMapMode = false;
        isMapJumpFlag = getIntent().getBooleanExtra("isMapJumpFlag", false);


        isMapAddPointJumpFlag = getIntent().getBooleanExtra("isMapAddPointJumpFlag", false);


        if (isMapAddPointJumpFlag) {

            Log.i("isMapAddPointJumpFlag", "onResume: isMapAddPointJumpFlag" + isMapAddPointJumpFlag);
            // 将地图的中心点设置为centerPosition
            double add_point_latitude = getIntent().getDoubleExtra("add_point_latitude", 0);
            double add_point_longitude = getIntent().getDoubleExtra("add_point_longitude", 0);
            aMap.moveCamera(CameraUpdateFactory.changeLatLng(new LatLng(add_point_latitude, add_point_longitude)));
            // 可以选择将缩放级别设置为一个合适的值
            aMap.moveCamera(CameraUpdateFactory.zoomTo(15.5f));
            Log.i("isMapAddPointJumpFlag", "onResume: add_point_latitude" + add_point_latitude);
            Log.i("isMapAddPointJumpFlag", "onResume: add_point_longitude" + add_point_longitude);

        }

        // 检查是否存在List
        if (getIntent().hasExtra("saveDataMap")) {
            // 从Intent中获取map数据
            saveDataMap = (HashMap<String, Object>) getIntent().getSerializableExtra("saveDataMap");
        }

//        startAddressChange();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent); // 确保getIntent()返回最新的Intent
        // 处理intent中的数据
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onPause() {
        super.onPause();
        MobclickAgent.onPause(this);
        Glide.with(context).pauseRequests();
        uiHandler.removeCallbacksAndMessages(null);
        mapView.onPause();
        hideLoadingDialog();
        // deactivate();
        sensorManager.unregisterListener(this);
        useMoveToLocationWithMapMode = false;
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        mapView.onSaveInstanceState(outState);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Log.i("TAG", "onActivityResult: " + data.toString());
        Log.i("TAG", "onActivityrequestCode: " + requestCode);
        Log.i("TAG", "onActivityresultCode: " + resultCode);

        if (requestCode == REQUEST_CODE) {
            if (resultCode == RESULT_OK && data != null) {


//                PoiItemV2 curData = data.getDataString();


                String returnData = data.getStringExtra("chooseMap");
                Log.i("TAG", "onActivityResult: " + returnData);

                selectedPoiItem = new Gson().fromJson(returnData, PoiItemV2.class);
                findOrganization();
//                int number = data.getIntExtra("number", 0);
                // 处理返回数据
            }
        } else if (requestCode == POI_DIALOG_REQUEST_CODE) {
            if (resultCode == RESULT_OK && data != null) {
                Log.i("MapActivity", "onActivityResult: " + data.toString());
                Double longitude = data.getDoubleExtra("longitude", 0);
                Double latitude = data.getDoubleExtra("latitude", 0);

                if (longitude != null && latitude != null) {
                    centerlatLng = new LatLng(latitude, longitude);
                } else {
                    centerlatLng = latLngLocation;
                }

                poiSearchChangeFlag = true;
                showBottomSheet();


//                showPoiSearchBottomSheet();


            }

        }
    }

    @Override
    protected MapContract.MapPresenter createPresenter() {
        return new MapContract.MapPresenter();
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        //注销当前Subscriber
        EventBus.getDefault().unregister(this);
        executor.shutdownNow();
        mapView.onDestroy();
        if (null != mlocationClient) {
            mlocationClient.onDestroy();
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.back_ll) {
            if (backFlag) {
                setDrawShowBtn(false);
            } else {
                finish();
            }
        } else if (v.getId() == R.id.manage_ll) {
            Intent intent = new Intent(this, MapTemPlateActivity.class);
            startActivity(intent);
        } else if (v.getId() == R.id.search_ll) {
            Intent intentSearch = new Intent(this, MapPointSearchActivity.class);
            startActivity(intentSearch);
        } else if (v.getId() == R.id.location_ll) {
            useMoveToLocationWithMapMode = true;
            if (mlocationClient != null) {
                if (null != latLngLocation) {
                    startMoveLocationAndMap(latLngLocation);
                } else {
                    mlocationClient.startLocation();
                }
            } else {
                newLocationClient();
            }
        } else if (v.getId() == R.id.list_ll) {
            VisibleRegion visibleRegion = aMap.getProjection().getVisibleRegion();
            LatLng farLeft = visibleRegion.farLeft;
            LatLng farRight = visibleRegion.farRight;
            LatLng nearLeft = visibleRegion.nearLeft;
            LatLng nearRight = visibleRegion.nearRight;

            MySPTool.setDouble(this, "farLeftLatitude", farLeft.latitude);
            MySPTool.setDouble(this, "farLeftLongitude", farLeft.longitude);
            MySPTool.setDouble(this, "farRightLatitude", farRight.latitude);
            MySPTool.setDouble(this, "farRightLongitude", farRight.longitude);
            MySPTool.setDouble(this, "nearLeftLatitude", nearLeft.latitude);
            MySPTool.setDouble(this, "nearLeftLongitude", nearLeft.longitude);
            MySPTool.setDouble(this, "nearRightLatitude", nearRight.latitude);
            MySPTool.setDouble(this, "nearRightLongitude", nearRight.longitude);

            Intent intentList = new Intent(this, MapListDataActivity.class);
            startActivity(intentList);
        } else if (v.getId() == R.id.add_store_plan_ll) {
            if (editPointFlag) {
                flagAddStorePlan = true;
                showBottomSheet();
            } else {
                showMsg("您没有点位的编辑权限");
            }
        } else if (v.getId() == R.id.add_business_ll) {
            if (editBuniessFlag) {
                flagAddStorePlan = false;
                setDrawShowBtn(true);
            } else {
                showMsg("您没有商圈的编辑权限");
            }
        } else if (v.getId() == R.id.cancel_tv) {
            findOrganizationDialog.dismiss();
        } else if (v.getId() == R.id.layer_ll) {
            showBottomSheetDialog();
        } else if (v.getId() == R.id.add_rl) {
            showTemplateDialog();
        } else if (v.getId() == R.id.tv_cancel) {
            templatebottomSheetDialog.dismiss();
        } else if (v.getId() == R.id.tv_reset) {
            templateReset();
        } else if (v.getId() == R.id.tv_save) {
            templateSave();
        } else if (v.getId() == R.id.center_tv) {
            addDrawMarkerCornerToMap(mapCameraCenterLatLng);
        } else if (v.getId() == R.id.center_free_tv) {
            centerFreeTv.setVisibility(View.GONE);
            drawView.setVisibility(View.VISIBLE);
            moveMapBtnLl.setVisibility(View.VISIBLE);
        } else if (v.getId() == R.id.move_map_btn_ll) {
            centerFreeTv.setVisibility(View.VISIBLE);
            drawView.setVisibility(View.GONE);
            moveMapBtnLl.setVisibility(View.GONE);
        } else if (v.getId() == R.id.cancel_ll) {
            if (isDrawBtnFlag) {
                if (isDrawWayFlag) {
                    isDrawBtnFlag = false;
                    if (drawPolygon != null) {
                        drawPolygon.remove();
                        drawPolygon = null;
                    }
                    drawView.setVisibility(View.VISIBLE);
                    moveMapBtnLl.setVisibility(View.VISIBLE);
                    centerFreeTv.setVisibility(View.GONE);
                    drawBottomLeftIv.setImageResource(R.drawable.map_draw_bottom_left_back_ghost_iv);
                    cancelTv.setTextColor(getResources().getColor(R.color.color_map_draw_bottom_btn_left_text));
                    drawBottomRightIv.setImageResource(R.drawable.map_draw_bottom_right_finish_ghost_iv);
                    finishTv.setTextColor(getResources().getColor(R.color.color_map_draw_bottom_btn_left_text));
                } else {
                    if (null != mapCameraCenterLatLngList && !mapCameraCenterLatLngList.isEmpty()) {
                        Log.i("TAG", "mapCameraCenterMarkerList-------1---size---" + mapCameraCenterMarkerList.size());
                        mapCameraCenterMarkerList.get(mapCameraCenterMarkerList.size() - 1).remove();
                        Log.i("TAG", "mapCameraCenterMarkerList-------2---size---" + mapCameraCenterMarkerList.size());
                        mapCameraCenterLatLngList.remove(mapCameraCenterLatLngList.get(mapCameraCenterLatLngList.size() - 1));
                        mapCameraCenterMarkerList.remove(mapCameraCenterMarkerList.get(mapCameraCenterMarkerList.size() - 1));
                        Log.i("TAG", "mapCameraCenterMarkerList-------3---size---" + mapCameraCenterMarkerList.size());
                        if (null != mapPolylineList && !mapPolylineList.isEmpty()) {
                            mapPolylineList.get(mapPolylineList.size() - 1).remove();
                            mapPolylineList.remove(mapPolylineList.get(mapPolylineList.size() - 1));
                        }
                        if (mapCameraCenterLatLngList.size() > 1) {
                            setDrawPolygonDrawable(mapCameraCenterLatLngList);
                        }

                        if (null != mapCameraCenterLatLngList && mapCameraCenterLatLngList.isEmpty()) {
                            isDrawBtnFlag = false;
                            drawBottomLeftIv.setImageResource(R.drawable.map_draw_bottom_left_back_ghost_iv);
                            cancelTv.setTextColor(getResources().getColor(R.color.color_map_draw_bottom_btn_left_text));
                            drawBottomRightIv.setImageResource(R.drawable.map_draw_bottom_right_finish_ghost_iv);
                            finishTv.setTextColor(getResources().getColor(R.color.color_map_draw_bottom_btn_left_text));
                        }
                    }
                }
            }
        } else if (v.getId() == R.id.finish_ll) {
            if (!isDrawWayFlag) {
                if (null != mapCameraCenterLatLngList && !mapCameraCenterLatLngList.isEmpty()) {
                    if (mapCameraCenterLatLngList.size() < 3) {
                        showMsg("请至少绘制3个点");
                        return;
                    }
                }
            }

            if (null != drawPolygon) {
                for (int i = 0; i < buniessAllList.size(); i++) {
                    boolean flag = AreaOverlapUtil.isPolygonsOverlap(drawPolygon.getPoints(), buniessAllList.get(i).getPoints());
                    if (flag) {
                        showMsg("商圈区域范围重叠");
                        return;
                    }
                }
            }

            findOrganization();
        } else if (v.getId() == R.id.make_name_tv) {
            if (poiSearchDialog != null) {
                poiSearchDialog.show();
            }
        }
    }

    private void setDrawShowBtn(boolean flag) {
        if (flag) {
            backFlag = true;
            isDrawBtnFlag = false;
            drawFixedRb.setChecked(true);
            drawBottomLl.setVisibility(View.VISIBLE);
            addBottomLl.setVisibility(View.GONE);
            manageRl.setVisibility(View.GONE);
            searchRl.setVisibility(View.GONE);
            templateLl.setVisibility(View.GONE);
            rightLl.setVisibility(View.INVISIBLE);
            if (drawMarker != null) {
                drawMarker.remove(); // 移除定点位置
                drawMarker = null; // 清空reference
            }
            addDrawMarkerToMap(aMap.getCameraPosition().target);
        } else {
            if (isDrawBtnFlag) {
                showBusinessDrawExitDialog();
            } else {
                backFlag = false;
                drawBottomLl.setVisibility(View.GONE);
                addBottomLl.setVisibility(View.VISIBLE);
                manageRl.setVisibility(View.VISIBLE);
                searchRl.setVisibility(View.VISIBLE);
                templateLl.setVisibility(View.VISIBLE);
                rightLl.setVisibility(View.VISIBLE);
                if (drawMarker != null) {
                    drawMarker.remove(); // 移除定点位置
                    drawMarker = null; // 清空reference
                }
            }
        }
    }

    /**
     * 获取插旗数据请求
     */
    private void getFlagPlantShortInfoUsing(TemplateAddRequestDetail templateAddRequestDetail) {
        FlagPlantInfoShortRequest body = new FlagPlantInfoShortRequest();
        if (templateAddRequestDetail != null) {
            //请求插旗数据接口
            List<Integer> areaCodeList = new ArrayList<>();
            List<AreaBean> areasList = templateAddRequestDetail.getAreas();
            if (null != areasList && !areasList.isEmpty()) {
                for (AreaBean item : areasList) {
                    areaCodeList.add(item.getArea_code());
                }
            }
            body.setArea_codes(areaCodeList);
            body.setBusiness_plan_states(templateAddRequestDetail.getBusiness_plan_states());
            body.setStore_plan_states(templateAddRequestDetail.getStore_plan_states());
            body.setErp_store_states(templateAddRequestDetail.getErp_store_states());
            body.setCollaborative_ids(templateAddRequestDetail.getCollaborative_ids());
            body.setCompetitor_ids(templateAddRequestDetail.getCompetitor_ids());
        } else {
            // body.setArea_codes(null);
            body.setBusiness_plan_states(null);
            body.setStore_plan_states(null);
            body.setErp_store_states(null);
        }

        VisibleRegion visibleRegion = aMap.getProjection().getVisibleRegion();
        LatLng farLeft = visibleRegion.farLeft;     //可视区域的左上角。
        LatLng farRight = visibleRegion.farRight;   //可视区域的右上角。
        LatLng nearLeft = visibleRegion.nearLeft;   //可视区域的左下角。
        LatLng nearRight = visibleRegion.nearRight; //可视区域的右下角。

        Log.i("TAG", "farLeft latitude: " + farLeft.latitude + "farLeft longitude: " + farLeft.longitude);
        Log.i("TAG", "nearRight latitude: " + nearRight.latitude + "nearRight longitude: " + nearRight.longitude);

        body.setLat_lng(latLngLocation);
        body.setTop_left(farLeft);
        body.setTop_right(farRight);
        body.setBottom_left(nearLeft);
        body.setBottom_right(nearRight);
        // 输出当前的缩放级别
        Log.i("TAG", "当前图层等级: " + aMap.getCameraPosition().zoom);
        //将层级float数据转成保留小数点后两位
        float level = aMap.getCameraPosition().zoom;
        String formattedString = String.format("%.2f", level);
        body.setLevel(Float.parseFloat(formattedString));
        Gson gson = new Gson();
        Log.i("TAG", "--------findFlagPlantShortInfoUsingPOST-----body: " + gson.toJson(body));

        mPresenter.findFlagPlantShortInfoUsingPOST(body);

    }


    @Override
    public void onLocationChanged(AMapLocation aMapLocation) {

        if (mListener != null && aMapLocation != null) {
            if (aMapLocation != null && aMapLocation.getErrorCode() == 0) {
                latLngLocation = new LatLng(aMapLocation.getLatitude(), aMapLocation.getLongitude());
                centerlatLng = new LatLng(aMapLocation.getLatitude(), aMapLocation.getLongitude());
                MySPTool.setDouble(this, "latitude", aMapLocation.getLatitude());
                MySPTool.setDouble(this, "longitude", aMapLocation.getLongitude());
                //展示自定义定位小蓝点
                if (locationMarker == null) {
                    //首次定位
                    locationMarker = aMap.addMarker(new MarkerOptions().position(latLngLocation)
                            .icon(BitmapDescriptorFactory.fromResource(R.drawable.map_location_mark))
                            .anchor(0.5f, 0.5f));

                    //首次定位,选择移动到地图中心点并修改级别到17级
                    if (isFisrtSelectFlag) {
                    aMap.moveCamera(CameraUpdateFactory.newLatLngZoom(latLngLocation, 15.5f));

                    }

//                    aMap.moveCamera(CameraUpdateFactory.newLatLngZoom(latLngLocation, 15.5f));
                } else {

                    if (useMoveToLocationWithMapMode) {
                        //二次以后定位，使用sdk中没有的模式，让地图和小蓝点一起移动到中心点（类似导航锁车时的效果）
                        startMoveLocationAndMap(latLngLocation);
                    } else {
                        startChangeLocation(latLngLocation);
                    }

                }

//                new Handler(Looper.getMainLooper()).postDelayed(() -> {
//                    aMap.moveCamera(CameraUpdateFactory.newLatLngZoom(ce, 15.5f));
//                }, 200);


            } else {
                String errText = "定位失败," + aMapLocation.getErrorCode() + ": " + aMapLocation.getErrorInfo();
                Log.e("AmapErr", errText);
            }

        }

    }

    /**
     * 在地图上添加定点绘制图标
     */
    private void addDrawMarkerToMap(LatLng latLng) {
        if (drawMarker == null) {
            VectorDrawable vectorDrawable = (VectorDrawable) ContextCompat.getDrawable(context, R.drawable.map_draw_bottom_red_pos_iv);
            Bitmap bitmap = Bitmap.createBitmap(vectorDrawable.getIntrinsicWidth(), vectorDrawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            vectorDrawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
            vectorDrawable.draw(canvas);

            drawMarkerOption = new MarkerOptions().icon(BitmapDescriptorFactory.fromBitmap(bitmap))
                    .position(latLng)
                    .setFlat(false)
                    .draggable(false);
        }
        drawMarker = aMap.addMarker(drawMarkerOption);
    }

    // 打开搜索对话框
    private void showPoiSearchBottomSheet() {
//        poiSearchDialog = DialogPoiSearchView.getInstance(
//                this,
//                centerlatLng.longitude,  // 当前经度
//                centerlatLng.latitude   // 当前纬度
//        );
//
//        poiSearchDialog.show();


        poiSearchDialog.show();
        poiSearchDialog.updateLocation(centerlatLng.longitude, centerlatLng.latitude);

        isPointSearchSheetDialogOpen = true;
    }


    /**
     * 在地图上添加定点绘制图标
     */
    private void addDrawMarkerCornerToMap(LatLng latLng) {
        if (null != mapCameraCenterLatLngList && !mapCameraCenterLatLngList.isEmpty()) {
            float distance = AMapUtils.calculateLineDistance(latLng, mapCameraCenterLatLngList.get(mapCameraCenterLatLngList.size() - 1));
            Log.i("TAG", "distance-------------" + distance);
            if (distance < 10) {
                showMsg("连续定点距离过近,请重新绘制");
                return;
            }
        }
        VectorDrawable vectorDrawable = (VectorDrawable) ContextCompat.getDrawable(context, R.drawable.map_draw_red_corner_line_iv);
        Bitmap bitmap = Bitmap.createBitmap(vectorDrawable.getIntrinsicWidth(), vectorDrawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        vectorDrawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        vectorDrawable.draw(canvas);
        drawMarkerOption = new MarkerOptions().icon(BitmapDescriptorFactory.fromBitmap(bitmap))
                .position(latLng)
                .anchor(0.5f, 0.5f)
                .setFlat(true)
                .draggable(false);
        Marker drawMarker = aMap.addMarker(drawMarkerOption);

        boolean lineOverlapFlag = false;
        if (null != mapCameraCenterLatLngList && !mapCameraCenterLatLngList.isEmpty()) {
            if (null != mapPolylineList && !mapPolylineList.isEmpty() && mapPolylineList.size() > 1) {
                LatLng startPoint = mapCameraCenterLatLngList.get(mapCameraCenterLatLngList.size() - 1);
                lineOverlapFlag = ViewUtils.isIntersectWithPolylines(startPoint, latLng, mapPolylineList);
                Log.i("TAG", "mapPolylineList-----mapCameraCenterLatLngList---------------" + mapCameraCenterLatLngList.size());
                Log.i("TAG", "mapPolylineList-----size---------------" + mapPolylineList.size());
                Log.i("TAG", "mapPolylineList-----lineOverlapFlag---------------" + lineOverlapFlag);
                if (lineOverlapFlag) {
                    showMsg("线与线不可重叠");
                    //lineOverlapFlag = false;
                    setDrawPolyDashedLine(startPoint, latLng);
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            // 你的代码，延迟1秒后执行
                            if (drawMarker != null) {
                                drawMarker.remove(); // 移除多段线
                            }
                        }
                    }, 1000);
                    return;
                }
            }
        }

        Log.i("TAG", "mapPolylineList-----lineOverlapFlag------2---------" + lineOverlapFlag);
        mapCameraCenterLatLngList.add(latLng);
        mapCameraCenterMarkerList.add(drawMarker);
        if (null != mapCameraCenterLatLngList && !mapCameraCenterLatLngList.isEmpty()) {
            isDrawBtnFlag = true;
            drawBottomLeftIv.setImageResource(R.drawable.map_draw_bottom_left_back_iv);
            cancelTv.setTextColor(getResources().getColor(R.color.color_map_template_cb_text_normal));
            drawBottomRightIv.setImageResource(R.drawable.map_draw_bottom_right_finish_iv);
            finishTv.setTextColor(getResources().getColor(R.color.color_map_template_cb_text_normal));
            if (mapCameraCenterLatLngList.size() > 1) {
                setDrawPolyLine(mapCameraCenterLatLngList.get(mapCameraCenterLatLngList.size() - 2), latLng);
            }
            //绘制多边形
            setDrawPolygonDrawable(mapCameraCenterLatLngList);
        }
        if (mapPolylineList != null && !mapPolylineList.isEmpty()) {
            Log.i("TAG", "mapPolylineList-----size----2-----------" + mapPolylineList.size());
        }
    }

    /**
     * 在地图上添加定位marker
     */
    private void addLocationMarkerToMap(LatLng latLng) {
        if (locationMarker == null) {
            locationMarkerOption = new MarkerOptions().icon(BitmapDescriptorFactory.fromResource(R.drawable.map_location_mark))
                    .position(latLng)
                    .anchor(0.5f, 0.5f);
            locationMarker = aMap.addMarker(locationMarkerOption);
        }
    }

    /**
     * 修改自定义定位小蓝点的位置
     *
     * @param latLng
     */
    private void startChangeLocation(LatLng latLng) {

        if (locationMarker != null) {
            LatLng curLatlng = locationMarker.getPosition();
            if (curLatlng == null || !curLatlng.equals(latLng)) {
                locationMarker.setPosition(latLng);
            }
        }
    }

    /**
     * 同时修改自定义定位小蓝点和地图的位置
     *
     * @param latLng
     */
    private void startMoveLocationAndMap(LatLng latLng) {

        //将小蓝点提取到屏幕上
        if (projection == null) {
            projection = aMap.getProjection();
        }
        if (locationMarker != null && projection != null) {
            LatLng markerLocation = locationMarker.getPosition();
            Point screenPosition = aMap.getProjection().toScreenLocation(markerLocation);
            locationMarker.setPositionByPixels(screenPosition.x, screenPosition.y);

        }

        //移动地图，移动结束后，将小蓝点放到放到地图上
        myCancelCallback.setTargetLatlng(latLng);
        //动画移动的时间，最好不要比定位间隔长，如果定位间隔2000ms 动画移动时间最好小于2000ms，可以使用1000ms
        //如果超过了，需要在myCancelCallback中进行处理被打断的情况
        aMap.animateCamera(CameraUpdateFactory.changeLatLng(latLng), 1000, myCancelCallback);

    }


    MyCancelCallback myCancelCallback = new MyCancelCallback();

    @Override
    public void onTouch(MotionEvent motionEvent) {
        Log.i("onTouch", "onTouch: ");
        switch (motionEvent.getAction()) {
            case MotionEvent.ACTION_DOWN:
                Log.i("TAG", "ACTION_DOWN-------------");
                //   hideLoadingDialog();
                //  NetworkApi.cancelAllRequests();
                // 手指按下地图
                mapOnTouchUp = false;
                markerOnTouch = false;
                poiSearchPointClick = false;
                //threadMarker.interrupt();
                break;
            case MotionEvent.ACTION_MOVE:
                mapOnTouchUp = false;
                //  hideLoadingDialog();
                // 手指在地图上移动
                // threadMarker.interrupt();
                break;
            case MotionEvent.ACTION_UP:
                Log.i("TAG", "ACTION_UP-------------");
                // 手指离开地图
                mapOnTouchUp = true;
                break;
            default:
                break;
        }
        Log.i("amap", "onTouch 关闭地图和小蓝点一起移动的模式");
        useMoveToLocationWithMapMode = false;
    }

    /**
     * 查询模版接口回调
     */
    @RequiresApi(api = Build.VERSION_CODES.Q)
    @Override
    public void templateQueryPOST(TemplateQueryReponse reponse) {
        if (templateQueryList != null && templateQueryList.size() > 0) {
            templateQueryList.clear();
        }
        if (mRadioGroup != null) {
            mRadioGroup.removeAllViews();
        }
        if (reponse != null) {
            //List<TemplateQueryReponseBean> templateList = reponse.data;
            templateQueryList.addAll(reponse.data);
            int acquiescePosion = 0;
            if (templateQueryList != null && templateQueryList.size() > 0) {
                for (int i = 0; i < templateQueryList.size(); i++) {
                    //添加radiobutton及设置参数(方便动态加载radiobutton)
                    RadioButton rb = new RadioButton(this);
                    //根据下标获取商品类别对象
                    TemplateQueryReponseBean bean = templateQueryList.get(i);
                    // rb.setHeight(60);
                    // rb.setWidth(ViewGroup.LayoutParams.WRAP_CONTENT);
                    rb.setText(bean.getName());
                    rb.setTextSize(14);
                    rb.setPadding(16, 12, 16, 12);
                    rb.setGravity(Gravity.CENTER);
                    //根据需要设置显示初始标签的个数，这里显示4个
                    //rb.setLayoutParams(new ViewGroup.LayoutParams((int)(screenWidth/4.5), ViewGroup.LayoutParams.FILL_PARENT));
                    LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                    params.rightMargin = 16;
                    rb.setLayoutParams(params);
                    rb.setBackgroundResource(R.drawable.map_template_radio_btn_bg);
                    //**原生radiobutton是有小圆点的，要去掉圆点而且最好按以下设置，设置为null的话在4.x.x版本上依然会出现**
                    rb.setButtonDrawable(new ColorDrawable(Color.TRANSPARENT));
                    rb.setTextColor(MapAddressActivity.this.getResources().getColorStateList(R.color.map_template_radio_btn_color));
                    if (radioBtTemplateName != null && radioBtTemplateName.equals(bean.getName())) {
                        radioBtTemplatePosition = i;
                        // radioBtTemplateNameFlg = true;
                    } else if (bean.isAcquiesce()) {
                        //isAddTemplateFlag = false;
                        acquiescePosion = i;
                    }
                    //向radiogroup中添加radiobutton
                    mRadioGroup.addView(rb);
                }

                if (radioBtTemplatePosition != -1) {
                    ((RadioButton) mRadioGroup.getChildAt(radioBtTemplatePosition)).setChecked(true);
                    templateQueryReponseBean = templateQueryList.get(radioBtTemplatePosition);
                } else {
                    //isAddTemplateFlag = false;
                    //设置初始check对象（第一个索引从0开始）
                    ((RadioButton) mRadioGroup.getChildAt(acquiescePosion)).setChecked(true);
                    templateQueryReponseBean = templateQueryList.get(acquiescePosion);
                }
                //监听check对象
                mRadioGroup.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
                    @Override
                    public void onCheckedChanged(RadioGroup group, int checkedId) {
                        int RadiobuttonId = group.getCheckedRadioButtonId();
                        //获取radiobutton对象
                        RadioButton bt = (RadioButton) group.findViewById(RadiobuttonId);
                        radioBtTemplateName = bt.getText().toString();
                        //获取单个对象中的位置
                        int index = group.indexOfChild(bt);
                        //设置滚动位置，可使点击radiobutton时，将该radiobutton移动至第二位置
                        mHorizontalScrollView.smoothScrollTo(bt.getLeft() - bt.getWidth(), 0);

                        if (!isAddTemplateFlag) {
                            showLoadingDialog();
                            if (null != storePlanShortDetailAllList && !storePlanShortDetailAllList.isEmpty()) {
                                storePlanShortDetailAllList.clear();
                            }
                            if (null != businessPlanDetailsAllList && !businessPlanDetailsAllList.isEmpty()) {
                                businessPlanDetailsAllList.clear();
                            }
                            if (null != erpStoreDetailsAllList && !erpStoreDetailsAllList.isEmpty()) {
                                erpStoreDetailsAllList.clear();
                            }
                            //清空地图上的marker
                            if (aMap != null) {
                                aMap.clear();
                                if (locationMarker != null) {
                                    locationMarker = null;
                                }
                                if (latLngLocation != null) {
                                    addLocationMarkerToMap(latLngLocation);
                                }
                            }
                            templateQueryReponseBean = null;
                            templateAddRequestDetail = null;
                            TemplateReadRequest body = new TemplateReadRequest();
                            templateId = templateQueryList.get(index).getId();
                            body.setId(templateId);
                            mPresenter.templateReadPOST(body);
                        }
                    }
                });
                //在mHorizontalScrollView加载mRadioGroup布局
                if (container != null) {
                    container.removeAllViews();
                }
                container.addView(mRadioGroup);
                LayoutInflater inflater = LayoutInflater.from(this);
                View myLayout = inflater.inflate(R.layout.item_template_add_horizontal, null);
                RelativeLayout addRl = myLayout.findViewById(R.id.add_rl);
                addRl.setOnClickListener(this);
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                params.rightMargin = 28;
                myLayout.setLayoutParams(params);
                container.addView(myLayout);

                if (templateQueryReponseBean == null) {
                    Log.d("TAG", "templateQueryReponseBean is null ");
                    templateAddRequestDetail = null;
                } else if (templateQueryReponseBean.getFlag_plant_template_read_res_dto() == null) {
                    templateAddRequestDetail = null;
                    Log.d("TAG", "templateQueryReponseBean.getFlag_plant_template_read_res_dto() is null ");
                } else if (templateQueryReponseBean.getFlag_plant_template_read_res_dto().getDetail() == null) {
                    templateAddRequestDetail = null;
                    Log.d("TAG", "templateQueryReponseBean.getFlag_plant_template_read_res_dto().getDetail() is null ");
                } else {
                    templateAddRequestDetail = templateQueryReponseBean.getFlag_plant_template_read_res_dto().getDetail();
                }
                showLoadingDialog();
                //获取插旗数据请求
                getFlagPlantShortInfoUsing(templateAddRequestDetail);

            } else {
                showMsg("暂无数据");
            }
        }
        radioBtTemplatePosition = -1;
        isAddTemplateFlag = false;
    }

    public void handleChangePoiItem() {
        HashMap<String, Object> map = new HashMap<>();
//        Map<String, Object> mapResult = ViewUtils.objectToMap(content);
//        map.put("org_id", orgId);
//        map.put("org_name", orgName);
        map.put("chooseMap", new Gson().toJson(selectedPoiItem));
//        map.put("id", mapResult.get("id"));
//        Intent mapAddPointIntent = new Intent(this, MapAddPointActivity.class);
//        mapAddPointIntent.putExtra("data", map);
        if (eventBusinessId != null) {
            map.put("id", eventBusinessId);
        }

        EventBus.getDefault().post(new PoiItemChangeEvent("EDIT_POI", map));

//        Intent intent = new Intent(this, MapAddPointActivity.class);

//        intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
//        startActivity(intent);
        finish();

    }

    @Override
    public void templateQueryPOSTFailed(Throwable e) {
        hideLoadingDialog();
    }

    @SuppressLint("NewApi")
    @Override
    public void findFlagPlantShortInfoUsingPOST(FlagPlantInfoShortReponse flagPlantInfoShortReponse) {

        mapOnTouchUp = false;

        if (null != storePlanShortDetailNewList && !storePlanShortDetailNewList.isEmpty()) {
            storePlanShortDetailNewList.clear();
        }
        if (null != businessPlanDetailsNewList && !businessPlanDetailsNewList.isEmpty()) {
            businessPlanDetailsNewList.clear();
        }
        if (null != erpStoreDetailsNewList && !erpStoreDetailsNewList.isEmpty()) {
            erpStoreDetailsNewList.clear();
        }
        if (null != collaborativeDetailsNewList && !collaborativeDetailsNewList.isEmpty()) {
            collaborativeDetailsNewList.clear();
        }
        if (null != competitorDetailsNewList && !competitorDetailsNewList.isEmpty()) {
            competitorDetailsNewList.clear();
        }

        if (null != markerOptionNewList && !markerOptionNewList.isEmpty()) {
            markerOptionNewList.clear();
        }

        if (flagPlantInfoShortReponse != null) {
            FlagPlantInfoShortBean bean = flagPlantInfoShortReponse.data;
            if (bean != null) {
                storePlanShortDetailNewList = bean.getStore_plan_detail_details();


                businessPlanDetailsNewList = bean.getBusiness_plan_details();


                erpStoreDetailsNewList = bean.getErp_store_details();

                //协同品牌列表
                collaborativeDetailsNewList = bean.getCollaborative_details();


                //竞品品牌列表
                competitorDetailsNewList = bean.getCompetitor_details();

                markerSizeSet(markerSize, false);
            }
        }
        hideLoadingDialog();
    }

    @Override
    public void findFlagPlantShortInfoUsingPOSTFailed(Throwable e) {
        mapOnTouchUp = false;
        // showMsg("数据获取异常");
        hideLoadingDialog();
    }

    @Override
    public void templateBrandPOST(TemplateBrandsReponse reponse) {
        if (reponse != null) {
            List<TemplateBrandReponseBean> list = reponse.data;
            if (list != null && !list.isEmpty()) {
                for (TemplateBrandReponseBean bean : list) {
                    if (bean.getType().equals("COLLABORATIVE")) {
                        collaborativeList.add(bean);
                    } else {
                        competitorList.add(bean);
                    }

                }

            }
        }
    }

    @Override
    public void templateBrandPOSTFailed(Throwable e) {

    }

    @Override
    public void templateAddPOST(TemplateAddReponse templateAddReponse) {
        hideLoadingDialog();
        isAddTemplateFlag = true;
        //获取数据
        TemplateQueryRequest templateBody = new TemplateQueryRequest();
        templateBody.setType("FLAG");
        mPresenter.findTemplatePOST(templateBody);
        if (templatebottomSheetDialog != null) {
            templateDefaultCB.setChecked(false);
            templateNameEt.setText("");
            templateReset();
            templatebottomSheetDialog.dismiss();

        }
        showMsg("添加模版成功");
    }

    @Override
    public void templateAddPOSTFailed(Throwable e) {
        hideLoadingDialog();
        if (e != null) {
            //服务器异常
            ExceptionHandle.ResponseThrowable responseThrowable = (ExceptionHandle.ResponseThrowable) e;
            String msg = responseThrowable.msg;
            // String msg = e.getMessage();
            if (!TextUtils.isEmpty(msg)) {
                showMsg(msg);
            }
        } else {
            showMsg("添加模版失败");
        }
    }

    @Override
    public void templateReadPOST(TemplateReadReponse templateReadReponse) {
        if (templateReadReponse == null) {
            Log.d("TAG", "templateReadReponse is null ");
            templateAddRequestDetail = null;
        }
        if (templateReadReponse.data == null) {
            Log.d("TAG", "templateReadReponse.data is null ");
            templateAddRequestDetail = null;
        }
        templateAddRequestDetail = templateReadReponse.data.getDetail();
        if (templateAddRequestDetail == null) {
            Log.d("TAG", "templateReadReponse.data.getDetail() is null ");
        }

        getFlagPlantShortInfoUsing(templateAddRequestDetail);

    }

    @Override
    public void templateReadPOSTFailed(Throwable e) {
        hideLoadingDialog();
    }

    @Override
    public void erpstoreReadPOST(MapPointErpStoreDetailReponse mapPointDetailReponse) {
        if (mapPointDetailReponse == null) {
            return;
        }
        int code = mapPointDetailReponse.code;
        if (code == 0) {
            LatLng latLng = new LatLng(mapPointDetailReponse.data.getLatitude(), mapPointDetailReponse.data.getLongitude());
            String name = mapPointDetailReponse.data.getName();
            String state = mapPointDetailReponse.data.getState();
            long markerId = mapPointDetailReponse.data.getId();
            if (isSearchSelected) {
                setSearchMakerDrawable(markerWidth, markerHeight, latLng, name,"", markerId, "erpStore", state, "", null);
                isSearchSelected = false;
            }

            //List<FileBean> files = mapPointDetailReponse.data.getFiles();
            Intent intent = new Intent(this, MapPointDetailTwoActivity.class);
            intent.putExtra("markerType", "erpStore");
            intent.putExtra("name", mapPointDetailReponse.data.getStore_name());
            intent.putExtra("state", mapPointDetailReponse.data.getState());
            //intent.putExtra("allow_look", mapPointDetailReponse.data.isAllow_look());
            //intent.putExtra("store_valuation", mapPointDetailReponse.data.getStore_valuation());
            intent.putExtra("business_plan_name", mapPointDetailReponse.data.getStore_full_address());
            //intent.putExtra("files", (Serializable) files);
            startActivity(intent);
        } else {
            if (!TextUtils.isEmpty(mapPointDetailReponse.msg)) {
                showMsg(mapPointDetailReponse.msg);
            }
        }

    }

    @Override
    public void erpstoreReadPOSTFailed(Throwable e) {
        if (e != null) {
            //服务器异常
            ExceptionHandle.ResponseThrowable responseThrowable = (ExceptionHandle.ResponseThrowable) e;
            String msg = responseThrowable.msg;
            // String msg = e.getMessage();
            if (!TextUtils.isEmpty(msg)) {
                showMsg(msg);
            }
        }
    }

    @Override
    public void businessReadPOST(MapPointBusinessDetailReponse mapBusinessDetailReponse) {
        if (mapBusinessDetailReponse == null) {
            return;
        }
        int code = mapBusinessDetailReponse.code;
        if (code == 0) {
            List<LatLng> area_range = mapBusinessDetailReponse.data.getArea_range();
            LatLng latLng = new LatLng(area_range.get(0).latitude, area_range.get(0).longitude);
            String name = mapBusinessDetailReponse.data.getName();
            String createTime = mapBusinessDetailReponse.data.getCreate_time();
            String state = mapBusinessDetailReponse.data.getState();
            long markerId = mapBusinessDetailReponse.data.getId();
            if (isSearchSelected) {
                setSearchMakerDrawable(markerWidth, markerHeight, latLng, name,createTime, markerId, "businessPlan", state, "", area_range);
                isSearchSelected = false;
            }

            List<FileBean> files = mapBusinessDetailReponse.data.getFiles();
//            List<String> labelNames = mapPointDetailReponse.data.getLabel_names();
            // 创建并初始化List
//            List<String> labelNames = new ArrayList<>();
//            labelNames.add("开发标签");
//            labelNames.add("Item2");
//            labelNames.add("3333@");
            Intent intent = new Intent(this, MapPointDetailTwoActivity.class);
            intent.putExtra("markerType", "businessPlan");
            intent.putExtra("storePlanId", mapBusinessDetailReponse.data.getId());
            intent.putExtra("name", name);
            intent.putExtra("state", state);
            intent.putExtra("schedule", mapBusinessDetailReponse.data.getSchedule());
            intent.putExtra("store_valuation", mapBusinessDetailReponse.data.getLevel_name());
            intent.putExtra("business_plan_name", mapBusinessDetailReponse.data.getArea_name());
            intent.putExtra("main_label_name", mapBusinessDetailReponse.data.getMain_label_name());
            intent.putExtra("sub_label_name", mapBusinessDetailReponse.data.getSub_label_name());
            //intent.putStringArrayListExtra("label_names", (ArrayList<String>) labelNames);

            intent.putExtra("files", (Serializable) files);
            startActivity(intent);
        } else {
            Log.d("TAG", "businessReadPOST  code : -----" + mapBusinessDetailReponse.code);
            if (!TextUtils.isEmpty(mapBusinessDetailReponse.msg)) {
                Log.d("TAG", "businessReadPOST  code : -----" + mapBusinessDetailReponse.msg);
                showMsg(mapBusinessDetailReponse.msg);
            }
        }

    }

    @Override
    public void businessReadPOSTFailed(Throwable e) {
        if (e != null) {
            //服务器异常
            ExceptionHandle.ResponseThrowable responseThrowable = (ExceptionHandle.ResponseThrowable) e;
            String msg = responseThrowable.msg;
            // String msg = e.getMessage();
            if (!TextUtils.isEmpty(msg)) {
                showMsg(msg);
            }
        }
    }

    /**
     * 在地图点位marker详情回调
     */
    @Override
    public void storeReadPOST(MapPointDetailReponse mapPointDetailReponse) {
        if (mapPointDetailReponse == null) {
            return;
        }
        int code = mapPointDetailReponse.code;
        if (code == 0) {
            LatLng latLng = new LatLng(mapPointDetailReponse.data.getInfo().getLatitude(), mapPointDetailReponse.data.getInfo().getLongitude());
            String name = mapPointDetailReponse.data.getName();
            String state = mapPointDetailReponse.data.getState();
            long markerId = mapPointDetailReponse.data.getId();
            if (isSearchSelected) {
                setSearchMakerDrawable(markerWidth, markerHeight, latLng, name,"", markerId, "storePlan", state, "", null);
                isSearchSelected = false;
            }

            List<FileBean> files = mapPointDetailReponse.data.getFiles();
            Intent intent = new Intent(this, MapPointDetailTwoActivity.class);
            intent.putExtra("markerType", "storePlan");
            intent.putExtra("storePlanId", mapPointDetailReponse.data.getId());
            intent.putExtra("name", name);
            intent.putExtra("state", state);
            intent.putExtra("allow_look", mapPointDetailReponse.data.isAllow_look());
            intent.putExtra("store_valuation", mapPointDetailReponse.data.getStore_valuation());
            intent.putExtra("business_plan_name", mapPointDetailReponse.data.getBusiness_plan_name());
            intent.putExtra("files", (Serializable) files);
            startActivity(intent);
        } else {
            if (!TextUtils.isEmpty(mapPointDetailReponse.msg)) {
                showMsg(mapPointDetailReponse.msg);
            }
        }

    }

    @Override
    public void storeReadPOSTFailed(Throwable e) {
        if (e != null) {
            //服务器异常
            ExceptionHandle.ResponseThrowable responseThrowable = (ExceptionHandle.ResponseThrowable) e;
            String msg = responseThrowable.msg;
            // String msg = e.getMessage();
            if (!TextUtils.isEmpty(msg)) {
                showMsg(msg);
            }
        }
    }

    @Override
    public void collaborativeReadPOST(MapPointCollaborativeDetailReponse reponse) {
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0) {
            LatLng latLng = new LatLng(reponse.data.getInfo().getLatitude(), reponse.data.getInfo().getLongitude());
            String name = reponse.data.getName();
            String state = reponse.data.getState();
            long markerId = reponse.data.getId();
            String markerUrl = reponse.data.getUrl();
            if (isSearchSelected) {
                setSearchMakerDrawable(markerWidth, markerHeight, latLng, name,"", markerId, "collaborativeMark", state, markerUrl, null);
                isSearchSelected = false;
            }

            List<FileBean> files = reponse.data.getFiles();
            // 计算距离
            double distance = AMapUtils.calculateLineDistance(latLngLocation, latLng);
            // 将距离和时间转换为更易于理解的格式
            double km = distance / 1000;
            String distanceStr = String.format("%.1fkm", km);

            Intent intent = new Intent(this, MapPointDetailTwoActivity.class);
            intent.putExtra("markerType", "brand");
            intent.putExtra("name", name);
            intent.putExtra("distance", distanceStr);
            intent.putExtra("state", state);
            intent.putExtra("files", (Serializable) files);
            intent.putExtra("business_plan_name", reponse.data.getAddress());
            startActivity(intent);
        }
    }

    @Override
    public void collaborativeReadPOSTFailed(Throwable e) {
        if (e != null) {
            //服务器异常
            ExceptionHandle.ResponseThrowable responseThrowable = (ExceptionHandle.ResponseThrowable) e;
            String msg = responseThrowable.msg;
            // String msg = e.getMessage();
            if (!TextUtils.isEmpty(msg)) {
                showMsg(msg);
            }
        }
    }

    @Override
    public void competitorReadPOST(MapPointCompetitorDetailReponse reponse) {
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0) {
            LatLng latLng = new LatLng(reponse.data.getInfo().getLatitude(), reponse.data.getInfo().getLongitude());
            String name = reponse.data.getName();
            String state = reponse.data.getState();
            long markerId = reponse.data.getId();
            String markerUrl = reponse.data.getUrl();
            if (isSearchSelected) {
                setSearchMakerDrawable(markerWidth, markerHeight, latLng, name,"", markerId, "competitorMark", state, markerUrl, null);
                isSearchSelected = false;
            }

            List<FileBean> files = reponse.data.getFiles();
            // 计算距离
            double distance = AMapUtils.calculateLineDistance(latLngLocation, latLng);
            // 将距离和时间转换为更易于理解的格式
            double km = distance / 1000;
            String distanceStr = String.format("%.1fkm", km);

            Intent intent = new Intent(this, MapPointDetailTwoActivity.class);
            intent.putExtra("markerType", "brand");
            intent.putExtra("name", name);
            intent.putExtra("distance", distanceStr);
            intent.putExtra("state", state);
            intent.putExtra("files", (Serializable) files);
            intent.putExtra("business_plan_name", reponse.data.getAddress());
            startActivity(intent);
        }
    }

    @Override
    public void competitorReadPOSTFailed(Throwable e) {
        if (e != null) {
            //服务器异常
            ExceptionHandle.ResponseThrowable responseThrowable = (ExceptionHandle.ResponseThrowable) e;
            String msg = responseThrowable.msg;
            // String msg = e.getMessage();
            if (!TextUtils.isEmpty(msg)) {
                showMsg(msg);
            }
        }
    }

    @Override
    public void storePlanInitialPOST(StorePlanInitailReponse reponse) {
        hideLoadingDialog();
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0) {
            Object content = reponse.data;
            if (null != content) {
                HashMap<String, Object> map = new HashMap<>();
                Map<String, Object> mapResult = ViewUtils.objectToMap(content);
                map.put("org_id", orgId);
                map.put("org_name", orgName);
                map.put("chooseMap", new Gson().toJson(selectedPoiItem));
                map.put("id", mapResult.get("id"));
                Intent mapAddPointIntent = new Intent(this, MapAddPointActivity.class);
                mapAddPointIntent.putExtra("data", map);

                startActivity(mapAddPointIntent);

//                // 转换为 JSONObject
//                Gson gson = new Gson();
//                Map<String, Object> mapResult = ViewUtils.objectToMap(content);
//                mapResult.put("org_id", orgId);
//                mapResult.put("org_name", orgName);
//                String json = gson.toJson(mapResult);
//                Log.i("TAG", "---------storePlanInitialPOST--content---json-" + json);
//                //发送eventBus消息通知MainActivity更新数据
//                EventBus.getDefault().post(new StorePlanMessageEvent("storePlanInitial", true, false, json));
//                //友盟数据统计
//                MobclickAgent.onEvent(this, "storePlanAdd_click");
//                new Handler().postDelayed(new Runnable() {
//                    @Override
//                    public void run() {
//                        // 你的代码，延迟2秒后执行
//                        Intent intenttwo = new Intent(MapActivity.this, MainActivity.class);
//                        startActivity(intenttwo);
//                    }
//                }, 2000);
            }
        }
    }

    @Override
    public void storePlanInitialPOSTFailed(Throwable e) {
        hideLoadingDialog();
        Log.i("TAG", "---------storePlanInitialPOSTFailed------");
    }

    @Override
    public void businessPlanInitialPOST(StorePlanInitailReponse reponse) {
        hideLoadingDialog();
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0) {
            Object content = reponse.data;
            // 转换为 JSONObject
            if (null != content) {
                Log.i("TAG", "---------businessPlanInitialPOST--content----" + String.valueOf(content));
                Gson gson = new Gson();
                HashMap<String, Object> map = new HashMap<>();
                Map<String, Object> mapResult = ViewUtils.objectToMap(content);
                map.put("org_id", orgId);
                map.put("org_name", orgName);
                map.put("id", mapResult.get("id"));
                String json = gson.toJson(mapResult);
                Log.i("TAG", "---------businessPlanInitialPOST--content---json-" + json);
                Intent mapAddBusinessIntent = new Intent(this, MapAddBusinessActivity.class);
                mapAddBusinessIntent.putExtra("drawType", isDrawWayFlag);
                mapAddBusinessIntent.putExtra("isMapJumpFlag", isMapJumpFlag);
                mapAddBusinessIntent.putExtra("scheduleStatus", "INIT");
                mapAddBusinessIntent.putExtra("mapLevel", aMap.getCameraPosition().zoom);
                mapAddBusinessIntent.putExtra("dataMap", map);
                if (isDrawWayFlag) {
                    mapAddBusinessIntent.putExtra("drawPoints", (Serializable) drawFreePoints);
                } else {
                    mapAddBusinessIntent.putExtra("drawPoints", (Serializable) mapCameraCenterLatLngList);
                }
                if (isMapJumpFlag) {
                    mapAddBusinessIntent.putExtra("saveDataMap", saveDataMap);
                }
                startActivity(mapAddBusinessIntent);
            }
        }
    }

    @Override
    public void businessPlanInitialPOSTFailed(Throwable e) {
        hideLoadingDialog();
        Log.i("TAG", "---------businessPlanInitialPOSTFailed------");
    }

    @Override
    public void findOrganizationPOST(FindOrganizationReponse reponse) {
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0) {
            organizationList = reponse.data;
            if (null != organizationList && !organizationList.isEmpty()) {
                if (1 == organizationList.size()) {
                    orgId = organizationList.get(0).getId();
                    orgName = organizationList.get(0).getName();
                    if (flagAddStorePlan) {
                        getStorePlanInitial();
                    } else {
                        getBusinessPlanInitial();
                    }
                } else {
                    showOrganizationDialog();
                }
            }
        } else {
            showMsg("获取组织失败");
        }
    }

    @Override
    public void findOrganizationPOSTFailed(Throwable e) {
        showMsg("获取组织失败");
    }

    @Override
    public void getKmsUserInfoPOST(KmsUserInfoResponse reponse) {

    }

    @Override
    public void getKmsUserInfoPOSTFailed(Throwable e) {

    }

    /**
     * 在地图上添加marker
     */
    private void addMarkersToMap(int width, int height, List<StorePlanShortDetail> storePlanShortDetailList
            , List<BusinessPlanShortDetail> businessPlanDetailsList
            , List<ErpStoreShortDetail> erpStoreDetailsList
            , List<CompetitorMarkShortDetail> collaborativeDetailsList
            , List<CompetitorMarkShortDetail> competitorDetailsList) {

        pointAnntionList.clear();
        buniessList.clear();
        if (storePlanShortDetailList != null && !storePlanShortDetailList.isEmpty()) {
            LatLng latlng;
            int makerId;
            String pointPositionState;
            for (int i = 0; i < storePlanShortDetailList.size(); i++) {
                latlng = new LatLng(storePlanShortDetailList.get(i).getInfo().getLatitude(), storePlanShortDetailList.get(i).getInfo().getLongitude());
                makerId = storePlanShortDetailList.get(i).getId();
                pointPositionState = storePlanShortDetailList.get(i).getState();
                String pointName = storePlanShortDetailList.get(i).getName();

                if (pointPositionState.equals("INIT_PRE")) {
                    setMakerDrawable(R.drawable.map_marker_two_iv, width, height, latlng, pointName,"", makerId, "storePlan",false);
//                    if (setSize.equals("big")) {
//                        setMakerDrawable(R.drawable.map_marker_two_big, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("middle")) {
//                        setMakerDrawable(R.drawable.map_marker_two_middle, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("small")) {
//                        setMakerDrawable(R.drawable.map_marker_two_small, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("very_small")) {
//                        setMakerDrawable(R.drawable.map_marker_two_very_small, latlng, pointName, makerId, "storePlan");
//                    }
                } else if (pointPositionState.equals("INIT") || pointPositionState.equals("FIRST_AUDIT")) {
                    setMakerDrawable(R.drawable.map_marker_four_iv, width, height, latlng, pointName,"", makerId, "storePlan",false);
//                    if (setSize.equals("big")) {
//                        setMakerDrawable(R.drawable.map_marker_four_big, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("middle")) {
//                        setMakerDrawable(R.drawable.map_marker_four_middle, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("small")) {
//                        setMakerDrawable(R.drawable.map_marker_four_small, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("very_small")) {
//                        setMakerDrawable(R.drawable.map_marker_four_very_small, latlng, pointName, makerId, "storePlan");
//                    }
                } else if (pointPositionState.equals("SECOND_AUDIT") || pointPositionState.equals("STORE_BUILD")) {
                    setMakerDrawable(R.drawable.map_marker_three_iv, width, height, latlng, pointName,"", makerId, "storePlan",false);
//                    if (setSize.equals("big")) {
//                        setMakerDrawable(R.drawable.map_marker_three_big, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("middle")) {
//                        setMakerDrawable(R.drawable.map_marker_three_middle, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("small")) {
//                        setMakerDrawable(R.drawable.map_marker_three_small, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("very_small")) {
//                        setMakerDrawable(R.drawable.map_marker_three_very_small, latlng, pointName, makerId, "storePlan");
//                    }
                } else if (pointPositionState.equals("STORE_SIGN")) { //已双签
                    setMakerDrawable(R.drawable.map_marker_one_iv, width, height, latlng, pointName,"", makerId, "storePlan",false);
//                    if (setSize.equals("big")) {
//                        setMakerDrawable(R.drawable.map_marker_one_big, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("middle")) {
//                        setMakerDrawable(R.drawable.map_marker_one_middle, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("small")) {
//                        setMakerDrawable(R.drawable.map_marker_one_small, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("very_small")) {
//                        setMakerDrawable(R.drawable.map_marker_one_very_small, latlng, pointName, makerId, "storePlan");
//                    }
                } else {
                    setMakerDrawable(R.drawable.map_marker_five_iv, width, height, latlng, pointName,"", makerId, "storePlan",false);
//                    if (setSize.equals("big")) {
//                        setMakerDrawable(R.drawable.map_marker_five_big, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("middle")) {
//                        setMakerDrawable(R.drawable.map_marker_five_middle, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("small")) {
//                        setMakerDrawable(R.drawable.map_marker_five_small, latlng, pointName, makerId, "storePlan");
//                    } else if (setSize.equals("very_small")) {
//                        setMakerDrawable(R.drawable.map_marker_five_very_small, latlng, pointName, makerId, "storePlan");
//                    }
                }
            }
        }
        if (businessPlanDetailsList != null && !businessPlanDetailsList.isEmpty()) {
            LatLng latlng;
            int markerId;
            String businessState, businessName,businessCreatTime;
            List<LatLng> area_range;

            for (int i = 0; i < businessPlanDetailsList.size(); i++) {
                area_range = businessPlanDetailsList.get(i).getArea_range();
                latlng = new LatLng(area_range.get(0).latitude, area_range.get(0).longitude);
                markerId = businessPlanDetailsList.get(i).getId();
                businessState = businessPlanDetailsList.get(i).getState();
                businessName = businessPlanDetailsList.get(i).getName();
                businessCreatTime = businessPlanDetailsList.get(i).getCreate_time();
                if (businessState.equals("INIT_PRE")) {
                    setMakerDrawable(R.drawable.map_business_marker_one_iv, width, height, latlng, businessName,businessCreatTime, markerId, "businessPlan",true);
//                    if (setSize.equals("big")) {
//                        setMakerDrawable(R.drawable.map_business_marker_one_big, latlng, businessName, markerId, "businessPlan");
//                    } else if (setSize.equals("middle")) {
//                        setMakerDrawable(R.drawable.map_business_marker_one_middle, latlng, businessName, markerId, "businessPlan");
//                    } else if (setSize.equals("small")) {
//                        setMakerDrawable(R.drawable.map_business_marker_one_small, latlng, businessName, markerId, "businessPlan");
//                    } else if (setSize.equals("very_small")) {
//                        setMakerDrawable(R.drawable.map_business_marker_one_very_small, latlng, businessName, markerId, "businessPlan");
//                    }
                    setPolygonDrawable(area_range, 1);
                } else if (businessState.equals("INIT") || businessState.equals("FIRST_AUDIT")) {
                    setMakerDrawable(R.drawable.map_business_marker_three_iv, width, height, latlng, businessName,businessCreatTime, markerId, "businessPlan",true);
//                    if (setSize.equals("big")) {
//                        setMakerDrawable(R.drawable.map_business_marker_three_big, latlng, businessName, markerId, "businessPlan");
//                    } else if (setSize.equals("middle")) {
//                        setMakerDrawable(R.drawable.map_business_marker_three_middle, latlng, businessName, markerId, "businessPlan");
//                    } else if (setSize.equals("small")) {
//                        setMakerDrawable(R.drawable.map_business_marker_three_small, latlng, businessName, markerId, "businessPlan");
//                    } else if (setSize.equals("very_small")) {
//                        setMakerDrawable(R.drawable.map_business_marker_three_very_small, latlng, businessName, markerId, "businessPlan");
//                    }
                    setPolygonDrawable(area_range, 2);
                } else if (businessState.equals("SECOND_AUDIT")) {
                    setMakerDrawable(R.drawable.map_business_marker_four_iv, width, height, latlng, businessName,businessCreatTime, markerId, "businessPlan",true);
//                    if (setSize.equals("big")) {
//                        setMakerDrawable(R.drawable.map_business_marker_four_big, latlng, businessName, markerId, "businessPlan");
//                    } else if (setSize.equals("middle")) {
//                        setMakerDrawable(R.drawable.map_business_marker_four_middle, latlng, businessName, markerId, "businessPlan");
//                    } else if (setSize.equals("small")) {
//                        setMakerDrawable(R.drawable.map_business_marker_four_small, latlng, businessName, markerId, "businessPlan");
//                    } else if (setSize.equals("very_small")) {
//                        setMakerDrawable(R.drawable.map_business_marker_four_very_small, latlng, businessName, markerId, "businessPlan");
//                    }
                    setPolygonDrawable(area_range, 3);
                } else {
                    setMakerDrawable(R.drawable.map_business_marker_two_iv, width, height, latlng, businessName,businessCreatTime, markerId, "businessPlan",true);
//                    if (setSize.equals("big")) {
//                        setMakerDrawable(R.drawable.map_business_marker_two_big, latlng, businessName, markerId, "businessPlan");
//                    } else if (setSize.equals("middle")) {
//                        setMakerDrawable(R.drawable.map_business_marker_two_middle, latlng, businessName, markerId, "businessPlan");
//                    } else if (setSize.equals("small")) {
//                        setMakerDrawable(R.drawable.map_business_marker_two_small, latlng, businessName, markerId, "businessPlan");
//                    } else if (setSize.equals("very_small")) {
//                        setMakerDrawable(R.drawable.map_business_marker_two_very_small, latlng, businessName, markerId, "businessPlan");
//                    }
                    setPolygonDrawable(area_range, 4);
                }
            }
        }
        if (erpStoreDetailsList != null && !erpStoreDetailsList.isEmpty()) {
            LatLng latlng;
            long markerId;
            String erpStoreName;
            for (int i = 0; i < erpStoreDetailsList.size(); i++) {
                latlng = new LatLng(erpStoreDetailsList.get(i).getLatitude(), erpStoreDetailsList.get(i).getLongitude());
                markerId = erpStoreDetailsList.get(i).getId();
                erpStoreName = erpStoreDetailsList.get(i).getName();
                setMakerDrawable(R.drawable.map_marker_one_iv, width, height, latlng, erpStoreName,"", markerId, "erpStore",false);
//                if (setSize.equals("big")) {
//                    setMakerDrawable(R.drawable.map_marker_one_big, latlng, erpStoreName, markerId, "erpStore");
//                } else if (setSize.equals("middle")) {
//                    setMakerDrawable(R.drawable.map_marker_one_middle, latlng, erpStoreName, markerId, "erpStore");
//                } else if (setSize.equals("small")) {
//                    setMakerDrawable(R.drawable.map_marker_one_small, latlng, erpStoreName, markerId, "erpStore");
//                } else if (setSize.equals("very_small")) {
//                    setMakerDrawable(R.drawable.map_marker_one_very_small, latlng, erpStoreName, markerId, "erpStore");
//                }
            }
        }
        if (collaborativeDetailsList != null && !collaborativeDetailsList.isEmpty()) {
            LatLng latlng;
            long markerId;
            String collaborativeName, collaborativeUrl;
            for (int i = 0; i < collaborativeDetailsList.size(); i++) {
                latlng = new LatLng(collaborativeDetailsList.get(i).getInfo().getLatitude(), collaborativeDetailsList.get(i).getInfo().getLongitude());
                markerId = collaborativeDetailsList.get(i).getId();
                collaborativeName = collaborativeDetailsList.get(i).getName();
                collaborativeUrl = collaborativeDetailsList.get(i).getUrl();
                setMakerDrawableUrl(collaborativeUrl, width, height, latlng, collaborativeName, markerId, "collaborativeMark");
            }
        }
        if (competitorDetailsList != null && !competitorDetailsList.isEmpty()) {
            LatLng latlng;
            long markerId;
            String competitorName, competitorUrl;
            for (int i = 0; i < competitorDetailsList.size(); i++) {
                latlng = new LatLng(competitorDetailsList.get(i).getInfo().getLatitude(), competitorDetailsList.get(i).getInfo().getLongitude());
                markerId = competitorDetailsList.get(i).getId();
                competitorName = competitorDetailsList.get(i).getName();
                competitorUrl = competitorDetailsList.get(i).getUrl();
                setMakerDrawableUrl(competitorUrl, width, height, latlng, competitorName, markerId, "competitorMark");
            }
        }
        if (pointAnntionList != null && !pointAnntionList.isEmpty()) {

            List<Marker> markers = pointMarkerList;
            List<Marker> deleteMark = new ArrayList<>(markers);
            ArrayList<MarkerOptions> addMark = new ArrayList<>(pointAnntionList);
            ArrayList<Marker> toKeep = new ArrayList<>();

//            先计算保持不动的数据
            for (MarkerOptions option : pointAnntionList) {
                for (int i = 0; i < markers.size(); i++) {
                    Marker marker = markers.get(i);
                    if (marker.getOptions().getSnippet().equals(option.getSnippet())) {
                        toKeep.add(marker);
                    }
                }
            }

//            计算需要添加的
            for (int i = 0; i < pointAnntionList.size(); i++) {
                MarkerOptions option = pointAnntionList.get(i);
                for (Marker marker : toKeep) {
                    if (marker.getOptions().getSnippet().equals(option.getSnippet())) {
                        addMark.remove(option);
                    }
                }
            }

//            计算需要删除的
            for (Marker marker : pointMarkerList) {
                for (int i = 0; i < toKeep.size(); i++) {
                    Marker marker1 = toKeep.get(i);
                    if (marker.getOptions().getSnippet().equals(marker1.getOptions().getSnippet())) {
                        deleteMark.remove(marker);
                    }
                }
            }

            for (Marker marker : deleteMark) {
                marker.remove();
            }

            ArrayList<Marker> toaddList = new ArrayList<>();
            if (!isFirstLoad) {//第一次加载不执行动画
                toaddList = aMap.addMarkers(addMark, false);
            } else {//第二次
                if (pointAnntionList.size() >= 50) {
                    toaddList = aMap.addMarkers(addMark, false);
                } else {//可以执行动画
                    for (MarkerOptions option : addMark) {
                        Marker marker = aMap.addMarker(option);
                        Animation animation = new ScaleAnimation(0, 1, 0, 1);
                        animation.setInterpolator(new LinearInterpolator());
                        //整个移动所需要的时间
                        animation.setDuration(300);
                        //设置动画
                        marker.setAnimation(animation);
                        //开始动画
                        marker.startAnimation();
                        toaddList.add(marker);
                    }
                }
            }


            pointMarkerList.clear();
            pointMarkerList.addAll(toaddList);
            pointMarkerList.addAll(toKeep);
            isFirstLoad = true;

            Log.d("TAG", "需要保持不动的点位=====" + toKeep.size());
            Log.d("TAG", "需要删除的点位=====" + deleteMark.size());
            Log.d("TAG", "需要添加的点位=====" + addMark.size());
            Log.d("TAG", "本次接口返回的点位=====" + pointAnntionList.size());
            Log.d("TAG", "刷新地图返回的点位=====" + pointMarkerList.size());

        }

        if (buniessList != null && !buniessList.isEmpty()) {
            List<Polygon> markers = buniessAllList;
            List<Polygon> deleteMark = new ArrayList<>(markers);
            ArrayList<PolygonOptions> addMark = new ArrayList<>(buniessList);
            ArrayList<Polygon> toKeep = new ArrayList<>();

//            先计算保持不动的数据
            for (PolygonOptions option : buniessList) {
                for (int i = 0; i < markers.size(); i++) {
                    Polygon poly = markers.get(i);
                    if (areLatLngListsEqual(poly.getPoints(), option.getPoints(), 1e-5)) {
                        toKeep.add(poly);
                    }
                }
            }

            //            计算需要添加的
            for (int i = 0; i < buniessList.size(); i++) {
                PolygonOptions option = buniessList.get(i);
                for (Polygon poly : toKeep) {
                    if (areLatLngListsEqual(poly.getPoints(), option.getPoints(), 1e-5)) {
                        addMark.remove(option);
                    }
                }
            }

            //            计算需要删除的
            for (Polygon marker : buniessAllList) {
                for (int i = 0; i < toKeep.size(); i++) {
                    Polygon marker1 = toKeep.get(i);
                    if (areLatLngListsEqual(marker.getPoints(), marker1.getPoints(), 1e-5)) {
                        deleteMark.remove(marker);
                    }
                }
            }

            for (Polygon marker : deleteMark) {
                marker.remove();
            }

            ArrayList<Polygon> toaddList = new ArrayList<>();
            for (PolygonOptions marker : addMark) {
                Polygon poly = aMap.addPolygon(marker);
                toaddList.add(poly);
            }


            buniessAllList.clear();
            buniessAllList.addAll(toaddList);
            buniessAllList.addAll(toKeep);

            Log.d("TAG", "需要保持不动的商圈=====" + toKeep.size());
            Log.d("TAG", "需要删除的商圈=====" + deleteMark.size());
            Log.d("TAG", "需要添加的商圈=====" + addMark.size());
            Log.d("TAG", "本次接口返回的商圈=====" + buniessList.size());
            Log.d("TAG", "刷新地图返回的商圈=====" + buniessAllList.size());

        }

    }

    public boolean areLatLngListsEqual(List<LatLng> list1, List<LatLng> list2, double epsilon) {
        if (list1.size() != list2.size()) return false;

        for (int i = 0; i < list1.size(); i++) {
            LatLng a = list1.get(i);
            LatLng b = list2.get(i);
            if (Math.abs(a.latitude - b.latitude) > epsilon ||
                    Math.abs(a.longitude - b.longitude) > epsilon) {
                return false;
            }
        }
        return true;
    }

    // 使用示例


    private void setMakerDrawable(int mapMark, int width, int height, LatLng latlng, String name,String creatTime, long makerId, String markerType,boolean isBusinessTimeShow) {
        markerOption = new MarkerOptions().icon(BitmapDescriptorFactory
                        .fromView(ViewUtils.getMakerBitmapView(this, mapMark, width, height, name,creatTime, isMarkerNameShow,isBusinessTimeShow)))
                .position(latlng)
                .title(markerType)
                .snippet(String.valueOf(makerId))
                .anchor(0.5f, 0.5f)
                .draggable(false);
        //.setFlat(true) // 将MarkerOptions的flat属性设置为true来让InfoWindow保持在地图平面上
        // markerOptionNewList.add(markerOption);
//        Marker marker = aMap.addMarker(markerOption);
        pointAnntionList.add(markerOption);

        if (isSearchSelected) {
            Marker marker = aMap.addMarker(markerOption);
//             将地图的中心点设置为centerPosition
            aMap.moveCamera(CameraUpdateFactory.changeLatLng(latlng));
            // 可以选择将缩放级别设置为一个合适的值
            aMap.moveCamera(CameraUpdateFactory.zoomTo(15.5f));
            growInto(marker);
        }
//        growStartInto(marker);
    }

    private void setMakerDrawableUrl(String mapMarkUrl, int width, int height, LatLng latlng, String name, long makerId, String markerType) {
        uiHandler.post(new Runnable() {
            @Override
            public void run() {
                markerOption = new MarkerOptions().icon(BitmapDescriptorFactory
                                .fromView(ViewUtils.getMakerBitmapUrlView(MapAddressActivity.this, mapMarkUrl, width, height, name, isMarkerNameShow)))
                        .position(latlng)
                        .title(markerType)
                        .anchor(0.5f, 0.5f)
                        .snippet(String.valueOf(makerId))
                        .draggable(false);
                //.setFlat(true) // 将MarkerOptions的flat属性设置为true来让InfoWindow保持在地图平面上
                //markerOptionNewList.add(markerOption);
            }
        });

//        Marker marker = aMap.addMarker(markerOption);
        pointAnntionList.add(markerOption);
        if (isSearchSelected) {
            Marker marker = aMap.addMarker(markerOption);
//             将地图的中心点设置为centerPosition
            aMap.moveCamera(CameraUpdateFactory.changeLatLng(latlng));
            // 可以选择将缩放级别设置为一个合适的值
            aMap.moveCamera(CameraUpdateFactory.zoomTo(15.5f));
            growInto(marker);
        }
//        growStartInto(marker);
    }

    //设置商圈多边形
    private void setPolygonDrawable(List<LatLng> area_range, int type) {
        int strokeColorId, fillColorId;
        if (1 == type) {
            strokeColorId = getResources().getColor(R.color.color_map_business_polygon_stroke_blue);
            fillColorId = getResources().getColor(R.color.color_map_business_polygon_fill_blue);
        } else if (2 == type) {
            strokeColorId = getResources().getColor(R.color.color_map_business_polygon_stroke_yellow);
            fillColorId = getResources().getColor(R.color.color_map_business_polygon_fill_yellow);
        } else if (3 == type) {
            strokeColorId = getResources().getColor(R.color.color_map_business_polygon_stroke_green);
            fillColorId = getResources().getColor(R.color.color_map_business_polygon_fill_green);
        } else {
            strokeColorId = getResources().getColor(R.color.color_map_business_polygon_stroke_grey);
            fillColorId = getResources().getColor(R.color.color_map_business_polygon_fill_grey);
        }
        // 声明 多边形参数对象
        PolygonOptions polygonOptions = new PolygonOptions();
// 添加 多边形的每个顶点（顺序添加）
        polygonOptions.addAll(area_range);
        polygonOptions.strokeWidth(4) // 多边形的边框
                .strokeColor(strokeColorId) // 边框颜色
                .fillColor(fillColorId);   // 多边形的填充色
//        aMap.addPolygon(polygonOptions);
        buniessList.add(polygonOptions);
    }


    //设置商圈多边形
    private void setDrawPolygonDrawable(List<LatLng> area_range) {
        if (drawPolygon != null) {
            drawPolygon.remove(); // 移除临时绘制的多边形
            drawPolygon = null; // 清空reference
        }
        if (null != area_range && !area_range.isEmpty()) {
            if (area_range.size() > 2) {
                // 声明 多边形参数对象
                PolygonOptions polygonOptions = new PolygonOptions();
// 添加 多边形的每个顶点（顺序添加）
                polygonOptions.addAll(area_range);
                polygonOptions.strokeWidth(4) // 多边形的边框
                        .strokeColor(getResources().getColor(R.color.color_map_draw_red)) // 边框颜色
                        .fillColor(getResources().getColor(R.color.color_map_draw_fill_red)); // 多边形的填充色
                drawPolygon = aMap.addPolygon(polygonOptions);
            }
        }

        //  setDrawPolyLineOthers();
    }

    //商圈自由绘制多边形
//    private void drawFreePolygon(List<LatLng> area_range) {
//        if (drawFreePolygon != null) {
//            drawFreePolygon.remove(); // 移除临时绘制的多边形
//            drawFreePolygon = null; // 清空reference
//        }
//        if (null != area_range && !area_range.isEmpty()) {
//            if (area_range.size() > 2) {
//                // 声明 多边形参数对象
//                PolygonOptions polygonOptions = new PolygonOptions();

    /// / 添加 多边形的每个顶点（顺序添加）
//                polygonOptions.addAll(area_range);
//                polygonOptions.strokeWidth(4) // 多边形的边框
//                        .strokeColor(getResources().getColor(R.color.color_map_draw_red)) // 边框颜色
//                        .fillColor(getResources().getColor(R.color.color_map_draw_fill_red)); // 多边形的填充色
//
//                drawFreePolygon = aMap.addPolygon(polygonOptions);
//            }
//        }
//        //  setDrawPolyLineOthers();
//    }

    //设置商圈直线
    private void setDrawPolyLine(LatLng latLng1, LatLng latLng2) {
        // 声明 多边形参数对象
        PolylineOptions options = new PolylineOptions()
                .add(latLng1)
                .add(latLng2)
                .color(getResources().getColor(R.color.color_map_draw_red)) // 设置线的颜色为红色
                // .setAntiAlias(true)
                .width(4); // 设置线的宽度为4像素
        Polyline polyline = aMap.addPolyline(options); // 在地图上绘制这条线
        mapPolylineList.add(polyline);
    }

    //设置商圈虚线
    private void setDrawPolyDashedLine(LatLng latLng1, LatLng latLng2) {
        // 声明 多边形参数对象
        PolylineOptions options = new PolylineOptions()
                .add(latLng1)
                .add(latLng2)
                .color(getResources().getColor(R.color.color_map_draw_red)) // 设置线的颜色为红色
                .setDottedLine(true)
                .width(4); // 设置线的宽度为4像素
        PolygonDashedLine = aMap.addPolyline(options); // 在地图上绘制这条线
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                // 你的代码，延迟1秒后执行
                if (PolygonDashedLine != null) {
                    PolygonDashedLine.remove(); // 移除多段线
                    PolygonDashedLine = null; // 清空reference
                }
            }
        }, 1000);
    }


    /**
     * 搜索界面 结果item点击回调地图界面显示
     */
    private void setSearchMakerDrawable(int width, int height, LatLng latLng, String name,String businessCreatTime, long markerId, String markerType, String state, String markerUrl, List<LatLng> area_range) {

        int markerDrawableId = 0;
        if ("storePlan".equals(markerType)) {
            if (state.equals("INIT_PRE")) {
                markerDrawableId = R.drawable.map_marker_two_iv;
//                if (markerSize.equals("big")) {
//                    markerDrawableId = R.drawable.map_marker_two_big;
//                } else if (markerSize.equals("middle")) {
//                    markerDrawableId = R.drawable.map_marker_two_middle;
//                } else if (markerSize.equals("small")) {
//                    markerDrawableId = R.drawable.map_marker_two_small;
//                } else if (markerSize.equals("very_small")) {
//                    markerDrawableId = R.drawable.map_marker_two_very_small;
//                }
            } else if (state.equals("INIT") || state.equals("FIRST_AUDIT")) {
                markerDrawableId = R.drawable.map_marker_four_iv;
//                if (markerSize.equals("big")) {
//                    markerDrawableId = R.drawable.map_marker_four_big;
//                } else if (markerSize.equals("middle")) {
//                    markerDrawableId = R.drawable.map_marker_four_middle;
//                } else if (markerSize.equals("small")) {
//                    markerDrawableId = R.drawable.map_marker_four_small;
//                } else if (markerSize.equals("very_small")) {
//                    markerDrawableId = R.drawable.map_marker_four_very_small;
//                }
            } else if (state.equals("SECOND_AUDIT")) {
                markerDrawableId = R.drawable.map_marker_three_iv;
//                if (markerSize.equals("big")) {
//                    markerDrawableId = R.drawable.map_marker_three_big;
//                } else if (markerSize.equals("middle")) {
//                    markerDrawableId = R.drawable.map_marker_three_middle;
//                } else if (markerSize.equals("small")) {
//                    markerDrawableId = R.drawable.map_marker_three_small;
//                } else if (markerSize.equals("very_small")) {
//                    markerDrawableId = R.drawable.map_marker_three_very_small;
//                }
            } else {
                markerDrawableId = R.drawable.map_marker_five_iv;
//                if (markerSize.equals("big")) {
//                    markerDrawableId = R.drawable.map_marker_five_big;
//                } else if (markerSize.equals("middle")) {
//                    markerDrawableId = R.drawable.map_marker_five_middle;
//                } else if (markerSize.equals("small")) {
//                    markerDrawableId = R.drawable.map_marker_five_small;
//                } else if (markerSize.equals("very_small")) {
//                    markerDrawableId = R.drawable.map_marker_five_very_small;
//                }
            }
            setMakerDrawable(markerDrawableId, markerWidth, markerHeight, latLng, name,"", markerId, markerType,false);

        } else if ("businessPlan".equals(markerType)) {
            if (state.equals("INIT_PRE")) {
                markerDrawableId = R.drawable.map_business_marker_one_iv;
//                if (markerSize.equals("big")) {
//                    markerDrawableId = R.drawable.map_business_marker_one_big;
//                } else if (markerSize.equals("middle")) {
//                    markerDrawableId = R.drawable.map_business_marker_one_middle;
//                } else if (markerSize.equals("small")) {
//                    markerDrawableId = R.drawable.map_business_marker_one_small;
//                } else if (markerSize.equals("very_small")) {
//                    markerDrawableId = R.drawable.map_business_marker_one_very_small;
//                }
                setMakerDrawable(markerDrawableId, markerWidth, markerHeight, latLng, name,businessCreatTime, markerId, markerType,true);
                setPolygonDrawable(area_range, 1);
            } else if (state.equals("INIT") || state.equals("FIRST_AUDIT")) {
                markerDrawableId = R.drawable.map_business_marker_three_iv;
//                if (markerSize.equals("big")) {
//                    markerDrawableId = R.drawable.map_business_marker_three_big;
//                } else if (markerSize.equals("middle")) {
//                    markerDrawableId = R.drawable.map_business_marker_three_middle;
//                } else if (markerSize.equals("small")) {
//                    markerDrawableId = R.drawable.map_business_marker_three_small;
//                } else if (markerSize.equals("very_small")) {
//                    markerDrawableId = R.drawable.map_business_marker_three_very_small;
//                }
                setMakerDrawable(markerDrawableId, markerWidth, markerHeight, latLng, name,businessCreatTime, markerId, markerType,true);
                setPolygonDrawable(area_range, 2);
            } else if (state.equals("SECOND_AUDIT")) {
                markerDrawableId = R.drawable.map_business_marker_four_iv;
//                if (markerSize.equals("big")) {
//                    markerDrawableId = R.drawable.map_business_marker_four_big;
//                } else if (markerSize.equals("middle")) {
//                    markerDrawableId = R.drawable.map_business_marker_four_middle;
//                } else if (markerSize.equals("small")) {
//                    markerDrawableId = R.drawable.map_business_marker_four_small;
//                } else if (markerSize.equals("very_small")) {
//                    markerDrawableId = R.drawable.map_business_marker_four_very_small;
//                }
                setMakerDrawable(markerDrawableId, markerWidth, markerHeight, latLng, name,businessCreatTime, markerId, markerType,true);
                setPolygonDrawable(area_range, 3);
            } else {
                markerDrawableId = R.drawable.map_business_marker_two_iv;
//                if (markerSize.equals("big")) {
//                    markerDrawableId = R.drawable.map_business_marker_two_big;
//                } else if (markerSize.equals("middle")) {
//                    markerDrawableId = R.drawable.map_business_marker_two_middle;
//                } else if (markerSize.equals("small")) {
//                    markerDrawableId = R.drawable.map_business_marker_two_small;
//                } else if (markerSize.equals("very_small")) {
//                    markerDrawableId = R.drawable.map_business_marker_two_very_small;
//                }

                setMakerDrawable(markerDrawableId, markerWidth, markerHeight, latLng, name,businessCreatTime, markerId, markerType,true);
                setPolygonDrawable(area_range, 4);
            }
        } else if ("erpStore".equals(markerType)) {
            markerDrawableId = R.drawable.map_marker_one_iv;
//            if (markerSize.equals("big")) {
//                markerDrawableId = R.drawable.map_marker_one_big;
//            } else if (markerSize.equals("middle")) {
//                markerDrawableId = R.drawable.map_marker_one_middle;
//            } else if (markerSize.equals("small")) {
//                markerDrawableId = R.drawable.map_marker_one_small;
//            } else if (markerSize.equals("very_small")) {
//                markerDrawableId = R.drawable.map_marker_one_very_small;
//            }
            setMakerDrawable(markerDrawableId, markerWidth, markerHeight, latLng, name,"", markerId, markerType,false);
        } else if ("collaborativeMark".equals(markerType)) {
            setMakerDrawableUrl(markerUrl, markerWidth, markerHeight, latLng, name, markerId, markerType);
        } else if ("competitorMark".equals(markerType)) {
            setMakerDrawableUrl(markerUrl, markerWidth, markerHeight, latLng, name, markerId, markerType);
        }

        //首次定位,选择移动到地图中心点并修改级别到14.5级
        aMap.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, 14.5f));

    }


    @Override
    public boolean onMarkerClick(Marker marker) {
        Log.i("TAG", "onMarkerClick-------------");
        markerOnTouch = true;
        growInto(marker);
        String str = marker.getSnippet().trim();

        long markerId = 0;
        try {
            markerId = Long.parseLong(str);
            // 使用转换后的整数
        } catch (NumberFormatException e) {
            // 处理转换错误
        }
        // 将地图的中心点设置为centerPosition
        aMap.animateCamera(CameraUpdateFactory.changeLatLng(marker.getOptions().getPosition()));
        String markerType = marker.getTitle();
        Log.d("TAG", "MapPointDetailRequest id-------------" + markerId);
        if (markerType.equals("storePlan")) {
            MapPointDetailRequest body = new MapPointDetailRequest();
            body.setCompany_id(companyId);
            body.setId((int) markerId);
            mPresenter.storeReadPOST(body);
        } else if (markerType.equals("businessPlan")) {
            MapPointDetailRequest body = new MapPointDetailRequest();
            body.setCompany_id(companyId);
            body.setId((int) markerId);
            mPresenter.businessReadPOST(body);
        } else if (markerType.equals("erpStore")) {
            MapPointErpStoreDetailRequest body = new MapPointErpStoreDetailRequest();
            body.setCompany_id(companyId);
            body.setId(markerId);
            mPresenter.erpstoreReadPOST(body);
        } else if (markerType.equals("collaborativeMark")) {
            MapPointDetailRequest body = new MapPointDetailRequest();
            body.setCompany_id(companyId);
            body.setId((int) markerId);
            mPresenter.collaborativeReadPOST(body);
        } else if (markerType.equals("competitorMark")) {
            MapPointDetailRequest body = new MapPointDetailRequest();
            body.setCompany_id(companyId);
            body.setId((int) markerId);
            mPresenter.competitorReadPOST(body);
        }

        return true;
    }

    /**
     * 从地上生长效果，
     *
     * @param marker
     */
    private void growStartInto(final Marker marker) {
        Animation animation = new ScaleAnimation(0, 1, 0, 1);
        animation.setInterpolator(new LinearInterpolator());
        //整个移动所需要的时间
        animation.setDuration(800);
        //设置动画
        marker.setAnimation(animation);
        //开始动画
        marker.startAnimation();
    }

    /**
     * 从地上生长效果，
     *
     * @param marker
     */
    private void growInto(final Marker marker) {
        Animation animation = new ScaleAnimation(1.6f, 1, 1.6f, 1);
        animation.setInterpolator(new LinearInterpolator());
        //整个移动所需要的时间
        animation.setDuration(800);
        //设置动画
        marker.setAnimation(animation);
        //开始动画
        marker.startAnimation();
    }

    @Override
    public void onCameraChange(CameraPosition cameraPosition) {
        //drawMarker = aMap.addMarker(drawMarkerOption);
        if (null != drawMarker) {
            Log.i("TAG", "--------drawMarker-----is----null-");
            drawMarker.setPosition(cameraPosition.target);
        }

        if (null != centerMarker && isPointSearchSheetDialogOpen) {
            centerMarker.setPosition(cameraPosition.target);

        }
    }

    private void drawCenterMarker(LatLng location) {
        if (isPointSearchSheetDialogOpen && location != null) {
            if (centerMarker == null) {
                VectorDrawable vectorDrawable = (VectorDrawable) ContextCompat.getDrawable(context, R.drawable.map_draw_bottom_red_pos_iv);
                Bitmap bitmap = Bitmap.createBitmap(vectorDrawable.getIntrinsicWidth(), vectorDrawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
                Canvas canvas = new Canvas(bitmap);
                vectorDrawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
                vectorDrawable.draw(canvas);

//                drawMarkerOption = new MarkerOptions().icon(BitmapDescriptorFactory.fromBitmap(bitmap))
                // 添加中心点标记
                centerMarker = aMap.addMarker(new MarkerOptions()
                        .position(location)
                        .icon(BitmapDescriptorFactory.fromBitmap(bitmap))
                        .anchor(0.5f, 0.5f));
            } else {
                // 更新位置并触发动画
                centerMarker.setPosition(location);
//                triggerSpringAnimation();
            }

            // 获取当前中心点坐标（纬度、经度）
            double lat = location.latitude;
            double lng = location.longitude;
//            poiSearchPointClick = true
            if (!poiSearchPointClick) {
                centerlatLng = location;
                updateLocation(lng, lat);
            }
            Log.d("CenterMarker", "当前中心点坐标：" + lat + ", " + lng);
        } else if (centerMarker != null) {
            centerMarker.remove();
            centerMarker = null;
        }
    }


    @Override
    public void onCameraChangeFinish(CameraPosition cameraPosition) {
        Log.i("TAG", "--------onCameraChangeFinish------markerOnTouch----" + markerOnTouch);
        Log.i("TAG", "--------onCameraChangeFinish------markerOnTouch----" + isPointSearchSheetDialogOpen);
        mapCameraCenterLatLng = cameraPosition.target;
        if (mapOnTouchUp && !markerOnTouch) {
//           showLoadingDialog();
//            //获取插旗数据请求
            getFlagPlantShortInfoUsing(templateAddRequestDetail);
        }
        drawCenterMarker(cameraPosition.target);

    }

//    private void triggerSpringAnimation() {
//        // 使用属性动画实现弹簧效果
//        ObjectAnimator anim = ObjectAnimator.ofFloat(centerMarker, "translationY", 0, 50, -30, 0);
//        anim.setDuration(500);
//        anim.setInterpolator(new BounceInterpolator());
//        anim.start();
//    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        if (event.sensor.getType() == Sensor.TYPE_ORIENTATION) {
            float degree = Math.round(event.values[0]); // 获取角度值
            currentDegree = -degree; // 更新当前角度值 加个负数是为了 反转方位角方向
            updateMarkerRotation(); // 更新Marker的旋转角度
        }
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {

    }

    private void updateMarkerRotation() {
        if (aMap != null && locationMarker != null) {
            locationMarker.setRotateAngle(currentDegree); // 设置Marker的旋转角度
        }
    }

    /**
     * 监控地图动画移动情况，如果结束或者被打断，都需要执行响应的操作
     */
    class MyCancelCallback implements AMap.CancelableCallback {

        LatLng targetLatlng;

        public void setTargetLatlng(LatLng latlng) {
            this.targetLatlng = latlng;
        }

        @Override
        public void onFinish() {
            if (locationMarker != null && targetLatlng != null) {
                locationMarker.setPosition(targetLatlng);
            }
        }

        @Override
        public void onCancel() {
            if (locationMarker != null && targetLatlng != null) {
                locationMarker.setPosition(targetLatlng);
            }
        }
    }

    /**
     * 激活定位
     */
    @Override
    public void activate(OnLocationChangedListener listener) {
        mListener = listener;
        newLocationClient();
    }

    private void newLocationClient() {
        if (mlocationClient == null) {
            try {
                mlocationClient = new AMapLocationClient(this);
                mLocationOption = new AMapLocationClientOption();
                //设置定位监听
                mlocationClient.setLocationListener(this);
                //设置为高精度定位模式
                mLocationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
                //是指定位间隔
                mLocationOption.setInterval(2000);
                //设置定位参数
                mlocationClient.setLocationOption(mLocationOption);
                // 此方法为每隔固定时间会发起一次定位请求，为了减少电量消耗或网络流量消耗，
                // 注意设置合适的定位时间的间隔（最小间隔支持为2000ms），并且在合适时间调用stopLocation()方法来取消定位请求
                // 在定位结束后，在合适的生命周期调用onDestroy()方法
                // 在单次定位情况下，定位无论成功与否，都无需调用stopLocation()方法移除请求，定位sdk内部会移除
                mlocationClient.startLocation();
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

    }

    /**
     * 停止定位
     */
    @Override
    public void deactivate() {
        mListener = null;
        if (mlocationClient != null) {
            mlocationClient.stopLocation();
            mlocationClient.onDestroy();
        }
        mlocationClient = null;
    }


    private void showPoiSearchDialog() {
        Intent intent = new Intent(this, MapPoiSearchDialogActivity.class);
        intent.putExtra("longitude", latLngLocation.longitude);
        intent.putExtra("latitude", latLngLocation.latitude);

        startActivityForResult(intent, REQUEST_CODE);
    }


    private void showBottomSheetDialog() {
        if (bottomSheetDialog == null) {
            bottomSheetDialog = new BottomSheetDialog(this, R.style.BottomSheetDialog);
            View bottomSheetView = getLayoutInflater().inflate(R.layout.dialog_map_bottom_sheet, null);
            bottomSheetDialog.setContentView(bottomSheetView);
            CheckBox cbMakeName = bottomSheetView.findViewById(R.id.cb_make_name);
            RelativeLayout makeNameRl = bottomSheetView.findViewById(R.id.make_name_rl);
            TextView makeNameTv = bottomSheetView.findViewById(R.id.make_name_tv);
            RadioGroup radioGroup = bottomSheetView.findViewById(R.id.map_rg);
            radioGroup.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(RadioGroup group, int checkedId) {
                    // 根据选中的RadioButton做相应的操作
                    if (checkedId == R.id.map_normal_rb) {
                        aMap.setMapType(AMap.MAP_TYPE_NORMAL); // 矢量地图模式
                    } else if (checkedId == R.id.map_satellite_rb) {
                        aMap.setMapType(AMap.MAP_TYPE_SATELLITE); // 卫星地图模式
                    } else if (checkedId == R.id.map_public_rb) {
                        aMap.setMapType(AMap.MAP_TYPE_BUS); // 公共交通地图模式
                    }
                }
            });

            cbMakeName.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    Log.i("TAG", "--------onCheckedChanged----------isChecked-----" + isChecked);
                    if (isChecked) {
                        makeNameTv.setTextColor(getResources().getColor(R.color.color_map_blue_text));
                        cbMakeName.setBackgroundResource(R.drawable.map_mark_iv_selected);
                        makeNameRl.setBackgroundResource(R.drawable.dialog_map_bottom_mark_selected_bg);
                        isMarkerNameShow = true;
                    } else {
                        makeNameRl.setBackgroundResource(R.drawable.dialog_map_bottom_mark_bg);
                        cbMakeName.setBackgroundResource(R.drawable.map_mark_iv);
                        makeNameTv.setTextColor(getResources().getColor(R.color.color_map_manage));
                        isMarkerNameShow = false;
                    }
                    // isAddTemplateFlag = false;
                    markerSizeSet(markerSize, true);
                }
            });

            RadioGroup radioGroupSetSize = bottomSheetView.findViewById(R.id.map_marker_set_size_rg);
            radioGroupSetSize.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(RadioGroup group, int checkedId) {
                    //isAddTemplateFlag = false;
                    // 根据选中的RadioButton做相应的操作
                    if (checkedId == R.id.big_rb) {
                        markerSize = 1;
                    } else if (checkedId == R.id.center_rb) {
                        markerSize = 2;
                    } else if (checkedId == R.id.small_rb) {
                        markerSize = 3;
                    } else if (checkedId == R.id.very_small_rb) {
                        markerSize = 4;
                    }

                    markerSizeSet(markerSize, true);
                }
            });

        }

        bottomSheetDialog.show();
    }

    /**
     * markerSizeValue 图标大小设置
     * isFlag  是否是从图层中 图标大小选择
     */
    private void markerSizeSet(int markerSizeValue, boolean isFlag) {
        markerSize = markerSizeValue;
        switch (markerSize) {
            case 1:
                markerWidth = ScreenUtils.dip2px(context, 37); // 设置宽
                markerHeight = ScreenUtils.dip2px(context, 42); // 设置高
                break;
            case 2:
                markerWidth = ScreenUtils.dip2px(context, 32); // 设置宽
                markerHeight = ScreenUtils.dip2px(context, 36); // 设置高
                break;
            case 3:
                markerWidth = ScreenUtils.dip2px(context, 27); // 设置宽
                markerHeight = ScreenUtils.dip2px(context, 30); // 设置高
                break;
            case 4:
                markerWidth = ScreenUtils.dip2px(context, 22); // 设置宽
                markerHeight = ScreenUtils.dip2px(context, 25); // 设置高
                break;
        }
        if (isFlag) {
            // 清空旧的Marker
            if (aMap != null) {
                aMap.clear();
                if (locationMarker != null) {
                    locationMarker = null;
                }
                if (latLngLocation != null) {
                    addLocationMarkerToMap(latLngLocation);
                }
            }

            if (null != markerOptionNewList && !markerOptionNewList.isEmpty()) {
                markerOptionNewList.clear();
            }
            if (null != markerOptionAllList && !markerOptionAllList.isEmpty()) {
                markerOptionAllList.clear();
            }

            executor.execute(new Runnable() {
                @Override
                public void run() {
                    pointMarkerList.clear();
                    buniessAllList.clear();
                    addMarkersToMap(markerWidth, markerHeight, storePlanShortDetailAllList, businessPlanDetailsAllList
                            , erpStoreDetailsAllList, collaborativeDetailsAllList, competitorDetailsAllList);

                }
            });

        } else {

            executor.execute(new Runnable() {
                @Override
                public void run() {
                    addMarkersToMap(markerWidth, markerHeight, storePlanShortDetailNewList, businessPlanDetailsNewList
                            , erpStoreDetailsNewList, collaborativeDetailsNewList, competitorDetailsNewList);

                }
            });

        }

    }

    /**
     * 新增模版
     */
    private void showTemplateDialog() {
        if (templatebottomSheetDialog == null) {
            templatebottomSheetDialog = new BaseBottomSheetDialog(this, R.style.BottomSheetDialog);
            View templatebottomSheetView = getLayoutInflater().inflate(R.layout.dialog_map_template_sheet, null);
            templatebottomSheetDialog.setContentView(templatebottomSheetView);
            templateNameEt = templatebottomSheetView.findViewById(R.id.et_name);
            templateNameEt.setOnDropArrowClickListener(new DropDownEditText.OnDropArrowClickListener() {
                @Override
                public void onDropArrowClick() {
                    templateNameEt.setText("");
                }
            });

            templatebottomSheetView.findViewById(R.id.tv_cancel).setOnClickListener(this);
            templatebottomSheetView.findViewById(R.id.tv_reset).setOnClickListener(this);
            templatebottomSheetView.findViewById(R.id.tv_save).setOnClickListener(this);

            templateDefaultCB = templatebottomSheetView.findViewById(R.id.cb_template_default);

            pointDocumentPreparationCB = templatebottomSheetView.findViewById(R.id.cb_document_preparation);
            pointWaitAssessCB = templatebottomSheetView.findViewById(R.id.cb_wait_assess);
            pointReAssessPassCB = templatebottomSheetView.findViewById(R.id.cb_re_assess_pass);
            pointVetoCB = templatebottomSheetView.findViewById(R.id.cb_veto);
            pointBuildingCB = templatebottomSheetView.findViewById(R.id.cb_building);
            pointCounterSignCB = templatebottomSheetView.findViewById(R.id.cb_counter_sign);

            businessDocumentPreparationCB = templatebottomSheetView.findViewById(R.id.cb_document_preparation_business);
            businessWaitAssessCB = templatebottomSheetView.findViewById(R.id.cb_wait_assess_business);
            businessReAssessPassCB = templatebottomSheetView.findViewById(R.id.cb_re_assess_pass_business);
            businessVetoCB = templatebottomSheetView.findViewById(R.id.cb_veto_business);

            storeWaitDeliverCB = templatebottomSheetView.findViewById(R.id.cb_wait_deliver);
            storeConstructionCB = templatebottomSheetView.findViewById(R.id.cb_construction);
            storeWaitBusinessCB = templatebottomSheetView.findViewById(R.id.cb_wait_business);
            storeInBusinessCB = templatebottomSheetView.findViewById(R.id.cb_in_business);
            storeClosedStoreCB = templatebottomSheetView.findViewById(R.id.cb_closed_store);
            storeCancelledCB = templatebottomSheetView.findViewById(R.id.cb_Cancelled);
            storeWaitCloseBusinessCB = templatebottomSheetView.findViewById(R.id.cb_wait_close_business);

            competitorLl = templatebottomSheetView.findViewById(R.id.competitor_ll);
            competitorFbl = templatebottomSheetView.findViewById(R.id.competitor_fbl);
            collaborativeFbl = templatebottomSheetView.findViewById(R.id.collaborative_fbl);
            collaborativeLl = templatebottomSheetView.findViewById(R.id.collaborative_ll);

            CheckBoxListener listener = new CheckBoxListener();
            templateDefaultCB.setOnCheckedChangeListener(listener);

            pointDocumentPreparationCB.setOnCheckedChangeListener(listener);
            pointWaitAssessCB.setOnCheckedChangeListener(listener);
            pointReAssessPassCB.setOnCheckedChangeListener(listener);
            pointVetoCB.setOnCheckedChangeListener(listener);
            pointBuildingCB.setOnCheckedChangeListener(listener);
            pointCounterSignCB.setOnCheckedChangeListener(listener);

            businessDocumentPreparationCB.setOnCheckedChangeListener(listener);
            businessWaitAssessCB.setOnCheckedChangeListener(listener);
            businessReAssessPassCB.setOnCheckedChangeListener(listener);
            businessVetoCB.setOnCheckedChangeListener(listener);

            storeWaitDeliverCB.setOnCheckedChangeListener(listener);
            storeConstructionCB.setOnCheckedChangeListener(listener);
            storeWaitBusinessCB.setOnCheckedChangeListener(listener);
            storeInBusinessCB.setOnCheckedChangeListener(listener);
            storeClosedStoreCB.setOnCheckedChangeListener(listener);
            storeCancelledCB.setOnCheckedChangeListener(listener);
            storeWaitCloseBusinessCB.setOnCheckedChangeListener(listener);

            LayoutInflater inflater = LayoutInflater.from(this);
            //View myLayout = inflater.inflate(R.layout.item_template_brand_cb, null);
            // LinearLayout brandLl = myLayout.findViewById(R.id.brand_ll);
            mCompetitorSelectedList.clear();
            competitorCheckBoxs.clear();
            if (competitorList != null && !competitorList.isEmpty()) {
                competitorFbl.setHorizontalSpace(12);//不设置默认为0
                competitorFbl.setVerticalSpace(12);//不设置默认为0
                for (int i = 0; i < competitorList.size(); i++) {
                    // 创建你的自定义视图
                    // View customView = createCheckBoxView();
                    // 添加自定义视图到LinearLayout
                    View myLayout = inflater.inflate(R.layout.item_template_brand_cb, null);
                    LinearLayout brandLl = myLayout.findViewById(R.id.brand_ll);
                    CheckBox brandCb = myLayout.findViewById(R.id.brand_cb);
                    brandCb.setText(competitorList.get(i).getName());
                    // Log.i("TAG", "competitorList---i-----" + i + "------getId()------" + competitorList.get(i).getId());
                    brandCb.setTag(i);
                    brandCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                        @Override
                        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                            int pos = (int) buttonView.getTag();
                            int selectedId = competitorList.get(pos).getId();
                            // Log.i("TAG", "competitorList---pos-----" + pos);
                            if (isChecked) {
                                Log.i("TAG", "competitorList--isChecked---pos---getId------" + competitorList.get(pos).getId());
                                if (!mCompetitorSelectedList.contains(selectedId)) {
                                    mCompetitorSelectedList.add(selectedId);
                                }
                            } else {
                                // Log.i("TAG", "competitorList--isChecked---delete---pos------" + pos);
                                // Log.i("TAG", "competitorList--isChecked---delete---id------" + competitorList.get(pos).getId());
                                int index = mCompetitorSelectedList.indexOf(selectedId);
                                if (index != -1) {
                                    mCompetitorSelectedList.remove(index);
                                }
                            }
                        }
                    });
                    competitorIdsAllList.add(competitorList.get(i).getId());
                    competitorCheckBoxs.add(brandCb);

                    competitorFbl.addView(brandLl);
                }
                competitorLl.setVisibility(View.VISIBLE);

            } else {
                competitorLl.setVisibility(View.GONE);
            }

            mCollaborativeSelectedList.clear();
            collaborativeCheckBoxs.clear();
            if (collaborativeList != null && !collaborativeList.isEmpty()) {
                collaborativeFbl.setHorizontalSpace(12);//不设置默认为0
                collaborativeFbl.setVerticalSpace(12);//不设置默认为0
                for (int i = 0; i < collaborativeList.size(); i++) {
                    // 创建你的自定义视图
                    // View customView = createCheckBoxView();
                    // 添加自定义视图到LinearLayout
                    View myLayout = inflater.inflate(R.layout.item_template_brand_cb, null);
                    LinearLayout brandLl = myLayout.findViewById(R.id.brand_ll);
                    CheckBox brandCb = myLayout.findViewById(R.id.brand_cb);
                    brandCb.setText(collaborativeList.get(i).getName());
                    //Log.i("TAG", "collaborativeList---i-----" + i + "------getId()------" + collaborativeList.get(i).getId());
                    //boolean lockState = false;
                    brandCb.setTag(i);
                    brandCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                        @Override
                        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                            //if (lockState) return;
                            int pos = (int) buttonView.getTag();
                            //Log.i("TAG", "mCollaborativeCheckStates---pos-----" + pos);
                            int selectedId = collaborativeList.get(pos).getId();
                            if (isChecked) {
                                // Log.i("TAG", "collaborativeList--isChecked---pos---getId------" + selectedId);
                                if (!mCollaborativeSelectedList.contains(selectedId)) {
                                    mCollaborativeSelectedList.add(selectedId);
                                }
                            } else {
//                                Log.i("TAG", "collaborativeList--isChecked---delete---pos------" + pos);
//                                Log.i("TAG", "collaborativeList--isChecked---delete---id------" + selectedId);
//                                Log.i("TAG", "collaborativeList--isChecked---mCollaborativeSelectedList---size------" + mCollaborativeSelectedList.size());
                                int index = mCollaborativeSelectedList.indexOf(selectedId);
                                if (index != -1) {
                                    mCollaborativeSelectedList.remove(index);
                                }
                            }
                            //  Log.i("TAG", "mCollaborativeSelectedList---size-----" + mCollaborativeSelectedList.size());
                        }
                    });
                    collaborativeIdsAllList.add(collaborativeList.get(i).getId());
                    collaborativeCheckBoxs.add(brandCb);

                    collaborativeFbl.addView(brandLl);
                }
                collaborativeLl.setVisibility(View.VISIBLE);

            } else {
                collaborativeLl.setVisibility(View.GONE);
            }
        }
        templatebottomSheetDialog.show();
    }

    class CheckBoxListener implements CompoundButton.OnCheckedChangeListener {
        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
            if (isChecked) {
                if (buttonView.getId() == R.id.cb_template_default) {
                    cbTemplateDefault = true;
                } else if (buttonView.getId() == R.id.cb_document_preparation) {
                    //制单
                    cbPointDocumentPreparation = true;
                } else if (buttonView.getId() == R.id.cb_wait_assess) {
                    //待审批
                    cbPointWaitAssess = true;
                } else if (buttonView.getId() == R.id.cb_re_assess_pass) {
                    //审批通过
                    cbPointReAssessPass = true;
                } else if (buttonView.getId() == R.id.cb_veto) {
                    //否决
                    cbPointVeto = true;
                } else if (buttonView.getId() == R.id.cb_building) {
                    //建店中
                    cbBuilding = true;
                } else if (buttonView.getId() == R.id.cb_counter_sign) {
                    //已双签
                    cbCounterSign = true;
                } else if (buttonView.getId() == R.id.cb_document_preparation_business) {
                    //商圈待评估
                    cbBusinessDocumentPreparation = true;
                } else if (buttonView.getId() == R.id.cb_wait_assess_business) {
                    //商圈待评估
                    cbBusinessWaitAssess = true;
                } else if (buttonView.getId() == R.id.cb_re_assess_pass_business) {
                    //商圈复评通过
                    cbBusinessReAssessPass = true;
                } else if (buttonView.getId() == R.id.cb_veto_business) {
                    //商圈否决
                    cbBusinessVeto = true;
                } else if (buttonView.getId() == R.id.cb_wait_deliver) {
                    //门店待交付
                    cbStoreWaitDeliver = true;
                } else if (buttonView.getId() == R.id.cb_construction) {
                    //门店营建中
                    cbStoreConstruction = true;
                } else if (buttonView.getId() == R.id.cb_wait_business) {
                    //门店待营业
                    cbStoreWaitBusiness = true;
                } else if (buttonView.getId() == R.id.cb_in_business) {
                    //门店营业中
                    cbStoreInBusiness = true;
                } else if (buttonView.getId() == R.id.cb_closed_store) {
                    //门店已闭店
                    cbStoreClosedStore = true;
                } else if (buttonView.getId() == R.id.cb_Cancelled) {
                    //门店已取消
                    cbStoreCancelled = true;
                } else if (buttonView.getId() == R.id.cb_wait_close_business) {
                    //门店待停业
                    cbStoreWaitCloseBusiness = true;
                }

            } else {
                if (buttonView.getId() == R.id.cb_template_default) {
                    cbTemplateDefault = false;
                } else if (buttonView.getId() == R.id.cb_document_preparation) {
                    //制单
                    cbPointDocumentPreparation = false;
                } else if (buttonView.getId() == R.id.cb_wait_assess) {
                    //待审批
                    cbPointWaitAssess = false;
                } else if (buttonView.getId() == R.id.cb_re_assess_pass) {
                    //审批通过
                    cbPointReAssessPass = false;
                } else if (buttonView.getId() == R.id.cb_veto) {
                    //否决
                    cbPointVeto = false;
                } else if (buttonView.getId() == R.id.cb_building) {
                    //建店中
                    cbBuilding = false;
                } else if (buttonView.getId() == R.id.cb_counter_sign) {
                    //已双签
                    cbCounterSign = false;
                } else if (buttonView.getId() == R.id.cb_document_preparation_business) {
                    //商圈制单
                    cbBusinessDocumentPreparation = false;
                } else if (buttonView.getId() == R.id.cb_wait_assess_business) {
                    //商圈待评估
                    cbBusinessWaitAssess = false;
                } else if (buttonView.getId() == R.id.cb_re_assess_pass_business) {
                    //商圈复评通过
                    cbBusinessReAssessPass = false;
                } else if (buttonView.getId() == R.id.cb_veto_business) {
                    //商圈否决
                    cbBusinessVeto = false;
                } else if (buttonView.getId() == R.id.cb_wait_deliver) {
                    //门店待交付
                    cbStoreWaitDeliver = false;
                } else if (buttonView.getId() == R.id.cb_construction) {
                    //门店营建中
                    cbStoreConstruction = false;
                } else if (buttonView.getId() == R.id.cb_wait_business) {
                    //门店待营业
                    cbStoreWaitBusiness = false;
                } else if (buttonView.getId() == R.id.cb_in_business) {
                    //门店营业中
                    cbStoreInBusiness = false;
                } else if (buttonView.getId() == R.id.cb_closed_store) {
                    //门店已闭店
                    cbStoreClosedStore = false;
                } else if (buttonView.getId() == R.id.cb_Cancelled) {
                    //门店已取消
                    cbStoreCancelled = false;
                } else if (buttonView.getId() == R.id.cb_wait_close_business) {
                    //门店待停业
                    cbStoreWaitCloseBusiness = false;
                }
            }
        }
    }

    /**
     * 模版重置按钮
     */
    private void templateReset() {
        pointDocumentPreparationCB.setChecked(false);
        pointWaitAssessCB.setChecked(false);
        pointReAssessPassCB.setChecked(true);
        pointVetoCB.setChecked(false);
        pointBuildingCB.setChecked(false);
        pointCounterSignCB.setChecked(false);

        businessDocumentPreparationCB.setChecked(false);
        businessWaitAssessCB.setChecked(false);
        businessReAssessPassCB.setChecked(true);
        businessVetoCB.setChecked(false);

        storeWaitDeliverCB.setChecked(false);
        storeConstructionCB.setChecked(false);
        storeWaitBusinessCB.setChecked(false);
        storeInBusinessCB.setChecked(true);
        storeClosedStoreCB.setChecked(false);
        storeCancelledCB.setChecked(false);
        storeWaitCloseBusinessCB.setChecked(false);

        if (competitorIdsAllList != null && !competitorIdsAllList.isEmpty()) {
            for (int i = 0; i < competitorIdsAllList.size(); i++) {
                competitorCheckBoxs.get(i).setChecked(false);
            }
        }

        if (collaborativeIdsAllList != null && !collaborativeIdsAllList.isEmpty()) {
            for (int i = 0; i < collaborativeIdsAllList.size(); i++) {
                collaborativeCheckBoxs.get(i).setChecked(false);
            }
        }
    }

    private void templateSave() {
        String etString = templateNameEt.getText().toString().trim();
        if (TextUtils.isEmpty(etString)) {
            showMsg("请输入模版名称");
            return;
        }
        TemplateAddRequest body = new TemplateAddRequest();
        //是否设置为默认
        body.setAcquiesce(cbTemplateDefault);
        body.setName(etString);
        body.setType("FLAG");//必填
        TemplateAddRequestDetail detail = new TemplateAddRequestDetail();
        //展示点位 点位类型：INIT_PRE(制单),INIT(待评估),FIRST_AUDIT(初审通过),SECOND_AUDIT(复审通过),REJECT(否决),STORE_BUILD(建店中)，STORE_SIGN(已双签)
        List<String> store_plan_states = new ArrayList<>();
        if (cbPointDocumentPreparation) store_plan_states.add("INIT_PRE");
        if (cbPointWaitAssess) store_plan_states.add("INIT");
        if (cbPointReAssessPass) store_plan_states.add("SECOND_AUDIT");
        if (cbPointVeto) store_plan_states.add("REJECT");
        if (cbBuilding) store_plan_states.add("STORE_BUILD");
        if (cbCounterSign) store_plan_states.add("STORE_SIGN");
        detail.setStore_plan_states(store_plan_states);

        //展示商圈 商圈类型：DRAFT(草稿) INIT(待评估),FIRST_AUDIT(初评通过),SECOND_AUDIT(复评通过),REJECT(否决)
        List<String> business_plan_states = new ArrayList<>();
        if (cbBusinessDocumentPreparation) business_plan_states.add("INIT_PRE");
        if (cbBusinessWaitAssess) business_plan_states.add("INIT");
        if (cbBusinessInitialAssessPass) business_plan_states.add("FIRST_AUDIT");
        if (cbBusinessReAssessPass) business_plan_states.add("SECOND_AUDIT");
        if (cbBusinessVeto) business_plan_states.add("REJECT");
        detail.setBusiness_plan_states(business_plan_states);

        //展示门店状态：TO_BE_DELIVERED(待交付),BUILDING(营建中),TO_BE_OPENED(待营业),OPENED(营业中),CLOSED(已闭店),CANCELLED(已取消),TO_BE_CLOSED(待停业)
        List<String> erp_store_states = new ArrayList<>();
        if (cbStoreWaitDeliver) erp_store_states.add("TO_BE_DELIVERED");
        if (cbStoreConstruction) erp_store_states.add("BUILDING");
        if (cbStoreWaitBusiness) erp_store_states.add("TO_BE_OPENED");
        if (cbStoreInBusiness) erp_store_states.add("OPENED");
        if (cbStoreClosedStore) erp_store_states.add("CLOSED");
        if (cbStoreCancelled) erp_store_states.add("CANCELLED");
        if (cbStoreWaitCloseBusiness) erp_store_states.add("TO_BE_CLOSED");
        detail.setErp_store_states(erp_store_states);
        //竞品品牌
        detail.setCompetitor_ids(mCompetitorSelectedList);
        //协品品牌
        detail.setCollaborative_ids(mCollaborativeSelectedList);

        body.setDetail(detail);

        mPresenter.templateAddPOST(body);
    }

    /**
     * 如果发送的消息类型是MessageEvent类型，则此方法会被回调。
     * 这种方法被称为订阅方法或者订阅函数。
     * 注意：从3.0.0版本开始，订阅方法必须添加注解@Subscribe
     * <p>
     * 该方法的参数类型是MessageEvent类型，所以只要发送的消息的类型是MessageEvent
     * 则不管方法名是什么都会回调。
     * <p>
     * 换句话说：订阅方法的回调只和参数类型有关，与方法名无关。
     * <p>
     * 但是出于可读性考虑，订阅方法一般用onXxxEvent来命名。
     */
    @Subscribe
    public void onEvent(MessageEvent event) {
        //接收MapTemPlateActivity传来的消息更新数据
        // showLoadingDialog();
        if (event != null) {
            if (!TextUtils.isEmpty(event.getMessage())) {
                if ("addTemplate".equals(event.getMessage())) {
                    isAddTemplateFlag = true;
                    findTemplatePOST();
                } else if ("deleteTemplate".equals(event.getMessage()) || "modifyTemplate".equals(event.getMessage())) {
                    findTemplatePOST();
                } else if ("MapStorePlanDetailActivity".equals(event.getMessage()) || "MapBusinessDetailActivity".equals(event.getMessage())) {
                    findTemplatePOST();
                } else if ("MapStorePlanDetailActivity".equals(event.getMessage())) {
                    findTemplatePOST();
                }
            }
        }
    }

    private void findTemplatePOST() {
        //获取数据
        TemplateQueryRequest templateBody = new TemplateQueryRequest();
        templateBody.setType("FLAG");
        mPresenter.findTemplatePOST(templateBody);
    }

    @Subscribe
    public void onEvent(WarpmapLocationFlagBean event) {
        //选择 点位 后

        PoiItemV2 curClickPoiItem = event.getCurClickPoiItem();
//                ArrayList<PoiItemV2> allList =  data.getParcelableArrayListExtra("pointAllList");

        searchEt.setText(event.getCurClickPoiItem().getTitle());

        aMap.moveCamera(
                CameraUpdateFactory.newLatLngZoom(
                        new LatLng(
                                curClickPoiItem.getLatLonPoint().getLatitude(),
                                curClickPoiItem.getLatLonPoint().getLongitude()
                        ), aMap.getCameraPosition().zoom
                )
        );

    }

    @Subscribe
    public void onEvent(SearchMessageEvent event) {
        //接收MapPointSearchActivity传来的消息更新数据
        if (event != null) {
            isSearchSelected = true;
            long markerId = event.getId();
            String markerType = event.getType();
            String state = event.getState();
            Log.i("TAG", "SearchMessageEvent markerId-------------" + markerId);
            Log.i("TAG", "SearchMessageEvent markerType-------------" + markerType);
            Log.i("TAG", "SearchMessageEvent companyId-------------" + companyId);
            if (markerType.equals("storePlan")) {
                MapPointDetailRequest body = new MapPointDetailRequest();
                body.setCompany_id(companyId);
                body.setId((int) markerId);
                mPresenter.storeReadPOST(body);
            } else if (markerType.equals("businessPlan")) {
                MapPointDetailRequest body = new MapPointDetailRequest();
                body.setCompany_id(companyId);
                body.setId((int) markerId);
                mPresenter.businessReadPOST(body);
            } else if (markerType.equals("erpStore")) {
                MapPointErpStoreDetailRequest body = new MapPointErpStoreDetailRequest();
                body.setCompany_id(companyId);
                body.setId(markerId);
                mPresenter.erpstoreReadPOST(body);
            } else if (markerType.equals("collaborativeMark")) {
                MapPointDetailRequest body = new MapPointDetailRequest();
                body.setCompany_id(companyId);
                body.setId((int) markerId);
                mPresenter.collaborativeReadPOST(body);
            } else if (markerType.equals("competitorMark")) {
                MapPointDetailRequest body = new MapPointDetailRequest();
                body.setCompany_id(companyId);
                body.setId((int) markerId);
                mPresenter.competitorReadPOST(body);
            }
        }
    }

    @Subscribe
    public void onEvent(AddBusinessEvent event) {
        //接收商圈添加后传来的消息更新数据
        if (event != null) {
            //findTemplatePOST();
            if (null != storePlanShortDetailAllList && !storePlanShortDetailAllList.isEmpty()) {
                storePlanShortDetailAllList.clear();
            }
            if (null != businessPlanDetailsAllList && !businessPlanDetailsAllList.isEmpty()) {
                businessPlanDetailsAllList.clear();
            }
            if (null != erpStoreDetailsAllList && !erpStoreDetailsAllList.isEmpty()) {
                erpStoreDetailsAllList.clear();
            }

            if (null != mapCameraCenterLatLngList && !mapCameraCenterLatLngList.isEmpty()) {
                for (Marker marker : mapCameraCenterMarkerList) {
                    marker.remove();
                }
                mapCameraCenterLatLngList.clear();
                if (null != mapPolylineList && !mapPolylineList.isEmpty()) {
                    for (Polyline polyline : mapPolylineList) {
                        polyline.remove();
                    }
                    mapPolylineList.clear();
                }
                if (drawPolygon != null) {
                    drawPolygon.remove(); // 移除临时绘制的多边形
                    drawPolygon = null; // 清空reference
                }
                if (drawMarker != null) {
                    drawMarker.remove(); // 移除定点位置
                    drawMarker = null; // 清空reference
                }
            }
            //清空地图上的marker
            if (aMap != null) {
                aMap.clear();
                if (locationMarker != null) {
                    locationMarker = null;
                }
                if (latLngLocation != null) {
                    addLocationMarkerToMap(latLngLocation);
                }
            }
            //获取插旗数据请求
            getFlagPlantShortInfoUsing(templateAddRequestDetail);

            float mapLevel = event.getMapLevel();
            LatLng polygonCenter = event.getPolygonCenter();
            // 将地图的中心点设置为centerPosition
            aMap.moveCamera(CameraUpdateFactory.changeLatLng(polygonCenter));
            // 可以选择将缩放级别设置为一个合适的值
            aMap.moveCamera(CameraUpdateFactory.zoomTo(mapLevel));
            isDrawBtnFlag = false;
            setDrawShowBtn(false);
        }

    }

    /**
     * 选择跟进人
     */
    private void showOrganizationDialog() {
        if (findOrganizationDialog == null) {
            findOrganizationDialog = new BaseBottomSheetDialog(this, R.style.TemplateBottomSheetDialog);
            View followBybottomSheetView = getLayoutInflater().inflate(R.layout.dialog_map_selected_organization, null);
            findOrganizationDialog.setContentView(followBybottomSheetView);
            followBybottomSheetView.findViewById(R.id.cancel_tv).setOnClickListener(this);
            RecyclerView contentRv = followBybottomSheetView.findViewById(R.id.content_rv);

            mMapOrganizationAdapter = new MapOrganizationAdapter(context, organizationList);
            contentRv.setLayoutManager(new LinearLayoutManager(context));
            // 添加间隔线
            int dividerHeight = 1; // 间隔线的高度
            contentRv.addItemDecoration(new DividerItemDecoration(getResources().getColor(R.color.color_map_template_bottom_line), dividerHeight));
            contentRv.setAdapter(mMapOrganizationAdapter);
            mMapOrganizationAdapter.setOnItemClickListener(new MapOrganizationAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(int position) {
                    if (null != findOrganizationDialog) {
                        findOrganizationDialog.dismiss();
                    }
                    orgId = organizationList.get(position).getId();
                    orgName = organizationList.get(position).getName();
                    if (flagAddStorePlan) {
                        getStorePlanInitial();
                    } else {
                        getBusinessPlanInitial();
                    }
                }
            });
        }
        findOrganizationDialog.show();
    }

    /**
     * 点击返回按钮时 如果处在商圈绘制状态下，弹出弹框提示是否退出商圈编辑状态
     */
    private void showBusinessDrawExitDialog() {

        OnClickListener onConfimClickListener = new OnClickListener() {
            @Override
            public void onClick(View v) {
                isDrawBtnFlag = false;
                drawBottomLeftIv.setImageResource(R.drawable.map_draw_bottom_left_back_ghost_iv);
                cancelTv.setTextColor(getResources().getColor(R.color.color_map_draw_bottom_btn_left_text));
                drawBottomRightIv.setImageResource(R.drawable.map_draw_bottom_right_finish_ghost_iv);
                finishTv.setTextColor(getResources().getColor(R.color.color_map_draw_bottom_btn_left_text));

                if (drawPolygon != null) {
                    drawPolygon.remove(); // 移除临时绘制的多边形
                    drawPolygon = null; // 清空reference
                }
                if (drawMarker != null) {
                    drawMarker.remove(); // 移除定点位置
                    drawMarker = null; // 清空reference
                }
                if (null != mapCameraCenterLatLngList && !mapCameraCenterLatLngList.isEmpty()) {
                    for (Marker marker : mapCameraCenterMarkerList) {
                        marker.remove();
                    }
                    mapCameraCenterLatLngList.clear();
                    if (null != mapPolylineList && !mapPolylineList.isEmpty()) {
                        for (Polyline polyline : mapPolylineList) {
                            polyline.remove();
                        }
                        mapPolylineList.clear();
                    }
                }
                backFlag = false;
                drawBottomLl.setVisibility(View.GONE);
                addBottomLl.setVisibility(View.VISIBLE);
                manageRl.setVisibility(View.VISIBLE);
                searchRl.setVisibility(View.VISIBLE);
                templateLl.setVisibility(View.VISIBLE);
                rightLl.setVisibility(View.VISIBLE);
            }
        };

        OnClickListener onCancelClickListener = new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null != drawDialog && drawDialog.isShowing()) {
                    drawDialog.dismiss();
                }
            }
        };

        showInfoDialog(onConfimClickListener, onCancelClickListener, getString(R.string.map_map_draw_exit_title), getString(R.string.map_map_draw_exit_content));
    }

    /**
     * 如果处在商圈某一个绘制状态下，变更绘制方式需弹出弹框提示是否确定更换
     */
    private void showDrawChangeDialog() {
        if (null != drawDialog && drawDialog.isShowing()) {
            return;
        }
        OnClickListener onConfimClickListener = new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isDrawWayFlag) {
                    if (drawPolygon != null) {
                        drawPolygon.remove(); // 移除临时绘制的多边形
                        drawPolygon = null; // 清空reference
                    }
                    drawView.setVisibility(View.GONE);
                    moveMapBtnLl.setVisibility(View.GONE);
                    drawCenterTv.setVisibility(View.VISIBLE);
                    centerFreeTv.setVisibility(View.GONE);
                    cancelTv.setText("撤销");
                    addDrawMarkerToMap(aMap.getCameraPosition().target);
                } else {
                    drawView.setVisibility(View.GONE);
                    drawCenterTv.setVisibility(View.GONE);
                    centerFreeTv.setVisibility(View.VISIBLE);
                    cancelTv.setText("重画");
                    if (null != mapCameraCenterLatLngList && !mapCameraCenterLatLngList.isEmpty()) {
                        for (Marker marker : mapCameraCenterMarkerList) {
                            marker.remove();
                        }
                        mapCameraCenterLatLngList.clear();
                        if (null != mapPolylineList && !mapPolylineList.isEmpty()) {
                            for (Polyline polyline : mapPolylineList) {
                                polyline.remove();
                            }
                            mapPolylineList.clear();
                        }
                        if (drawPolygon != null) {
                            drawPolygon.remove(); // 移除临时绘制的多边形
                            drawPolygon = null; // 清空reference
                        }
                        if (drawMarker != null) {
                            drawMarker.remove(); // 移除定点位置
                            drawMarker = null; // 清空reference
                        }
                    }
                }
                isDrawBtnFlag = false;
                isDrawWayFlag = !isDrawWayFlag;
                drawBottomLeftIv.setImageResource(R.drawable.map_draw_bottom_left_back_ghost_iv);
                cancelTv.setTextColor(getResources().getColor(R.color.color_map_draw_bottom_btn_left_text));
                drawBottomRightIv.setImageResource(R.drawable.map_draw_bottom_right_finish_ghost_iv);
                finishTv.setTextColor(getResources().getColor(R.color.color_map_draw_bottom_btn_left_text));
                //               showLoadingDialog();
//                StorePlanRevokeRequest body = new StorePlanRevokeRequest();
//                body.setBusiness_key(String.valueOf(storePlanId));
//                body.setProcess_definition_key("STORE_PLAN");
//                mPresenter.storePlanRevokePOST(body);
            }
        };

        OnClickListener onCancelClickListener = new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isDrawWayFlag) {
                    drawFreeRb.setChecked(true);
                    // drawFixedRb.setChecked(false);
                } else {
                    drawFixedRb.setChecked(true);
                    //drawFreeRb.setChecked(false);
                }
                if (null != drawDialog && drawDialog.isShowing()) {
                    drawDialog.dismiss();
                }
            }
        };

        showInfoDialog(onConfimClickListener, onCancelClickListener, getString(R.string.map_map_draw_change_title), getString(R.string.map_map_draw_change_content));
    }

    protected void showInfoDialog(OnClickListener confirmOnClick, OnClickListener
            cancelOnClick, String title, String content) {
        DialogTitleContentView.Builder builder = new DialogTitleContentView.Builder(this);
        builder.setButtonConfirm("确定", confirmOnClick);
        builder.setButtonCancel("取消", cancelOnClick);
        builder.setTitle(title);
        builder.setInfo(content);
        drawDialog = builder.create();
        drawDialog.show();
    }

    public static MapAddressActivity getInstance() {
        return instance;
    }

    public void enableMapTouch(boolean enable) {
        runOnUiThread(() -> {
            aMap.getUiSettings().setAllGesturesEnabled(enable);
            mapView.setClickable(enable);
        });
    }

    // 显示底部弹窗
    private void showBottomSheet() {
        pointSelectViewSheet.setVisibility(View.VISIBLE);
        pointSelectViewSheet.animate().translationY(0).setDuration(1);
        startSearch("");
        isPointSearchSheetDialogOpen = true;
        drawCenterMarker(latLngLocation);

    }

    // 隐藏底部弹窗
    private void hideBottomSheet() {
        int height = pointSelectViewSheet.getHeight();
        pointSelectViewSheet.animate().translationY(height).setDuration(1);
        pointSelectViewSheet.setVisibility(View.GONE);
        isPointSearchSheetDialogOpen = false;
        poiSearchPointClick = false;

        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        // 假设你的EditText的id是editTextId
        imm.hideSoftInputFromWindow(searchEt.getWindowToken(), 0);
        drawCenterMarker(centerlatLng);

    }

    private void performSearch() {
        if (centerlatLng == null) return;
        pageNumber = 0;
        allList.clear();
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
        startSearch(TextUtils.isEmpty(searchContent) ? "" : searchContent);
    }

    public void updateLocation(double newLng, double newLat) {

        centerlatLng = new LatLng(newLat, newLng);

        Log.i("CenterMarker", "updateLocation: " + newLng + "lat:" + newLat);
        pageNumber = 0; // 重置分页
        allList.clear();
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
        performSearch(); // 执行新的搜索
    }


    private void startSearch(String keyword) {
        if (pageNumber < 0) pageNumber = 0;
        if (pageNumber == 0) {
            loadingView.setVisibility(View.VISIBLE);
            contentRv.setVisibility(View.GONE);
        }
        String types = "汽车服务|汽车销售|汽车维修|摩托车服务|餐饮服务|购物服务|生活服务|体育休闲服务|医疗保健服务|住宿服务|风景名胜|商务住宅|政府机构及社会团体|科教文化服务|交通设施服务|金融保险服务|公司企业|道路附属设施|地名地址信息|公共设施";
//        PoiSearchV2.Query query = new PoiSearchV2.Query(keyword, types, "");
        PoiSearchV2.Query query = new PoiSearchV2.Query(keyword, types, "");
//        query.setLocation(new LatLonPoint(centerlatLng.latitude, centerlatLng.longitude)); // 使用最新坐标
//        query.setDistanceSort(true);
        query.setPageNum(pageNumber);
        query.setPageSize(15);

        if (centerlatLng != null && TextUtils.isEmpty(keyword)) {
            query.setLocation(new LatLonPoint(centerlatLng.latitude, centerlatLng.longitude));
        }

        Log.i("onPoiSearched", "query" + new Gson().toJson(query));


        try {
            PoiSearchV2 search = new PoiSearchV2(context, query);

            search.setOnPoiSearchListener(this);
            if (centerlatLng != null && TextUtils.isEmpty(keyword)) {
                search.setBound(new PoiSearchV2.SearchBound(new LatLonPoint(centerlatLng.latitude, centerlatLng.longitude), 1500));
            }
            search.searchPOIAsyn();
        } catch (AMapException e) {
            Toast.makeText(context, "搜索初始化失败", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onPoiItemSearched(PoiItemV2 poiItem, int code) {
        loadingView.setVisibility(View.GONE);
        contentRv.setVisibility(View.VISIBLE);

    }

    @Override
    public void onPoiSearched(PoiResultV2 result, int code) {
        loadingView.setVisibility(View.GONE);
        contentRv.setVisibility(View.VISIBLE);
        Log.i("onPoiSearched", "onPoiSearched: " + result + "code:" + code);
        Log.i("onPoiSearched", "longitude: " + centerlatLng.longitude + "latitude:" + centerlatLng.latitude);


        if (code == AMapException.CODE_AMAP_SUCCESS && result != null) {
            ArrayList<PoiItemV2> pois = result.getPois();
            Log.i("onPoiSearched", "onPoiSearched: " + pois.toString());
            if (pois != null && !pois.isEmpty()) {
                if (pageNumber == 0) {
                    allList.clear();
                }
                for (PoiItemV2 item : pois) {
                    if (!allList.contains(item)) {
                        allList.add(item);
                    }
                }
//                allList.addAll(pois);
                updateAdapter();
                pageNumber++;
            } else if (pois != null && pois.isEmpty()) {
                if (!TextUtils.isEmpty(searchContent) && pageNumber == 0) {
                    allList.clear();
                    updateAdapter();
                }


            }
        }
    }

    private void updateAdapter() {
        if (adapter == null) {
            adapter = new PointSearchContentAdapter(context, allList, new PointSearchContentAdapter.OnItemListener() {
                @Override
                public void onItem(int position) {
                    selectedPoiItem = allList.get(position);
                    if (poiSearchChangeFlag) {
                        handleChangePoiItem();
                    } else {
                        findOrganization();

                    }
                    hideBottomSheet();

                }

                @Override
                public void onClickItem(int position) {
                    PoiItemV2 curClickPoiItem = allList.get(position);
                    if (curClickPoiItem != null) {

                        if (curClickPoiItem.getLatLonPoint() != null) {
                            poiSearchPointClick = true;

                            aMap.moveCamera(CameraUpdateFactory.newLatLngZoom(new LatLng(curClickPoiItem.getLatLonPoint().getLatitude(), curClickPoiItem.getLatLonPoint().getLongitude()), aMap.getCameraPosition().zoom));
                        }
                    }

                }

            }, true);
            contentRv.setLayoutManager(new LinearLayoutManager(context));
            contentRv.setAdapter(adapter);
        } else {
            adapter.notifyDataSetChanged();
        }

    }

    private void startAddressChange() {

        Intent intent = getIntent();

        Log.i("startAddressChange", "startAddressChange: " + intent.getStringExtra("actionType"));
        Log.i("startAddressChange", "startAddressChange:getExtras " + intent.getExtras());

        if (intent.hasExtra("actionType") && !TextUtils.isEmpty(intent.getStringExtra("actionType")) && intent.getStringExtra("actionType").equals("changeAddress")) {
            Double longitude = intent.getDoubleExtra("add_point_longitude", 0);
            Double latitude = intent.getDoubleExtra("add_point_latitude", 0);

            if (longitude != 0 && latitude != 0) {
                isFisrtSelectFlag = false;

                centerlatLng = new LatLng(latitude, longitude);
            } else {
                isFisrtSelectFlag = true;

                centerlatLng = latLngLocation;
            }
            Log.i("startAddressChange", "latLngLocation: " + latLngLocation);


            poiSearchChangeFlag = true;
            showBottomSheet();
            aMap.moveCamera(CameraUpdateFactory.changeLatLng(centerlatLng));
            // 可以选择将缩放级别设置为一个合适的值
            aMap.moveCamera(CameraUpdateFactory.zoomTo(15.5f));



        } else if (intent.hasExtra("actionType") && !TextUtils.isEmpty(intent.getStringExtra("actionType")) && intent.getStringExtra("actionType").equals("selectAddress")) {
            Double longitude = intent.getDoubleExtra("longitude", 0);
            Double latitude = intent.getDoubleExtra("latitude", 0);

            if (longitude != 0 && latitude != 0) {
                isFisrtSelectFlag = false;

                centerlatLng = new LatLng(latitude, longitude);
            } else {
                isFisrtSelectFlag = true;

                centerlatLng = latLngLocation;
            }

            poiSearchChangeFlag = true;
            if (intent.hasExtra("id") && !TextUtils.isEmpty(intent.getStringExtra("id"))) {
                eventBusinessId = intent.getStringExtra("id");
            }
            showBottomSheet();
            aMap.moveCamera(CameraUpdateFactory.changeLatLng(centerlatLng));
            // 可以选择将缩放级别设置为一个合适的值
            aMap.moveCamera(CameraUpdateFactory.zoomTo(15.5f));




        }


    }


}

