#!/bin/bash
set -euo pipefail

# 获取所有 .env.* 文件
ENV_FILES=(.env.*)

if [ ${#ENV_FILES[@]} -eq 0 ]; then
  echo "❌ 当前目录下没有找到任何 .env.* 文件"
  exit 1
fi

# 选择环境
echo "请选择要本地要使用的环境配置文件："
for i in "${!ENV_FILES[@]}"; do
  printf "  [%d] %s\n" "$i" "${ENV_FILES[$i]}"
done

read -rp "test(测试) staging(灰度) prod(正式) 输入编号（默认 0）: " SELECTED_INDEX
SELECTED_INDEX="${SELECTED_INDEX:-0}"

if ! [[ "$SELECTED_INDEX" =~ ^[0-9]+$ ]] || [ "$SELECTED_INDEX" -lt 0 ] || [ "$SELECTED_INDEX" -ge "${#ENV_FILES[@]}" ]; then
  echo "❌ 输入不合法，请输入 0 ~ $(( ${#ENV_FILES[@]} - 1 )) 之间的数字"
  exit 1
fi

SELECTED_ENV="${ENV_FILES[$SELECTED_INDEX]}"
echo "✅ 你选择的是: $SELECTED_ENV"

cp "$SELECTED_ENV" .env
echo "📋 已复制 $SELECTED_ENV 为 .env"


# 检查 react-native
if ! command -v react-native >/dev/null 2>&1; then
  echo "❌ react-native 命令未找到，请确保已安装 react-native-cli"
  exit 1
fi

# 构建 APK
echo "🚀 正在构建 APK (Release)..."
cd android
./gradlew "assembleRelease"
cd ..

APK_PATH="android/app/build/outputs/apk/release/app-release.apk"
if [ -f "$APK_PATH" ]; then
  echo "✅ 构建完成：$APK_PATH"
else
  echo "❌ 构建失败，未找到 APK 文件"
  exit 1
fi

# 是否安装到设备
echo
read -rp "📱 是否使用 adb 安装到设备？[y/N]: " INSTALL_CONFIRM
INSTALL_CONFIRM="${INSTALL_CONFIRM:-N}"

if [[ "$INSTALL_CONFIRM" =~ ^[Yy]$ ]]; then
  if ! command -v adb >/dev/null 2>&1; then
    echo "❌ 未找到 adb 命令，请检查 Android SDK 是否安装"
    exit 1
  fi

  DEVICE_COUNT=$(adb devices | grep -w "device" | wc -l)
  if [ "$DEVICE_COUNT" -eq 0 ]; then
    echo "❌ 未检测到已连接的 Android 设备，请检查设备连接"
    exit 1
  fi

  echo "📦 正在安装 APK 到设备..."
  adb install -r "$APK_PATH" && echo "✅ 安装成功" || echo "❌ 安装失败"
else
  echo "ℹ️ 已跳过安装步骤"
fi
