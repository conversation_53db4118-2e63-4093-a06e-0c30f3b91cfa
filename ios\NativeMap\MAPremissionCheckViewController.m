//
//  MAPremissionCheckViewController.m
//  xlb
//
//  Created by 莱昂纳多·迪卡普里奥 on 2025/6/5.
//

#import "MAPremissionCheckViewController.h"
#import "UIView+Common.h"
#import "NSDictionary+Common.h"
#import "NSArray+Common.h"
#import "UIImageView+WebCache.h"
#import "Masonry.h"
#import <CoreLocation/CLLocationManager.h>

// 判断字符串是否为空
#define BCStringIsEmpty(str) ([str isKindOfClass:[NSNull class]] || str == nil || ![str isKindOfClass:[NSString class]] || [str length]< 1 ? YES : NO || [str isEqualToString:@"null"] || [str isEqualToString:@"<null>"] || [str isEqualToString:@"(null)"])
// 判断数组是否为空
#define BCArrayIsEmpty(array) (array == nil || [array isKindOfClass:[NSNull class]] || ![array isKindOfClass:[NSArray class]] || [array count] == 0)
// 判断字典是否为空
#define BCDictIsEmpty(dic) (dic == nil || [dic isKindOfClass:[NSNull class]] || ![dic isKindOfClass:[NSDictionary class]] || dic.allKeys.count == 0)

#define BCWidth   [UIScreen mainScreen].bounds.size.width
#define BCHeight  [UIScreen mainScreen].bounds.size.height
#define COLOR(R, G, B) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:1]
#define ACOLOR(R, G, B,A) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:A]

#define RealSize(value)  MAX(round(value * [UIScreen mainScreen].bounds.size.width / 400.0), value)
#define MutilFont(value)  [UIScreen mainScreen].bounds.size.width > 420 ? (value + 2) : value

#ifdef DEBUG
#define DDLog(format, ...) printf("class: <%p %s:(%d) > method: %s \n%s\n", self, [[[NSString stringWithUTF8String:__FILE__] lastPathComponent] UTF8String], __LINE__, __PRETTY_FUNCTION__, [[NSString stringWithFormat:(format), ##__VA_ARGS__] UTF8String] )
#else
#define DDLog(format, ...)
#endif

@interface MAPremissionCheckViewController ()

@end

@implementation MAPremissionCheckViewController

- (void)viewDidLoad {
  [super viewDidLoad];
  
  if (@available(iOS 13.0, *)) {
    self.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
  }
  self.view.backgroundColor = [UIColor whiteColor];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationWillEnterBackground) name:UIApplicationWillEnterForegroundNotification object:nil];
  
  [self creatUI];
}

- (void)applicationWillEnterBackground {
  
  dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    BOOL locationServicesEnabled = [CLLocationManager locationServicesEnabled];
        dispatch_async(dispatch_get_main_queue(), ^{
          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (locationServicesEnabled && status != kCLAuthorizationStatusDenied) {
              [self dismissViewControllerAnimated:YES completion:^{
                [[NSNotificationCenter defaultCenter] postNotificationName:@"RECEIVELOCATIONPREMISS" object:nil];
              }];
            }
          });
            
        });
    });
  
}

- (void)creatUI{
  
  UILabel *tipL = [[UILabel alloc] init];
  tipL.text = self.tipStr;
  tipL.textColor = COLOR(29, 33, 41);
  tipL.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  [self.view addSubview:tipL];
  [tipL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(16);
    make.top.mas_equalTo(10);
    make.width.mas_equalTo(BCWidth - 32);
    make.height.mas_equalTo(50);
  }];
  
  UIImageView *centerIM = [[UIImageView alloc] init];
  centerIM.contentMode = UIViewContentModeScaleAspectFill;
  if (self.permissionType == 0) {
    centerIM.image = [UIImage imageNamed:@"icon_showmap"];
  }
  centerIM.layer.cornerRadius = 8;
  centerIM.clipsToBounds = YES;
  [self.view addSubview:centerIM];
  [centerIM mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerX.mas_equalTo(self.view);
    make.height.mas_equalTo(168);
    make.width.mas_equalTo(BCWidth - 32);
    make.top.mas_equalTo(tipL.mas_bottom);
  }];
  
  
  
  UIButton *sureBtn = [[UIButton alloc] init];
  sureBtn.backgroundColor = COLOR(26, 106, 255);
  sureBtn.layer.cornerRadius = 6;
  if (@available(iOS 13.0, *)) {
    sureBtn.layer.cornerCurve = kCACornerCurveContinuous;
  }
  sureBtn.titleLabel.font = [UIFont systemFontOfSize:MutilFont(17) weight:UIFontWeightMedium];
  [sureBtn setTitle:@"去打开" forState:UIControlStateNormal];
  [sureBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
  [sureBtn addTarget:self action:@selector(openSetting) forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:sureBtn];
  [sureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(16);
    make.width.mas_equalTo(BCWidth - 32);
    make.height.mas_equalTo(50);
    make.top.mas_equalTo(centerIM.mas_bottom).offset(16);
  }];
  
  
  UIButton *cancelBtn = [[UIButton alloc] init];
  cancelBtn.backgroundColor = COLOR(247, 248, 250);
  cancelBtn.layer.cornerRadius = 6;
  if (@available(iOS 13.0, *)) {
    cancelBtn.layer.cornerCurve = kCACornerCurveContinuous;
  }
  cancelBtn.titleLabel.font = [UIFont systemFontOfSize:MutilFont(17) weight:UIFontWeightMedium];
  [cancelBtn setTitle:@"拒绝" forState:UIControlStateNormal];
  [cancelBtn setTitleColor:COLOR(29, 33, 41) forState:UIControlStateNormal];
  [cancelBtn addTarget:self action:@selector(closeClick) forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:cancelBtn];
  [cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(16);
    make.width.mas_equalTo(BCWidth - 32);
    make.height.mas_equalTo(50);
    make.top.mas_equalTo(sureBtn.mas_bottom).offset(12);
  }];
  
  
}

- (void)openSetting{
  
  [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString] options:@{} completionHandler:nil];
}

- (void)closeClick{
  [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)dealloc
{
  DDLog(@"权限弹窗释放");
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}
@end
