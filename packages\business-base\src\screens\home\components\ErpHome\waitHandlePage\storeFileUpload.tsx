import React, { useState } from 'react'
import { ProText } from '@xlb/common/src/components'
import { Image, Modal, Platform, StyleSheet, View, useWindowDimensions } from 'react-native'
import DocumentPicker from '@react-native-documents/picker'
import ImageCropPicker, { Image as ImageProps } from 'react-native-image-crop-picker'
import { ActionSheet } from '@xlb/business-kms/src/screens/pointApply/components/ActionSheet'
import { IconAssets } from '@xlb/common/src/assets/iconfont'
import { uploadFiles } from '@xlb/business-kms/src/screens/pointApply/utils/upload'
import Toast from 'react-native-root-toast'
import ImageViewer from 'react-native-image-zoom-viewer'

interface Iprops {
  store_id: number
  type: string
  onChange: (value: any) => void
}

export default function StoreFileUpload(props: Iprops) {
  const { store_id, type, onChange } = props
  const { height } = useWindowDimensions()
  const [isVisible, setIsVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  const [image, setImage] = useState<{ url: string | null }>({ url: null })
  const pickerLists = [
    {
      label: '拍照',
      value: 'openCamera',
    },
    {
      label: '从相册选取',
      value: 'openPicker',
    },
  ]

  enum CameraActionType {
    OPEN_CAMERA = 'openCamera',
    OPEN_PICKER = 'openPicker',
    OPEN_PHOTO = 'openPhoto',
  }

  const imageSuffix = ['jpg', 'jpeg', 'png', 'bmp']

  //   const handleImage = (image: ImageProps) => {
  //     const type = image.mime ? 'mime' : 'type'

  //     image.filename = new Date().getTime() + `.${image[type]?.split('/')[1]}`
  //     image.path = image.path || image.uri
  //     return {
  //       url: '',
  //       file: image,
  //     } as FileItem
  //   }

  const handleMixFile = (file: ImageProps) => {
    const mineArr = (file?.mime || file?.type || '').split('/')
    const suffix = mineArr[mineArr.length - 1]
    const isImage = imageSuffix.includes(suffix)
    if (!isImage) {
      Toast.show('请选择图片')
      return null
    }

    file.filename = new Date().getTime() + `${isImage ? `.${mineArr[1]}` : '.mp4'}`
    file.path = file?.path || file?.uri
    return file as ImageProps
  }

  const uploadMixFileByCamera = async () => {
    const data: any = await ImageCropPicker.openCamera({
      multiple: true,
      mediaType: 'photo',
      includeBase64: true,
      cropping: false,
      compressImageMaxWidth: 1000,
      compressImageQuality: 0.7,
      cropperChooseText: '确认',
      cropperCancelText: '取消',
    }).catch(() => {
      return null
    })
    if (data) {
      const image: any = handleMixFile(data[0])
      if (image) {
        handleUploadFile(image)
      }
    }
  }

  const uploadMixFileByPicker = async () => {
    //  ios直接进相册，安卓和鸿蒙去文件夹里选
    const data: any =
      Platform.OS === 'ios'
        ? await ImageCropPicker.openPicker({
            // multiple: false,
            // mediaType: 'photo',
            // includeBase64: true,
            // cropping: false,
            // compressImageMaxWidth: 1000,
            // compressImageQuality: 0.7,
            // cropperChooseText: '确认',
            // cropperCancelText: '取消',

            multiple: false,
            mediaType: 'photo',
            includeBase64: true,
            cropping: false,
            compressImageMaxWidth: 1000,
            compressImageQuality: 0.7,
            cropperChooseText: '确认',
            cropperCancelText: '取消',
          }).catch(() => {
            Toast.show('文件选择失败，请检查上传文件是否为图片或者视频')
            return null
          })
        : await DocumentPicker.pick({ type: [DocumentPicker.types.images] })
    if (data) {
      const image: any = handleMixFile(data instanceof Array ? data[0] : data)
      if (image) {
        handleUploadFile(image)
      }
    }
  }
  const handleUploadFile = async (file: Image) => {
    const result = await uploadFiles(file, { refType: type, refId: store_id }, store_id, '/erp/hxl.erp.file.upload').catch(() => {
      Toast.show('操作失败,请检查网络连接！')
      setLoading(false)
      return null
    })
    if (!result) return Toast.show('操作失败,请检查网络连接！')
    onChange(result)
    setImage(result)
    setLoading(false)
  }

  const handleUpload = (type: CameraActionType) => {
    if (type === CameraActionType.OPEN_PICKER) return uploadMixFileByPicker()
    if (type === CameraActionType.OPEN_CAMERA) return uploadMixFileByCamera()
    if (type === CameraActionType.OPEN_PHOTO) return setIsVisible(true)
  }
  return (
    <ActionSheet
      showCancel
      containerStyle={styles.containerStyle}
      lists={image?.url ? [{ label: '查看大图', value: 'openPhoto' }, ...pickerLists] : pickerLists}
      percent={parseFloat(((image?.url ? 200 : 145) / height).toFixed(4))}
      hiddenHeader
      onChange={(item) => {
        if (Platform.OS === 'ios') {
          setTimeout(() => handleUpload(item.value), 1000)
        } else {
          handleUpload(item.value)
        }
      }}
    >
      <View style={styles.uploadBox}>
        <View style={styles.uploadBackground}>
          {!image?.url ? (
            <>
              <IconAssets color={'#C9CDD4'} />
              <ProText style={{ fontSize: 14, color: '#C9CDD4' }}>点击上传附件，可自动识别信息</ProText>
            </>
          ) : (
            <Image resizeMode={'cover'} style={{ width: 211, height: 140 }} source={{ uri: image?.url }} />
          )}
        </View>
      </View>
      <Modal visible={isVisible}>
        <ImageViewer
          index={0}
          enableSwipeDown
          onClick={() => setIsVisible(false)}
          onSwipeDown={() => setIsVisible(false)}
          imageUrls={[image]}
          menuContext={{ saveToLocal: '保存到相册', cancel: '取消' }}
        />
      </Modal>
    </ActionSheet>
  )
}

const styles = StyleSheet.create({
  containerStyle: {
    backgroundColor: '#F2F3F5',
    paddingHorizontal: 0,
    paddingVertical: 0,
    overflow: 'hidden',
  },
  uploadBox: {
    height: 168,
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: 10,
    marginTop: 10,
    justifyContent: 'center',
    marginHorizontal: 10,
    alignItems: 'center',
  },
  uploadBackground: {
    width: '100%',
    height: 140,
    borderColor: '#E5E6EA',
    borderStyle: 'dashed',
    borderWidth: 0.5,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
})
