// src/components/xlbUpdate/remoteUpdate.ts (增强版)
import {ScriptManager} from '@callstack/repack/client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Config from 'react-native-config';
import {clearVersionCache} from '../../../bundleLoader.ts';
import {bundlePrefix} from "@/components/xlbUpdate/constant.ts";

export interface RemoteModuleInfo {
    name: string;
    currentVersion: string;
    remoteVersion: string;
    needsUpdate: boolean;
    size?: number; // 模块大小（字节）
    lastUsed?: number; // 上次使用时间戳
}

export interface PreloadOptions {
    priority?: 'low' | 'normal' | 'high';
    timeout?: number;
    retryCount?: number;
}

class RemoteModuleUpdater {
    private readonly ossHost: string;
    private versionCache: Map<string, { version: string; timestamp: number }> = new Map();
    private moduleMetadataCache: Map<string, RemoteModuleInfo> = new Map();
    private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟
    private readonly METADATA_CACHE_DURATION = 10 * 60 * 1000; // 10分钟
    private preloadQueue: Array<{ moduleName: string; options: PreloadOptions }> = [];
    private isPreloading: boolean = false;

    constructor() {
        this.ossHost = bundlePrefix;
    }

    // 检查所有远程模块的更新
    async checkAllModulesUpdate(): Promise<RemoteModuleInfo[]> {
        try {
            const res = await this.fetchWithTimeout(`${this.ossHost}/version.json`);
            const remoteVersions = await res.json();

            const moduleInfos: RemoteModuleInfo[] = [];

            for (const moduleName in remoteVersions) {
                const moduleInfo = await this.getModuleInfo(moduleName, remoteVersions[moduleName]);
                if (moduleInfo) {
                    moduleInfos.push(moduleInfo);
                }
            }

            return moduleInfos;
        } catch (error) {
            console.error('检查模块更新失败:', error);
            return [];
        }
    }

    // 检查单个模块更新
    async checkModuleUpdate(moduleName: string): Promise<RemoteModuleInfo | null> {
        try {
            const res = await this.fetchWithTimeout(`${this.ossHost}/version.json`);
            const remoteVersions = await res.json();

            return await this.getModuleInfo(moduleName, remoteVersions[moduleName]);
        } catch (error) {
            console.error(`检查模块 ${moduleName} 更新失败:`, error);
            return null;
        }
    }

    // 获取模块详细信息
    private async getModuleInfo(moduleName: string, versionData: any): Promise<RemoteModuleInfo | null> {
        try {
            const remoteVersion = versionData?.[Config.BRANCH_NAME!];

            // 从缓存获取当前版本信息
            let currentVersion = await this.getCurrentModuleVersion(moduleName);

            // 如果没有当前版本信息，尝试从元数据缓存获取
            if (currentVersion === 'unknown') {
                const cachedMetadata = this.moduleMetadataCache.get(moduleName);
                if (cachedMetadata && (Date.now() - cachedMetadata.lastUsed! < this.METADATA_CACHE_DURATION)) {
                    currentVersion = cachedMetadata.currentVersion;
                }
            }

            if (remoteVersion && currentVersion) {
                const needsUpdate = remoteVersion !== currentVersion;

                const moduleInfo: RemoteModuleInfo = {
                    name: moduleName,
                    currentVersion,
                    remoteVersion,
                    needsUpdate,
                    lastUsed: Date.now()
                };

                // 缓存模块元数据
                this.moduleMetadataCache.set(moduleName, moduleInfo);

                return moduleInfo;
            }
        } catch (error) {
            console.warn(`获取模块 ${moduleName} 信息失败:`, error);
        }

        return null;
    }

    // 获取当前模块版本
    private async getCurrentModuleVersion(moduleName: string): Promise<string> {
        // 从缓存中获取
        const cached = this.versionCache.get(moduleName);
        const now = Date.now();

        if (cached && (now - cached.timestamp) < this.CACHE_DURATION) {
            return cached.version;
        }

        try {
            // 从 AsyncStorage 中获取当前加载的版本信息
            const cacheKey = `Repack.ScriptManager.Cache.v4.debug`;
            const cacheData = await AsyncStorage.getItem(cacheKey);

            if (cacheData) {
                const cache = JSON.parse(cacheData);
                // 尝试从缓存中提取版本信息
                // 这里需要根据实际的缓存结构来解析
                if (cache[moduleName]) {
                    const version = this.extractVersionFromCache(cache[moduleName]);
                    if (version) {
                        this.versionCache.set(moduleName, { version, timestamp: now });
                        return version;
                    }
                }
            }
        } catch (error) {
            console.warn(`获取模块 ${moduleName} 当前版本失败:`, error);
        }

        return 'unknown';
    }

    // 从缓存数据中提取版本信息
    private extractVersionFromCache(cacheEntry: any): string | null {
        try {
            // 根据实际缓存结构调整解析逻辑
            if (cacheEntry?.url) {
                // 从URL中提取版本信息
                const urlParts = cacheEntry.url.split('/');
                const versionIndex = urlParts.findIndex((part: string) =>
                    /^\d{14}-[a-z]+-[a-f0-9]+$/.test(part)
                );
                if (versionIndex !== -1) {
                    return urlParts[versionIndex];
                }
            }
            return null;
        } catch (error) {
            console.warn('提取版本信息失败:', error);
            return null;
        }
    }

    // 带超时的 fetch
    private async fetchWithTimeout(url: string, timeout: number = 10000): Promise<Response> {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        try {
            const response = await fetch(url, {
                signal: controller.signal,
            });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }
}

export const remoteModuleUpdater = new RemoteModuleUpdater();
export default remoteModuleUpdater;
