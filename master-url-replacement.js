#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 导入各个处理模块
const URLReplacer = require('./url-replacement-script');
const DynamicPathHandler = require('./dynamic-path-handler');
const URLVerifier = require('./url-verification-script');

class MasterURLReplacement {
  constructor() {
    this.startTime = new Date();
    this.logs = {
      phases: [],
      summary: {
        totalPhases: 4,
        completedPhases: 0,
        startTime: this.startTime.toISOString(),
        endTime: null,
        overallSuccess: false
      }
    };
  }

  // 记录阶段日志
  logPhase(phase, status, details = {}) {
    const phaseLog = {
      phase,
      status,
      timestamp: new Date().toISOString(),
      ...details
    };
    
    this.logs.phases.push(phaseLog);
    
    if (status === 'completed') {
      this.logs.summary.completedPhases++;
    }
    
    console.log(`\n${'='.repeat(60)}`);
    console.log(`📋 阶段 ${phase}: ${status.toUpperCase()}`);
    if (details.message) {
      console.log(`📝 ${details.message}`);
    }
    console.log(`${'='.repeat(60)}\n`);
  }

  // 阶段1: 直接字符串路径替换
  async phase1_DirectStringReplacement() {
    this.logPhase('1 - 直接字符串路径替换', 'starting', {
      message: '开始处理直接字符串引用的URL路径'
    });

    try {
      const replacer = new URLReplacer();
      await replacer.run();
      
      this.logPhase('1 - 直接字符串路径替换', 'completed', {
        message: `完成直接字符串替换，修改了 ${replacer.replacementLog.summary.modifiedFiles} 个文件`,
        modifiedFiles: replacer.replacementLog.summary.modifiedFiles,
        totalReplacements: replacer.replacementLog.summary.totalReplacements
      });
      
      return true;
    } catch (error) {
      this.logPhase('1 - 直接字符串路径替换', 'failed', {
        message: `直接字符串替换失败: ${error.message}`,
        error: error.message
      });
      return false;
    }
  }

  // 阶段2: 动态路径构建处理
  async phase2_DynamicPathHandling() {
    this.logPhase('2 - 动态路径构建处理', 'starting', {
      message: '开始处理动态路径构建（actionModule模式等）'
    });

    try {
      const handler = new DynamicPathHandler();
      await handler.run();
      
      this.logPhase('2 - 动态路径构建处理', 'completed', {
        message: `完成动态路径处理，修改了 ${handler.dynamicLog.summary.modifiedFiles} 个文件`,
        modifiedFiles: handler.dynamicLog.summary.modifiedFiles,
        totalReplacements: handler.dynamicLog.summary.totalReplacements
      });
      
      return true;
    } catch (error) {
      this.logPhase('2 - 动态路径构建处理', 'failed', {
        message: `动态路径处理失败: ${error.message}`,
        error: error.message
      });
      return false;
    }
  }

  // 阶段3: 第二轮验证和补充处理
  async phase3_SecondRoundProcessing() {
    this.logPhase('3 - 第二轮验证和补充处理', 'starting', {
      message: '进行第二轮验证，处理遗漏的路径'
    });

    try {
      // 先进行验证
      const verifier = new URLVerifier();
      await verifier.run();
      
      const oldPathsFound = verifier.verificationLog.summary.totalOldPathsFound;
      
      if (oldPathsFound > 0) {
        console.log(`⚠️  发现 ${oldPathsFound} 个未处理的旧路径，进行第二轮处理...`);
        
        // 如果还有旧路径，再次运行替换
        const replacer = new URLReplacer();
        await replacer.run();
        
        this.logPhase('3 - 第二轮验证和补充处理', 'completed', {
          message: `第二轮处理完成，原有 ${oldPathsFound} 个旧路径，新增修改 ${replacer.replacementLog.summary.modifiedFiles} 个文件`,
          originalOldPaths: oldPathsFound,
          additionalModifiedFiles: replacer.replacementLog.summary.modifiedFiles
        });
      } else {
        this.logPhase('3 - 第二轮验证和补充处理', 'completed', {
          message: '第二轮验证通过，无需额外处理',
          oldPathsFound: 0
        });
      }
      
      return true;
    } catch (error) {
      this.logPhase('3 - 第二轮验证和补充处理', 'failed', {
        message: `第二轮处理失败: ${error.message}`,
        error: error.message
      });
      return false;
    }
  }

  // 阶段4: 最终验证
  async phase4_FinalVerification() {
    this.logPhase('4 - 最终验证', 'starting', {
      message: '进行最终验证，确保100%完成率'
    });

    try {
      const verifier = new URLVerifier();
      await verifier.run();
      
      const summary = verifier.verificationLog.summary;
      const completionRate = this.calculateCompletionRate(summary);
      
      const isSuccess = summary.totalOldPathsFound === 0;
      
      this.logPhase('4 - 最终验证', isSuccess ? 'completed' : 'warning', {
        message: `最终验证完成，完成率: ${completionRate.toFixed(2)}%`,
        completionRate: completionRate,
        oldPathsRemaining: summary.totalOldPathsFound,
        newPathsFound: summary.totalNewPathsFound,
        unmappedErpPaths: summary.unmappedErpPaths.length,
        isFullyComplete: isSuccess
      });
      
      return isSuccess;
    } catch (error) {
      this.logPhase('4 - 最终验证', 'failed', {
        message: `最终验证失败: ${error.message}`,
        error: error.message
      });
      return false;
    }
  }

  // 计算完成率
  calculateCompletionRate(summary) {
    const totalExpected = summary.totalNewPathsFound + summary.totalOldPathsFound;
    if (totalExpected === 0) return 100;
    return (summary.totalNewPathsFound / totalExpected) * 100;
  }

  // 清理临时文件
  cleanup() {
    const tempFiles = [
      'url-replacement-script.js',
      'url-verification-script.js', 
      'dynamic-path-handler.js',
      'master-url-replacement.js'
    ];
    
    console.log('\n🧹 清理临时文件...');
    tempFiles.forEach(file => {
      try {
        if (fs.existsSync(file)) {
          fs.unlinkSync(file);
          console.log(`✅ 删除: ${file}`);
        }
      } catch (error) {
        console.log(`⚠️  无法删除 ${file}: ${error.message}`);
      }
    });
  }

  // 生成最终报告
  generateFinalReport() {
    this.logs.summary.endTime = new Date().toISOString();
    this.logs.summary.overallSuccess = this.logs.summary.completedPhases === this.logs.summary.totalPhases;
    
    const reportFile = './final-replacement-report.json';
    fs.writeFileSync(reportFile, JSON.stringify(this.logs, null, 2));
    
    console.log('\n' + '='.repeat(80));
    console.log('🎉 URL路径替换任务最终报告');
    console.log('='.repeat(80));
    console.log(`⏱️  开始时间: ${this.logs.summary.startTime}`);
    console.log(`⏱️  结束时间: ${this.logs.summary.endTime}`);
    console.log(`📊 完成阶段: ${this.logs.summary.completedPhases}/${this.logs.summary.totalPhases}`);
    console.log(`✅ 整体状态: ${this.logs.summary.overallSuccess ? '成功' : '部分完成'}`);
    console.log(`📄 详细报告: ${reportFile}`);
    console.log('='.repeat(80));
    
    // 显示各阶段状态
    console.log('\n📋 各阶段执行状态:');
    this.logs.phases.forEach((phase, index) => {
      const statusIcon = phase.status === 'completed' ? '✅' : 
                        phase.status === 'warning' ? '⚠️' : 
                        phase.status === 'failed' ? '❌' : '🔄';
      console.log(`  ${statusIcon} ${phase.phase}: ${phase.status.toUpperCase()}`);
      if (phase.message) {
        console.log(`     ${phase.message}`);
      }
    });
    
    return this.logs.summary.overallSuccess;
  }

  // 主执行函数
  async run() {
    console.log('🚀 开始URL路径替换主任务...\n');
    console.log('📋 执行计划:');
    console.log('  1️⃣  直接字符串路径替换');
    console.log('  2️⃣  动态路径构建处理');
    console.log('  3️⃣  第二轮验证和补充处理');
    console.log('  4️⃣  最终验证');
    console.log('');

    try {
      // 执行各个阶段
      const phase1Success = await this.phase1_DirectStringReplacement();
      const phase2Success = await this.phase2_DynamicPathHandling();
      const phase3Success = await this.phase3_SecondRoundProcessing();
      const phase4Success = await this.phase4_FinalVerification();
      
      // 生成最终报告
      const overallSuccess = this.generateFinalReport();
      
      // 清理临时文件
      this.cleanup();
      
      if (overallSuccess) {
        console.log('\n🎉 URL路径替换任务全部完成！');
        process.exit(0);
      } else {
        console.log('\n⚠️  URL路径替换任务部分完成，请检查报告。');
        process.exit(1);
      }
      
    } catch (error) {
      console.error('\n❌ 主任务执行失败:', error);
      this.generateFinalReport();
      process.exit(1);
    }
  }
}

// 运行主脚本
if (require.main === module) {
  const master = new MasterURLReplacement();
  master.run();
}

module.exports = MasterURLReplacement;
