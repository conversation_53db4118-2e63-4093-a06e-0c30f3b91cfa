import useStausBarHeight from '@xlb/business-base/src/hook/useStatusBarHeight';
import React, {
  PropsWithChildren,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import {
  DeviceEventEmitter,
  ImageBackground,
  NativeModules,
  Platform,
  StatusBar,
  Text,
  View,
  StyleSheet,
  Image,
  ScrollView,
  Pressable,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import {$modalAlert} from '@xlb/common/src/components/SecondModal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import {
  XlbIconfont,
  XlbDropdown,
  XlbCard,
  XlbText,
  XlbTag,
  TOKEN,
  XlbIconfontNew,
  XlbPopup,
  XlbCell,
} from '@xlb/components-rn';
// import * as AliyunPush from 'aliyun-react-native-push'
import {colors, normalize} from '@xlb/common/src/config/theme';
import {authModel, roleAuth} from '@xlb/business-base/src/models/auth';
import {
  useFocusEffect,
  useNavigation,
  useIsFocused,
} from '@react-navigation/native';
import {XlbScanCode} from '@xlb/common/src/components/features';
import useRefreshStore from '@xlb/common/src/models/refresh';
import {
  Dropdown,
  Portal,
  Flex,
  NoticeBar,
} from '@fruits-chain/react-native-xiaoshu';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttp';
import XLBStorage from '@xlb/common/src/utils/storage';
import useAliCloudPush from '@xlb/common/src/hooks/useAliCloudPush';
import {background} from 'native-base/lib/typescript/theme/styled-system';
import {images} from '@xlb/common/src/config/images';
import DrawerMine from './mine';
import LinearGradient from 'react-native-linear-gradient';
import XlbIcon from '@xlb/common/src/assets/iconfont';
import useStore from '@xlb/common/src/components/features/xlbStoreText/model';
import {useNetInfo} from '@react-native-community/netinfo';
// import { KMS_ORG_LIST_KEY } from '@xlb/business-kms/src/screens/newPointApply/components/OrgSelectActionSheet'
import IconFont from '@xlb/common/src/assets/iconfont';
import Toast from 'react-native-root-toast';
import Loading from '@xlb/common/src/components/RootView/Loading';
// import userInfosModal from '@xlb/business-erp/src/models/userInfosModal'
import useSelectStore from '@xlb/business-base/src/screens/home/<USER>/ErpHome/store';
import userInfosModal from '../../models/userInfosModal';
import useSystemStore from '@xlb/common/src/models/system';

const {height} = useStausBarHeight();
let {NetworkSettings} = NativeModules; // 调用原生方法
interface PageWarpperProps {
  translucent?: boolean;
  isSearch?: boolean;
  isCustom?: boolean;
}

const PageWarpper: React.FC<PropsWithChildren<PageWarpperProps>> = ({
  translucent = true,
  isSearch = false,
  children,
  isCustom = false,
  ...rest
}) => {
  const userInfos = authModel?.state?.userInfos;
  const insets = useSafeAreaInsets();
  const navigation = useNavigation<any>();
  const {initPush} = useAliCloudPush();
  const isFocused = useIsFocused();
  const [ScanItem, setScanItem] = useState<any>({});
  const ViewRef = useRef<View>(null);
  const AvatarRef = useRef<Image>(null);
  const [popupProps, setPopupProps] = useState({
    targetHeight: 0,
    targetPageY: 0,
    visible: false,
  });
  const [accountList, setAccountList] = useState<any>([]);
  const [visible, setVisible] = useState(false);
  const setStoreLists = useStore((state: any) => state.setStoreList);
  const {type, isConnected} = useNetInfo();
  const setRefresh = useRefreshStore((state: any) => state.setHomeRefresh);
  const {setUserInfos} = userInfosModal((state: any) => state);
  const setStoreList = useSelectStore((state: any) => state.setStoreList);
  const {setTheme, setThemeName, themeName} = useSystemStore(
    (state: any) => state,
  );

  const getRole = async () => {
    const res = await ErpHttp.post<CommonResponse>(
      '/erp/hxl.erp.account.user.find',
      {tel: userInfos?.tel},
    );
    if (res.code === 0) {
      setAccountList(res.data.account_user_list);
    }
  };

  const accountChange = async (account: any, company_id: any, v: any) => {
    // 解决首页切换门店问题
    // setStoreList([])
    setStoreLists([]);
    XLBStorage.setItem('currentSelectedStore', null);

    authModel
      .onLogin(account, company_id, undefined, undefined)
      .then(async () => {
        setRefresh();
        XLBStorage.removeItem('PointPlanAdd');
        XLBStorage.removeItem('BusinessDistrictAdd');
        XLBStorage.removeItem('NewStockAdjustOrderAdd');
        // kms 多组织缓存数据
        XLBStorage.removeItem(KMS_ORG_LIST_KEY);
      });
    // AliyunPush.unbindAccount().then(() => initPush())
  };

  const changeStore = async () => {
    if (authModel?.state?.userInfos?.access_token?.length <= 6) {
      Toast.show('无操作权限');
      return;
    }
    // navigation.navigate('ErpSelectStore', {
    //   isMultiple: false,
    //   postUrl: '/erp/hxl.erp.user.switchstore.page',
    //   storeChange,
    // })

    navigation.navigate('RemoteAppBase.DeprecatedSelectStoreH5', {
      isMultiple: false,
      postUrl: '/erp/hxl.erp.user.switchstore.page',
      storeIds: authModel?.state?.userInfos?.store?.id
        ? [authModel?.state?.userInfos?.store?.id]
        : [],
      onSelect: data => {
        // console.log('当前选择门店', data)
        storeChange(data['list'][0]);
        XLBStorage.setItem('currentSelectedStore', data['list'][0]);
      },
    });
  };
  const storeChange = async (item: any) => {
    Loading.show();
    const res = await ErpHttp.post<CommonResponse>(
      '/erp/hxl.erp.user.store.switch',
      {id: item?.id},
    );
    if (res?.code === 0 && res?.data) {
      authModel?.setUserInfos(res.data);
      authModel?.setSystem(roleAuth(res.data));
      setUserInfos(res.data);
    }
    Loading.hide();
    // 解决首页切换门店问题
    setStoreList([]);
    setStoreLists([]);
    setRefresh();
    DeviceEventEmitter.emit('DepartDocumenRefresh'); //刷新待办事项内的发车单
  };
  const onPressShade = useCallback(() => {
    setPopupProps(s => ({
      ...s,
      visible: false,
    }));
  }, []);

  useEffect(() => {
    getRole();
  }, []);

  // useFocusEffect(
  //   React.useCallback(() => {
  //     // 在页面聚焦时执行操作
  //     StatusBar.setBarStyle('dark-content')
  //     StatusBar.setBackgroundColor(translucent ? 'transparent' : '#fff')
  //     StatusBar.setTranslucent(translucent)
  //     return () => {}
  //   }, [isFocused])
  // )
  useEffect(() => {
    ScanItem.code && navigation.navigate('ScanLoginWeb', {code: ScanItem.code});
  }, [ScanItem]);

  const enum userType {
    'USER' = '门店',
    'SUPPLIER' = '供应商',
    'CLIENT' = '批发',
  }

  const userTypeMap = {
    USER: {
      text: '门店',
      color: TOKEN.yellow_10,
    },
    SUPPLIER: {
      text: '供应商',
      color: TOKEN.green_10,
    },
    CLIENT: {
      text: '批发',
      color: TOKEN.primary_10,
    },
    BRAND: {
      text: '品牌',
      color: TOKEN.red_10,
    },
    DELIVERY: {
      text: '配送',
      color: '#9F40FF',
    },
  };

  let avatar_url = userInfos?.avatar_url
    ? {
        uri: userInfos?.avatar_url,
      }
    : images['newAvatar'];

  let user_type: 'USER' | 'SUPPLIER' | 'CLIENT' | 'BRAND' | 'DELIVERY' =
    userInfos?.user_type;
  if (user_type == 'USER' && userInfos.store.enable_delivery_center) {
    if (
      userInfos.store.store_name == '管理中心' ||
      userInfos.store.enable_organization
    ) {
      user_type = 'BRAND';
    } else {
      user_type = 'DELIVERY';
    }
  }

  let themeMap = {
    default: {
      bg: require('@/assets/images/home_bg.png'),
    },
    blue: {
      bg: require('@/assets/images/home_bg-1.png'),
    },
    green: {
      bg: require('@/assets/images/home_bg-3.png'),
    },
    orange: {
      bg: require('@/assets/images/home_bg-2.png'),
    },
  };

  const themeColor = themeName === 'default' ? TOKEN.grey_10 : '#fff';
  return (
    <>
      {/* <DrawerMine visible={visible} setVisible={setVisible} avatar={avatar_url}> */}
      <View
        style={{
          backgroundColor: '#F4F5F7',
          flex: 1,
          position: 'relative',
        }}>
        <StatusBar
          hidden={false}
          translucent
          // animated
          backgroundColor={'transparent'}
          barStyle="dark-content"></StatusBar>
        {translucent && (
          <Image
            style={{
              width: '100%',
              height: 200,
              zIndex: 1,
              position: 'absolute',
              left: 0,
              top: 0,
              borderBottomLeftRadius: TOKEN.space_4,
              borderBottomRightRadius: TOKEN.space_4,
            }}
            resizeMode="stretch"
            source={themeMap[themeName as keyof typeof themeMap]?.bg}></Image>
        )}
        <View
          style={{
            zIndex: 2,
            flex: 1,
            backgroundColor: 'translucent',
          }}>
          {/* 账户信息 */}
          <View
            style={{
              ...styles['header-wrap'],
              backgroundColor: translucent ? 'transparent' : '#fff',
              marginTop: insets.top,
            }}
            ref={ViewRef}
            collapsable={false}>
            <TouchableOpacity
              activeOpacity={0.5}
              onPress={() => {
                setVisible(true);
                if (navigation?.openDrawer) {
                  navigation?.openDrawer();
                }
              }}
              style={styles['header-hd']}>
              <Image
                ref={AvatarRef}
                source={avatar_url}
                style={{width: normalize(36), height: normalize(36)}}></Image>
            </TouchableOpacity>

            <TouchableOpacity
              activeOpacity={0.5}
              onPress={() => {
                // ViewRef.current?.measure((x, y, width, height, pageX, pageY) => {
                //   setPopupProps((s) => ({
                //     ...s,
                //     targetHeight: height,
                //     targetPageY: pageY,
                //     visible: true,
                //   }))
                // })
                changeStore();
              }}
              style={styles['header-bd']}>
              {/* {!!userTypeMap[userInfos?.user_type]?.text && <XlbTag color={userTypeMap[user_type]?.color}>{userTypeMap[user_type]?.text}</XlbTag>} */}
              <Text
                style={{
                  ...styles['header-bd__text'],
                  color: themeColor,
                }}
                numberOfLines={1}>
                {/* {userInfos?.company_id}｜{userInfos?.company_name?.replace('品牌零食', '')} */}
                {/* 门店用户且不是管理中心的直接显示门店名称 */}
                {/* {userInfos?.user_type === 'USER' && userInfos?.store_name !== '管理中心' ? `${userInfos?.store_name}` : `${userInfos?.company_name}`} */}
                {userInfos?.store_name}
              </Text>

              <XlbIcon
                name="sanjiaoxiala"
                color={themeColor}
                size={normalize(16)}
                style={{
                  transform: [
                    {rotate: popupProps.visible ? '-180deg' : '0deg'},
                  ],
                }}></XlbIcon>
            </TouchableOpacity>
            {/* <View style={styles['header-ft']}>
            
            <XlbIconfont name="saoyisao1" size={normalize(20)}></XlbIconfont>
          </View> */}

            {isCustom && (
              <TouchableOpacity
                activeOpacity={0.4}
                onPress={() => navigation.navigate('customModule')}
                style={{
                  width: 32,
                  height: 32,
                  alignItems: 'flex-end',
                  justifyContent: 'center',
                }}>
                <IconFont
                  color={themeColor}
                  name="mokuai"
                  size={normalize(18)}
                />
              </TouchableOpacity>
            )}
            {isSearch && (
              <TouchableOpacity
                activeOpacity={0.6}
                onPress={() => navigation.navigate('Search')}
                style={styles['header-ft']}>
                <XlbIconfont
                  name="sousuo"
                  color={themeColor}
                  size={normalize(20)}></XlbIconfont>
              </TouchableOpacity>
            )}
          </View>
          {/* <View>
            <XlbText>{isConnected}</XlbText>
            <XlbText>{type}</XlbText>
          </View> */}
          {!isConnected && (
            <NoticeBar
              renderLeftIcon={(color, size) => (
                <XlbIconfont
                  style={{alignItems: 'center', alignSelf: 'center'}}
                  name="bohui"
                  color={'red'}
                  size={normalize(15)}
                />
              )}
              color={TOKEN.grey_10}
              iconColor={TOKEN.grey_45}
              onPress={() => {
                // navigation.navigate('Notice')

                NetworkSettings.goToNetworkSettings();
              }}
              message="请检查你的网络设置"
              mode="link"
              status="error"
            />
          )}

          <View
            style={{
              flex: 1,
              zIndex: 2,
              // paddingHorizontal: normalize(12),
              paddingTop: normalize(8),
              // backgroundColor: '#F4F5F7',
            }}>
            {typeof children === 'function' ? children(AvatarRef) : children}
          </View>
        </View>

        {/* 选择账户弹窗 */}
        <Portal>
          <XlbDropdown.Popup
            {...popupProps}
            onPressShade={onPressShade}
            onPressOverlay={onPressShade}
            contentStyle={{
              borderBottomLeftRadius: TOKEN.space_4,
              borderBottomRightRadius: TOKEN.space_4,
              paddingBottom: TOKEN.space_3,
              overflow: 'hidden',
            }}>
            {translucent && (
              <Image
                style={{
                  width: '100%',
                  height: 200,
                  zIndex: 1,
                  position: 'absolute',
                  top: -(popupProps.targetPageY + popupProps.targetHeight),
                  borderBottomLeftRadius: TOKEN.space_4,
                  borderBottomRightRadius: TOKEN.space_4,
                }}
                resizeMode="stretch"
                source={require('@xlb/common/src/assets/images/home_bg1.png')}></Image>
            )}
            <ScrollView
              style={{
                maxHeight: 304,
                // minHeight: 200 - (popupProps.targetPageY + popupProps.targetHeight) - TOKEN.space_3,
                borderBottomLeftRadius: TOKEN.space_4,
                borderBottomRightRadius: TOKEN.space_4,
                zIndex: 2,
                // backgroundColor: 'red',
              }}>
              {accountList.map((e: any) => {
                let isSelect =
                  userInfos?.company_id + userInfos?.account ===
                  e.company_id + e.account;

                let user_type = e?.user_type;
                if (user_type == 'USER' && e.enable_delivery_center) {
                  if (e?.store_name == '管理中心' || e?.enable_organization) {
                    user_type = 'BRAND';
                  } else {
                    user_type = 'DELIVERY';
                  }
                }
                return (
                  <XlbCard
                    key={e.company_id + e.account}
                    title={
                      <Flex>
                        <XlbText
                          font_size_6
                          grey_10
                          semiBold
                          style={{
                            marginRight: TOKEN.space_2,
                          }}>{`${e.company_id}｜${e.company_name}`}</XlbText>

                        {!!userTypeMap[userInfos?.user_type]?.text && (
                          <XlbTag color={userTypeMap[user_type]?.color}>
                            {userTypeMap[user_type]?.text}
                          </XlbTag>
                        )}
                      </Flex>
                    }
                    containerStyle={{
                      backgroundColor: TOKEN.low_bg,
                      ...(isSelect && {
                        borderColor: TOKEN.primary_5,
                        borderWidth: StyleSheet.hairlineWidth,
                      }),
                    }}
                    onPress={() => {
                      if (authModel?.state?.loginMethod === 'phone') {
                        return $modalAlert(
                          '提示',
                          '账号密码登录用户无法切换账户',
                          () => {},
                          () => {},
                          '',
                          '确认',
                          true,
                        );
                      }
                      if (authModel?.state?.userInfos?.isTemporarily) return;
                      accountChange(e.account, e.company_id, e);
                    }}>
                    <>
                      <View>
                        <Text style={styles['item-text']}>
                          {e?.store_name}-{e?.account}
                        </Text>
                      </View>
                      {isSelect && (
                        <View style={styles['select-icon']}>
                          <XlbIconfont
                            name="xuanzhong"
                            size={normalize(16)}
                            color="#1A6AFF"></XlbIconfont>
                        </View>
                      )}
                    </>
                  </XlbCard>
                );
              })}
            </ScrollView>
          </XlbDropdown.Popup>
        </Portal>
      </View>
      {/* </DrawerMine> */}
    </>
  );
};

export default PageWarpper;

const styles = StyleSheet.create({
  'header-wrap': {
    paddingHorizontal: normalize(12),
    ...(Platform.OS === 'android'
      ? {
          marginTop: height,
          height: normalize(44),
        }
      : {
          marginTop: 0,
          paddingTop: height,
          height: normalize(44) + height,
        }),

    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
    // backgroundColor: 'red',
  },
  'header-hd': {
    width: normalize(36),
    height: normalize(36),
    marginRight: normalize(12),
    borderRadius: normalize(18),
    overflow: 'hidden',
  },
  'header-bd': {
    display: 'flex',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  'header-bd__text': {
    color: '#1F2126',
    fontSize: normalize(17),
    fontWeight: '600',
    // marginRight: normalize(4),
    marginLeft: normalize(4),
    maxWidth: normalize(230),
  },
  'header-ft': {
    width: normalize(20),
    height: normalize(20),
    marginRight: normalize(4),
  },
  'select-icon': {
    position: 'absolute',
    right: normalize(12),
    top: '50%',
    marginTop: normalize(4),
  },
  'item-text': {
    color: 'rgba(30, 33, 38, 0.45)',
    fontSize: normalize(13),
  },
});
