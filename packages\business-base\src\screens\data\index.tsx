import React, {useEffect, useRef, useState} from 'react';
import {
  DeviceEventEmitter,
  ImageBackground,
  NativeModules,
  Platform,
  StatusBar,
  Text,
  View,
  StyleSheet,
  Image,
  Pressable,
  ScrollView,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';

import {authModel, roleAuth} from '@xlb/business-base/src/models/auth';
import {
  BottomUp,
  commonStyles,
  cs,
  Header,
  Item,
  Space,
  XText,
} from '@xlb/common/src/components';
import useAliCloudPush from '@xlb/common/src/hooks/useAliCloudPush';

import {NavigationProp, useNavigation} from '@react-navigation/native';
import {
  XlbStoreText,
  XlbToggleChange,
} from '@xlb/common/src/components/features';

import {colors, normalize} from '@xlb/common/src/config/theme';
import useStausBarHeight from '@xlb/business-base/src/hook/useStatusBarHeight';
import SwiperCard from '@xlb/common/src/components/Swiper';
import PageWarpper from '@xlb/business-base/src/components/pageWarpper';
import SelectDate from '@xlb/business-base/src/components/selectDate';
import {
  XlbIconfont,
  XlbButton,
  XlbTabs,
  XlbIconfontNew,
  XlbSegmentedControl,
  XlbDatePickerView,
  XlbCard,
  XlbText,
  TOKEN,
} from '@xlb/components-rn';
import useStore from '@xlb/common/src/components/features/xlbStoreText/model';
import {images, icons} from '@xlb/common/src/config/images';
import DatePicker from 'react-native-date-picker';
import dayjs from 'dayjs';
import {DatePickerView, Picker, PickerView} from '@ant-design/react-native';
import RNEChartsPro from 'react-native-echarts-pro';

import {dateMap} from '@xlb/business-base/src/components/selectDate/config';
import {ScreenWidth} from '@dplus/rn-ui';
import {Col, Row, Flex} from '@fruits-chain/react-native-xiaoshu';
import FastImage from 'react-native-fast-image';
// import { ErpRoutes } from '@xlb/business-erp/src/config/route'
import useHasAuth from '@xlb/common/src/hooks/useHasAuth';
import Toast from 'react-native-root-toast';
import useSystemStore from '@xlb/common/src/models/system';
import {routes} from '@xlb/common/src/config/routeWeb';
import {
  handlePreloadPage,
  preWebView,
} from '@xlb/common/src/navigation/preLoadRouter';
const {height} = useStausBarHeight();

type MenuListType = {
  title: string;
  children: {
    title: string;
    icon: any;
    subTitle: string;
    route?: string;
    isPreLoad?: boolean;
    h5Route?: string;
    isHasAuth?: boolean;
  }[];
};

/**
 * 数据
 * @constructor
 */
const Data: React.FC = () => {
  const navigation = useNavigation<NavigationProp<any>>();

  const store = useStore((state: any) => state);
  const [tabValue, setTabValue] = useState('1');
  const userInfos = authModel?.state?.userInfos;
  const {isH5Load, setIsH5Load} = useSystemStore((state: any) => state);
  // 监听路由，如果门店用户没有进件，需要弹窗提示
  useEffect(() => {}, [navigation]);

  const hasAuth =
    useHasAuth(100161) ||
    useHasAuth(100162) ||
    useHasAuth(100178) ||
    useHasAuth(100179) ||
    useHasAuth(100180) ||
    useHasAuth(100181) ||
    useHasAuth(100182) ||
    useHasAuth(100205);

  let menuList: MenuListType[] = [
    {
      title: '门店经营',
      children: [
        {
          title: '日商分析',
          icon: images.storeAnalysis,
          subTitle: '数据全局预览',
          route: 'RemoteAppBi.DayBusinessAnalysis',
          isPreLoad: true,
          h5Route: '/dayBusinessAnalysis',
          isHasAuth: useHasAuth(['营业收款报表', '查询']),
        },
        {
          title: '销售分析',
          icon: images.storeAnalysis,
          subTitle: '数据全局预览',
          route: 'RemoteAppBi.SaleAnalysisH5',
          isPreLoad: true,
          h5Route: '/salesAnalysis',
          isHasAuth: useHasAuth(['营业收款报表', '查询']),
        },
        {
          title: '支付分析',
          icon: images.appCollectionAnlysis,
          route: 'RemoteAppBi.PaymentAnalysis',
          subTitle: '多渠道看营业情况',
          isHasAuth: useHasAuth(['营业收款报表', '查询']),
        },
        {
          title: '营业收款分析',
          icon: images.appCollectionAnlysis,
          route: 'RemoteAppBi.CollectionAnalysis',
          subTitle: '多渠道看营业情况',
          isHasAuth: useHasAuth(['营业收款报表', '查询']),
        },

        {
          title: '消费券返款',
          icon: images.ConsumerRollRebate,
          subTitle: '消费券返款情况',
          route: 'RemoteAppBi.consumerRollRebateH5',
          isPreLoad: true,
          h5Route: '/couponsRefund',
          isHasAuth: useHasAuth(['APP消费券返款', '查询']),
        },
        {
          title: '商品销售分析',
          icon: images.salesAnalysis,
          subTitle: '销售多维分析',
          route: 'RemoteAppBi.ProductSalesAnalysis',
          isPreLoad: true,
          h5Route: '/xlb_erp/goodsSalesAnalysis',
          isHasAuth: useHasAuth(['商品销售分析', '查询']),
        },
        {
          title: '商品分层分析',
          icon: images.Stratificationanalysis,
          route: 'RemoteAppBi.Stratificationanalysis',
          isPreLoad: true,
          h5Route: '/stratificationanalysis',
          subTitle: '商品abc、me分析',
          isHasAuth: useHasAuth(['商品分层分析', '查询']),
        },
        {
          title: '商品复购分析',
          icon: images.GoodsRepurchase,
          subTitle: '商品消费人群数据',
          route: 'RemoteAppBi.GoodsRepurchase',
          isPreLoad: true,
          h5Route: '/goodsRepurchase',
          isHasAuth: useHasAuth(['商品复购分析', '查询']),
        },
        {
          title: '异常单品',
          icon: images.suppplierManage,
          subTitle: '商品缺货、断货预警',
          route: 'RemoteAppBi.AbnormalItem',
          isHasAuth: useHasAuth(['异常单品', '查询']),
        },
        {
          title: '时段销售分析',
          icon: images.timePeriodAnalysis,
          route: 'RemoteAppBi.TimePeriodAnalysis',
          isPreLoad: true,
          h5Route: '/timePeriodAnalysis',
          isHasAuth: useHasAuth(['时段销售分析', '查询']),
          subTitle: '按时段看销售趋势',
        },
        // {
        //   title: '消费券分析',
        //   icon: images.couponAnalysisReport,
        //   route: ErpRoutes.CouponAnalysisReport,
        //   isPreLoad: true,
        //   h5Route: '/couponAnalysisReport',
        //   isHasAuth: useHasAuth(['消费券分析', '查询']),
        //   subTitle: '消费券核销情况',
        // },
        {
          title: '短保商品报表',
          icon: images.shortInsuranceAnalysis,
          route: 'RemoteAppBi.ShortInsuranceGoodsAnalysis',
          isPreLoad: true,
          h5Route: '/shortInsuranceGoodsAnalysis',
          isHasAuth: useHasAuth(['短保商品报表', '查询']),
          subTitle: '短保质期商品统计',
        },
        {
          title: '实时库存',
          icon: images.suppplierManage,
          subTitle: '多角度看商品库存',
        },
        {
          title: '单品详情',
          icon: images.suppplierManage,
          subTitle: '商品查询和详细数据',
        },
        {
          title: '经营货盘分析',
          icon: images.suppplierManage,
          subTitle: '多维度看门店货盘',
        },
      ],
    },
    {
      title: '运营分析',
      children: [
        {
          title: '门店经营分析',
          route: 'RemoteAppBi.storeBusinessAnalysis',
          subTitle: '门店租售比、坪效等',
          icon: images.storeBusinessAnalysis,
          isHasAuth: useHasAuth(['门店经营状况分析', '查询']),
        },
        {
          title: '门店综合分析',
          route: 'RemoteAppBi.ShopRunAnalysis',
          subTitle: '门店租售比、坪效等',
          icon: images.storeBusinessAnalysis,
          isHasAuth: useHasAuth(['门店经营状况分析', '查询']),
        },
        {
          title: '门店对比',
          icon: images.StoreCompared,
          route: 'RemoteAppBi.StoreCompared',
          subTitle: '同纬度数据对比分析',
          isHasAuth: useHasAuth(['店横对比', '查询']),
        },
        {
          title: '区域销售分析',
          icon: images.areaSaleThink,
          route: 'RemoteAppBi.AreaSaleThinkIndex',
          subTitle: '按区域看销售数据',
          isHasAuth: useHasAuth(['区域销售分析', '查询']),
        },
        {
          title: '门店销售统计',
          icon: images.storeSaleRank,
          route: 'RemoteAppBi.StoreSaleAnalysis',
          subTitle: '多维度看分级门店',
          isHasAuth: useHasAuth(['门店销售分级统计', '查询']),
        },
        {
          title: '配送分析',
          icon: images.deliveryMargins,
          route: 'RemoteAppBi.DeliveryMargins',
          subTitle: '多维度看配送数据',
          isHasAuth: useHasAuth(['配送分析', '查询']),
        },
        // {
        //   title: 'BOSS报表',
        //   route: 'RemoteAppBi.BossTable',
        //   icon: images.erpStoreBoss,
        //   subTitle: '多维度看门店经营',
        //   isHasAuth: useHasAuth(['BOSS报表', '查询']),
        // },
        {
          title: '备货跟踪',
          route: 'RemoteAppBi.StockingTracking',
          icon: icons.stocking_tracking,
          subTitle: '备货进度跟踪',
          isHasAuth: useHasAuth(['备货跟踪', '查询']),
        },
      ],
    },
    {
      title: '明细数据',
      children: [
        {
          title: '已结账单查询',
          icon: images.postOrder,
          route: 'RemoteAppBi.PostOrder',
          subTitle: '门店订单数据',
          isHasAuth: useHasAuth(['已结账单查询', '查询']),
        },
        {
          title: '异常收银分析',
          icon: images.AbnormalCashAnalysis,
          route: 'RemoteAppBi.AbnormalCashAnalysis',
          subTitle: '门店异常收银记录',
          isHasAuth: useHasAuth(['异常收银分析', '查询']),
        },
        {
          title: '单品业绩',
          icon: images.singleProduct,
          route: 'RemoteAppBi.ProductPerformance',
          subTitle: '基础信息、采销配情况',
          isHasAuth: useHasAuth(['单品业绩', '查询']),
        },
      ],
    },
  ];

  let menuItem: any = menuList.reduce((acc: any, item) => {
    if (item.children && item.children.length > 0) {
      acc = [...acc, ...item.children];
    }
    return acc;
  }, []);
  console.log('menuItem', menuItem);
  return (
    <PageWarpper>
      <ScrollView style={{flex: 1}}>
        {hasAuth && (
          <TouchableOpacity
            onPress={() => {
              // StatusBar.setTranslucent(false)
              // StatusBar.setBackgroundColor('#1A6AFF')
              // StatusBar.setBarStyle('light-content')
              Toast.show('敬请期待', {
                position: 80,
              });
              return false;
            }}
            activeOpacity={0.5}
            style={{
              height: normalize(100),
              marginHorizontal: normalize(12),
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#fff',
              overflow: 'hidden',
              borderRadius: normalize(8),
              marginBottom: normalize(12),
            }}>
            <FastImage
              resizeMode="stretch"
              style={{width: '100%', height: normalize(100)}}
              source={require('@xlb/common/src/assets/images/banner-data.png')}></FastImage>
          </TouchableOpacity>
        )}

        {menuList.map((item: any, index: number) => {
          let {title, children} = item;
          if (!children.find(e => e.isHasAuth === true)) return false;
          return (
            <XlbCard
              key={index}
              title={title}
              containerStyle={{
                marginTop: !!!index ? 0 : normalize(12),
              }}>
              <Row>
                {children?.map((item: any, index: number) => {
                  let {
                    title,
                    icon,
                    subTitle,
                    route,
                    isHasAuth,
                    h5Route,
                    isPreLoad,
                  } = item;
                  if (!isHasAuth) return false;
                  return (
                    <Col span={12} key={index}>
                      <TouchableOpacity
                        activeOpacity={0.5}
                        style={{marginTop: normalize(20)}}
                        onPress={() => {
                          if (preWebView?.[route]) {
                            handlePreloadPage(navigation, {
                              path: preWebView[route]?.url,
                              appType: 'ERP',
                            });

                            return false;
                          } else {
                            console.log('route', route);
                            const obj = menuItem.find(
                              (item: any) => item.route === route,
                            );
                            if (route) {
                              navigation.navigate(route, {
                                pageName: obj?.title || '',
                              });
                            }
                          }

                          // setTimeout(() => {
                          // setH5Visible(true)
                          // }, 700)
                          // if (route) navigation.navigate(route)
                        }}>
                        <Flex>
                          <View>
                            <FastImage
                              style={{
                                width: normalize(36),
                                height: normalize(36),
                              }}
                              source={icon}></FastImage>
                          </View>
                          <View style={{marginLeft: TOKEN.space_2}}>
                            <XlbText
                              font_size_3
                              grey_10
                              style={{marginBottom: normalize(2)}}>
                              {title}
                            </XlbText>
                            <XlbText font_size_1 grey_45 numberOfLines={1}>
                              {subTitle}
                            </XlbText>
                          </View>
                        </Flex>
                      </TouchableOpacity>
                    </Col>
                  );
                })}
              </Row>
            </XlbCard>
          );
        })}
        <View
          style={{
            height: normalize(12),
          }}></View>
      </ScrollView>
    </PageWarpper>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    paddingTop: normalize(4),
    paddingBottom: normalize(4),
    paddingHorizontal: normalize(12),
    // backgroundColor: 'red',
  },
  footer: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
    flexDirection: 'row',
    backgroundColor: '#fff',
  },
});

export default Data;
