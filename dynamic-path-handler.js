#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 配置
const CONFIG = {
  apiMappingFile: './api-mapping.json',
  outputLogFile: './dynamic-path-log.json',
  fileExtensions: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx'],
  excludePatterns: [
    'node_modules/**',
    'dist/**',
    'build/**',
    '.git/**',
    'coverage/**',
    '*.log',
    'url-replacement-*.js',
    'url-verification-*.js',
    'dynamic-path-*.js',
    '*.json'
  ]
};

class DynamicPathHandler {
  constructor() {
    this.apiMapping = {};
    this.dynamicLog = {
      summary: {
        totalFiles: 0,
        modifiedFiles: 0,
        totalReplacements: 0,
        startTime: new Date().toISOString(),
        endTime: null
      },
      fileDetails: [],
      errors: []
    };
  }

  // 加载API映射
  loadApiMapping() {
    try {
      const content = fs.readFileSync(CONFIG.apiMappingFile, 'utf8');
      this.apiMapping = JSON.parse(content);
      console.log(`✅ 加载了 ${Object.keys(this.apiMapping).length} 个API映射`);
    } catch (error) {
      console.error('❌ 加载API映射失败:', error.message);
      process.exit(1);
    }
  }

  // 获取所有需要处理的文件
  getAllFiles() {
    const allFiles = [];
    
    CONFIG.fileExtensions.forEach(pattern => {
      const files = glob.sync(pattern, {
        ignore: CONFIG.excludePatterns,
        nodir: true
      });
      allFiles.push(...files);
    });

    const uniqueFiles = [...new Set(allFiles)];
    console.log(`📁 找到 ${uniqueFiles.length} 个文件需要处理`);
    return uniqueFiles;
  }

  // 创建动态路径模式
  createDynamicPatterns() {
    // 从API映射中提取路径模式
    const erpPaths = Object.keys(this.apiMapping);
    const pathPatterns = new Set();
    
    erpPaths.forEach(path => {
      // 提取 /erp/hxl.erp.xxx 中的 xxx 部分
      const match = path.match(/^\/erp\/hxl\.erp\.(.+)$/);
      if (match) {
        pathPatterns.add(match[1]);
      }
    });

    return [
      // 模式1: `/${actionModule}/hxl.${actionModule}.xxx`
      {
        name: 'actionModule模式',
        regex: /(`|\"|')\/\$\{([^}]+)\}\/hxl\.\$\{\2\}\.([^`"'?]+)(\?[^`"']*)?(`|\"|')/g,
        replacement: (match, quote1, variable, pathSuffix, query, quote2) => {
          const fullPath = `hxl.erp.${pathSuffix}`;
          if (pathPatterns.has(fullPath)) {
            const queryPart = query || '';
            return `${quote1}\${${variable} === 'erp' ? \`/erp-mdm/hxl.\${${variable}}.${pathSuffix}${queryPart}\` : \`/\${${variable}}/hxl.\${${variable}}.${pathSuffix}${queryPart}\`}${quote2}`;
          }
          return match;
        }
      },
      
      // 模式2: `/${variable}/hxl.${variable}.xxx` (不限于actionModule)
      {
        name: '通用变量模式',
        regex: /(`|\"|')\/\$\{([^}]+)\}\/hxl\.\$\{\2\}\.([^`"'?]+)(\?[^`"']*)?(`|\"|')/g,
        replacement: (match, quote1, variable, pathSuffix, query, quote2) => {
          const fullPath = `hxl.erp.${pathSuffix}`;
          if (pathPatterns.has(fullPath)) {
            const queryPart = query || '';
            return `${quote1}\${${variable} === 'erp' ? \`/erp-mdm/hxl.\${${variable}}.${pathSuffix}${queryPart}\` : \`/\${${variable}}/hxl.\${${variable}}.${pathSuffix}${queryPart}\`}${quote2}`;
          }
          return match;
        }
      },
      
      // 模式3: 字符串拼接形式 variable + '/hxl.' + variable + '.xxx'
      {
        name: '字符串拼接模式',
        regex: /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\+\s*['"`]\/hxl\.\1\.([^'"`?]+)(\?[^'"`]*)?['"`]/g,
        replacement: (match, variable, pathSuffix, query) => {
          const fullPath = `hxl.erp.${pathSuffix}`;
          if (pathPatterns.has(fullPath)) {
            const queryPart = query || '';
            return `(${variable} === 'erp' ? '/erp-mdm/hxl.' + ${variable} + '.${pathSuffix}${queryPart}' : '/' + ${variable} + '/hxl.' + ${variable} + '.${pathSuffix}${queryPart}')`;
          }
          return match;
        }
      },
      
      // 模式4: 函数调用形式 buildPath(module, 'xxx')
      {
        name: '函数调用模式',
        regex: /buildPath\s*\(\s*([^,]+)\s*,\s*['"`]([^'"`]+)['"`]\s*\)/g,
        replacement: (match, variable, pathSuffix) => {
          const fullPath = `hxl.erp.${pathSuffix}`;
          if (pathPatterns.has(fullPath)) {
            return `buildPath(${variable} === 'erp' ? 'erp-mdm' : ${variable}, '${pathSuffix}')`;
          }
          return match;
        }
      }
    ];
  }

  // 处理单个文件
  processFile(filePath) {
    try {
      const originalContent = fs.readFileSync(filePath, 'utf8');
      let modifiedContent = originalContent;
      let fileReplacements = [];
      let hasChanges = false;

      const patterns = this.createDynamicPatterns();
      
      patterns.forEach(pattern => {
        const matches = [...modifiedContent.matchAll(pattern.regex)];
        
        matches.forEach(match => {
          const replacement = pattern.replacement(...match);
          
          if (replacement && replacement !== match[0]) {
            modifiedContent = modifiedContent.replace(match[0], replacement);
            hasChanges = true;
            
            fileReplacements.push({
              pattern: pattern.name,
              original: match[0],
              replacement: replacement,
              line: this.getLineNumber(originalContent, match.index)
            });
          }
        });
      });

      // 如果有修改，写入文件
      if (hasChanges) {
        fs.writeFileSync(filePath, modifiedContent, 'utf8');
        this.dynamicLog.summary.modifiedFiles++;
        this.dynamicLog.summary.totalReplacements += fileReplacements.length;
        
        this.dynamicLog.fileDetails.push({
          filePath,
          replacementCount: fileReplacements.length,
          replacements: fileReplacements
        });
        
        console.log(`✅ ${filePath}: ${fileReplacements.length} 个动态路径替换`);
      }

      this.dynamicLog.summary.totalFiles++;
      return hasChanges;
      
    } catch (error) {
      const errorMsg = `处理文件 ${filePath} 时出错: ${error.message}`;
      console.error(`❌ ${errorMsg}`);
      this.dynamicLog.errors.push(errorMsg);
      return false;
    }
  }

  // 获取行号
  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  // 主处理函数
  async run() {
    console.log('🔄 开始动态路径处理任务...\n');
    
    // 加载API映射
    this.loadApiMapping();
    
    // 获取所有文件
    const allFiles = this.getAllFiles();
    
    console.log('🔄 开始处理动态路径...\n');
    
    // 处理每个文件
    allFiles.forEach(file => {
      this.processFile(file);
    });
    
    // 完成处理
    this.dynamicLog.summary.endTime = new Date().toISOString();
    
    // 保存日志
    fs.writeFileSync(CONFIG.outputLogFile, JSON.stringify(this.dynamicLog, null, 2));
    
    // 输出总结
    this.printSummary();
  }

  // 打印总结
  printSummary() {
    const { summary } = this.dynamicLog;
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 动态路径处理完成总结');
    console.log('='.repeat(60));
    console.log(`📁 总文件数: ${summary.totalFiles}`);
    console.log(`✏️  修改文件数: ${summary.modifiedFiles}`);
    console.log(`🔄 总替换次数: ${summary.totalReplacements}`);
    console.log(`⏱️  开始时间: ${summary.startTime}`);
    console.log(`⏱️  结束时间: ${summary.endTime}`);
    console.log(`❌ 错误数量: ${this.dynamicLog.errors.length}`);
    console.log(`📄 详细日志: ${CONFIG.outputLogFile}`);
    console.log('='.repeat(60));
    
    if (this.dynamicLog.errors.length > 0) {
      console.log('\n❌ 错误列表:');
      this.dynamicLog.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    if (summary.modifiedFiles > 0) {
      console.log('\n✅ 修改的文件:');
      this.dynamicLog.fileDetails.forEach(detail => {
        console.log(`  📄 ${detail.filePath} (${detail.replacementCount} 个替换)`);
        detail.replacements.forEach(replacement => {
          console.log(`    - 行${replacement.line}: ${replacement.pattern}`);
          console.log(`      原: ${replacement.original}`);
          console.log(`      新: ${replacement.replacement}`);
        });
      });
    }
  }
}

// 运行脚本
if (require.main === module) {
  const handler = new DynamicPathHandler();
  handler.run().catch(error => {
    console.error('❌ 动态路径处理脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = DynamicPathHandler;
