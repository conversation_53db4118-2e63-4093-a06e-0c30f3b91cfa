import axios, { AxiosError } from 'axios'
import { enhance } from 'foca-axios'
import dayjs from 'dayjs'
import { getToken, saveToken, signOut } from '../auth'
import XLBStorage from '../../utils/storage'
import { USER_ACCOUNT, USER_LICENSE, USER_PASSWORD } from '../../config/constant'
import XLBBaseApi from '@xlb/business-base/src/api'
// import { Toast } from 'native-base'
import Config from 'react-native-config'
import { authModel } from '@xlb/business-base/src/models/auth'
import { DeviceEventEmitter } from 'react-native'

import { Toast as ToastXS } from '@fruits-chain/react-native-xiaoshu'
import { cloneDeep } from 'lodash'

let isRefresh = false
let retryList: any[] = []

const green = '\u001b[32m'
const yellow = '\u001b[33m'
const magenta = '\u001b[35m'
const cyan = '\u001b[36m'
const white = '\u001b[37m'
const reset = '\u001b[0m'

const instance = axios.create()

instance.interceptors.request.use(
  // 动态获取baseUrl 便于切换环境
  async (config) => {
    // const accessToken = config.url === '/login/login' ? null : await refreshToken();
    // console.info(cyan + '| ----------------- fetcher loging ----------------- ');
    // console.info(cyan + '| fetcher      | ' + magenta + 'requestFetcher');
    // console.info(cyan + '| endpoint     | ' + yellow + `${config.url}`);
    // console.info(cyan + '| body         | ' + white, config.data);
    // console.info(cyan + '| Bearer token | ' + accessToken);
    // console.info(cyan + '| -------------------------------------------------- ' + reset);
    //循环遍历&替换参数
    let RequestBody: any = []
    for (let key in config.data) {
      let encodedKey = encodeURIComponent(key)
      let encodedValue = encodeURIComponent(config.data[key])
      //requestBody.append(encodedKey, encodedValue);
      RequestBody.push(encodedKey + '=' + encodedValue)
    }
    config.data = RequestBody.join('&')
    // config.baseURL = await XLBGlobalStorage.getItem(BASE_URL);
    // config.baseURL = Config.BASE_URL;
    const prefixList: any = {
      4754: 'https://api.xlbsoft.com',
      66666: 'https://ws-api.xlbsoft.com',
    }
    const userInfos: any = authModel.state.userInfos
    // console.log('userInfos', userInfos.id)
    const url = prefixList[userInfos.company_id]
    if (url) {
      config.baseURL = url
    } else {
      config.baseURL = 'https://cs-api.xlbsoft.com'
    }
    config.headers = {
      'Content-type': 'application/x-www-form-urlencoded',
      Authorization: `Bearer ${authModel?.state?.userInfos?.old_access_token}`,
      'Company-Id': authModel?.state?.userInfos?.company_id || '',
      'User-Id': authModel?.state?.userInfos?.id,
      'Org-Ids': authModel?.state.userInfos?.org_ids ? authModel?.state.userInfos?.org_ids.join(',') : '',
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

instance.interceptors.response.use(
  (response) => {
    if (response.data.code !== '0') {
      // Toast.show({ title: response.data.msg })
      if (response.status == 401) {
        ToastXS.fail('用户暂无该接口权限\n请联系信息部处理')
      } else {
        if ((response.data.msg === '登录已过期，请重新登录' || response.data?.code === 1005) && !response.config.url?.includes('usernotice.page')) {
          DeviceEventEmitter.emit('logOut')

          // if (!isRefresh) {
          //   isRefresh = true
          //   return instance
          //     .post('/erp/hxl.erp.user.token.refresh', {
          //       refresh_token: authModel.state.userInfos.refresh_token,
          //       company_id: authModel.state.userInfos.company_id,
          //     })
          //     .then((res) => {
          //       if (res?.code === 0) {
          //         const { access_token, refresh_token } = res.data
          //         authModel.setUserInfos({ ...cloneDeep(authModel.state.userInfos), access_token, token: access_token, refresh_token })
          //         XLBStorage.setItem('loginInfo', { loginTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), refresh_token })

          //         // token 刷新后将数组的方法重新执行
          //         retryList.forEach((cb) => {
          //           console.log(138, cb)

          //           cb()
          //         })
          //         retryList = [] // 重新请求完清空
          //         return instance({ ...response.config, isHiddenMsg: true, data: JSON.parse(response.config.data) }) // 第一次401进入的接口重试
          //       } else {
          //         DeviceEventEmitter.emit('logOut')
          //         retryList = []
          //       }
          //     })
          //     .catch((err) => {
          //       console.log('抱歉，您的登录状态已失效，请重新登录！')
          //       return Promise.reject(err)
          //     })
          //     .finally(() => {
          //       isRefresh = false
          //     })
          // } else {
          //   // 返回未执行 resolve 的 Promise
          //   return new Promise((resolve) => {
          //     // 用函数形式将 resolve 存入，等待刷新后再执行
          //     retryList.push(() => {
          //       resolve(instance({ ...response.config, isHiddenMsg: true, data: JSON.parse(response.config.data) }))
          //     })
          //   })
          // }
        }
        if (response.data.msg) {
          ToastXS.fail(response.data.msg)
        }
      }
    }
    return response.config.url === '/login/login' ? response : response.data
  },
  (err: AxiosError) => {
    // Toast.show({ title: err.message })
    return Promise.reject(err)
  }
)

export const XLBHttp = enhance(instance, {
  cache: {
    enable: false,
  },
  throttle: {
    enable: true,
  },
  retry: {
    enable: true,
  },
})

async function refreshToken() {
  const token = await getToken()
  let accessToken = token.accessToken
  if (!token || !token?.accessToken) {
    await signOut()
    return ''
  }
  // 判断当前日期是否晚于tokenExpireTime，如果是表示token已经过期，根据持久化的数据去调用登录接口返回token
  if (dayjs().isAfter(dayjs.unix(<number>token.time))) {
    const license = await XLBStorage.getItem(USER_LICENSE)
    const account = await XLBStorage.getItem(USER_ACCOUNT)
    const password = await XLBStorage.getItem(USER_PASSWORD)
    const response: VerifyPasswordSuccessPayload = await XLBHttp.post('/login/login', {
      license: license,
      account: account,
      pwd: password,
      from: 'app',
    })
    //登录成功，将token放置缓存中便于查询
    const tokenInfo: Token = {}
    tokenInfo.accessToken = response.access_token
    tokenInfo.userId = response.data._u_id
    tokenInfo.time = response.data._time
    tokenInfo.xlbToken = response.data._token
    tokenInfo.username = response.data.name
    saveToken(tokenInfo)
    return XLBBaseApi.Base.getUserInfo()
  }
  return accessToken
}
