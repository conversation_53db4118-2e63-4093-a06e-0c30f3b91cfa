import { <PERSON>Footer, ImagesModal } from '@dplus/images'
import React, { useState } from 'react'
import { View, StyleSheet, Linking, ScrollView, Dimensions, Text, Image } from 'react-native'
import ProText from '../ProText/ProText'
import Show from '../Show/Show'
import cs from '../cs'
import Row from '../Row/Row'
import Flex from '../Flex/Flex'
import lodash from 'lodash'
import ProModal from '../ProModal'
import WebView from 'react-native-webview'
import { Button } from '@dplus/rn-ui'
import Space from '../Space/Space'
import { IconNewArrowDown, IconNewDel } from '../../assets/iconfont'
import FastImage from 'react-native-fast-image'
import PDF from 'react-native-pdf'
import { decodeFileName, isEncodedFileName } from '../../utils/xlbUpload'

interface FilesProps {
  data: string[]
  title?: string
  nameComponent?: (index: number) => React.ReactNode
  onDelete?: (index: number, v?: any) => void
  containerStyle?: any
  fileList?: { name: string; url: string }[]

  /** 是否折叠 */
  foldable?: boolean
  /** 折叠起来的个数 */
  foldableNum?: number
}
type FileType = 'word' | 'pdf' | 'ppt' | 'image' | 'excel' | 'audio' | 'video' | 'default'

// 图标路径映射
const fileIcons: Record<FileType, string> = {
  word: require('./fileEnum/word.png'),
  pdf: require('./fileEnum/pdf.png'),
  ppt: require('./fileEnum/ppt.png'),
  image: require('./fileEnum/image.png'),
  excel: require('./fileEnum/excel.png'),
  audio: require('./fileEnum/audio.png'),
  video: require('./fileEnum/video.png'),
  default: require('./fileEnum/default.png'),
}

// 文件后缀分类
const fileTypes: Record<FileType, string[]> = {
  word: ['doc', 'docx'],
  pdf: ['pdf'],
  ppt: ['ppt', 'pptx'],
  image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'ico'],
  excel: ['xls', 'xlsx', 'csv'],
  audio: ['mp3', 'wav', 'aac', 'ogg', 'flac', 'm4a'],
  video: ['mp4', 'mkv', 'avi', 'mov', 'flv', 'wmv', 'mpeg', '3gp'],
  default: [], // 未知类型
}

/**
 * 获取文件后缀对应的图标路径
 * @param filePath 文件路径
 * @returns 对应图标的路径
 */
function getFileIconPath(filePath: string): any {
  // 提取文件后缀
  const extension = filePath.split('.').pop()?.toLowerCase() || ''

  // 查找匹配的文件类型
  for (const [type, extensions] of Object.entries(fileTypes)) {
    if (extensions.includes(extension)) {
      return fileIcons[type as FileType] // 返回对应图标路径
    }
  }

  return fileIcons.default // 默认图标路径
}

const Files: React.FC<FilesProps> = ({ data, title, onDelete, nameComponent, containerStyle, fileList, foldable = false, foldableNum = 3 }) => {


  const [imagesModal, setImagesModal] = useState({
    visible: false,
    imageIndex: 0,
  })
  const [url, setUrl] = useState('')
  const [pdfUrl, setPdfUrl] = useState('')

  const [isFolded, setIsFolded] = useState(true)

  const fileExtensions = lodash.map(data, (url) => {
    return lodash.last(lodash.split(url, '.'))?.toUpperCase()
  })

  const isImage = (v: string) => ['.jpg', '.jpeg', '.png', '.bmp', '.JPG', '.JPEG', '.PNG', '.BMP'].some((img) => v?.includes(img))
  // console.log(data, "allImages---->");

  const allImages = data?.filter((v) => isImage(v)) ?? []

  const handleShowFile = (v: string) => {
    if (v.includes('.mp4')) {
      setUrl(v)
    } else if (v.includes('.pdf')) {
      // 神特喵，路径里带中文。坑啊
      setPdfUrl(encodeURI(v))
    } else if (!isImage(v)) {
      Linking.openURL(v)
    } else {
      setImagesModal({ imageIndex: allImages.indexOf(v), visible: true })
    }
  }

  const getFileName = (name: string, index: number) => {
    if (fileList && fileList.length) {
      if (fileList[index].decodeName) return fileList[index].decodeName
      if (fileList[index].name && isEncodedFileName(fileList[index].name)) return decodeFileName(fileList[index].name)
      return fileList[index].name
    } else {
      return name.substring(name.length - 20, name.length)
    }
  }

  // ? 后端返回的fileList不能保证顺序和data里面的问题一致，所以要处理一下

  const curData = fileList?.length ? fileList.map((v) => v?.url) : data

  const list = curData?.filter((v) => !!v) ?? []

  const curList = foldable && isFolded ? list.slice(0, foldableNum) : list

  console.log(curList, 'curList11232323232323', fileList, 'fileListggggggg')

  return (
    <Show show={!!data}>
      <ImagesModal
        images={allImages.map((uri: string) => {
          return {
            uri,
          }
        })}
        imageIndex={imagesModal.imageIndex}
        visible={imagesModal.visible}
        onRequestClose={() => setImagesModal({ ...imagesModal, visible: false })}
        FooterComponent={({ imageIndex }) => <ImageFooter imageIndex={imageIndex} imagesCount={allImages.length} />}
      />

      <Show show={!!title && data.length > 0}>
        <ProText style={[cs.mt20, cs.ml20, cs.second, cs.fz12, { marginTop: 14 }]}>附件信息</ProText>
      </Show>
      <ScrollView showsVerticalScrollIndicator={false}>
        {curList
          // ?.filter((v) => !!v)
          .map((v: string, index: number) => {
            // console.log(fileList, 'fileList===>')

            return (
              <Row
                onPress={() => handleShowFile(v)}
                key={index}
                style={StyleSheet.flatten([styles.container, { marginTop: index === 0 ? 0 : 8 }, containerStyle])}
              >
                <View style={styles.box}>
                  <FastImage style={styles.imageBox} source={getFileIconPath(v)} />
                </View>

                <Space onPress={() => handleShowFile(v)} style={{ ...cs.f1, alignItems: 'center' }}>
                  {nameComponent ? nameComponent(index) : <ProText style={styles.name}>{getFileName(v, index)}</ProText>}

                  <Show show={!!onDelete}>
                    <IconNewDel
                      onPress={() => {
                        onDelete?.(index, v)
                      }}
                      size={18}
                    />
                  </Show>
                </Space>
              </Row>
            )
          })}
        <Row style={{ marginTop: 12 }}>
          <Show show={foldable && list.length > foldableNum && isFolded}>
            <ProText onPress={() => setIsFolded(false)} style={{ color: 'rgba(26, 106, 255, 1)', fontSize: 13, marginRight: 8 }}>
              展开其余{list?.length - foldableNum}个附件
            </ProText>
            <IconNewArrowDown onPress={() => setIsFolded(false)} size={14} color={'rgba(26, 106, 255, 1)'} />
          </Show>
          <Show show={foldable && list.length > foldableNum && !isFolded}>
            <ProText onPress={() => setIsFolded(true)} style={{ color: 'rgba(26, 106, 255, 1)', fontSize: 13, marginRight: 8 }}>
              收起
            </ProText>
          </Show>
        </Row>
      </ScrollView>

      <ProModal statusBarTranslucent visible={!!pdfUrl} animationType="slide">
        <Flex>
          <View style={styles.pdf}>
            <PDF trustAllCerts={false} source={{ uri: pdfUrl, cache: false }} style={styles.pdf} />
          </View>
          <Button title="关闭" onPress={() => setPdfUrl('')} />
        </Flex>
      </ProModal>

      <ProModal statusBarTranslucent visible={!!url} animationType="slide">
        <Flex>
          <WebView source={{ uri: url }} />
          <Button title="关闭" onPress={() => setUrl('')} />
        </Flex>
      </ProModal>
    </Show>
  )
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 4,
    backgroundColor: '#F2F3F5',
    paddingHorizontal: 8,
    paddingVertical: 8,
    alignItems: 'center',
    height: 44,
    marginTop: 8,
  },
  name: {
    color: '#26272A',
  },
  box: {
    marginRight: 8,
    height: 28,
    width: 28,
  },
  nameBox: {
    backgroundColor: '#F53F3F',
    flex: 1,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageBox: {
    height: 28,
    width: 28,
  },
  pdf: {
    flex: 1,
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height - 100,
  },
})

Files.displayName = 'Files'

export default Files
