{"name": "host-app-xlb", "version": "0.0.1", "private": true, "scripts": {"win:android": "copy .env.dev .env & react-native run-android", "android": "sh scripts/run.android.sh", "android:build": "sh scripts/build.android.sh", "android:install": "sh scripts/install.apk.sh", "ios": "sh scripts/run.ios.sh", "ios:publish": "sh scripts/publish.ios.sh", "lint": "eslint .", "start": "react-native webpack-start --config rspack.config.mjs --port=8081", "bundle:android": "react-native webpack-bundle --config rspack.config.mjs --platform android --entry-file index.js --dev=false", "bundle:ios": "react-native webpack-bundle --config rspack.config.mjs --platform ios --entry-file index.js --dev=false", "test": "jest", "connect": "adb reverse tcp:8081 tcp:8081 & adb reverse tcp:9090 tcp:9090"}, "dependencies": {"@ant-design/icons-react-native": "2.3.2", "@ant-design/react-native": "5.4.2", "@babel/plugin-syntax-typescript": "^7.27.1", "@dplus/images": "0.5.0", "@dplus/rn-tree": "0.20.0", "@dplus/rn-ui": "0.1.2", "@fruits-chain/react-native-xiaoshu": "0.4.6", "@gluestack-ui/icon": "0.1.27", "@gluestack-ui/nativewind-utils": "1.0.26", "@gluestack-ui/overlay": "0.1.22", "@gluestack-ui/toast": "1.0.9", "@module-federation/enhanced": "0.17.0", "@module-federation/runtime-core": "0.17.0", "@react-native-async-storage/async-storage": "2.2.0", "@react-native-camera-roll/camera-roll": "^7.10.1", "@react-native-clipboard/clipboard": "1.16.3", "@react-native-community/netinfo": "11.4.1", "@react-native-community/slider": "5.0.0", "@react-native-documents/picker": "10.1.5", "@react-native/babel-plugin-codegen": "^0.80.1", "@react-navigation/bottom-tabs": "7.3.14", "@react-navigation/drawer": "7.5.3", "@react-navigation/native": "7.1.14", "@react-navigation/native-stack": "7.3.21", "@react-navigation/stack": "7.4.2", "@rsdoctor/rspack-plugin": "^1.1.8", "@rspack/plugin-react-refresh": "1.4.3", "@sentry/react-native": "6.17.0", "@shopify/flash-list": "1.8.3", "@types/crypto-js": "4.2.2", "@types/lodash": "4.17.17", "@xlb/components-rn": "0.0.88-beta.86", "@xlb/react-native-amap3d": "0.0.39", "ahooks": "3.9.0", "aliyun-react-native-push": "1.0.0-beta.1", "axios": "1.10.0", "babel-plugin-syntax-hermes-parser": "^0.29.1", "crypto-js": "4.2.0", "dayjs": "^1.11.13", "expo": "53.0.19", "expo-modules-core": "2.4.2", "fbjs": "^3.0.5", "foca": "4.0.1", "foca-axios": "4.3.0", "gluestack": "0.1.1", "lodash": "4.17.21", "lodash-es": "4.17.21", "native-base": "3.4.28", "nativewind": "4.1.23", "number-precision": "1.6.0", "prop-types": "^15.8.1", "react": "19.1.0", "react-dom": "19.1.0", "react-error-boundary": "6.0.0", "react-hook-form": "7.60.0", "react-is": "19.1.0", "react-native": "0.80.1", "react-native-animated-numbers": "0.6.3", "react-native-appstate-hook": "1.0.6", "react-native-audio-record": "0.2.2", "react-native-ble-plx": "^3.5.0", "react-native-blob-util": "^0.22.2", "react-native-bluetooth-classic": "1.73.0-rc.13", "react-native-camera": "4.2.1", "react-native-common-date-picker": "2.3.9", "react-native-config": "1.5.5", "react-native-create-thumbnail": "^2.1.1", "react-native-css-interop": "0.1.22", "react-native-date-picker": "5.0.13", "react-native-device-info": "^14.0.4", "react-native-draggable-flatlist": "4.0.3", "react-native-draggable-grid": "2.2.2", "react-native-draglist": "3.9.6", "react-native-echarts-pro": "1.9.3", "react-native-element-dropdown": "2.12.4", "react-native-error-boundary": "2.0.0", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "2.27.1", "react-native-image-crop-picker": "^0.50.1", "react-native-image-marker": "1.2.6", "react-native-image-zoom-viewer": "3.0.1", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "14.0.0-rc.1", "react-native-orientation-locker": "1.7.0", "react-native-pager-view": "6.8.1", "react-native-pdf": "6.7.7", "react-native-pdf-renderer": "^2.2.1", "react-native-permissions": "^5.4.1", "react-native-qrcode-svg": "6.3.15", "react-native-reanimated": "3.18.0", "react-native-root-siblings": "5.0.1", "react-native-root-toast": "4.0.1", "react-native-safe-area-context": "5.5.2", "react-native-screens": "4.11.1", "react-native-shadow-2": "7.1.1", "react-native-signature-pad": "0.1.2", "react-native-size-matters": "0.4.2", "react-native-sound": "^0.11.2", "react-native-splash-screen": "3.3.0", "react-native-svg": "^15.12.0", "react-native-swipe-list-view": "3.2.9", "react-native-tab-view": "4.1.2", "react-native-test-app": "4.4.0", "react-native-update": "10.29.2", "react-native-vector-icons": "^10.2.0", "react-native-video": "6.16.1", "react-native-vision-camera": "^4.7.0", "react-native-walkthrough-tooltip": "1.6.0", "react-native-webview": "^13.15.0", "tailwindcss": "3.4.17", "xlb-reactnative-amap-geolocation": "1.0.5", "zustand": "5.0.5"}, "devDependencies": {"@babel/core": "7.28.0", "@babel/preset-env": "7.28.0", "@babel/runtime": "7.27.6", "@callstack/repack": "5.1.3", "@callstack/repack-plugin-reanimated": "5.1.3", "@react-native-community/cli": "19.1.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.1", "@react-native/codegen": "0.80.1", "@react-native/eslint-config": "0.80.1", "@react-native/gradle-plugin": "0.80.1", "@react-native/js-polyfills": "0.80.1", "@react-native/metro-config": "0.80.1", "@react-native/typescript-config": "0.80.1", "@rspack/core": "1.4.6", "@swc/helpers": "0.5.17", "@types/jest": "30.0.0", "@types/react": "19.1.8", "@types/react-test-renderer": "19.1.0", "babel-loader": "10.0.0", "babel-plugin-module-resolver": "5.0.2", "eslint": "9.31.0", "glob": "^11.0.3", "jest": "30.0.4", "jscodeshift": "17.3.0", "prettier": "3.6.2", "react-devtools-core": "6.1.5", "react-test-renderer": "19.1.0", "reactotron-react-native": "^5.1.14", "terser-webpack-plugin": "5.3.14", "typescript": "5.8.3", "webpack": "5.100.1"}, "engines": {"node": ">=24"}, "pnpm": {"patchedDependencies": {"@module-federation/webpack-bundler-runtime@0.17.0": "patches/@<EMAIL>", "@module-federation/runtime-core@0.17.0": "patches/@<EMAIL>", "@callstack/repack": "patches/@callstack__repack.patch", "react-devtools-core": "patches/react-devtools-core.patch", "react-native-camera": "patches/react-native-camera.patch", "react-native": "patches/react-native.patch", "native-base@3.4.28": "patches/<EMAIL>", "@react-native/js-polyfills@0.80.1": "patches/@<EMAIL>", "react-native-sound": "patches/react-native-sound.patch", "foca": "patches/foca.patch", "xlb-reactnative-amap-geolocation@1.0.5": "patches/<EMAIL>", "react-native-image-marker": "patches/react-native-image-marker.patch", "rn-placeholder": "patches/rn-placeholder.patch", "@xlb/react-native-amap3d": "patches/@xlb__react-native-amap3d.patch", "react-native-date-picker@5.0.13": "patches/<EMAIL>", "react-native-pdf": "patches/react-native-pdf.patch", "react-native-screens": "patches/react-native-screens.patch", "@react-navigation/routers": "patches/@react-navigation__routers.patch"}}}