import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import LinearGradient from 'react-native-linear-gradient'

// 渐变色进度条组件
const ProgressBar = ({ label, progress=0, colors = ['rgba(255, 111, 0, 0.1) 0%', 'rgba(255, 111, 0, 0.6) 100%'], rankNum, needPercent = true }: any) => {
  return (
    <View style={styles.container}>
      {
        rankNum ?
          <Text
            style={styles.rankNums}
          >
            {rankNum + 1}
          </Text> : <></>
      }


      <View style={styles.progressContainer}>
        <View style={styles.labelContainer}>
          <Text style={styles.label}>{label}</Text>
          <Text style={styles.percentage}>{progress}{needPercent ? '%': ''}</Text>
        </View>

        <View style={styles.progressBackground}>
          <LinearGradient colors={colors} style={[styles.progressFill, { width: `${progress}%` }]} start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} />
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center'
  },
  rankNums: {
    marginTop: 8,
    marginRight: 12,
    backgroundColor: 'rgba(30, 33, 38, 0.04)',
    width: 20,
    height: 20,
    borderRadius: 10,
    textAlign: 'center',
    fontWeight: '700',
    fontSize: 12,
    lineHeight: 20,
    color: 'black'
  },
  progressContainer: {
    flex: 1,
    marginBottom: 14
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 3,
  },
  label: {
    fontSize: 14,
    color: '#1D2129',
    fontWeight: '600',
    marginBottom: 5
  },
  percentage: {
    fontSize: 14,
    color: '#1D2129',
    fontWeight: '700',
  },
  progressBackground: {
    height: 8,
    backgroundColor: 'white',
    borderRadius: 10,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 10,
  },
  countContainer: {
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  countText: {
    fontSize: 12,
    color: '#888',
  },
})

export default ProgressBar;
