import {getToken} from '../services/auth';
import {Toast} from 'native-base';
import Config from 'react-native-config';
import {authModel} from '../../../business-base/src/models/auth';
import {Platform, ToastAndroid} from 'react-native';

import useSystemConfig from '@xlb/common/src/models/system';
import define from '@xlb/common/src/utils/const';

export default class XLBUpload {
  /**
   * 上传图片
   * @param image
   */
  static async uploadImage(image: any, id: number) {
    const formData = new FormData();
    formData.append('file', {
      uri: image.path,
      name: image.filename || 'xlb_default.jpg',
      type: 'multipart/form-data',
    } as any);
    formData.append('refType', 'ITEM_PICTURE');
    formData.append('refId', id);

    // 调接口 上传
    const requestUrl =
      define[authModel.state.currentEnv] + '/erp/hxl.erp.file.upload';

    return new Promise((resolve, reject) => {
      fetch(requestUrl, {
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
          'Access-Token': `${authModel?.state?.userInfos?.access_token}`,
          'Api-Version': '1.5.0',
        },
        body: formData,
      })
        .then(res => res.json())
        .then(result => {
          if (result.code === 0) {
            resolve(result.data.url);
          } else {
            if (Platform.OS === 'android') {
              ToastAndroid.show(result.msg, ToastAndroid.LONG);
            }
            reject(result.msg);
          }
        })
        .catch(error => {
          Toast.show({title: error});
          reject(error);
        });
    });
  }
  static async uploadCenterFile(image: any) {
    const formData = new FormData();
    formData.append('file', {
      uri: image.path,
      name: image.filename || 'xlb_default.jpg',
      type: 'multipart/form-data',
    } as any);

    const url = '/oa/hxl.oa.file.upload';
    const urlSplit = url?.split('/')?.filter(item => !!item) || [];
    const urlEnvName = ['ENV', 'DEVELOP', 'TEST'].includes(Config.ENV || '')
      ? 'test'
      : Config.BRANCH_NAME;
    const base_url = `https://react-web.react-web.ali-${urlEnvName}.xlbsoft.com/`;

    // 调接口 上传
    const requestUrl = base_url.replace(/\/$/, '') + url;
    return new Promise((resolve, reject) => {
      fetch(requestUrl, {
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
          'Access-Token': `${authModel?.state?.userInfos?.access_token}`,
          'Api-Version': '1.5.0',
        },
        body: formData,
      })
        .then(res => res.json())
        .then(result => {
          if (result.code === 0) {
            resolve({
              url: result?.data?.url,
              name: decodeURIComponent(result?.data?.name),
            });
          } else {
            if (Platform.OS === 'android') {
              ToastAndroid.show(result.msg, ToastAndroid.LONG);
            }
            reject(result.msg);
          }
        })
        .catch(error => {
          Toast.show({title: error});
          reject(error);
        });
    });
  }

  static async uploadCenterFileOther(file: any) {
    const formData = new FormData();

    const filePath = file.uri || file.path;
    const fileName = file.name;

    formData.append('file', {
      uri: filePath,
      name: encodeURIComponent(fileName),
      type: 'multipart/form-data',
    } as any);

    const url = '/oa/hxl.oa.file.upload';
    const urlSplit = url?.split('/')?.filter(item => !!item) || [];
    const urlEnvName = ['ENV', 'DEVELOP', 'TEST'].includes(Config.ENV || '')
      ? 'test'
      : Config.BRANCH_NAME;
    const base_url = `https://react-web.react-web.ali-${urlEnvName}.xlbsoft.com/`;

    // 调接口 上传
    const requestUrl = base_url.replace(/\/$/, '') + url;
    return new Promise((resolve, reject) => {
      fetch(requestUrl, {
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
          'Access-Token': `${authModel?.state?.userInfos?.access_token}`,
          'Api-Version': '1.5.0',
        },
        body: formData,
      })
        .then(res => res.json())
        .then(result => {
          if (result.code === 0) {
            resolve({
              url: result.data.url,
              name: decodeURIComponent(result.data.name),
            });
          } else {
            if (Platform.OS === 'android') {
              ToastAndroid.show(result.msg, ToastAndroid.LONG);
            }
            reject(result.msg);
          }
        })
        .catch(error => {
          Toast.show({title: error});
          reject(error);
        });
    });
  }
}
