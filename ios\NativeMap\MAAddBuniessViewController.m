//
//  MAAddBuniessViewController.m
//  xlb
//
//  Created by 莱昂纳多·迪卡普里奥 on 2025/1/3.
//

#import "MAAddBuniessViewController.h"
#import "UIView+Toast.h"
#import "UIView+Common.h"
#import "NSDictionary+Common.h"
#import "NSArray+Common.h"
#import "UIImageView+WebCache.h"
#import "MALoadWaveView.h"
#import "MAHttpTool.h"
#import "UIButton+Common.h"
#import "MANativeAlert.h"
#import "MABottomSingleSelectAlert.h"
#import "MABottomMutilSelectAlert.h"
#import "MAUploadSingleImageView.h"
#import "ZKPhotoBrowser.h"
#import "MAUploadMutilImageView.h"
#import "MASingleDateView.h"
#import "UITextView+Common.h"
#import "UITextField+Common.h"
#import "Masonry.h"
#import "MAChooseCityView.h"
#import <AMapFoundationKit/AMapFoundationKit.h>
#import <AMapNaviKit/MAMapKit.h>
#import <AMapLocationKit/AMapLocationKit.h>
#import "MACreatBuniessPolygon.h"
#import "MACreatBuniessPolygonRender.h"
#import "MABuniessEditDrawViewController.h"
#import <UMAnalytics/MobClick.h>
#import "MATopToastView.h"
#import "MATopNotiflyView.h"
#import <AMapLocationKit/AMapLocationKit.h>
#import "MALookPointViewController.h"
#import "MAChooseLocationViewController.h"
#import "MACustomBuniessTableView.h"
#import "MAOpenStoreView.h"
#import "MAAddOpenStoreViewController.h"
#import "MAUserAnnotationView.h"
#import "MALocalAnnotation.h"
#import "MALocalAnnotationView.h"
#import "MAAddPointViewController.h"

// 判断字符串是否为空
#define BCStringIsEmpty(str) ([str isKindOfClass:[NSNull class]] || str == nil || ![str isKindOfClass:[NSString class]] || [str length]< 1 ? YES : NO || [str isEqualToString:@"null"] || [str isEqualToString:@"<null>"] || [str isEqualToString:@"(null)"])
// 判断数组是否为空
#define BCArrayIsEmpty(array) (array == nil || [array isKindOfClass:[NSNull class]] || ![array isKindOfClass:[NSArray class]] || [array count] == 0)
// 判断字典是否为空
#define BCDictIsEmpty(dic) (dic == nil || [dic isKindOfClass:[NSNull class]] || ![dic isKindOfClass:[NSDictionary class]] || dic.allKeys.count == 0)

#define BCWidth   [UIScreen mainScreen].bounds.size.width
#define BCHeight  [UIScreen mainScreen].bounds.size.height
#define COLOR(R, G, B) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:1]
#define ACOLOR(R, G, B,A) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:A]
#define WS(weakSelf)  __weak __typeof(self)weakSelf = self;

#define RealSize(value)  MAX(round(value * [UIScreen mainScreen].bounds.size.width / 400.0), value)
#define MutilFont(value)  [UIScreen mainScreen].bounds.size.width > 420 ? (value + 2) : value

#ifdef DEBUG
#define DDLog(format, ...) printf("class: <%p %s:(%d) > method: %s \n%s\n", self, [[[NSString stringWithUTF8String:__FILE__] lastPathComponent] UTF8String], __LINE__, __PRETTY_FUNCTION__, [[NSString stringWithFormat:(format), ##__VA_ARGS__] UTF8String] )
#else
#define DDLog(format, ...)
#endif

@interface MAAddBuniessViewController ()<UIScrollViewDelegate,UITextFieldDelegate,UITextViewDelegate,MAMapViewDelegate,CAAnimationDelegate>

@property (nonatomic, assign) CGFloat heightTop;
@property (nonatomic, strong) UIScrollView *homeScrollView;//首页scrollview
@property (nonatomic, strong) UIView *activeTV;
@property (nonatomic, assign) CGFloat mainHeight;//scrollview的高度
@property (nonatomic, strong) MALoadWaveView *loadingView;//加载
@property (nonatomic, strong) NSString *buniessId;//初始化商圈id
@property (nonatomic, strong) NSArray *itemChangeArr;//变动项数据源
@property (nonatomic, strong) UIView *topBackV;
@property (nonatomic, strong) UIScrollView *topTabV;//顶部选项卡
@property (nonatomic, strong) UIView *indicateLayer;//选项卡指示器
@property (nonatomic, strong) UIView *topLineV;
@property (nonatomic, strong) UIButton *selectTabButton;//控制单选tab按钮

@property (nonatomic, strong) UIView *draftView;//草稿
@property (nonatomic, strong) NSDictionary *draftData;//草稿数据源
@property (nonatomic, assign) BOOL showDraft;//是否启用草稿
@property (nonatomic, strong) NSDictionary *draftitemChangeDic;//草稿里的变动项数据源
@property (nonatomic, assign) BOOL isSaveDraft;//是否保存草稿

@property (nonatomic, strong) MABottomSingleSelectAlert *followAlert;//跟进人弹窗
@property (nonatomic, strong) NSDictionary *followDic;//单选弹窗数据

@property (nonatomic, strong) MAChooseCityView *areaAlert;//商圈区域弹窗
@property (nonatomic, strong) NSDictionary *areaDic;//区域数据

@property (nonatomic, strong) MABottomSingleSelectAlert *levelAlert;//商圈等级弹窗
@property (nonatomic, strong) NSDictionary *levelDic;//单选弹窗数据

@property (nonatomic, strong) MABottomSingleSelectAlert *mainLabelAlert;//商圈主标签弹窗
@property (nonatomic, strong) NSDictionary *mainLableDic;//单选弹窗数据

@property (nonatomic, strong) MABottomSingleSelectAlert *subLabelAlert;//商圈副标签弹窗
@property (nonatomic, strong) NSDictionary *subLableDic;//单选弹窗数据

@property (nonatomic, assign) BOOL isCLickScroll;//是否是点击tab
@property (nonatomic, strong) UIView *changeView;//变动项主视图

@property (nonatomic, strong) UIButton *submitButton;//提交按钮
@property (nonatomic, strong) CAShapeLayer *shapeLayer;
@property (nonatomic, strong) UIView *basicView;//基本信息主视图

@property (nonatomic, strong) UITextField *nameTF;//商圈名称
@property (nonatomic, strong) UITextField *openStoreTF;//可开门店数
@property (nonatomic, strong) UITextField *competitorTF;//同行品牌

@property (nonatomic, strong) MAUploadMutilImageView *heatUploadView;//商圈热力图
@property (nonatomic, assign) BOOL heatDisplay;//商圈热力图是否显示
@property (nonatomic, assign) BOOL heatRequired;//商圈热力图是否必填
@property (nonatomic, strong) NSArray *heatArr;//上传图片

@property (nonatomic, strong) MAUploadMutilImageView *surroundUploadView;//周边
@property (nonatomic, assign) BOOL surroundDisplay;//商圈环境是否显示
@property (nonatomic, assign) BOOL surroundRequired;//商圈环境是否必填
@property (nonatomic, strong) NSArray *surroundArr;//上传图片

@property (nonatomic, strong) MAMapView *mapView;//高德地图
@property (nonatomic, strong) MACreatBuniessPolygon *creatPolygon;//商圈多边形

@property (nonatomic, strong) NSMutableDictionary *requiredDic;//基本信息每一小项是否必填

@property (nonatomic, strong) MATopToastView *statusView;//加载
@property (nonatomic, strong) MATopNotiflyView *notiflyView;//加载

@property (nonatomic, strong) UIView *tipAnimationV;//提示动画
@property (nonatomic, strong) UIButton *followBtn;
@property (nonatomic, strong) UIButton *buniessAreaBtn;
@property (nonatomic, strong) UIButton *buniessLevelBtn;
@property (nonatomic, strong) UIButton *mainTypeBtn;
@property (nonatomic, strong) UIButton *subTypeBtn;

@property (nonatomic, strong) AMapLocationManager *locationManager;//定位
@property (nonatomic, strong) NSMutableArray *changeItemContions;//变动项条件数组
@property (nonatomic, strong) NSMutableDictionary *changeItemDictionary;//变动项条件值

@property (nonatomic, assign) BOOL buniessAreaDisplay;//商圈范围是否显示
@property (nonatomic, assign) BOOL buniessAreaRequired;//商圈范围是否必填

@property (nonatomic, assign) BOOL openStoreDisplay;//可开店区域是否显示
@property (nonatomic, assign) BOOL openStoreRequired;//可开店区域是否必填
@property (nonatomic, strong) UIView *openStoreView;//可开店区域view

@property (nonatomic, strong) MAOpenStoreView *openStoreListV;//可开店区域列表
@property (nonatomic, strong) NSMutableArray *openStoreList;//可开店区域列表数据源
@end

@implementation MAAddBuniessViewController

- (void)viewDidLoad {
  [super viewDidLoad];
  
  self.view.backgroundColor = COLOR(242, 243, 245);
  
  if (@available(iOS 13.0, *)) {
    self.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
  }
  
  _heightTop = 54;
  if (@available(iOS 13.0, *)) {
    NSSet *set = [UIApplication sharedApplication].connectedScenes;
    UIWindowScene *windowScene = [set anyObject];
    if (windowScene) {
      UIWindow *window = windowScene.windows.firstObject;
      
      if (window.safeAreaInsets.top > 0) {
        _heightTop = window.safeAreaInsets.top;
      } else {
        _heightTop = 54;
      }
    }
    
  } else {
    UIWindow *window = [[UIApplication sharedApplication].windows firstObject];
    if (window.safeAreaInsets.top > 0) {
      _heightTop = window.safeAreaInsets.top;
    } else {
      _heightTop = 54;
    }
  }
  
  self.requiredDic = [NSMutableDictionary dictionary];
  self.changeItemContions = [NSMutableArray array];
  self.changeItemDictionary = [NSMutableDictionary dictionary];
  self.openStoreList = [NSMutableArray array];
  
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillShow:) name:UIKeyboardWillShowNotification object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillHidden:) name:UIKeyboardWillHideNotification object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationWillEnterBackground) name:UIApplicationDidEnterBackgroundNotification object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(receiveEditDraw:) name:@"EDITDRAWBUNIESS" object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(receiveAddOpenStore:) name:@"ADDDRAWOPENSTORE" object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(receiveEditOpenStore:) name:@"EDITDRAWOPENSTORE" object:nil];
  
  //  界面ui
  [self initToolBar];
  
  [self loadMoreRequest];
  
  
}


// 获取当前第一响应者,uitextfield或者uitextview
- (UIResponder *)findFirstResponderInView:(UIView *)view {
  if (view.isFirstResponder) {
    return view;
  }
  for (UIView *subview in view.subviews) {
    UIResponder *responder = [self findFirstResponderInView:subview];
    if (responder) {
      return responder;
    }
  }
  return nil;
}

#pragma mark 收到编辑可开店区域
- (void)receiveEditOpenStore:(NSNotification *)notification{
  NSDictionary *pointDictionary = [notification userInfo];
  DDLog(@"收到数据%@",pointDictionary);
  NSInteger indexRow = [[pointDictionary objectNilForKey:@"index"] integerValue];
  if (self.openStoreDisplay) {
    [self.openStoreList replaceObjectAtIndex:indexRow withObject:[pointDictionary objectNilForKey:@"data"]];
    [self.openStoreListV reloadOpenView:[self.openStoreList mutableCopy] andBuniessState:@"制单"];
  }
  
  //    加载已经生成的可开店区域

  [self.mapView removeAnnotations:self.mapView.annotations];
  
  for (int i = 0; i < self.openStoreList.count; i ++) {
    NSDictionary *itemDic = [self.openStoreList objectAtIndexCheck:i];
    if (![[itemDic objectNilForKey:@"draw_type"] isEqualToString:@"DRAW_POINT"]) {
      continue;
    }
    
    NSArray *areaRange = [itemDic objectForKeyNil:@"area_range"];
    if (!BCArrayIsEmpty(areaRange)) {
      NSDictionary *dic = [areaRange firstObject];
      CLLocationCoordinate2D currentCoordinate = CLLocationCoordinate2DMake([[dic objectForKeyNil:@"latitude"] floatValue], [[dic objectForKeyNil:@"longitude"] floatValue]);
      MALocalAnnotation *addPointAnn = [[MALocalAnnotation alloc] init];
      addPointAnn.coordinate = currentCoordinate;
      addPointAnn.pointName = [itemDic objectForKeyNil:@"store_open_area_name"];
      addPointAnn.pointLevel = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"priority"]];
      addPointAnn.pointType = MarkerOpenStorePointType;
      [self.mapView addAnnotation:addPointAnn];
    }
    
  }
}
#pragma mark 收到新增可开店区域
- (void)receiveAddOpenStore:(NSNotification *)notification{
  NSDictionary *pointDictionary = [notification userInfo];
  if (self.openStoreDisplay) {
    [self.openStoreList addObject:[pointDictionary objectNilForKey:@"data"]];
    [self.openStoreListV reloadOpenView:[self.openStoreList mutableCopy] andBuniessState:@"制单"];
  }
  
  //    加载已经生成的可开店区域
 
  [self.mapView removeAnnotations:self.mapView.annotations];
  
  for (int i = 0; i < self.openStoreList.count; i ++) {
    NSDictionary *itemDic = [self.openStoreList objectAtIndexCheck:i];
    if (![[itemDic objectNilForKey:@"draw_type"] isEqualToString:@"DRAW_POINT"]) {
      continue;
    }
    
    NSArray *areaRange = [itemDic objectForKeyNil:@"area_range"];
    if (!BCArrayIsEmpty(areaRange)) {
      NSDictionary *dic = [areaRange firstObject];
      CLLocationCoordinate2D currentCoordinate = CLLocationCoordinate2DMake([[dic objectForKeyNil:@"latitude"] floatValue], [[dic objectForKeyNil:@"longitude"] floatValue]);
      MALocalAnnotation *addPointAnn = [[MALocalAnnotation alloc] init];
      addPointAnn.coordinate = currentCoordinate;
      addPointAnn.pointName = [itemDic objectForKeyNil:@"store_open_area_name"];
      addPointAnn.pointLevel = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"priority"]];
      addPointAnn.pointType = MarkerOpenStorePointType;
      [self.mapView addAnnotation:addPointAnn];
    }
    
  }
}
#pragma mark 收到编辑商圈范围消息
- (void)receiveEditDraw:(NSNotification *)notification{
  
  NSDictionary *pointDictionary = [notification userInfo];
  self.pointArray = [pointDictionary objectForKeyNil:@"pointArray"];
  
  CLLocationCoordinate2D commonPolylineCoords[self.pointArray.count];
  for (int i = 0; i < self.pointArray.count; i ++) {
    
    NSDictionary *dic = [self.pointArray objectAtIndexCheck:i];
    CLLocationCoordinate2D coor = CLLocationCoordinate2DMake([[dic objectForKeyNil:@"latitude"] floatValue], [[dic objectForKeyNil:@"longitude"] floatValue]);
    commonPolylineCoords[i] = coor;
  }
  [self.mapView removeOverlay:self.creatPolygon];
  self.creatPolygon = nil;
  self.creatPolygon = [MACreatBuniessPolygon polygonWithCoordinates:commonPolylineCoords count:self.pointArray.count];
  [self.mapView addOverlay:self.creatPolygon];
  [self.mapView showOverlays:@[self.creatPolygon] edgePadding:UIEdgeInsetsMake(20, 20, 20, 20) animated:NO];
}
#pragma mark 获取变动项
- (void)loadMoreRequest{
  
  [self.loadingView showModal];
  
  
  dispatch_group_t group = dispatch_group_create();
  
  //  初始化
  [self request:@"kms/hxl.kms.businessplan.initial" group:group pamares:@{}];
  
  //  是否有草稿接口
  NSDictionary *draftDic = BCStringIsEmpty(self.orgId) ? @{@"type":@"BUSINESS_PLAN"}: @{@"type":@"BUSINESS_PLAN",@"org_id":self.orgId};
  [self request:@"kms/hxl.kms.businessdraft.read" group:group pamares:draftDic];
  
  //  跟进人
  NSDictionary *followDic = BCStringIsEmpty(self.orgId) ? @{}: @{@"org_id":self.orgId};
  [self request:@"kms/hxl.kms.businessplan.head.find" group:group pamares:followDic];
  
  //  商圈区域
  [self request:@"kms/hxl.kms.area.user.find" group:group pamares:followDic];
  
  
  //商圈主副标签
  NSDictionary *labelDic = BCStringIsEmpty(self.orgId) ? @{@"page_number":@(0),@"page_size":@(200),@"type":@"LABEL",@"enable":@(1)}: @{@"page_number":@(0),@"page_size":@(200),@"type":@"LABEL",@"enable":@(1),@"org_id":self.orgId};
  [self request:@"kms/hxl.kms.dictionary.page" group:group pamares:labelDic];
  
  dispatch_group_notify(group, dispatch_get_main_queue(), ^{
    
    if (BCStringIsEmpty(self.buniessId)) {
      [self.loadingView hideModal];
      [self.statusView showModal:ToastFail andTitle:@"新增商圈初始化失败,请重试"];
      [self.navigationController popViewControllerAnimated:YES];
      return;
    }
    
    //    变动项接口
    NSDictionary *chndic;
    if (BCStringIsEmpty(self.orgId)) {
      chndic = @{@"type":@"BUSINESS"};
    } else {
      chndic = @{@"type":@"BUSINESS",@"org_id":self.orgId};
    }
    
    [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.itemchange.find" Params:chndic success:^(NSDictionary * _Nonnull successResult) {
      
      [self.loadingView hideModal];
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if (BCArrayIsEmpty(receiveData)) {
        return;
      }
      
      DDLog(@"===变动项===%@",receiveData);
      NSMutableArray *itemArr = [NSMutableArray array];
      for (int i = 0; i < receiveData.count; i ++) {
        
        NSDictionary *dic = [receiveData objectAtIndexCheck:i];
        
        if ([[dic objectForKeyNil:@"name"] isEqualToString:@"基本信息"]) {//判断基本信息必填必显
          
          NSArray *basicArr =  [[dic objectForKeyNil:@"content"] objectForKeyNil:@"details"];
          if (!BCArrayIsEmpty(basicArr)) {
            //    apiname为key，显隐 必填为内容
            for (NSDictionary *isReqiredDic in basicArr) {
              NSString *name = [NSString stringWithFormat:@"%@",[isReqiredDic objectNilForKey:@"api_name"]];
              NSString *reqired = [NSString stringWithFormat:@"%@",[isReqiredDic objectNilForKey:@"init_required"]];
              NSString *showed = [NSString stringWithFormat:@"%@",[isReqiredDic objectNilForKey:@"init_display"]];
              [self.requiredDic setObject:@{@"required":reqired,@"display":showed} forKey:name];
            }
          }
        }
        
        if ([[dic objectNilForKey:@"name"] isEqualToString:@"商圈现场"]) {//商圈现场变动项
          NSArray *details = [[dic objectForKeyNil:@"content"] objectForKeyNil:@"details"];
          if (!BCArrayIsEmpty(details)) {
            for (NSDictionary *detailDic in details) {
              if ([[detailDic objectForKeyNil:@"title"] isEqualToString:@"商圈热力图"]) {
                self.heatDisplay = [[detailDic objectNilForKey:@"init_display"] integerValue] == 1 ? YES :NO;
                self.heatRequired = [[detailDic objectNilForKey:@"init_required"] integerValue] == 1 ? YES :NO;
                
              } else if ([[detailDic objectForKeyNil:@"title"] isEqualToString:@"商圈环境"]){
                
                self.surroundDisplay = [[detailDic objectNilForKey:@"init_display"] integerValue] == 1 ? YES :NO;
                self.surroundRequired = [[detailDic objectNilForKey:@"init_required"] integerValue] == 1 ? YES :NO;
              }
            }
          }
        }
        
        if ([[dic objectNilForKey:@"name"] isEqualToString:@"商圈数据"]) {//商圈数据
          
          NSArray *details = [[dic objectForKeyNil:@"content"] objectForKeyNil:@"details"];
          if (!BCArrayIsEmpty(details)) {
            for (NSDictionary *detailDic in details) {
              
              if ([[detailDic objectForKeyNil:@"title"] isEqualToString:@"商圈范围"]) {
                
                self.buniessAreaDisplay = [[detailDic objectNilForKey:@"init_display"] integerValue] == 1 ? YES :NO;
                self.buniessAreaRequired = [[detailDic objectNilForKey:@"init_required"] integerValue] == 1 ? YES :NO;
                
              } else  if ([[detailDic objectForKeyNil:@"title"] isEqualToString:@"可开店区域"]) {
                
                self.openStoreDisplay = [[detailDic objectNilForKey:@"init_display"] integerValue] == 1 ? YES :NO;
                self.openStoreRequired = [[detailDic objectNilForKey:@"init_required"] integerValue] == 1 ? YES :NO;
                
              }
              
            }
            
          }
          
        }
        
        if ([[dic objectForKeyNil:@"acquiesce"] isEqual:@(1) ]) {//跳过系统预设的变动项，基本信息，房东信息等等
          continue;
        }
        
        if ([[dic objectForKeyNil:@"enable"] isEqual:@(0)]) {//未启用的也要跳过
          continue;
        }
        
        [itemArr addObject:dic];
      }
      
      self.itemChangeArr = itemArr;//变动项数据
      DDLog(@"==变动=%@",receiveData);
      
      //      ==============所有请求结束开始加载页面ui===============
      [self loadTab];//顶部tab
      [self loadBottomView];//保存按钮
      if (!BCDictIsEmpty(self.draftData)) {//草稿
        [self loadDraftView];
      }
      [self loadMainScrollView];//滚动视图
      [UIView animateWithDuration:0.25 animations:^{
        self.homeScrollView.alpha = 1;
      }];
      
    } failure:^(NSString * _Nonnull errorResult) {
      [self.loadingView hideModal];
      [self.view makeToast:errorResult duration:1 position:CSToastPositionCenter];
    }];
    
  });
}

- (void)request:(NSString *)url group:(dispatch_group_t)group pamares:(NSDictionary *)para{
  
  dispatch_group_enter(group);
  
  [[MAHttpTool defaultManagerTool] postWithURL:url Params:para success:^(NSDictionary * _Nonnull successResult) {
    dispatch_group_leave(group);
    
    if ([url isEqualToString:@"kms/hxl.kms.businessplan.initial"]){//初始化接口
      
      NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (![receiveData isKindOfClass:[NSDictionary class]]) {
        return;
      }
      
      if (BCDictIsEmpty(receiveData)) {
        return;
      }
      
      self.buniessId = [NSString stringWithFormat:@"%@",[receiveData objectForKeyNil:@"id"]];
      
    } else  if ([url isEqualToString:@"kms/hxl.kms.businessdraft.read"]){//是否有草稿接口
      
      NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (![receiveData isKindOfClass:[NSDictionary class]] || BCDictIsEmpty(receiveData)) {
        return;
      }
      
      NSDictionary *infoDic = [receiveData objectForKeyNil:@"info"];
      if (![infoDic isKindOfClass:[NSDictionary class]] || BCDictIsEmpty(infoDic)) {
        return;
      }
      
      self.draftData = infoDic;
      
    } else if ([url isEqualToString:@"kms/hxl.kms.businessplan.head.find"]) {//跟进人
      
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      
      NSString *userName = [[NSUserDefaults standardUserDefaults] objectForKey:@"MAP_USERNAME"];
      NSDictionary *chooseI = @{};
      for (NSDictionary *item in receiveData) {
        if ([[item objectForKeyNil:@"name"] isEqualToString:userName]) {
          chooseI = item;
          break;
        }
      }
      BOOL showS = receiveData.count < 15 ? NO : YES;
      self.followDic = chooseI;
      self.followAlert = [[MABottomSingleSelectAlert alloc] initWithTitle:@"选择跟进人" showKey:@"name" showSearch:showS maskClose:YES andData:receiveData endChooseItem:chooseI];
      
      
    } else if ([url isEqualToString:@"kms/hxl.kms.dictionary.page"]) {//多种数据
      
      NSDictionary *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      if (BCDictIsEmpty(receiveData)) {
        return;
      }
      
      NSArray *content = [receiveData objectNilForKey:@"content"];
      if (![content isKindOfClass:[NSArray class]]) {
        return;
      }
      
      if ([[para objectForKeyNil:@"type"] isEqualToString:@"LABEL"]){//商圈主标签
        BOOL showS = content.count < 15 ? NO : YES;
        self.mainLabelAlert = [[MABottomSingleSelectAlert alloc] initWithTitle:@"选择商圈主标签" showKey:@"name" showSearch:showS maskClose:YES andData:content endChooseItem:@{}];
        self.subLabelAlert = [[MABottomSingleSelectAlert alloc] initWithTitle:@"选择商圈副标签" showKey:@"name" showSearch:showS maskClose:YES andData:content endChooseItem:@{}];
        
      }
      
    } else if ([url isEqualToString:@"kms/hxl.kms.area.user.find"]) {//商圈区域
      NSArray *receiveData = [NSDictionary cleanNull:[successResult objectNilForKey:@"data"]];
      
      if (![receiveData isKindOfClass:[NSArray class]]) {
        return;
      }
      
      
      NSMutableArray *areaArray = [NSMutableArray array];
      for (NSDictionary *dic in receiveData) {
        
        if ([[dic objectForKeyNil:@"level"] isEqual:@(0)]) {//省
          
          NSMutableArray *cityArray = [NSMutableArray array];
          if ([[dic objectForKeyNil:@"name"] isEqualToString:@"北京市"] || [[dic objectForKeyNil:@"name"] isEqualToString:@"天津市"] || [[dic objectForKeyNil:@"name"] isEqualToString:@"上海市"] || [[dic objectForKeyNil:@"name"] isEqualToString:@"重庆市"] || [[dic objectForKeyNil:@"name"] containsString:@"香港"] || [[dic objectForKeyNil:@"name"] containsString:@"澳门"]) {
            
            NSMutableArray *districtArray = [NSMutableArray array];
            for (NSDictionary *districtDic in receiveData) {
              
              if ([[districtDic objectForKeyNil:@"level"] isEqual:@(2)] && [[dic objectForKeyNil:@"code"] isEqual:[districtDic objectForKeyNil:@"parent_code"]]) {
                
                
                [districtArray addObject:districtDic];
              }
            }
            
            NSMutableDictionary *cityDic = [NSMutableDictionary dictionaryWithDictionary:dic];
            [cityDic setObject:@(1) forKey:@"level"];
            [cityArray addObject:@{@"item":cityDic,@"sub":districtArray}];
            
            
          } else {
            for (NSDictionary *cityDic in receiveData) {//市
              if ([[cityDic objectForKeyNil:@"level"] isEqual:@(1)] && [[cityDic objectForKeyNil:@"parent_code"] isEqual:[dic objectForKeyNil:@"code"]]) {
                
                
                NSMutableArray *districtArray = [NSMutableArray array];
                for (NSDictionary *districtDic in receiveData) {
                  
                  if ([[districtDic objectForKeyNil:@"level"] isEqual:@(2)] && [[cityDic objectForKeyNil:@"code"] isEqual:[districtDic objectForKeyNil:@"parent_code"]]) {
                    
                    
                    [districtArray addObject:districtDic];
                  }
                }
                
                [cityArray addObject:@{@"item":cityDic,@"sub":districtArray}];
                
              }
            }
          }
          
          [areaArray addObject:@{@"item":dic,@"sub":cityArray}];
          
        }
      }
      
      self.areaAlert = [[MAChooseCityView alloc] initWithCityData:areaArray allData:receiveData andName:@"选择商圈区域"];
    }
    
    
  } failure:^(NSString * _Nonnull errorResult) {
    dispatch_group_leave(group);
    [self.view makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}


#pragma mark 顶部tab
- (void)loadTab{
  
  [self.view addSubview:self.topBackV];
  [self.topBackV addSubview:self.topTabV];
  [self.topTabV addSubview:self.indicateLayer];
  
  NSMutableArray *titleArr = [NSMutableArray arrayWithArray:@[@"基本信息"]];
  for (int i = 0; i < self.itemChangeArr.count; i ++) {
    
    NSDictionary *dic = [self.itemChangeArr objectAtIndexCheck:i];
    
    NSString *name = [NSString stringWithFormat:@"%@",[dic objectForKeyNil:@"name"]];
    [titleArr addObject:name];
  }
  
  CGFloat w = 0;
  for (int i = 0; i < titleArr.count; i ++) {
    
    NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(14) weight:UIFontWeightMedium]};
    CGFloat length = [titleArr[i] boundingRectWithSize:CGSizeMake(MAXFLOAT, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.width;
    length = ceil(length) + 2;
    UIButton *titleButton = [UIButton buttonWithType:UIButtonTypeCustom];
    titleButton.frame = CGRectMake(w, 0, length , 44);
    titleButton.tag = 1014 + i;
    [titleButton addTarget:self action:@selector(titleClick:) forControlEvents:UIControlEventTouchUpInside];
    [_topTabV addSubview:titleButton];
    
    UILabel *titleL = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, length, 44)];
    titleL.textColor  = ACOLOR(30, 33, 38, 0.70);
    titleL.font = [UIFont systemFontOfSize:MutilFont(14)];
    titleL.text = titleArr[i];
    titleL.tag = 222;
    [titleButton addSubview:titleL];
    
    if (i == 0) {
      titleButton.selected = YES;
      titleL.font = [UIFont systemFontOfSize:MutilFont(14) weight:UIFontWeightMedium];
      titleL.textColor = COLOR(26, 106, 255);
      _selectTabButton = titleButton;
      _indicateLayer.frame = CGRectMake(titleButton.left + (titleButton.width - 20)/2, titleButton.bottom - 10, 20, 3);
      
    }
    
    w = titleButton.frame.size.width +  titleButton.frame.origin.x + 24;
  }
  _topTabV.contentSize = CGSizeMake(w - 8 , 44);
  [self.topTabV bringSubviewToFront:_indicateLayer];
  [self.topBackV addSubview:self.topLineV];
  
}
#pragma mark 点击顶部选项卡
- (void)titleClick:(UIButton *)sender{
  
  if (sender != _selectTabButton) {
    
    UILabel *titleL = [_selectTabButton viewWithTag:222];
    titleL.font = [UIFont systemFontOfSize:MutilFont(14)];
    titleL.textColor = ACOLOR(30, 33, 38, 0.7);
    
    UILabel *titleLs = [sender viewWithTag:222];
    titleLs.font = [UIFont systemFontOfSize:MutilFont(14) weight:UIFontWeightMedium];
    titleLs.textColor = COLOR(26, 106, 255);
    
    _selectTabButton = sender;
    
    //    指示条动画
    [UIView animateWithDuration:0.2 animations:^{
      self.indicateLayer.frame = CGRectMake(sender.left + (sender.width - 20)/2, sender.bottom - 10, 20, 3);
    }];
    
    [UIView animateWithDuration:0.3 animations:^{
      sender.transform = CGAffineTransformMakeScale(1.05, 1.05);
    } completion:^(BOOL finished) {
      [UIView animateWithDuration:0.3 animations:^{
        sender.transform = CGAffineTransformIdentity;
      }];
    }];
    
    //   计算选项卡滚动位置
    [self scrollSelectedViewToCenter:sender];
    self.isCLickScroll = YES;
    //   计算滚动scrollview滚动位置
    if (sender.tag == 1014) {
      [self.homeScrollView setContentOffset:CGPointMake(0, 0) animated:YES];
    }  else {
      
      // 变动项
      NSInteger index = sender.tag - 1015;
      UIView *bottomV = [self.changeView viewWithTag:10000 + index];
      CGRect tvRect = [bottomV convertRect:bottomV.bounds toView:self.homeScrollView];
      
      // 如果变动项高度大于滚动视图高度
      if (tvRect.size.height >= self.homeScrollView.height) {
        CGFloat offsetY = tvRect.origin.y - (self.homeScrollView.height / 2.0);
        offsetY = MAX(0, MIN(offsetY, self.homeScrollView.contentSize.height - self.homeScrollView.height));
        [self.homeScrollView setContentOffset:CGPointMake(0, offsetY) animated:YES];
      } else {
        // 将变动项中心滚动到屏幕中心
        CGFloat targetCenterY = tvRect.origin.y + (tvRect.size.height / 2.0) - (self.homeScrollView.height / 2.0);
        CGFloat offsetY = MAX(0, MIN(targetCenterY, self.homeScrollView.contentSize.height - self.homeScrollView.height));
        [self.homeScrollView setContentOffset:CGPointMake(0, offsetY) animated:YES];
      }
    }
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      self.isCLickScroll = NO;
    });
    
  }
  
}

// 设置标题滚动区域的偏移量
- (void)scrollSelectedViewToCenter:(UIButton *)subView {
  
  
  CGFloat offsetX = subView.center.x - BCWidth * 0.5;
  
  if (offsetX < 0) {
    offsetX = -16;
  }
  
  CGFloat maxOffsetX = self.topTabV.contentSize.width - BCWidth;
  
  if (maxOffsetX < 0) {
    maxOffsetX = -16;
  }
  
  if (offsetX > maxOffsetX) {
    offsetX = maxOffsetX;
  }
  
  [self.topTabV setContentOffset:CGPointMake(offsetX, 0) animated:YES];
}
#pragma mark 底部保存按钮
- (void)loadBottomView{
  UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, BCHeight - 90, BCWidth, 90)];
  bottomV.backgroundColor = [UIColor whiteColor];
  [self.view addSubview:bottomV];
  
  UIButton *sureBtn = [[UIButton alloc] initWithFrame:CGRectMake(16, 12, BCWidth - 32, 44)];
  sureBtn.layer.cornerRadius = 4;
  if (@available(iOS 13.0, *)) {
    sureBtn.layer.cornerCurve = kCACornerCurveContinuous;
  }
  sureBtn.clipsToBounds = YES;
  sureBtn.backgroundColor = COLOR(26, 106, 255);
  sureBtn.titleLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  [sureBtn setTitle:@"提交" forState:UIControlStateNormal];
  [sureBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
  [sureBtn addTarget:self action:@selector(clickSubmit) forControlEvents:UIControlEventTouchUpInside];
  [bottomV addSubview:sureBtn];
  self.submitButton = sureBtn;
  
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(0,0, BCWidth , 1)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [bottomV addSubview:lineV];
  
  
}

- (void)scrollToMySubview:(UIView *)targetView{
  
  CGRect tvRect = [targetView convertRect:targetView.bounds toView:self.homeScrollView];
  CGFloat offsetX = tvRect.origin.x - (self.homeScrollView.bounds.size.width / 2) + (tvRect.size.width / 2);
  CGFloat offsetY = tvRect.origin.y - (self.homeScrollView.bounds.size.height / 2) + (tvRect.size.height / 2);
  offsetX = MAX(0, MIN(offsetX, self.homeScrollView.contentSize.width - self.homeScrollView.bounds.size.width));
  offsetY = MAX(0, MIN(offsetY, self.homeScrollView.contentSize.height - self.homeScrollView.bounds.size.height));
  [self.homeScrollView setContentOffset:CGPointMake(offsetX, offsetY) animated:YES];
}

- (void)animateBackgroundColorForView:(UIView *)view andIsHaveFatherView:(BOOL)isFather endPosition:(CGRect)position{
  
  if (isFather) {
    
    [view.layer removeAllAnimations];
    CABasicAnimation *colorAnimation = [CABasicAnimation animationWithKeyPath:@"backgroundColor"];
    colorAnimation.fromValue = (__bridge id)[UIColor whiteColor].CGColor;
    colorAnimation.toValue = (__bridge id)[UIColor colorWithRed:255/255.0 green:66/255.0 blue:66/255.0 alpha:0.2].CGColor;
    colorAnimation.duration = 0.5;
    colorAnimation.repeatCount = 2;
    colorAnimation.autoreverses = YES;
    [view.layer addAnimation:colorAnimation forKey:nil];
    
  } else {
    
    if (self.tipAnimationV) {
      return;
    }
    
    self.tipAnimationV = [[UIView alloc] initWithFrame:CGRectMake(0, position.origin.y, BCWidth, position.size.height)];
    self.tipAnimationV.backgroundColor = [UIColor whiteColor];
    [view.superview insertSubview:self.tipAnimationV atIndex:0];
    CABasicAnimation *colorAnimation = [CABasicAnimation animationWithKeyPath:@"backgroundColor"];
    colorAnimation.fromValue = (__bridge id)[UIColor whiteColor].CGColor;
    colorAnimation.toValue = (__bridge id)[UIColor colorWithRed:255/255.0 green:66/255.0 blue:66/255.0 alpha:0.2].CGColor;
    colorAnimation.duration = 0.5;
    colorAnimation.repeatCount = 2;
    colorAnimation.autoreverses = YES;
    colorAnimation.delegate = self;
    [self.tipAnimationV.layer addAnimation:colorAnimation forKey:nil];
  }
  
}

- (void)animationDidStop:(CAAnimation *)anim finished:(BOOL)flag{
  if (flag && self.tipAnimationV) {
    [self.tipAnimationV.layer removeAllAnimations];
    [self.tipAnimationV removeFromSuperview];
    self.tipAnimationV = nil;
  }
}
#pragma mark 点击底部提交按钮
- (void)clickSubmit{
  
  if (self.notiflyView.showView) {
    return;
  }
  
  //  校验参数
  if (_nameTF.text.length == 0) {
    NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_business_name"];
    if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"1"] && [[tmpDic objectNilForKey:@"display"] isEqualToString:@"1"]) {
      
      [self.homeScrollView setContentOffset:CGPointMake(0, -12) animated:YES];
      [self.notiflyView showModal:NotiflyFail andTitle:@"商圈名称为必填项"];
      CGRect tvRect = [_nameTF convertRect:_nameTF.bounds toView:self.homeScrollView];
      [self animateBackgroundColorForView:_nameTF andIsHaveFatherView:NO endPosition:CGRectMake(0, tvRect.origin.y, BCWidth, 50)];
      
      return;
    }
  }
  if (BCDictIsEmpty(self.followDic)) {
    NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_follow_by"];
    if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"1"] && [[tmpDic objectNilForKey:@"display"] isEqualToString:@"1"]) {
      
      [self.homeScrollView setContentOffset:CGPointMake(0, -12) animated:YES];
      [self.notiflyView showModal:NotiflyFail andTitle:@"跟进人为必选项"];
      
      CGRect tvRect = [self.followBtn convertRect:self.followBtn.bounds toView:self.homeScrollView];
      [self animateBackgroundColorForView:self.followBtn andIsHaveFatherView:NO endPosition:CGRectMake(0, tvRect.origin.y, BCWidth, 50)];
      return;
    }
  }
  
  if (BCDictIsEmpty(self.areaDic)) {
    NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_area_range"];
    if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"1"] && [[tmpDic objectNilForKey:@"display"] isEqualToString:@"1"]) {
      
      [self.homeScrollView setContentOffset:CGPointMake(0, -12) animated:YES];
      [self.notiflyView showModal:NotiflyFail andTitle:@"商圈区域为必选项"];
      
      CGRect tvRect = [self.buniessAreaBtn convertRect:self.buniessAreaBtn.bounds toView:self.homeScrollView];
      [self animateBackgroundColorForView:self.buniessAreaBtn andIsHaveFatherView:NO endPosition:CGRectMake(0, tvRect.origin.y, BCWidth, 50)];
      
      return;
    }
  }
  
  if (BCDictIsEmpty(self.levelDic)) {
    NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_level_name"];
    if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"1"] && [[tmpDic objectNilForKey:@"display"] isEqualToString:@"1"]) {
      
      [self.homeScrollView setContentOffset:CGPointMake(0, -12) animated:YES];
      [self.notiflyView showModal:NotiflyFail andTitle:@"商圈等级为必选项"];
      
      CGRect tvRect = [self.buniessLevelBtn convertRect:self.buniessLevelBtn.bounds toView:self.homeScrollView];
      [self animateBackgroundColorForView:self.buniessLevelBtn andIsHaveFatherView:NO endPosition:CGRectMake(0, tvRect.origin.y, BCWidth, 50)];
      return;
    }
  }
  if (BCDictIsEmpty(self.mainLableDic)) {
    NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_main_label"];
    if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"1"] && [[tmpDic objectNilForKey:@"display"] isEqualToString:@"1"]) {
      
      [self.homeScrollView setContentOffset:CGPointMake(0, -12) animated:YES];
      [self.notiflyView showModal:NotiflyFail andTitle:@"商圈主标签为必选项"];
      
      CGRect tvRect = [self.mainTypeBtn convertRect:self.mainTypeBtn.bounds toView:self.homeScrollView];
      [self animateBackgroundColorForView:self.mainTypeBtn andIsHaveFatherView:NO endPosition:CGRectMake(0, tvRect.origin.y, BCWidth, 50)];
      
      return;
    }
  }
  if (BCDictIsEmpty(self.subLableDic)) {
    NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_sub_label"];
    if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"1"] && [[tmpDic objectNilForKey:@"display"] isEqualToString:@"1"]) {
      
      [self.homeScrollView setContentOffset:CGPointMake(0, -12) animated:YES];
      [self.notiflyView showModal:NotiflyFail andTitle:@"商圈副标签为必选项"];
      
      CGRect tvRect = [self.subTypeBtn convertRect:self.subTypeBtn.bounds toView:self.homeScrollView];
      [self animateBackgroundColorForView:self.subTypeBtn andIsHaveFatherView:NO endPosition:CGRectMake(0, tvRect.origin.y, BCWidth, 50)];
      return;
    }
  }
  
  if (_openStoreTF.text.length == 0) {
    NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_open_store"];
    if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"1"] && [[tmpDic objectNilForKey:@"display"] isEqualToString:@"1"]) {
      
      [self scrollToMySubview:self.openStoreTF];
      [self.notiflyView showModal:NotiflyFail andTitle:@"可开门店数为必填项"];
      
      CGRect tvRect = [self.openStoreTF convertRect:self.openStoreTF.bounds toView:self.homeScrollView];
      [self animateBackgroundColorForView:self.openStoreTF andIsHaveFatherView:NO endPosition:CGRectMake(0, tvRect.origin.y, BCWidth, 50)];
      
      return;
    }
  }
  
  if (_competitorTF.text.length == 0) {
    NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_competitor"];
    if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"1"] && [[tmpDic objectNilForKey:@"display"] isEqualToString:@"1"]) {
      
      [self scrollToMySubview:self.competitorTF];
      [self.notiflyView showModal:NotiflyFail andTitle:@"同行品牌为必填项"];
      
      CGRect tvRect = [self.competitorTF convertRect:self.competitorTF.bounds toView:self.homeScrollView];
      [self animateBackgroundColorForView:self.competitorTF andIsHaveFatherView:NO endPosition:CGRectMake(0, tvRect.origin.y, BCWidth, 50)];
      
      return;
    }
  }
  
  if (self.heatDisplay && self.heatRequired && BCArrayIsEmpty(self.heatArr)) {
    
    [self scrollToMySubview:self.heatUploadView];
    [self.notiflyView showModal:NotiflyFail andTitle:@"商圈热力图为必传项"];
    
    CGRect tvRect = [self.heatUploadView convertRect:self.heatUploadView.bounds toView:self.homeScrollView];
    [self animateBackgroundColorForView:self.heatUploadView andIsHaveFatherView:NO endPosition:CGRectMake(0, tvRect.origin.y - 43, BCWidth, tvRect.size.height + 57)];
    
    return;
  }
  
  if (self.surroundDisplay && self.surroundRequired && BCArrayIsEmpty(self.surroundArr)) {
    
    [self scrollToMySubview:self.surroundUploadView];
    [self.notiflyView showModal:NotiflyFail andTitle:@"商圈环境为必传项"];
    
    
    CGRect tvRect = [self.surroundUploadView convertRect:self.surroundUploadView.bounds toView:self.homeScrollView];
    [self animateBackgroundColorForView:self.surroundUploadView andIsHaveFatherView:NO endPosition:CGRectMake(0, tvRect.origin.y - 43, BCWidth, tvRect.size.height + 57)];
    
    return;
  }
  
  if (self.openStoreDisplay && self.openStoreRequired && BCArrayIsEmpty(self.openStoreList)) {
    
    [self scrollToMySubview:self.openStoreView];
    [self.notiflyView showModal:NotiflyFail andTitle:@"可开店区域为必填项"];
    [self animateBackgroundColorForView:self.openStoreView andIsHaveFatherView:YES endPosition:CGRectZero];
    return;
  }
  
  //  校验变动项里的必填项
  BOOL changeItemReque = NO;
  for (int i = 0; i < self.itemChangeArr.count; i ++) {
    
    NSDictionary *dic = [self.itemChangeArr objectAtIndexCheck:i];
    if ([[dic objectForKeyNil:@"acquiesce"] isEqual:@(1) ]) {//跳过系统预设的变动项，基本信息，房东信息等等
      continue;
    }
    
    if ([[dic objectForKeyNil:@"enable"] isEqual:@(0)]) {//未启用的也要跳过
      continue;
    }
    
    UIView *bottomV = [self.changeView viewWithTag:10000 + i];
    NSArray *items = [[dic objectForKeyNil:@"content"] objectForKeyNil:@"details"];
    if (BCArrayIsEmpty(items)) {
      continue;
    }
    
    
    for (int j = 0; j < items.count; j ++) {
      NSDictionary *itemDic = [items objectAtIndexCheck:j];
      if (BCDictIsEmpty(itemDic)) {
        continue;
      }
      NSString *rightStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"component_type"]];
      
      
      if ([[itemDic objectForKeyNil:@"init_required"] isEqual:@(1)]) {
        
        if ([rightStr isEqualToString:@"TEXT"] || [rightStr isEqualToString:@"NUMBER"] || [rightStr isEqualToString:@"AMOUNT"]) {//输入框
          UITextField *tf = [bottomV viewWithTag:20000 + j];
          UIView *itemV = [bottomV viewWithTag:30000 + j + i];
          if (tf.text.length == 0 && itemV.height > 0) {
            
            [self scrollToMySubview:itemV];
            NSString *titleNameStr = [NSString stringWithFormat:@"%@为必填项",[itemDic objectNilForKey:@"title"]];
            [self.notiflyView showModal:NotiflyFail andTitle:titleNameStr];
            [self animateBackgroundColorForView:itemV andIsHaveFatherView:YES endPosition:CGRectZero];
            changeItemReque = YES;
            
            break;
          }
        } else if ([rightStr isEqualToString:@"IMAGE"] || [rightStr isEqualToString:@"VIDEO"]){
          MAUploadMutilImageView *tf = [bottomV viewWithTag:20000 + j];
          UIView *itemV = [bottomV viewWithTag:30000 + j + i];
          if (BCArrayIsEmpty(tf.resultArr) && itemV.height > 0 ) {
            
            [self scrollToMySubview:itemV];
            NSString *titleNameStr = [NSString stringWithFormat:@"%@为必传项",[itemDic objectNilForKey:@"title"]];
            [self.notiflyView showModal:NotiflyFail andTitle:titleNameStr];
            [self animateBackgroundColorForView:itemV andIsHaveFatherView:YES endPosition:CGRectZero];
            changeItemReque = YES;
            
            break;
          }
          
        } else if ([rightStr isEqualToString:@"DATE"] || [rightStr isEqualToString:@"DATE_TIME"] || [rightStr isEqualToString:@"SINGLE_CHOICE"] || [rightStr isEqualToString:@"MULTI_CHOICE"] || [rightStr isEqualToString:@"SINGLE_RADIO"]){
          UILabel *chooseL = [bottomV viewWithTag:20000 + j];
          UIView *itemV = [bottomV viewWithTag:30000 + j + i];
          
          if ([chooseL.text isEqualToString:@"请选择"] && itemV.height > 0) {
            
            [self scrollToMySubview:itemV];
            NSString *titleNameStr = [NSString stringWithFormat:@"%@为必选项",[itemDic objectNilForKey:@"title"]];
            [self.notiflyView showModal:NotiflyFail andTitle:titleNameStr];
            [self animateBackgroundColorForView:itemV andIsHaveFatherView:YES endPosition:CGRectZero];
            changeItemReque = YES;
            
            break;
          }
        } else if ([rightStr isEqualToString:@"LONG_TEXT"]){
          UITextView *tf = [bottomV viewWithTag:20000 + j];
          UIView *itemV = [bottomV viewWithTag:30000 + j + i];
          if (tf.text.length == 0 && itemV.height > 0) {
            
            [self scrollToMySubview:itemV];
            NSString *titleNameStr = [NSString stringWithFormat:@"%@为必填项",[itemDic objectNilForKey:@"title"]];
            [self.notiflyView showModal:NotiflyFail andTitle:titleNameStr];
            [self animateBackgroundColorForView:itemV andIsHaveFatherView:YES endPosition:CGRectZero];
            changeItemReque = YES;
            
            break;
          }
        } else if ([rightStr isEqualToString:@"LOCATION"]){
          
          UIButton *tf = [bottomV viewWithTag:20000 + j];
          UIView *itemV = [bottomV viewWithTag:30000 + j + i];
          NSDictionary *dic = [tf getMoreParams];
          
          if (BCDictIsEmpty(dic) && itemV.height > 0) {
            
            [self scrollToMySubview:itemV];
            NSString *titleNameStr = [NSString stringWithFormat:@"%@为必填项",[itemDic objectNilForKey:@"title"]];
            [self.notiflyView showModal:NotiflyFail andTitle:titleNameStr];
            [self animateBackgroundColorForView:itemV andIsHaveFatherView:YES endPosition:CGRectZero];
            changeItemReque = YES;
            
            break;
            
          }
          
        }
      } else if ([rightStr isEqualToString:@"TABLE"]){//表格单独校验
        
        UIView *itemV = [bottomV viewWithTag:30000 + j + i];
        MACustomBuniessTableView *tb = [bottomV viewWithTag:20000 + j];
        UIView *checkV = [tb checkHaveNotRequired];
        if (checkV && itemV.height > 0) {
          [self scrollToMySubview:checkV];
          UILabel *titleL = [checkV viewWithTag:60000];
          NSString *titleNameStr = [NSString stringWithFormat:@"%@为必填项",titleL.text];
          [self.notiflyView showModal:NotiflyFail andTitle:titleNameStr];
          [self animateBackgroundColorForView:checkV andIsHaveFatherView:YES endPosition:CGRectZero];
          changeItemReque = YES;
          break;
        }
        
      }
      
    }
    if (changeItemReque == YES) {
      break;
    }
  }
  
  if (changeItemReque == YES) {
    return;
  }
  
  
  //  校验通过提交参数
  NSMutableDictionary *dic = [NSMutableDictionary dictionary];
  [dic setObject:self.buniessId forKey:@"id"];
  [dic setObject:@"INIT" forKey:@"schedule"];
  
  if (!BCStringIsEmpty(self.orgId)) {
    [dic setObject:self.orgId forKey:@"org_id"];
    [dic setObject:self.orgName forKey:@"org_name"];
  }
  [dic setObject:self.pointArray forKey:@"area_range"];
  if (!BCArrayIsEmpty(self.openStoreList)) {
    [dic setObject:self.openStoreList forKey:@"store_open_area"];
  }
  
  if (_nameTF.text.length != 0) {
    [dic setObject:_nameTF.text forKey:@"name"];
  }
  
  if (!BCDictIsEmpty(self.followDic)) {
    [dic setObject:[self.followDic objectForKeyNil:@"user_id"] forKey:@"head_id"];
    [dic setObject:[self.followDic objectForKeyNil:@"name"] forKey:@"head_by"];
  }
  
  
  if (!BCDictIsEmpty(self.areaDic)) {
    [dic setObject:[self.areaDic objectForKeyNil:@"areaName"] forKey:@"area_name"];
    [dic setObject:[self.areaDic objectForKeyNil:@"areaCode"] forKey:@"area_code"];
  }
  
  if (!BCDictIsEmpty(self.levelDic)) {
    [dic setObject:[self.levelDic objectForKeyNil:@"id"] forKey:@"level_name"];
  }
  
  //  商圈主副标签
  if (!BCDictIsEmpty(self.mainLableDic)) {
    [dic setObject:[self.mainLableDic objectForKeyNil:@"id"] forKey:@"main_label_id"];
    [dic setObject:[self.mainLableDic objectForKeyNil:@"name"] forKey:@"main_label_name"];
  }
  if (!BCDictIsEmpty(self.subLableDic)) {
    [dic setObject:[self.subLableDic objectForKeyNil:@"id"] forKey:@"sub_label_id"];
    [dic setObject:[self.subLableDic objectForKeyNil:@"name"] forKey:@"sub_label_name"];
  }
  
  if (_openStoreTF.text.length != 0) {
    [dic setObject:_openStoreTF.text forKey:@"open_store"];
  }
  
  if (_competitorTF.text.length != 0) {
    [dic setObject:_competitorTF.text forKey:@"competitor"];
  }
  //  拼接热力图，商圈环境
  NSMutableArray *arr = [NSMutableArray array];
  if (!BCArrayIsEmpty(self.heatArr)) {
    [arr addObjectsFromArray:self.heatArr];
  }
  if (!BCArrayIsEmpty(self.surroundArr)) {
    [arr addObjectsFromArray:self.surroundArr];
  }
  
  [dic setObject:arr forKey:@"files"];
  
  //  拼接变动项参数
  NSMutableArray *contentArr = [NSMutableArray array];
  for (int i = 0; i < self.itemChangeArr.count; i ++) {
    
    NSDictionary *dic = [self.itemChangeArr objectAtIndexCheck:i];
    if ([[dic objectForKeyNil:@"acquiesce"] isEqual:@(1) ]) {//跳过系统预设的变动项，基本信息，房东信息等等
      continue;
    }
    
    if ([[dic objectForKeyNil:@"enable"] isEqual:@(0)]) {//未启用的也要跳过
      continue;
    }
    
    UIView *bottomV = [self.changeView viewWithTag:10000 + i];
    NSArray *items = [[dic objectForKeyNil:@"content"] objectForKeyNil:@"details"];
    
    if (BCArrayIsEmpty(items)) {
      continue;
    }
    
    NSMutableDictionary *contentDic = [NSMutableDictionary dictionary];
    [contentDic setValue:[dic objectForKeyNil:@"id"] forKey:@"id"];
    [contentDic setValue:[dic objectForKeyNil:@"name"] forKey:@"name"];
    
    NSMutableDictionary *detailDic = [NSMutableDictionary dictionary];
    
    for (int j = 0; j < items.count; j ++) {
      
      NSDictionary *itemDic = [items objectAtIndexCheck:j];
      if (BCDictIsEmpty(itemDic)) {
        continue;
      }
      
      UIView *itemV = [bottomV viewWithTag:30000 + j + i];
      if (itemV.height == 0) {
        continue;
      }
      
      NSString *rightStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"component_type"]];
      
      
      if ([rightStr isEqualToString:@"TEXT"] || [rightStr isEqualToString:@"NUMBER"] || [rightStr isEqualToString:@"AMOUNT"]) {//输入框
        UITextField *tf = [bottomV viewWithTag:20000 + j];
        if (tf.text.length != 0) {
          [detailDic setValue:tf.text forKey:[itemDic objectForKeyNil:@"id"]];
        }
      }  else if ([rightStr isEqualToString:@"IMAGE"] || [rightStr isEqualToString:@"VIDEO"]){
        MAUploadMutilImageView *tf = [bottomV viewWithTag:20000 + j];
        if (!BCArrayIsEmpty(tf.resultArr)) {
          [detailDic setValue:tf.resultArr forKey:[itemDic objectForKeyNil:@"id"]];
        }
        
      } else if ([rightStr isEqualToString:@"DATE"] || [rightStr isEqualToString:@"DATE_TIME"] || [rightStr isEqualToString:@"SINGLE_CHOICE"] || [rightStr isEqualToString:@"SINGLE_RADIO"]){
        UILabel *chooseL = [bottomV viewWithTag:20000 + j];
        if (![chooseL.text isEqualToString:@"请选择"]) {
          [detailDic setValue:chooseL.text forKey:[itemDic objectForKeyNil:@"id"]];
        }
        
      } else if ([rightStr isEqualToString:@"MULTI_CHOICE"]){
        
        UILabel *chooseL = [bottomV viewWithTag:20000 + j];
        if (![chooseL.text isEqualToString:@"请选择"]) {
          NSArray *array = [chooseL.text componentsSeparatedByString:@","];
          NSMutableArray *resultArr = [NSMutableArray array];
          for (NSString *str in array) {
            NSDictionary *strdic = @{@"label":str,@"value":str};
            [resultArr addObject:strdic];
          }
          
          [detailDic setValue:resultArr forKey:[itemDic objectForKeyNil:@"id"]];
        }
        
      } else if ([rightStr isEqualToString:@"LONG_TEXT"]){
        UITextView *tf = [bottomV viewWithTag:20000 + j];
        if (tf.text.length != 0) {
          [detailDic setValue:tf.text forKey:[itemDic objectForKeyNil:@"id"]];
        }
        
      } else if ([rightStr isEqualToString:@"LOCATION"]){
        
        UIButton *tf = [bottomV viewWithTag:20000 + j];
        NSDictionary *dic = [tf getMoreParams];
        if (!BCDictIsEmpty(dic)) {
          [detailDic setValue:dic forKey:[itemDic objectNilForKey:@"id"]];
        }
        
      } else if ([rightStr isEqualToString:@"TABLE"]){//表格
        
        UIView *itemV = [bottomV viewWithTag:30000 + j + i];
        MACustomBuniessTableView *tb = [bottomV viewWithTag:20000 + j];
        NSArray *allValue = [tb getAllItemValue];
        if (!BCArrayIsEmpty(allValue)) {
          [detailDic setValue:allValue forKey:[itemDic objectNilForKey:@"id"]];
        }
        
      }
      
    }
    
    [contentDic setValue:detailDic forKey:@"details"];
    [contentArr addObject:contentDic];
    
  }
  
  [dic setObject:contentArr forKey:@"content"];
  
  
  __weak typeof(self)weakSelf = self;
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定要提交吗?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    
    
    [weakSelf startAnimation];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.6 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [weakSelf submitData:dic];
    });
    
  };
  
}
#pragma mark 提交数据到服务器端
- (void)submitData:(NSDictionary *)params{
  
  DDLog(@"商圈提交参数===%@",params);
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.save" Params:params success:^(NSDictionary *successResult) {
    
    self.isSaveDraft = YES;
    [self.statusView showModal:ToastSuccess andTitle:@"提交成功"];
    [self stopAnimation];
    [MobClick event:@"FlagMapSuccessCreatBuniess"];
    
    //  发送通知给地图
    
    NSDictionary *tmpDic = @{@"latitude":[NSString stringWithFormat:@"%f",self.creatPolygon.coordinate.latitude],@"longitude":[NSString stringWithFormat:@"%f",self.creatPolygon.coordinate.longitude]};
    [[NSNotificationCenter defaultCenter] postNotificationName:@"ADDNEWPOINT" object:nil userInfo:tmpDic];
    
    //    提交成功后删除草稿
    NSDictionary *draftDic = BCStringIsEmpty(self.orgId) ? @{@"type":@"BUSINESS_PLAN"}: @{@"type":@"BUSINESS_PLAN",@"org_id":self.orgId};
    [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessdraft.delete" Params:draftDic success:^(NSDictionary *successResult) {
      
      
      
    } failure:^(NSString *errorResult) {
      
    }];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self.navigationController popViewControllerAnimated:YES];
    });
    
  } failure:^(NSString *errorResult) {
    
    [self stopAnimation];
    [self.statusView showModal:ToastFail andTitle:errorResult];
  }];
}
-(void)startAnimation{
  CGPoint center = self.submitButton.center;
  CGFloat width = self.submitButton.frame.size.height;
  CGFloat height = self.submitButton.frame.size.height;
  CGRect desFrame = CGRectMake(center.x - width / 2, center.y - height / 2, width, height);
  
  self.submitButton.userInteractionEnabled = NO;
  
  [UIView animateWithDuration:0.3 animations:^{
    self.submitButton.titleLabel.alpha = .0f;
    self.submitButton.frame = desFrame;
    self.submitButton.layer.cornerRadius = self.submitButton.frame.size.height/2;
    
  } completion:^(BOOL finished) {
    
    UIBezierPath* path = [[UIBezierPath alloc] init];
    [path addArcWithCenter:CGPointMake(22,22) radius:(44/2 - 5) startAngle:0 endAngle:M_PI_2 * 2 clockwise:YES];
    self.shapeLayer = [[CAShapeLayer alloc] init];
    self.shapeLayer.lineWidth = 2.5;
    self.shapeLayer.strokeColor = [UIColor whiteColor].CGColor;
    self.shapeLayer.fillColor = COLOR(26, 106, 255).CGColor;
    self.shapeLayer.path = path.CGPath;
    self.shapeLayer.lineCap = kCALineCapRound;
    [self.submitButton.layer addSublayer:self.shapeLayer];
    
    
    CABasicAnimation *baseAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    baseAnimation.duration = 0.4;
    baseAnimation.fromValue = @(0);
    baseAnimation.toValue = @(2 * M_PI);
    baseAnimation.repeatCount = MAXFLOAT;
    baseAnimation.removedOnCompletion = NO;
    baseAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
    [self.submitButton.layer addAnimation:baseAnimation forKey:nil];
  }];
}

- (void)stopAnimation{
  [self.shapeLayer removeFromSuperlayer];
  [self.submitButton.layer removeAllAnimations];
  self.shapeLayer = nil;
  [UIView animateWithDuration:0.3 animations:^{
    
    self.submitButton.frame = CGRectMake(16, 12, BCWidth - 32, 44);
    self.submitButton.layer.cornerRadius = 4;
    self.submitButton.titleLabel.alpha = 1.0f;
  } completion:^(BOOL finished) {
    self.submitButton.userInteractionEnabled = YES;
    
  }];
}
#pragma mark 加载草稿view
- (void)loadDraftView{
  [self.view addSubview:self.draftView];
  
  UIImageView *backIM = [[UIImageView alloc] initWithFrame:CGRectMake(16, 13, 16, 16)];
  backIM.image = [UIImage imageNamed:@"icon_draft"];
  [self.draftView addSubview:backIM];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(backIM.right + 6,0,100,42);
  nameLabel.textColor = COLOR(29, 33, 41);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(14)];
  nameLabel.text = @"有历史草稿";
  [self.draftView addSubview:nameLabel];
  
  
  UIButton *cancelBtn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - RealSize(30) - 16, 0, RealSize(30), 42)];
  cancelBtn.titleLabel.font = [UIFont systemFontOfSize:MutilFont(14)];
  [cancelBtn setTitle:@"删除" forState:UIControlStateNormal];
  [cancelBtn setTitleColor:COLOR(26, 106, 255) forState:UIControlStateNormal];
  [cancelBtn addTarget:self action:@selector(deleteDraft) forControlEvents:UIControlEventTouchUpInside];
  [self.draftView addSubview:cancelBtn];
  [cancelBtn setEnlargeEdgeWithTop:0 right:16 bottom:0 left:9];
  
  UIButton *sureBtn = [[UIButton alloc] initWithFrame:CGRectMake(cancelBtn.left - 18 - RealSize(58), 0, RealSize(58), 42)];
  sureBtn.titleLabel.font = [UIFont systemFontOfSize:MutilFont(14)];
  [sureBtn setTitle:@"打开草稿" forState:UIControlStateNormal];
  [sureBtn setTitleColor:COLOR(26, 106, 255) forState:UIControlStateNormal];
  [sureBtn addTarget:self action:@selector(openDraft) forControlEvents:UIControlEventTouchUpInside];
  [self.draftView addSubview:sureBtn];
  [sureBtn setEnlargeEdgeWithTop:0 right:9 bottom:0 left:16];
}
- (void)applicationWillEnterBackground {
  
  if (self.tipAnimationV) {
    [self.tipAnimationV.layer removeAllAnimations];
    [self.tipAnimationV removeFromSuperview];
    self.tipAnimationV = nil;
  }
  [self saveDraft];
}
#pragma mark 页面退出时保存草稿
- (void)viewDidDisappear:(BOOL)animated{
  [super viewDidDisappear: animated];
  if (![self.navigationController.viewControllers containsObject:self] && !self.isSaveDraft) {
    [self saveDraft];
  }
}


#pragma mark 保存草稿
- (void)saveDraft{
  
  NSMutableDictionary *dic = [NSMutableDictionary dictionary];
  [dic setObject:self.buniessId forKey:@"id"];
  [dic setObject:self.pointArray forKey:@"area_range"];
  if (!BCArrayIsEmpty(self.openStoreList)) {
    [dic setObject:self.openStoreList forKey:@"store_open_area"];
  }
  
  if (_nameTF.text.length != 0) {
    [dic setObject:_nameTF.text forKey:@"name"];
  }
  
  if (!BCDictIsEmpty(self.followDic)) {
    [dic setObject:[self.followDic objectForKeyNil:@"user_id"] forKey:@"head_id"];
    [dic setObject:[self.followDic objectForKeyNil:@"name"] forKey:@"head_by"];
  }
  
  
  if (!BCDictIsEmpty(self.areaDic)) {
    [dic setObject:[self.areaDic objectForKeyNil:@"areaName"] forKey:@"area_name"];
    [dic setObject:[self.areaDic objectForKeyNil:@"areaCode"] forKey:@"area_code"];
  }
  
  if (!BCDictIsEmpty(self.levelDic)) {
    [dic setObject:[self.levelDic objectForKeyNil:@"id"] forKey:@"level_name"];
  }
  
  //  商圈主副标签
  if (!BCDictIsEmpty(self.mainLableDic)) {
    [dic setObject:[self.mainLableDic objectForKeyNil:@"id"] forKey:@"main_label_id"];
    [dic setObject:[self.mainLableDic objectForKeyNil:@"name"] forKey:@"main_label_name"];
  }
  if (!BCDictIsEmpty(self.subLableDic)) {
    [dic setObject:[self.subLableDic objectForKeyNil:@"id"] forKey:@"sub_label_id"];
    [dic setObject:[self.subLableDic objectForKeyNil:@"name"] forKey:@"sub_label_name"];
  }
  
  if (_openStoreTF.text.length != 0) {
    [dic setObject:_openStoreTF.text forKey:@"open_store"];
  }
  
  if (_competitorTF.text.length != 0) {
    [dic setObject:_competitorTF.text forKey:@"competitor"];
  }
  //  拼接热力图，商圈环境
  NSMutableArray *arr = [NSMutableArray array];
  if (!BCArrayIsEmpty(self.heatArr)) {
    [arr addObjectsFromArray:self.heatArr];
  }
  if (!BCArrayIsEmpty(self.surroundArr)) {
    [arr addObjectsFromArray:self.surroundArr];
  }
  
  [dic setObject:arr forKey:@"files"];
  
  //  拼接变动项参数
  NSMutableArray *contentArr = [NSMutableArray array];
  for (int i = 0; i < self.itemChangeArr.count; i ++) {
    
    NSDictionary *dic = [self.itemChangeArr objectAtIndexCheck:i];
    if ([[dic objectForKeyNil:@"acquiesce"] isEqual:@(1) ]) {//跳过系统预设的变动项，基本信息，房东信息等等
      continue;
    }
    
    if ([[dic objectForKeyNil:@"enable"] isEqual:@(0)]) {//未启用的也要跳过
      continue;
    }
    
    UIView *bottomV = [self.changeView viewWithTag:10000 + i];
    NSArray *items = [[dic objectForKeyNil:@"content"] objectForKeyNil:@"details"];
    
    if (BCArrayIsEmpty(items)) {
      continue;
    }
    
    NSMutableDictionary *contentDic = [NSMutableDictionary dictionary];
    [contentDic setValue:[dic objectForKeyNil:@"id"] forKey:@"id"];
    [contentDic setValue:[dic objectForKeyNil:@"name"] forKey:@"name"];
    
    NSMutableDictionary *detailDic = [NSMutableDictionary dictionary];
    
    for (int j = 0; j < items.count; j ++) {
      
      NSDictionary *itemDic = [items objectAtIndexCheck:j];
      if (BCDictIsEmpty(itemDic)) {
        continue;
      }
      
      NSString *rightStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"component_type"]];
      
      if ([rightStr isEqualToString:@"TEXT"] || [rightStr isEqualToString:@"NUMBER"] || [rightStr isEqualToString:@"AMOUNT"]) {//输入框
        UITextField *tf = [bottomV viewWithTag:20000 + j];
        if (tf.text.length != 0) {
          [detailDic setValue:tf.text forKey:[itemDic objectForKeyNil:@"id"]];
        }
      }  else if ([rightStr isEqualToString:@"IMAGE"] || [rightStr isEqualToString:@"VIDEO"]){
        MAUploadMutilImageView *tf = [bottomV viewWithTag:20000 + j];
        if (!BCArrayIsEmpty(tf.resultArr)) {
          [detailDic setValue:tf.resultArr forKey:[itemDic objectForKeyNil:@"id"]];
        }
        
      } else if ([rightStr isEqualToString:@"DATE"] || [rightStr isEqualToString:@"DATE_TIME"] || [rightStr isEqualToString:@"SINGLE_CHOICE"] || [rightStr isEqualToString:@"SINGLE_RADIO"]){
        UILabel *chooseL = [bottomV viewWithTag:20000 + j];
        if (![chooseL.text isEqualToString:@"请选择"]) {
          [detailDic setValue:chooseL.text forKey:[itemDic objectForKeyNil:@"id"]];
        }
        
      } else if ([rightStr isEqualToString:@"MULTI_CHOICE"]){
        
        UILabel *chooseL = [bottomV viewWithTag:20000 + j];
        if (![chooseL.text isEqualToString:@"请选择"]) {
          NSArray *array = [chooseL.text componentsSeparatedByString:@","];
          NSMutableArray *resultArr = [NSMutableArray array];
          for (NSString *str in array) {
            NSDictionary *strdic = @{@"label":str,@"value":str};
            [resultArr addObject:strdic];
          }
          
          [detailDic setValue:resultArr forKey:[itemDic objectForKeyNil:@"id"]];
        }
        
      } else if ([rightStr isEqualToString:@"LONG_TEXT"]){
        UITextView *tf = [bottomV viewWithTag:20000 + j];
        if (tf.text.length != 0) {
          [detailDic setValue:tf.text forKey:[itemDic objectForKeyNil:@"id"]];
        }
        
      } else if ([rightStr isEqualToString:@"LOCATION"]){
        
        UIButton *tf = [bottomV viewWithTag:20000 + j];
        NSDictionary *dic = [tf getMoreParams];
        if (!BCDictIsEmpty(dic)) {
          [detailDic setValue:dic forKey:[itemDic objectNilForKey:@"id"]];
        }
        
      }else if ([rightStr isEqualToString:@"TABLE"]){//表格
        
        UIView *itemV = [bottomV viewWithTag:30000 + j + i];
        MACustomBuniessTableView *tb = [bottomV viewWithTag:20000 + j];
        NSArray *allValue = [tb getAllItemValue];
        if (!BCArrayIsEmpty(allValue)) {
          [detailDic setValue:allValue forKey:[itemDic objectNilForKey:@"id"]];
        }
        
      }
      
    }
    
    [contentDic setValue:detailDic forKey:@"details"];
    [contentArr addObject:contentDic];
    
  }
  
  [dic setObject:contentArr forKey:@"content"];
  
  NSDictionary *draftDic = BCStringIsEmpty(self.orgId) ? @{@"type":@"BUSINESS_PLAN",@"info":dic}: @{@"type":@"BUSINESS_PLAN",@"org_id":self.orgId,@"info":dic};
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessdraft.save" Params:draftDic success:^(NSDictionary *successResult) {
    
    [self.statusView showModal:ToastSuccess andTitle:@"已自动保存为草稿"];
    
    
  } failure:^(NSString *errorResult) {
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}

#pragma mark 删除草稿
- (void)deleteDraft{
  __weak typeof(self)weakSelf = self;
  [self.homeScrollView endEditing:YES];
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定删除该草稿?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf didDeleteDraft];
  };
}
- (void)didDeleteDraft{
  
  NSDictionary *draftDic = BCStringIsEmpty(self.orgId) ? @{@"type":@"BUSINESS_PLAN"}: @{@"type":@"BUSINESS_PLAN",@"org_id":self.orgId};
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessdraft.delete" Params:draftDic success:^(NSDictionary *successResult) {
    
    //    [[UIApplication sharedApplication].keyWindow makeToast:@"草稿已删除" duration:1 position:CSToastPositionCenter];
    [self.statusView showModal:ToastSuccess andTitle:@"草稿已删除"];
    //    调整页面
    self.draftData = nil;
    [self.draftView removeFromSuperview];
    self.draftView = nil;
    self.showDraft = NO;
    
    CGFloat top =  self.heightTop + 44  + 44;
    self.homeScrollView.frame = CGRectMake(0, top, BCWidth, BCHeight - top - 90);
    self.mainHeight = BCHeight - top - 90;
    
  } failure:^(NSString *errorResult) {
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}
#pragma mark 打开草稿
- (void)openDraft{
  __weak typeof(self)weakSelf = self;
  [self.homeScrollView endEditing:YES];
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定打开该草稿？" content:@"打开草稿后，当前内容将会被覆盖" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    [weakSelf.loadingView showModal];
    [weakSelf didOpenDraft];
  };
}
- (void)didOpenDraft{
  
  //    处理数据
  dispatch_async(dispatch_get_global_queue(0, 0), ^{
    
    self.buniessId = [NSString stringWithFormat:@"%@",[self.draftData objectForKeyNil:@"id"]];
    
    //    固定单选框不需要校验
    if (!BCStringIsEmpty([self.draftData objectForKeyNil:@"level_name"])) {
      self.levelDic = [NSDictionary dictionaryWithObjectsAndKeys:[self.draftData objectForKeyNil:@"level_name"],@"name",[self.draftData objectForKeyNil:@"level_name"],@"id", nil];
    }
    
    //    商圈范围
    if (!BCArrayIsEmpty([self.draftData objectForKeyNil:@"area_range"])) {
      self.pointArray = [self.draftData objectForKeyNil:@"area_range"];
    }
    
    //    可开店区域
    if (!BCArrayIsEmpty([self.draftData objectForKeyNil:@"store_open_area"])) {
      self.openStoreList = [self.draftData objectForKeyNil:@"store_open_area"];
    }
    
    //        处理图片
    if (!BCArrayIsEmpty([self.draftData objectForKeyNil:@"files"])) {
      
      NSMutableArray *heatNewArr = [NSMutableArray array];
      NSMutableArray *surroundNewArr = [NSMutableArray array];
      
      for (NSDictionary *urlDic in [self.draftData objectForKeyNil:@"files"]) {
        
        if ([[urlDic objectNilForKey:@"ref_sub_type"] isEqualToString:@"businessHotImgIn"] ) {//热力
          [heatNewArr addObject:urlDic];
        } else  if ([[urlDic objectNilForKey:@"ref_sub_type"] isEqualToString:@"businessEnviroment"] ) {//周边
          [surroundNewArr addObject:urlDic];
        }
      }
      
      self.heatArr = heatNewArr;
      self.surroundArr = surroundNewArr;
    }
    
    //        处理变动项
    NSMutableDictionary *valueDic = [NSMutableDictionary dictionaryWithCapacity:100];
    if (!BCArrayIsEmpty([self.draftData objectForKeyNil:@"content"])) {
      for (NSDictionary *dic in [self.draftData objectForKeyNil:@"content"]) {
        [valueDic addEntriesFromDictionary:[dic objectForKeyNil:@"details"]];
      }
      
      self.draftitemChangeDic = valueDic;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
      
      //      下面处理的单选弹窗数据是防止切换组织后，没有对应的选项，所以要先去判断一下
      for (NSDictionary *dic in self.followAlert.dataArray) {
        NSString *oneId = [NSString stringWithFormat:@"%@",[dic objectForKeyNil:@"user_id"]];
        NSString *twoId = [NSString stringWithFormat:@"%@",[self.draftData objectForKeyNil:@"head_id"]];
        if (BCStringIsEmpty(twoId)) {
          break;
        }
        if ([oneId isEqualToString:twoId]) {
          self.followDic = dic;
          break;
        }
      }
      
      for (NSDictionary *dic in self.areaAlert.originArr) {
        NSString *oneId = [NSString stringWithFormat:@"%@",[dic objectForKeyNil:@"code"]];
        NSString *twoId = [NSString stringWithFormat:@"%@",[self.draftData objectForKeyNil:@"area_code"]];
        if (BCStringIsEmpty(twoId)) {
          break;
        }
        
        if ([oneId isEqualToString:twoId]) {
          self.areaDic = [NSDictionary dictionaryWithObjectsAndKeys:[self.draftData objectForKeyNil:@"area_name"],@"areaName",[self.draftData objectForKeyNil:@"area_code"],@"areaCode", nil];
          break;
        }
      }
      
      for (NSDictionary *dic in self.mainLabelAlert.dataArray) {
        NSString *oneId = [NSString stringWithFormat:@"%@",[dic objectForKeyNil:@"id"]];
        NSString *twoId = [NSString stringWithFormat:@"%@",[self.draftData objectForKeyNil:@"main_label_id"]];
        if (BCStringIsEmpty(twoId)) {
          break;
        }
        if ([oneId isEqualToString:twoId]) {
          self.mainLableDic = dic;
          break;
        }
      }
      
      for (NSDictionary *dic in self.subLabelAlert.dataArray) {
        NSString *oneId = [NSString stringWithFormat:@"%@",[dic objectForKeyNil:@"id"]];
        NSString *twoId = [NSString stringWithFormat:@"%@",[self.draftData objectForKeyNil:@"sub_label_id"]];
        if (BCStringIsEmpty(twoId)) {
          break;
        }
        if ([oneId isEqualToString:twoId]) {
          self.subLableDic = dic;
          break;
        }
      }
      
      self.showDraft = YES;
      [self loadMainScrollView];
      
      
      //    移除
      [self.draftView removeFromSuperview];
      self.draftView = nil;
      [self.loadingView hideModal];
    });
    
    
  });
  
}

#pragma mark 滚动主scrollview
- (void)loadMainScrollView{
  
  if (self.mapView) {
    _mapView.showsUserLocation = NO;
    [_mapView.layer removeAllAnimations];
    [_mapView removeAnnotations:_mapView.annotations];
    [_mapView removeOverlays:_mapView.overlays];
    [_mapView removeFromSuperview];
    _mapView.delegate = nil;
    _mapView = nil;
  }
  
  [self.homeScrollView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
  CGFloat top = self.showDraft ? self.heightTop + 44 + 44 : !BCDictIsEmpty(self.draftData) ? self.heightTop + 44 + 42 + 44 : self.heightTop + 44  + 44;
  
  self.homeScrollView.frame = CGRectMake(0, top, BCWidth, BCHeight - top - 90);
  [self.view addSubview:self.homeScrollView];
  
  self.mainHeight = BCHeight - top - 90;
  
  //  基本信息
  [self loadBasicInfoView];
  
  if (self.openStoreDisplay) {
    [self loadOpenStoreList];
  }
  //  变动项
  [self loadChangeItemView];
  
  
}

#pragma mark 基本信息
- (void)loadBasicInfoView{
  
  __weak typeof(self)weakSelf = self;
  self.basicView = [[UIView alloc] init];
  self.basicView.backgroundColor = [UIColor whiteColor];
  [self.homeScrollView addSubview:self.basicView];
  [self.basicView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.left.mas_equalTo(0);
    make.right.equalTo(self.homeScrollView);
    make.width.equalTo(self.homeScrollView);
  }];
  
  UILabel *nameLabel = [[UILabel alloc] init];
  nameLabel.frame = CGRectMake(16,0,100,50);
  nameLabel.textColor = COLOR(29, 33, 41);
  nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  nameLabel.text = @"基本信息";
  [self.basicView addSubview:nameLabel];
  
  //    分割线
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(16,49, BCWidth - 16, 1)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [self.basicView addSubview:lineV];
  
  NSArray *titleArr = @[@"商圈名称",@"所属组织",@"跟进人",@"开发进度",@"商圈区域",@"商圈等级",@"商圈主标签",@"商圈副标签",@"可开门店数",@"同行品牌"];
  
  UIView *lastV;
  for (int i = 0; i < titleArr.count; i++) {
    
    UILabel *leftL = [[UILabel alloc] init];
    leftL.text = [titleArr objectAtIndex:i];
    leftL.font = [UIFont systemFontOfSize:MutilFont(15)];
    if ([[titleArr objectAtIndex:i] isEqualToString:@"所属组织"] || [[titleArr objectAtIndex:i] isEqualToString:@"开发进度"]) {
      leftL.textColor = COLOR(134, 144, 156);
    } else {
      leftL.textColor = COLOR(31, 33, 38);
    }
    
    [self.basicView addSubview:leftL];
    [leftL mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(16);
      make.height.mas_equalTo(50);
      make.width.mas_equalTo(120);
      make.top.equalTo(lastV ? lastV.mas_bottom : @50);
    }];
    
    UILabel *startL = [[UILabel alloc] init];
    startL.text = @"*";
    startL.font = [UIFont systemFontOfSize:MutilFont(15)];
    startL.textColor = COLOR(255, 33, 33);
    [self.basicView addSubview:startL];
    [startL mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(8);
      make.top.mas_equalTo(leftL.mas_top);
      make.width.mas_equalTo(8);
      make.height.mas_equalTo(50);
    }];
    
    if (i == 0) {//名称
      _nameTF = [[UITextField alloc] init];
      _nameTF.placeholder = @"请输入";
      _nameTF.returnKeyType = UIReturnKeyDone;
      _nameTF.textColor = COLOR(31, 33, 38);
      _nameTF.delegate = self;
      _nameTF.textAlignment = NSTextAlignmentRight;
      _nameTF.font = [UIFont systemFontOfSize:MutilFont(15)];
      _nameTF.inputAccessoryView = [self returnDone];
      [self.basicView addSubview:_nameTF];
      [_nameTF  mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(leftL.mas_right).offset(6);
        make.top.mas_equalTo(leftL.mas_top);
        make.right.mas_equalTo(-16);
        make.height.mas_equalTo(50);
      }];
      
      if (self.showDraft && !BCStringIsEmpty([self.draftData objectForKeyNil:@"name"])) {
        _nameTF.text = [NSString stringWithFormat:@"%@",[self.draftData objectForKeyNil:@"name"]];
      }
      
      NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_business_name"];
      if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"0"]) {
        startL.hidden = YES;
      }
      
      if ([[tmpDic objectNilForKey:@"display"] isEqualToString:@"0"]) {
        [_nameTF mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        leftL.hidden = YES;
      }
      
    } else if (i == 1){//组织
      
      UILabel *orgL = [[UILabel alloc] init];
      orgL.font = [UIFont systemFontOfSize:MutilFont(15)];
      orgL.textAlignment = NSTextAlignmentRight;
      orgL.numberOfLines = 0;
      orgL.textColor = COLOR(134, 144, 156);
      [self.basicView addSubview:orgL];
      [orgL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(leftL.mas_right).offset(6);
        make.top.mas_equalTo(leftL.mas_top);
        make.right.mas_equalTo(-16);
        make.height.mas_equalTo(50);
      }];
      
      NSString *name = [NSString stringWithFormat:@"%@",self.orgName];
      if (BCStringIsEmpty(name)) {
        orgL.text = @"-";
      } else {
        orgL.text = name;
      }
      
      NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_org_name"];
      if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"0"]) {
        startL.hidden = YES;
      }
      if ([[tmpDic objectNilForKey:@"display"] isEqualToString:@"0"]) {
        [orgL mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        leftL.hidden = YES;
      }
    } else if (i == 2){//跟进人
      
      UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
      self.followBtn = switchMap;
      [self.basicView addSubview:switchMap];
      [switchMap addTarget:self action:@selector(chooseFollow:) forControlEvents:UIControlEventTouchUpInside];
      [switchMap mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(leftL.mas_right).offset(6);
        make.top.mas_equalTo(leftL.mas_top);
        make.right.mas_equalTo(0);
        make.height.mas_greaterThanOrEqualTo(50);
      }];
      
      UIImageView *leftIM = [[UIImageView alloc] init];
      leftIM.image = [UIImage imageNamed:@"icon_single"];
      [switchMap addSubview:leftIM];
      [leftIM mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(17);
        make.height.mas_equalTo(18);
        make.centerY.mas_equalTo(switchMap.mas_centerY);
        make.right.mas_equalTo(-13);
      }];
      
      UILabel *switchlabel = [[UILabel alloc] init];
      switchlabel.tag = 111;
      switchlabel.textAlignment = NSTextAlignmentRight;
      switchlabel.text = @"请选择";
      switchlabel.textColor = ACOLOR(30, 33, 38, 0.25);
      switchlabel.font = [UIFont systemFontOfSize:MutilFont(15)];
      [switchMap addSubview:switchlabel];
      [switchlabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.mas_equalTo(0);
        make.height.mas_equalTo(50);
        make.right.mas_equalTo(-33);
      }];
      
      if (self.showDraft && !BCDictIsEmpty(self.followDic)) {
        
        switchlabel.text = [NSString stringWithFormat:@"%@",[self.followDic objectForKeyNil:@"name"]];
        switchlabel.textColor = COLOR(31, 33, 38);
      } else {
        
        if (!BCDictIsEmpty(self.followDic)) {
          switchlabel.text = [NSString stringWithFormat:@"%@",[self.followDic objectForKeyNil:@"name"]];
          switchlabel.textColor = COLOR(31, 33, 38);
        }
      }
      
      NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_follow_by"];
      if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"0"]) {
        startL.hidden = YES;
      }
      if ([[tmpDic objectNilForKey:@"display"] isEqualToString:@"0"]) {
        [switchMap mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        [leftIM mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        [switchlabel mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        leftL.hidden = YES;
      }
      
    } else if (i == 3){//开发进度
      
      UILabel *rightL = [[UILabel alloc] init];
      rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
      rightL.textAlignment = NSTextAlignmentRight;
      rightL.numberOfLines = 0;
      rightL.text = @"待开发";
      rightL.textColor = COLOR(134, 144, 156);
      [self.basicView addSubview:rightL];
      [rightL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(leftL.mas_right).offset(6);
        make.top.mas_equalTo(leftL.mas_top);
        make.height.mas_equalTo(50);
        make.right.mas_equalTo(-16);
        
      }];
      
      NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_schedule_name"];
      if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"0"]) {
        startL.hidden = YES;
      }
      if ([[tmpDic objectNilForKey:@"display"] isEqualToString:@"0"]) {
        [rightL mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        leftL.hidden = YES;
      }
    } else if (i >= 4 && i <= 7){//单选弹窗
      
      UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
      [self.basicView addSubview:switchMap];
      [switchMap addTarget:self action:@selector(clickSingle:) forControlEvents:UIControlEventTouchUpInside];
      [switchMap mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(leftL.mas_right).offset(6);
        make.top.mas_equalTo(leftL.mas_top);
        make.right.mas_equalTo(0);
        make.height.mas_greaterThanOrEqualTo(50);
      }];
      
      UIImageView *leftIM = [[UIImageView alloc] init];
      leftIM.image = [UIImage imageNamed:@"icon_single"];
      [switchMap addSubview:leftIM];
      [leftIM mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(17);
        make.height.mas_equalTo(18);
        make.centerY.mas_equalTo(switchMap.mas_centerY);
        make.right.mas_equalTo(-13);
      }];
      
      UILabel *switchlabel = [[UILabel alloc] init];
      switchlabel.tag = 111;
      switchlabel.textAlignment = NSTextAlignmentRight;
      switchlabel.text = @"请选择";
      switchlabel.textColor = ACOLOR(30, 33, 38, 0.25);
      switchlabel.font = [UIFont systemFontOfSize:MutilFont(15)];
      [switchMap addSubview:switchlabel];
      [switchlabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.mas_equalTo(0);
        make.height.mas_equalTo(50);
        make.right.mas_equalTo(-33);
      }];
      
      if (i == 4) {//商圈区域
        self.buniessAreaBtn = switchMap;
        if (self.showDraft && !BCDictIsEmpty(self.areaDic)) {
          switchlabel.text = [NSString stringWithFormat:@"%@",[self.areaDic objectForKeyNil:@"areaName"]];
          switchlabel.textColor = COLOR(31, 33, 38);
        }
        
        switchMap.tag = 334;
        NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_area_range"];
        if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"0"]) {
          startL.hidden = YES;
        }
        if ([[tmpDic objectNilForKey:@"display"] isEqualToString:@"0"]) {
          [switchMap mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          [leftIM mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          [switchlabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          leftL.hidden = YES;
        }
        
      } else if (i == 5){//商圈等级
        
        self.buniessLevelBtn = switchMap;
        if (self.showDraft && !BCDictIsEmpty(self.levelDic)) {
          switchlabel.text = [NSString stringWithFormat:@"%@",[self.levelDic objectForKeyNil:@"name"]];
          switchlabel.textColor = COLOR(31, 33, 38);
        }
        
        switchMap.tag = 335;
        NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_level_name"];
        if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"0"]) {
          startL.hidden = YES;
        }
        if ([[tmpDic objectNilForKey:@"display"] isEqualToString:@"0"]) {
          [switchMap mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          [leftIM mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          [switchlabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          leftL.hidden = YES;
        }
      } else if (i == 6){//商圈主标签
        
        self.mainTypeBtn = switchMap;
        if (self.showDraft && !BCDictIsEmpty(self.mainLableDic)) {
          switchlabel.text = [NSString stringWithFormat:@"%@",[self.mainLableDic objectForKeyNil:@"name"]];
          switchlabel.textColor = COLOR(31, 33, 38);
        }
        
        switchMap.tag = 336;
        NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_main_label"];
        if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"0"]) {
          startL.hidden = YES;
        }
        if ([[tmpDic objectNilForKey:@"display"] isEqualToString:@"0"]) {
          [switchMap mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          [leftIM mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          [switchlabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          leftL.hidden = YES;
        }
      } else if (i == 7){//商圈副标签
        
        self.subTypeBtn = switchMap;
        if (self.showDraft && !BCDictIsEmpty(self.subLableDic)) {
          switchlabel.text = [NSString stringWithFormat:@"%@",[self.subLableDic objectForKeyNil:@"name"]];
          switchlabel.textColor = COLOR(31, 33, 38);
        }
        
        switchMap.tag = 337;
        NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_sub_label"];
        if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"0"]) {
          startL.hidden = YES;
        }
        if ([[tmpDic objectNilForKey:@"display"] isEqualToString:@"0"]) {
          [switchMap mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          [leftIM mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          [switchlabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
          leftL.hidden = YES;
        }
        
      }
      
    } else if (i == 8){//可开门店数
      _openStoreTF = [[UITextField alloc] init];
      _openStoreTF.placeholder = @"请输入";
      _openStoreTF.returnKeyType = UIReturnKeyDone;
      _openStoreTF.keyboardType = UIKeyboardTypeNumberPad;
      _openStoreTF.textColor = COLOR(31, 33, 38);
      _openStoreTF.delegate = self;
      _openStoreTF.textAlignment = NSTextAlignmentRight;
      _openStoreTF.font = [UIFont systemFontOfSize:MutilFont(15)];
      _openStoreTF.inputAccessoryView = [self returnDone];
      [self.basicView addSubview:_openStoreTF];
      [_openStoreTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(leftL.mas_right).offset(6);
        make.top.mas_equalTo(leftL.mas_top);
        make.right.mas_equalTo(-16);
        make.height.mas_equalTo(50);
      }];
      
      if (self.showDraft && !BCStringIsEmpty([self.draftData objectForKeyNil:@"open_store"])) {
        _openStoreTF.text = [NSString stringWithFormat:@"%@",[self.draftData objectForKeyNil:@"open_store"]];
      }
      
      NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_open_store"];
      if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"0"]) {
        startL.hidden = YES;
      }
      
      if ([[tmpDic objectNilForKey:@"display"] isEqualToString:@"0"]) {
        [ _openStoreTF mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        leftL.hidden = YES;
      }
      
    } else if (i == 9){//同行品牌
      _competitorTF = [[UITextField alloc] init];
      _competitorTF.placeholder = @"请输入";
      _competitorTF.returnKeyType = UIReturnKeyDone;
      _competitorTF.textColor = COLOR(31, 33, 38);
      _competitorTF.delegate = self;
      _competitorTF.textAlignment = NSTextAlignmentRight;
      _competitorTF.font = [UIFont systemFontOfSize:MutilFont(15)];
      _competitorTF.inputAccessoryView = [self returnDone];
      [self.basicView addSubview:_competitorTF];
      [_competitorTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(leftL.mas_right).offset(6);
        make.top.mas_equalTo(leftL.mas_top);
        make.right.mas_equalTo(-16);
        make.height.mas_equalTo(50);
      }];
      
      if (self.showDraft && !BCStringIsEmpty([self.draftData objectForKeyNil:@"competitor"])) {
        _competitorTF.text = [NSString stringWithFormat:@"%@",[self.draftData objectForKeyNil:@"competitor"]];
      }
      
      NSDictionary *tmpDic = [self.requiredDic objectForKeyNil:@"basic_info_competitor"];
      if ([[tmpDic objectNilForKey:@"required"] isEqualToString:@"0"]) {
        startL.hidden = YES;
      }
      
      if ([[tmpDic objectNilForKey:@"display"] isEqualToString:@"0"]) {
        [_competitorTF mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        leftL.hidden = YES;
      }
      
    }
    
    
    //    分割线
    if (leftL.hidden) {//如果该项被隐藏了
      continue;
    }
    
    //    分割线
    UIView *lineV1 = [[UIView alloc] init];
    lineV1.backgroundColor = ACOLOR(31, 33, 38, 0.10);
    [self.basicView addSubview:lineV1];
    [lineV1 mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(16);
      make.width.mas_equalTo(BCWidth - 16);
      make.height.mas_equalTo(1);
      make.top.mas_equalTo(leftL.mas_bottom).offset(-0.5);
    }];
    lastV = lineV1;
    
  }
  
  //  商品热力图
  UILabel *headL = [[UILabel alloc] init];
  headL.text = @"商圈热力图";
  headL.font = [UIFont systemFontOfSize:MutilFont(15)];
  headL.textColor = COLOR(31, 33, 38);
  [self.basicView addSubview:headL];
  [headL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(16);
    make.top.mas_equalTo(lastV.mas_bottom).offset(14);
    make.width.mas_equalTo(120);
    make.height.mas_equalTo(21);
  }];
  
  UILabel *headSL = [[UILabel alloc] init];
  headSL.text = @"*";
  headSL.font = [UIFont systemFontOfSize:MutilFont(15)];
  headSL.textColor = COLOR(255, 33, 33);
  [self.basicView addSubview:headSL];
  [headSL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(8);
    make.top.mas_equalTo(headL.mas_top);
    make.width.mas_equalTo(8);
    make.height.mas_equalTo(21);
  }];
  
  self.heatUploadView = [[MAUploadMutilImageView alloc] initWithFrame:CGRectMake(0, 0, BCWidth - 32,self.heatDisplay ? (BCWidth - 32 - 24) / 4.0 : 0) withUrl:@"kms/hxl.kms.businessplan.file.upload" withMaxCount:20 withUploadType:UploadTypeOnlyPhoto withParams:@{@"fid":self.buniessId,@"fileType":@"businessHotImgIn"} endUploadUrl:self.heatArr enable:NO];
  [self.basicView addSubview:self.heatUploadView];
  [self.heatUploadView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(16);
    make.top.mas_equalTo(headL.mas_bottom).offset(8);
    make.width.mas_equalTo(BCWidth - 32);
    
    if (BCArrayIsEmpty(self.heatArr)) {
      make.height.mas_equalTo((BCWidth - 32 - 24) / 4.0);
    } else {
      
      CGFloat itemH = (BCWidth - 32 - 24) / 4.0 + 8;
      CGFloat editHeight = (BCWidth - 32 - 24) / 4.0;
      
      if (self.heatArr.count >= 20) {
        if (self.heatArr.count / 4 == 0) {//就一行
          
        } else {
          editHeight = self.heatArr.count / 4  * itemH - 8;
        }
      } else {
        
        if ((self.heatArr.count + 1) % 4 == 0) {//一行正好放得下
          editHeight = ((self.heatArr.count + 1) / 4 ) * (itemH) - 8;
        } else {
          editHeight = ((self.heatArr.count + 1) / 4  + 1) * (itemH ) - 8;
        }
      }
      
      make.height.mas_equalTo(editHeight);
    }
  }];
  
  self.heatUploadView.chooseBlock = ^(CGFloat bottom) {
    [weakSelf.heatUploadView mas_updateConstraints:^(MASConstraintMaker *make) {
      make.height.mas_equalTo(bottom);
    }];
  };
  self.heatUploadView.uploadDataBlock = ^(NSArray *uploadData) {
    weakSelf.heatArr = uploadData;
  };
  
  UIView *roomLineV = [[UIView alloc] init];
  roomLineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [self.basicView addSubview:roomLineV];
  [roomLineV mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(16);
    make.top.mas_equalTo(self.heatUploadView.mas_bottom).offset(14);
    make.width.mas_equalTo(BCWidth - 16);
    make.height.mas_equalTo(1);
  }];
  
  //  如果热力图非必填
  if (!self.heatRequired) {
    headSL.hidden = YES;
  }
  
  
  //  如如果商圈热力图不显示
  if (!self.heatDisplay) {
    [headL mas_updateConstraints:^(MASConstraintMaker *make) {
      make.height.mas_equalTo(0);
      make.top.mas_equalTo(lastV.mas_bottom).offset(0);
    }];
    [headSL mas_updateConstraints:^(MASConstraintMaker *make) {
      make.height.mas_equalTo(0);
    }];
    [self.heatUploadView mas_updateConstraints:^(MASConstraintMaker *make) {
      make.height.mas_equalTo(0);
      make.top.mas_equalTo(headL.mas_bottom).offset(0);
    }];
    [roomLineV mas_updateConstraints:^(MASConstraintMaker *make) {
      make.height.mas_equalTo(0);
      make.top.mas_equalTo(self.heatUploadView.mas_bottom).offset(0);
    }];
  }
  
  //  周边环境
  UILabel *surroundL = [[UILabel alloc] init];
  surroundL.text = @"商圈环境";
  surroundL.font = [UIFont systemFontOfSize:MutilFont(15)];
  surroundL.textColor = COLOR(31, 33, 38);
  [self.basicView addSubview:surroundL];
  [surroundL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(16);
    make.top.mas_equalTo(roomLineV.mas_bottom).offset(14);
    make.width.mas_equalTo(120);
    make.height.mas_equalTo(21);
  }];
  
  UILabel *surroundSL = [[UILabel alloc] init];
  surroundSL.text = @"*";
  surroundSL.font = [UIFont systemFontOfSize:MutilFont(15)];
  surroundSL.textColor = COLOR(255, 33, 33);
  [self.basicView addSubview:surroundSL];
  [surroundSL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(8);
    make.top.mas_equalTo(surroundL.mas_top);
    make.width.mas_equalTo(8);
    make.height.mas_equalTo(21);
  }];
  
  
  self.surroundUploadView = [[MAUploadMutilImageView alloc] initWithFrame:CGRectMake(0, 0, BCWidth - 32,self.surroundDisplay ? (BCWidth - 32 - 24) / 4.0 : 0) withUrl:@"kms/hxl.kms.businessplan.file.upload" withMaxCount:20 withUploadType:UploadTypePhotoWithVideo withParams:@{@"fid":self.buniessId,@"fileType":@"businessEnviroment"} endUploadUrl:self.surroundArr enable:NO];
  [self.basicView addSubview:self.surroundUploadView];
  [self.surroundUploadView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(16);
    make.top.mas_equalTo(surroundL.mas_bottom).offset(8);
    make.width.mas_equalTo(BCWidth - 32);
    
    if (BCArrayIsEmpty(self.surroundArr)) {
      make.height.mas_equalTo((BCWidth - 32 - 24) / 4.0);
    } else {
      
      CGFloat itemH = (BCWidth - 32 - 24) / 4.0 + 8;
      CGFloat editHeight = (BCWidth - 32 - 24) / 4.0;
      
      if (self.surroundArr.count >= 20) {
        if (self.surroundArr.count / 4 == 0) {//就一行
          
        } else {
          editHeight = self.surroundArr.count / 4  * itemH - 8;
        }
      } else {
        
        if ((self.surroundArr.count + 1) % 4 == 0) {//一行正好放得下
          editHeight = ((self.surroundArr.count + 1) / 4 ) * (itemH) - 8;
        } else {
          editHeight = ((self.surroundArr.count + 1) / 4  + 1) * (itemH ) - 8;
        }
      }
      
      make.height.mas_equalTo(editHeight);
    }
  }];
  
  self.surroundUploadView.chooseBlock = ^(CGFloat bottom) {
    [weakSelf.surroundUploadView mas_updateConstraints:^(MASConstraintMaker *make) {
      make.height.mas_equalTo(bottom);
    }];
  };
  
  self.surroundUploadView.uploadDataBlock = ^(NSArray *uploadData) {
    weakSelf.surroundArr = uploadData;
  };
  
  UIView *surroundLineV = [[UIView alloc] init];
  surroundLineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [self.basicView addSubview:surroundLineV];
  [surroundLineV mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(16);
    make.top.mas_equalTo(self.surroundUploadView.mas_bottom).offset(14);
    make.width.mas_equalTo(BCWidth - 16);
    make.height.mas_equalTo(1);
  }];
  
  //  如果商圈环境非必填
  if (!self.surroundRequired) {
    surroundSL.hidden = YES;
  }
  //  如果商圈环境不显示
  if (!self.surroundDisplay) {
    [surroundL mas_updateConstraints:^(MASConstraintMaker *make) {
      make.height.mas_equalTo(0);
      make.top.mas_equalTo(roomLineV.mas_bottom).offset(0);
    }];
    [surroundSL mas_updateConstraints:^(MASConstraintMaker *make) {
      make.height.mas_equalTo(0);
    }];
    [self.surroundUploadView mas_updateConstraints:^(MASConstraintMaker *make) {
      make.height.mas_equalTo(0);
      make.top.mas_equalTo(surroundL.mas_bottom).offset(0);
    }];
    [surroundLineV mas_updateConstraints:^(MASConstraintMaker *make) {
      make.height.mas_equalTo(0);
      make.top.mas_equalTo(self.surroundUploadView.mas_bottom).offset(0);
    }];
    
  }
  
  //  地图
  UILabel *inRoomL = [[UILabel alloc] init];
  inRoomL.text = @"商圈范围";
  inRoomL.font = [UIFont systemFontOfSize:MutilFont(15)];
  inRoomL.textColor = COLOR(31, 33, 38);
  [self.basicView addSubview:inRoomL];
  [inRoomL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(16);
    make.top.mas_equalTo(surroundLineV.mas_bottom).offset(14);
    make.width.mas_equalTo(120);
    make.height.mas_equalTo(21);
  }];
  
  UILabel *inRoomSL = [[UILabel alloc] initWithFrame:CGRectMake(8, inRoomL.top , 8, 21)];
  inRoomSL.text = @"*";
  inRoomSL.font = [UIFont systemFontOfSize:MutilFont(15)];
  inRoomSL.textColor = COLOR(255, 33, 33);
  [self.basicView addSubview:inRoomSL];
  [inRoomSL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(8);
    make.top.mas_equalTo(inRoomL.mas_top);
    make.width.mas_equalTo(8);
    make.height.mas_equalTo(21);
  }];
  
  UIButton *newButton = [[UIButton alloc] init];
  newButton.layer.cornerRadius = 4;
  newButton.clipsToBounds = YES;
  [newButton addTarget:self action:@selector(clickEditDraw) forControlEvents:UIControlEventTouchUpInside];
  [self.basicView addSubview:newButton];
  [newButton mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.mas_equalTo(inRoomL.mas_bottom).offset(10);
    make.left.mas_equalTo(16);
    make.right.mas_equalTo(-16);
    make.height.mas_equalTo(RealSize(200));
    make.bottom.equalTo(self.basicView).offset(-14);
  }];
  
  _mapView = [[MAMapView alloc] init];
  _mapView.rotateCameraEnabled = NO;
  _mapView.zoomEnabled = NO;
  _mapView.showsScale = NO;
  _mapView.scrollEnabled = NO;
  _mapView.showsCompass = NO;
  _mapView.delegate = self;
  _mapView.rotateEnabled = NO;
  _mapView.mapType = MAMapTypeStandard;
  _mapView.zoomingInPivotsAroundAnchorPoint = YES;
  _mapView.customizeUserLocationAccuracyCircleRepresentation = YES;
  _mapView.userTrackingMode = MAUserTrackingModeFollow;
  _mapView.zoomLevel = 15.5;
  _mapView.userInteractionEnabled = NO;
  _mapView.showsUserLocation = NO;
  [newButton addSubview:self.mapView];
  [self.mapView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.top.height.width.mas_equalTo(newButton);
  }];
  
  
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    CLLocationCoordinate2D commonPolylineCoords[self.pointArray.count];
    for (int i = 0; i < self.pointArray.count; i ++) {
      
      NSDictionary *dic = [self.pointArray objectAtIndexCheck:i];
      CLLocationCoordinate2D coor = CLLocationCoordinate2DMake([[dic objectForKeyNil:@"latitude"] floatValue], [[dic objectForKeyNil:@"longitude"] floatValue]);
      commonPolylineCoords[i] = coor;
    }
    
    self.creatPolygon = [MACreatBuniessPolygon polygonWithCoordinates:commonPolylineCoords count:self.pointArray.count];
    [self.mapView addOverlay:self.creatPolygon];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self.mapView showOverlays:@[self.creatPolygon] edgePadding:UIEdgeInsetsMake(40, 40, 40, 40) animated:NO];
    });
    
    
    //    加载已经生成的可开店区域
    [self.mapView removeAnnotations:self.mapView.annotations];
    
    for (int i = 0; i < self.openStoreList.count; i ++) {
      NSDictionary *itemDic = [self.openStoreList objectAtIndexCheck:i];
      if (![[itemDic objectNilForKey:@"draw_type"] isEqualToString:@"DRAW_POINT"]) {
        continue;
      }
      
      NSArray *areaRange = [itemDic objectForKeyNil:@"area_range"];
      if (!BCArrayIsEmpty(areaRange)) {
        NSDictionary *dic = [areaRange firstObject];
        CLLocationCoordinate2D currentCoordinate = CLLocationCoordinate2DMake([[dic objectForKeyNil:@"latitude"] floatValue], [[dic objectForKeyNil:@"longitude"] floatValue]);
        MALocalAnnotation *addPointAnn = [[MALocalAnnotation alloc] init];
        addPointAnn.coordinate = currentCoordinate;
        addPointAnn.pointName = [itemDic objectForKeyNil:@"store_open_area_name"];
        addPointAnn.pointLevel = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"priority"]];
        addPointAnn.pointType = MarkerOpenStorePointType;
        [self.mapView addAnnotation:addPointAnn];
      }
      
    }
    
    
  });
  
}

#pragma mark 加载可开店区域
- (void)loadOpenStoreList{
  
  __weak typeof(self)weakSelf = self;
  self.openStoreView = [[UIView alloc] init];
  self.openStoreView.backgroundColor = [UIColor whiteColor];
  [self.homeScrollView addSubview:self.openStoreView];
  [self.openStoreView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(0);
    make.top.mas_equalTo(self.basicView.mas_bottom).offset(12);
    make.height.mas_greaterThanOrEqualTo(50);
    make.right.equalTo(self.homeScrollView);
    make.width.equalTo(self.homeScrollView);
  }];
  
  UILabel *openStoreL = [[UILabel alloc] init];
  openStoreL.text = @"可开店区域";
  openStoreL.font = [UIFont systemFontOfSize:MutilFont(15) weight:UIFontWeightMedium];
  openStoreL.textColor = COLOR(31, 33, 38);
  [self.openStoreView addSubview:openStoreL];
  [openStoreL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(16);
    make.top.mas_equalTo(0);
    make.width.mas_equalTo(120);
    make.height.mas_equalTo(50);
  }];
  
  if (self.openStoreRequired) {
    UILabel *openStoreSL = [[UILabel alloc] init];
    openStoreSL.text = @"*";
    openStoreSL.font = [UIFont systemFontOfSize:MutilFont(15)];
    openStoreSL.textColor = COLOR(255, 33, 33);
    [self.openStoreView addSubview:openStoreSL];
    [openStoreSL mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(8);
      make.top.mas_equalTo(0);
      make.width.mas_equalTo(8);
      make.height.mas_equalTo(50);
    }];
  }
  
  UIView *openStoreLineV = [[UIView alloc] init];
  openStoreLineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [self.openStoreView addSubview: openStoreLineV];
  [openStoreLineV mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(16);
    make.top.mas_equalTo(49);
    make.width.mas_equalTo(BCWidth - 16);
    make.height.mas_equalTo(1);
  }];
  
  //  添加按钮
  UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
  [self.openStoreView addSubview:switchMap];
  [switchMap addTarget:self action:@selector(clickAddOpenStore) forControlEvents:UIControlEventTouchUpInside];
  [switchMap mas_makeConstraints:^(MASConstraintMaker *make) {
    make.height.mas_equalTo(50);
    make.top.mas_equalTo(0);
    make.right.mas_equalTo(-16);
    make.width.mas_equalTo(20 + RealSize(62));
  }];
  
  UIImageView *leftIM = [[UIImageView alloc] init];
  leftIM.image = [UIImage imageNamed:@"add_landlord"];
  [switchMap addSubview:leftIM];
  [leftIM mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(0);
    make.centerY.mas_equalTo(switchMap.mas_centerY);
    make.width.height.mas_equalTo(18);
  }];
  
  UILabel *switchlabel = [[UILabel alloc] init];
  switchlabel.text = @"点击添加";
  switchlabel.textColor = COLOR(26, 106, 255);
  switchlabel.font = [UIFont systemFontOfSize:MutilFont(15)];
  [switchMap addSubview:switchlabel];
  [switchlabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(20);
    make.top.mas_equalTo(0);
    make.width.mas_equalTo(RealSize(62));
    make.height.mas_equalTo(50);
  }];
  
  self.openStoreListV = [[MAOpenStoreView alloc] initWithCustomView:CGRectZero];
  [self.openStoreView addSubview:self.openStoreListV];
  [self.openStoreListV mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(0);
    make.top.mas_equalTo(openStoreLineV.mas_bottom).offset(0);
    make.width.mas_equalTo(BCWidth);
    make.height.mas_greaterThanOrEqualTo(0);
    make.bottom.equalTo(self.openStoreView).offset(0);
  }];
  
  self.openStoreListV.clickCell = ^(NSDictionary *data, NSInteger indexRow) {
    [weakSelf clickEditOpenStore:data andIndex:indexRow];
  };
  
  self.openStoreListV.deleteCell = ^(NSInteger indexRow) {
    if (indexRow < weakSelf.openStoreList.count) {
      [weakSelf.openStoreList removeObjectAtIndex:indexRow];
    }
  };
  
  [self.openStoreListV reloadOpenView:self.openStoreList andBuniessState:@"制单"];
  
}


#pragma mark 点击编辑可开店区域
- (void)clickEditOpenStore:(NSDictionary *)dic andIndex:(NSInteger)indexRow{
  
  MAAddOpenStoreViewController *addVC = [[MAAddOpenStoreViewController alloc] init];
  addVC.pointArray = self.pointArray;
  addVC.buniessId = self.buniessId;
  addVC.openStoreArray = self.openStoreList;
  addVC.type = 1;
  addVC.editData = dic;
  addVC.editIndex = indexRow;
  [self.navigationController pushViewController:addVC animated:YES];
  
}


#pragma mark 点击添加可开店区域
- (void)clickAddOpenStore{
  
  MAAddOpenStoreViewController *addVC = [[MAAddOpenStoreViewController alloc] init];
  addVC.pointArray = self.pointArray;
  addVC.buniessId = self.buniessId;
  addVC.openStoreArray = self.openStoreList;
  addVC.type = 0;
  [self.navigationController pushViewController:addVC animated:YES];
}

#pragma mark 地图商圈显示代理
- (MAOverlayRenderer *)mapView:(MAMapView *)mapView rendererForOverlay:(id<MAOverlay>)overlay{
  if ([overlay isKindOfClass:[MACreatBuniessPolygon class]])
  {
    
    MACreatBuniessPolygonRender *polygonRenderer = [[MACreatBuniessPolygonRender alloc] initWithPolygon:overlay];
    polygonRenderer.lineWidth   = 2;
    polygonRenderer.strokeColor = [UIColor colorWithRed:255/255.0 green:33/255.0 blue:33/255.0 alpha:1];
    polygonRenderer.fillColor   = [UIColor colorWithRed:255/255.0 green:33/255.0 blue:33/255.0 alpha:0.2];
    
    return polygonRenderer;
  }
  
  return nil;
}

#pragma mark 地图大头针显示代理
- (MAAnnotationView *)mapView:(MAMapView *)mapView viewForAnnotation:(id<MAAnnotation>)annotation {
  
  if ([annotation isKindOfClass:[MALocalAnnotation class]]) {//自定义本地图小兔子标点
    MALocalAnnotation *pointMarker = (MALocalAnnotation *)annotation;
    static NSString *reuseIndetifier = @"annotationReuseIndetifierLocal";
    MALocalAnnotationView *headAnnotationV = (MALocalAnnotationView *)[mapView dequeueReusableAnnotationViewWithIdentifier:reuseIndetifier];
    if (!headAnnotationV){
      headAnnotationV = [[MALocalAnnotationView alloc] initImageSize:1 withShowLabel:YES withShowTime:NO WithAnnotation:annotation reuseIdentifier:reuseIndetifier];
      headAnnotationV.enabled = NO;
    }
    
   
    if (pointMarker.pointType == MarkerOpenStorePointType) {//可开店区域新建点位
     
      if ([pointMarker.pointLevel isEqualToString:@"1"]) {
        headAnnotationV.pointIM.image = [UIImage imageNamed:@"openstore_top"];
        headAnnotationV.zIndex = 900;
      } else if ([pointMarker.pointLevel isEqualToString:@"2"]) {
        headAnnotationV.zIndex = 800;
        headAnnotationV.pointIM.image = [UIImage imageNamed:@"openstore_mid"];
      } else {
        headAnnotationV.zIndex = 0;
        headAnnotationV.pointIM.image = [UIImage imageNamed:@"openstore_bottom"];
      }
      
      [headAnnotationV resetText:pointMarker.pointName];
      
    }
    
    return headAnnotationV;
    
  }
  
 
  return nil;
}

#pragma mark 点击编辑商圈
- (void)clickEditDraw{
  
  MABuniessEditDrawViewController *editVC = [[MABuniessEditDrawViewController alloc] init];
  editVC.pointArray = self.pointArray;
  editVC.buniessId = self.buniessId;
  editVC.orgId = self.orgId;
  editVC.openStoreArray = self.openStoreList;
  [self.navigationController pushViewController:editVC animated:YES];
}

#pragma mark 点击基本信息右侧单选弹窗按钮
- (void)clickSingle:(UIButton *)sender{
  
  __weak typeof(self)weakSelf = self;
  NSInteger index = sender.tag;
  
  UILabel *rightL = [sender viewWithTag:111];
  
  switch (index) {
    case 334://商圈区域
    {
      [self.areaAlert showModal];
      self.areaAlert.okBlock = ^(NSDictionary *data) {
        NSLog(@"区域数据%@",data);
        rightL.text = [NSString stringWithFormat:@"%@",[data objectForKeyNil:@"areaName"]];
        rightL.textColor = COLOR(31, 33, 38);
        weakSelf.areaDic = data;
      };
      
    }
      break;
    case 335://商圈等级
    {
      [self.levelAlert showModal];
      self.levelAlert.chooseBlock = ^(NSDictionary *chooseDic) {
        rightL.text = [NSString stringWithFormat:@"%@",[chooseDic objectForKeyNil:@"name"]];
        rightL.textColor = COLOR(31, 33, 38);
        weakSelf.levelDic = chooseDic;
      };
    }
      break;
      
    case 336://商圈主标签
    {
      [self.mainLabelAlert showModal];
      self.mainLabelAlert.chooseBlock = ^(NSDictionary *chooseDic) {
        rightL.text = [NSString stringWithFormat:@"%@",[chooseDic objectForKeyNil:@"name"]];
        rightL.textColor = COLOR(31, 33, 38);
        weakSelf.mainLableDic = chooseDic;
      };
    }
      break;
      
    case 337://商圈副标签
    {
      [self.subLabelAlert showModal];
      self.subLabelAlert.chooseBlock = ^(NSDictionary *chooseDic) {
        rightL.text = [NSString stringWithFormat:@"%@",[chooseDic objectForKeyNil:@"name"]];
        rightL.textColor = COLOR(31, 33, 38);
        weakSelf.subLableDic = chooseDic;
      };
    }
      break;
      
    default:
      break;
  }
  
}

#pragma mark 选择跟进人
- (void)chooseFollow:(UIButton *)sender{
  __weak typeof(self)weakSelf = self;
  UILabel *rightL = [sender viewWithTag:111];
  [self.followAlert showModal];
  self.followAlert.chooseBlock = ^(NSDictionary *chooseDic) {
    rightL.text = [NSString stringWithFormat:@"%@",[chooseDic objectForKeyNil:@"name"]];
    rightL.textColor = COLOR(31, 33, 38);
    weakSelf.followDic = chooseDic;
  };
  
}
#pragma mark 检查草稿的值是否满足变动项条件公式
- (BOOL)checkDraftContions:(NSArray *)contions{
  
  if (BCArrayIsEmpty(contions)) {
    return YES;
  }
  
  if (BCDictIsEmpty(self.draftitemChangeDic)) {
    return NO;
  }
  
  
  for (NSDictionary *subDic in contions) {
    NSArray *conditions = [subDic objectForKeyNil:@"conditions"];
    if (BCArrayIsEmpty(conditions)) {
      continue;
    }
    
    NSMutableArray *sult = [NSMutableArray array];
    for (NSDictionary *lastDic in conditions) {
      NSString *itemValue = [NSString stringWithFormat:@"%@",[self.draftitemChangeDic objectForKeyNil:[lastDic objectForKeyNil:@"field"]]];//找到已保存字典的条件中的每个值
      
      NSString *itemOperator = [NSString stringWithFormat:@"%@",[lastDic objectForKeyNil:@"operator"]];
      NSString *operatorValue;
      if ([[lastDic objectForKeyNil:@"value"] isKindOfClass:[NSArray class]]) {
        NSArray *myarr = [lastDic objectForKeyNil:@"value"];
        operatorValue = [myarr componentsJoinedByString:@","];
      } else {
        operatorValue = [NSString stringWithFormat:@"%@",[lastDic objectForKeyNil:@"value"]];
      }
      
      if ([itemOperator isEqualToString:@"EQUALS"] ) {//相等
        if ([itemValue isEqualToString:operatorValue]) {
          [sult addObject:@"1"];
        } else {
          [sult addObject:@"0"];
        }
        
        
      } else  if ([itemOperator isEqualToString:@"NOT_EQUALS"]) {//不等
        if (![itemValue isEqualToString:operatorValue]) {
          [sult addObject:@"1"];
        } else {
          [sult addObject:@"0"];
        }
        
      } else if ([itemOperator isEqualToString:@"CONTAINS"]) {//包含
        if ([itemValue containsString:operatorValue]) {
          [sult addObject:@"1"];
        } else {
          [sult addObject:@"0"];
        }
      } else if ([itemOperator isEqualToString:@"NOT_CONTAINS"]) {//不包含
        if (![itemValue containsString:operatorValue]) {
          [sult addObject:@"1"];
        } else {
          [sult addObject:@"0"];
        }
        
      } else if ([itemOperator isEqualToString:@"IS_NULL"]){//为空
        
        if (BCStringIsEmpty(itemValue)) {
          [sult addObject:@"1"];
        } else {
          [sult addObject:@"0"];
        }
        
      } else if ([itemOperator isEqualToString:@"NOT_NULL"]){//不为空
        
        if (!BCStringIsEmpty(itemValue)) {
          [sult addObject:@"1"];
        } else {
          [sult addObject:@"0"];
        }
        
      } else if ([itemOperator isEqualToString:@"GREATER_THAN"]){//大于
        
        if ([itemValue integerValue] > [operatorValue integerValue]) {
          [sult addObject:@"1"];
        } else {
          [sult addObject:@"0"];
        }
        
      } else if ([itemOperator isEqualToString:@"GREATER_THAN_OR_EQUALS"]){//大于等于
        if ([itemValue integerValue] >= [operatorValue integerValue]) {
          [sult addObject:@"1"];
        } else {
          [sult addObject:@"0"];
        }
      } else if ([itemOperator isEqualToString:@"LESS_THAN"]){//小于
        if ([itemValue integerValue] < [operatorValue integerValue]) {
          [sult addObject:@"1"];
        } else {
          [sult addObject:@"0"];
        }
      } else if ([itemOperator isEqualToString:@"LESS_THAN_OR_EQUALS"]){//小于等于
        if ([itemValue integerValue] <= [operatorValue integerValue]) {
          [sult addObject:@"1"];
        } else {
          [sult addObject:@"0"];
        }
      } else if ([itemOperator isEqualToString:@"IN"]){//属于
        if ([operatorValue containsString:itemValue]) {
          [sult addObject:@"1"];
        } else {
          [sult addObject:@"0"];
        }
      } else if ([itemOperator isEqualToString:@"NOT_IN"]){//属于
        if (![operatorValue containsString:itemValue]) {
          [sult addObject:@"1"];
        } else {
          [sult addObject:@"0"];
        }
      }
    }
    NSLog(@"测试的值%@",sult);
    if ([sult containsObject:@"0"]) {//代表校验未通过,继续校验
      
    } else {//校验通过
      
      return YES;
      
    }
  }
  
  
  
  return NO;
}

#pragma mark 检查变动项的值是否满足条件公式
- (void)checkItemContions:(NSString *)itemId{
  
  DDLog(@"===变动项条件值===%@",self.changeItemDictionary);
  DDLog(@"===变动项条件数组===%@",self.changeItemContions);
  
  if (BCArrayIsEmpty(self.changeItemContions)) {
    return;
  }
  //  找出符合条件的
  dispatch_async(dispatch_get_global_queue(0, 0), ^{
    NSMutableArray *parentArr = [NSMutableArray array];
    BOOL isSatisfy = NO;
    for (int i = 0; i < self.changeItemContions.count; i ++) {
      NSDictionary *dic = [self.changeItemContions objectAtIndexCheck:i];
      
      NSArray *subArr = [dic objectForKeyNil:@"subId"];
      if (BCArrayIsEmpty(subArr)) {
        continue;
      }
      
      if ([subArr containsObject:itemId]) {//如果变动项条件数组其中一项包含了当前输入的id，则进入判断
        [parentArr addObject:@{@"parentTag":[dic objectNilForKey:@"parentTag"],@"itemH":[dic objectForKeyNil:@"itemH"],@"show":@"0",@"superTag":[dic objectNilForKey:@"superTag"]}];
        
        NSArray *conditionGroups = [dic objectForKeyNil:@"conditionGroups"];//条件数组
        if (BCArrayIsEmpty(conditionGroups)) {
          continue;
        }
        
        for (NSDictionary *subDic in conditionGroups) {
          NSArray *conditions = [subDic objectForKeyNil:@"conditions"];
          if (BCArrayIsEmpty(conditions)) {
            continue;
          }
          
          NSMutableArray *sult = [NSMutableArray array];
          for (NSDictionary *lastDic in conditions) {
            NSString *itemValue = [NSString stringWithFormat:@"%@",[self.changeItemDictionary objectForKeyNil:[lastDic objectForKeyNil:@"field"]]];//找到已保存字典的条件中的每个值
            
            NSString *itemOperator = [NSString stringWithFormat:@"%@",[lastDic objectForKeyNil:@"operator"]];
            NSString *operatorValue;
            if ([[lastDic objectForKeyNil:@"value"] isKindOfClass:[NSArray class]]) {
              NSArray *myarr = [lastDic objectForKeyNil:@"value"];
              operatorValue = [myarr componentsJoinedByString:@","];
            } else {
              operatorValue = [NSString stringWithFormat:@"%@",[lastDic objectForKeyNil:@"value"]];
            }
            
            if ([itemOperator isEqualToString:@"EQUALS"] ) {//相等
              if ([itemValue isEqualToString:operatorValue]) {
                [sult addObject:@"1"];
              } else {
                [sult addObject:@"0"];
              }
              
              
            } else  if ([itemOperator isEqualToString:@"NOT_EQUALS"]) {//不等
              if (![itemValue isEqualToString:operatorValue]) {
                [sult addObject:@"1"];
              } else {
                [sult addObject:@"0"];
              }
              
            } else if ([itemOperator isEqualToString:@"CONTAINS"]) {//包含
              if (!BCStringIsEmpty(itemValue) && [itemValue containsString:operatorValue]) {
                [sult addObject:@"1"];
              } else {
                [sult addObject:@"0"];
              }
            } else if ([itemOperator isEqualToString:@"NOT_CONTAINS"]) {//不包含
              if (!BCStringIsEmpty(itemValue) && ![itemValue containsString:operatorValue]) {
                [sult addObject:@"1"];
              } else {
                [sult addObject:@"0"];
              }
              
            } else if ([itemOperator isEqualToString:@"IS_NULL"]){//为空
              
              if (BCStringIsEmpty(itemValue)) {
                [sult addObject:@"1"];
              } else {
                [sult addObject:@"0"];
              }
              
            } else if ([itemOperator isEqualToString:@"NOT_NULL"]){//不为空
              
              if (!BCStringIsEmpty(itemValue)) {
                [sult addObject:@"1"];
              } else {
                [sult addObject:@"0"];
              }
              
            } else if ([itemOperator isEqualToString:@"GREATER_THAN"]){//大于
              
              if ([itemValue integerValue] > [operatorValue integerValue]) {
                [sult addObject:@"1"];
              } else {
                [sult addObject:@"0"];
              }
              
            } else if ([itemOperator isEqualToString:@"GREATER_THAN_OR_EQUALS"]){//大于等于
              if ([itemValue integerValue] >= [operatorValue integerValue]) {
                [sult addObject:@"1"];
              } else {
                [sult addObject:@"0"];
              }
            } else if ([itemOperator isEqualToString:@"LESS_THAN"]){//小于
              if ([itemValue integerValue] < [operatorValue integerValue]) {
                [sult addObject:@"1"];
              } else {
                [sult addObject:@"0"];
              }
            } else if ([itemOperator isEqualToString:@"LESS_THAN_OR_EQUALS"]){//小于等于
              if ([itemValue integerValue] <= [operatorValue integerValue]) {
                [sult addObject:@"1"];
              } else {
                [sult addObject:@"0"];
              }
            } else if ([itemOperator isEqualToString:@"IN"]){//属于
              if (!BCStringIsEmpty(itemValue) && [operatorValue containsString:itemValue]) {
                [sult addObject:@"1"];
              } else {
                [sult addObject:@"0"];
              }
            } else if ([itemOperator isEqualToString:@"NOT_IN"]){//属于
              if (!BCStringIsEmpty(itemValue) && ![operatorValue containsString:itemValue]) {
                [sult addObject:@"1"];
              } else {
                [sult addObject:@"0"];
              }
            }
          }
          
          if ([sult containsObject:@"0"]) {//代表校验未通过,继续校验
            
          } else {//校验通过
            isSatisfy = YES;
            if (i < parentArr.count) {
              [parentArr replaceObjectAtIndex:i withObject:@{@"parentTag":[dic objectForKeyNil:@"parentTag"],@"show":@"1",@"itemH":[dic objectForKeyNil:@"itemH"],@"superTag":[dic objectNilForKey:@"superTag"]}];
            }
            break;
          }
        }
        
      } else {
        [parentArr addObject:@{}];
      }
      
      //      if (isSatisfy) {
      //        break;
      //      }
      
    }
    
    
    dispatch_async(dispatch_get_main_queue(), ^{
      //      更新视图
      NSLog(@"符合条件的===%@",parentArr);
      if (!BCArrayIsEmpty(parentArr)) {
        
        for (NSDictionary *showDic in parentArr) {
          if (BCDictIsEmpty(showDic)) {
            continue;
          }
          UIView *superV = [self.changeView viewWithTag:[[showDic objectForKeyNil:@"superTag"] integerValue]];//最外层一整块view
          UIView *itemV = [superV viewWithTag:[[showDic objectForKeyNil:@"parentTag"] integerValue]];
          if (itemV) {
            NSString *show = [showDic objectForKeyNil:@"show"];
            NSNumber *itemH = [showDic objectForKeyNil:@"itemH"];
           
            if ([show isEqualToString:@"1"]) {
              if (itemV.height <= 0) {
                [UIView animateWithDuration:0.3 animations:^{
                  [itemV mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo(itemH);
                  }];
                  [superV layoutIfNeeded];
                }];
              }
            } else {
              
              if (itemV.height > 0) {
                [UIView animateWithDuration:0.3 animations:^{
                  [itemV mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo(0);
                  }];
                  [superV layoutIfNeeded];
                }];
              }
            }
            
          }
        }
      }
    });
  });
}
#pragma mark 变动项
- (void)loadChangeItemView{
  
  __weak typeof(self)weakSelf = self;
  
  
  self.changeView = [[UIView alloc] init];
  [self.homeScrollView addSubview:self.changeView];
  [self.changeView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.mas_equalTo(0);
    if (self.openStoreDisplay) {
      make.top.mas_equalTo(self.openStoreView.mas_bottom).offset(12);
    } else {
      make.top.mas_equalTo(self.basicView.mas_bottom).offset(12);
    }
    make.bottom.equalTo(self.homeScrollView).offset(-40);
    //    下面2行为了设置contentsize的宽度，这样键盘才能适配
    make.right.equalTo(self.homeScrollView);
    make.width.equalTo(self.homeScrollView);
    
  }];
  
  UIView *lastOutV;//最外层的view
  for (int i = 0; i < self.itemChangeArr.count; i ++) {
    
    NSDictionary *dic = [self.itemChangeArr objectAtIndexCheck:i];
    if ([[dic objectForKeyNil:@"acquiesce"] isEqual:@(1) ]) {//跳过系统预设的变动项，基本信息，房东信息等等
      continue;
    }
    
    if ([[dic objectForKeyNil:@"enable"] isEqual:@(0)]) {//未启用的也要跳过
      continue;
    }
    
    //    每一大块变动项
    UIView *bottomV = [[UIView alloc] init];
    bottomV.tag = 10000 + i;
    bottomV.backgroundColor = [UIColor whiteColor];
    [self.changeView addSubview:bottomV];
    [bottomV mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(0);
      make.width.mas_equalTo(BCWidth);
      make.height.mas_greaterThanOrEqualTo(50);
      if (lastOutV) {
        make.top.mas_equalTo(lastOutV.mas_bottom).offset(12);
      } else {
        make.top.mas_equalTo(0);
      }
      if (i == self.itemChangeArr.count - 1) {
        make.bottom.equalTo(self.changeView).offset(0);
      }
    }];
    
    //    最外层变动项标题
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.textColor = COLOR(31, 33, 38);
    nameLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
    nameLabel.text = [NSString stringWithFormat:@"%@",[dic objectNilForKey:@"name"]];
    [bottomV addSubview:nameLabel];
    [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.mas_equalTo(16);
      make.width.mas_equalTo(BCWidth - 32);
      make.top.mas_equalTo(0);
      make.height.mas_equalTo(50);
    }];
    
    //    变动项
    NSArray *items = [[dic objectForKeyNil:@"content"] objectForKeyNil:@"details"];
    if (BCArrayIsEmpty(items)) {
      continue;
    }
    
    UIView *lastInV;//里面的每一小项选项
    for (int j = 0; j < items.count; j ++) {
      
      NSDictionary *itemDic = [items objectAtIndexCheck:j];
      if (BCDictIsEmpty(itemDic)) {
        continue;
      }
      
      NSInteger display = [[NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"init_display"]] integerValue];
     
      //      右边的根据类型返回不同的组件
      NSString *rightStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"component_type"]];
  
      NSString *leftMemo = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"memo"]];
      
      //      每一小项的id
      NSString *itemId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
      
      NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
      NSString *titleNameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"title"]];
      CGFloat height = [titleNameStr boundingRectWithSize:CGSizeMake(120, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
      height = ceil(height);
      height = height < 22 ? 22 : height;
      
      //      条件显示
      NSInteger conditionDisplay = [[itemDic objectForKeyNil:@"condition_display"] integerValue];
      NSArray *conditionGroups = [itemDic objectForKeyNil:@"condition_groups"];
      
      //      每一个小块选项
      UIView *itemV = [[UIView alloc] init];
      itemV.tag =  30000 + j + i;
      itemV.clipsToBounds = YES;
      [bottomV addSubview:itemV];
      [itemV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(0);
        make.top.mas_equalTo(lastInV ? lastInV.mas_bottom : @50);
        make.width.mas_equalTo(BCWidth);
        make.height.mas_greaterThanOrEqualTo(50);
        
        if (j == items.count - 1) {
          make.bottom.equalTo(bottomV).offset(0);
        }
      }];
      
      UIButton *leftBtn = [[UIButton alloc] init];
      [itemV addSubview:leftBtn];
      [leftBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.top.mas_equalTo(14);
        make.height.mas_equalTo(height);
        
      }];
      
      //      每个小项的标题
      UILabel *leftL = [[UILabel alloc] init];
      leftL.text = titleNameStr;
      leftL.numberOfLines = 0;
      leftL.font = [UIFont systemFontOfSize:MutilFont(15)];
      leftL.textColor = COLOR(29, 33, 41);
      [leftBtn addSubview:leftL];
      [leftL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.mas_equalTo(0);
        make.width.mas_lessThanOrEqualTo(120);
        make.height.mas_equalTo(height);
      }];
      
      
      UILabel *startPL = [[UILabel alloc] init];
      startPL.text = @"*";
      startPL.font = [UIFont systemFontOfSize:MutilFont(15)];
      startPL.textColor = COLOR(255, 33, 33);
      startPL.hidden = YES;
      [itemV addSubview:startPL];
      [startPL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(8);
        make.top.mas_equalTo(leftL.mas_top);
        make.width.mas_equalTo(8);
        make.height.mas_equalTo(leftL.mas_height);
      }];
      
      if ([[itemDic objectForKeyNil:@"init_required"] isEqual:@(1)]) {
        startPL.hidden = NO;
      }
      //      字段说明
      UIImageView *leftMemoIM = [[UIImageView alloc] init];
      [leftBtn addSubview:leftMemoIM];
      leftMemoIM.image = [UIImage imageNamed:@"help_icon"];
      [leftMemoIM mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(leftL.mas_right).offset(2);
        make.centerY.mas_equalTo(leftBtn.mas_centerY);
        if (BCStringIsEmpty(leftMemo)) {
          make.height.width.mas_equalTo(0);
        } else {
          make.height.width.mas_equalTo(18);
          
        }
        make.right.mas_equalTo(leftBtn).offset(-4);
      }];
      
      if (!BCStringIsEmpty(leftMemo)) {
        [leftBtn addtargetBlock:^(UIButton *button) {          
          [weakSelf.notiflyView showModal:NotiflyNomal andTitle:@"字段说明" andSubTitle:leftMemo withTime:2];
        }];
      }
      
      if ([rightStr isEqualToString:@"TEXT"]) {//文本输入框
        
        if (conditionDisplay == 1) {
          NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
          NSString *parentTag = [NSString stringWithFormat:@"%d", 30000 + j + i];
          NSString *superTag = [NSString stringWithFormat:@"%ld", bottomV.tag];
          NSMutableArray *someArr = [NSMutableArray array];
          for (NSDictionary *somed in conditionGroups) {
            NSArray *subArr = [somed objectForKeyNil:@"conditions"];
            if (BCArrayIsEmpty(subArr)) {
              continue;
            }
            
            for (NSDictionary *subD in subArr) {
              [someArr addObject:[subD objectForKeyNil:@"field"]];
            }
          }
          
          if (!BCArrayIsEmpty(someArr)) {
            NSDictionary *conditionDic = [NSDictionary dictionaryWithObjectsAndKeys:parentId,@"parentId",@(height + 28),@"itemH",parentTag,@"parentTag",superTag,@"superTag",someArr,@"subId",conditionGroups,@"conditionGroups", nil];
            [self.changeItemContions addObject:conditionDic];
          }
          
        }
        
        UITextField *rightTF = [[UITextField alloc] init];
        rightTF.placeholder = @"请输入";
        rightTF.tag = 20000 + j;
        rightTF.returnKeyType = UIReturnKeyDone;
        rightTF.textColor = COLOR(31, 33, 38);
        rightTF.textAlignment = NSTextAlignmentRight;
        rightTF.font = [UIFont systemFontOfSize:MutilFont(15)];
        rightTF.delegate = self;
        rightTF.inputAccessoryView = [self returnDone];
        [itemV addSubview:rightTF];
        [rightTF mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(leftBtn.mas_right).offset(6);
          make.top.mas_equalTo(14);
          make.right.mas_equalTo(-16);
          make.height.mas_equalTo(height);
          make.bottom.equalTo(itemV).offset(-14);
        }];
        
        NSString *str = [NSString stringWithFormat:@"%@",[self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]];
        if (self.draftData && !BCStringIsEmpty(str)) {
          rightTF.text = str;
          [self.changeItemDictionary setObject:rightTF.text forKey:itemId];
        }
        
        //       输入框是否达到最大长度
        NSInteger maxCount = [[itemDic objectForKeyNil:@"max"] integerValue];
        NSInteger minCount = [[itemDic objectForKeyNil:@"min"] integerValue];
        
        [rightTF addtargetBlock:^(UITextField *tf) {
          
          if (tf.text.length != 0) {
            
            if (maxCount != 0) {
              if (tf.text.length >= maxCount) {
                rightTF.text = [tf.text substringToIndex:maxCount];
              }
            }
            
          }
          
          [weakSelf.changeItemDictionary setObject:tf.text forKey:itemId];
          [weakSelf checkItemContions:itemId];
          
        }];
        
        //        输入框是否满足最小值
        if (minCount != 0) {
          [rightTF addEndEditBlock:^(UITextField *tf) {
            if (tf.text.length < minCount && tf.text.length > 0) {
              NSString *errorResult = [NSString stringWithFormat:@"%@最少输入%ld个字",titleNameStr,(long)minCount];
              [weakSelf.notiflyView showModal:NotiflyFail andTitle:errorResult];
            }
          }];
        }
        
        if ([titleNameStr isEqualToString:@"预估回本周期"]) {
          rightTF.userInteractionEnabled = NO;
          rightTF.text = @"待生成";
        } else {
          rightTF.userInteractionEnabled = YES;
          rightTF.placeholder = @"请输入";
        }
        
      } else  if ([rightStr isEqualToString:@"NUMBER"]) {//数字输入框
       
        if (conditionDisplay == 1) {
          NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
          NSString *parentTag = [NSString stringWithFormat:@"%d",30000 + j + i];
          NSString *superTag = [NSString stringWithFormat:@"%ld", bottomV.tag];
          NSMutableArray *someArr = [NSMutableArray array];
          for (NSDictionary *somed in conditionGroups) {
            NSArray *subArr = [somed objectForKeyNil:@"conditions"];
            if (BCArrayIsEmpty(subArr)) {
              continue;
            }
            
            for (NSDictionary *subD in subArr) {
              [someArr addObject:[subD objectForKeyNil:@"field"]];
            }
          }
          
          if (!BCArrayIsEmpty(someArr)) {
            NSDictionary *conditionDic = [NSDictionary dictionaryWithObjectsAndKeys:parentId,@"parentId",@(height + 28),@"itemH",parentTag,@"parentTag",superTag,@"superTag",someArr,@"subId",conditionGroups,@"conditionGroups", nil];
            [self.changeItemContions addObject:conditionDic];
          }
          
        }
        
        UITextField *rightTF = [[UITextField alloc] init];
        rightTF.placeholder = @"请输入";
        rightTF.tag = 20000 + j;
        rightTF.keyboardType = UIKeyboardTypeNumberPad;
        rightTF.returnKeyType = UIReturnKeyDone;
        rightTF.textColor = COLOR(31, 33, 38);
        rightTF.textAlignment = NSTextAlignmentRight;
        rightTF.font = [UIFont systemFontOfSize:MutilFont(15)];
        rightTF.delegate = self;
        rightTF.inputAccessoryView = [self returnDone];
        [itemV addSubview:rightTF];
        [rightTF mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(leftBtn.mas_right).offset(6);
          make.top.mas_equalTo(14);
          make.right.mas_equalTo(-16);
          make.height.mas_equalTo(height);
          make.bottom.equalTo(itemV).offset(-14);
        }];
        
        NSString *str = [NSString stringWithFormat:@"%@",[self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]];
        if (self.draftData && !BCStringIsEmpty(str)) {
          rightTF.text = str;
          [self.changeItemDictionary setObject:rightTF.text forKey:itemId];
        }
        
        NSInteger maxCount = [[itemDic objectForKeyNil:@"max"] integerValue];
        NSInteger minCount = [[itemDic objectForKeyNil:@"min"] integerValue];
        
        [rightTF addtargetBlock:^(UITextField *tf) {
          
          if (tf.text.length != 0) {
            if (maxCount !=0 && [tf.text integerValue] > maxCount) {
              rightTF.text = [NSString stringWithFormat:@"%ld",(long)maxCount];
            }
            if (minCount !=0 && [tf.text integerValue] < minCount) {
              rightTF.text = [NSString stringWithFormat:@"%ld",(long)minCount];
            }
          }
          [weakSelf.changeItemDictionary setObject:tf.text forKey:itemId];
          [weakSelf checkItemContions:itemId];
        }];
        
      } else  if ([rightStr isEqualToString:@"AMOUNT"]) {//金额输入框
        
        if (conditionDisplay == 1) {
          NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
          NSString *parentTag = [NSString stringWithFormat:@"%d",30000 + j + i];
          NSString *superTag = [NSString stringWithFormat:@"%ld", bottomV.tag];
          NSMutableArray *someArr = [NSMutableArray array];
          for (NSDictionary *somed in conditionGroups) {
            NSArray *subArr = [somed objectForKeyNil:@"conditions"];
            if (BCArrayIsEmpty(subArr)) {
              continue;
            }
            
            for (NSDictionary *subD in subArr) {
              [someArr addObject:[subD objectForKeyNil:@"field"]];
            }
          }
          
          if (!BCArrayIsEmpty(someArr)) {
            NSDictionary *conditionDic = [NSDictionary dictionaryWithObjectsAndKeys:parentId,@"parentId",@(height + 28),@"itemH",parentTag,@"parentTag",superTag,@"superTag",someArr,@"subId",conditionGroups,@"conditionGroups", nil];
            [self.changeItemContions addObject:conditionDic];
          }
          
        }
        
        UITextField *rightTF = [[UITextField alloc] init];
        rightTF.placeholder = @"请输入";
        rightTF.tag = 20000 + j;
        rightTF.keyboardType = UIKeyboardTypeDecimalPad;
        rightTF.returnKeyType = UIReturnKeyDone;
        rightTF.textColor = COLOR(31, 33, 38);
        rightTF.textAlignment = NSTextAlignmentRight;
        rightTF.font = [UIFont systemFontOfSize:MutilFont(15)];
        rightTF.delegate = self;
        rightTF.inputAccessoryView = [self returnDone];
        [itemV addSubview:rightTF];
        [rightTF mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(leftBtn.mas_right).offset(6);
          make.top.mas_equalTo(14);
          make.right.mas_equalTo(-16);
          make.height.mas_equalTo(height);
          make.bottom.equalTo(itemV).offset(-14);
        }];
       
        NSString *str = [NSString stringWithFormat:@"%@",[self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]];
        if (self.draftData && !BCStringIsEmpty(str)) {
          rightTF.text = str;
          [self.changeItemDictionary setObject:rightTF.text forKey:itemId];
        }
        
        NSInteger maxCount = [[itemDic objectForKeyNil:@"max"] integerValue];
        NSInteger minCount = [[itemDic objectForKeyNil:@"min"] integerValue];
        
        [rightTF addtargetBlock:^(UITextField *tf) {
          
          if (tf.text.length != 0) {
            if (maxCount !=0 && [tf.text integerValue] > maxCount) {
              rightTF.text = [NSString stringWithFormat:@"%ld",(long)maxCount];
            }
            if (minCount !=0 && [tf.text integerValue] < minCount) {
              rightTF.text = [NSString stringWithFormat:@"%ld",(long)minCount];
            }
          }
          [weakSelf.changeItemDictionary setObject:tf.text forKey:itemId];
          [weakSelf checkItemContions:itemId];
        }];
        
        
      }else if ([rightStr isEqualToString:@"SINGLE_CHOICE"] || [rightStr isEqualToString:@"SINGLE_RADIO"]) {//单选
      
        if (conditionDisplay == 1) {
          NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
          NSString *parentTag = [NSString stringWithFormat:@"%d",30000 + j + i];
          NSString *superTag = [NSString stringWithFormat:@"%ld", bottomV.tag];
          NSMutableArray *someArr = [NSMutableArray array];
          for (NSDictionary *somed in conditionGroups) {
            NSArray *subArr = [somed objectForKeyNil:@"conditions"];
            if (BCArrayIsEmpty(subArr)) {
              continue;
            }
            
            for (NSDictionary *subD in subArr) {
              [someArr addObject:[subD objectForKeyNil:@"field"]];
            }
          }
          
          if (!BCArrayIsEmpty(someArr)) {
            NSDictionary *conditionDic = [NSDictionary dictionaryWithObjectsAndKeys:parentId,@"parentId",@(height + 28),@"itemH",parentTag,@"parentTag",superTag,@"superTag",someArr,@"subId",conditionGroups,@"conditionGroups", nil];
            [self.changeItemContions addObject:conditionDic];
          }
          
        }
        
        UIButton *rentDayBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [itemV addSubview:rentDayBtn];
        [rentDayBtn mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(leftBtn.mas_right).offset(6);
          make.top.mas_equalTo(14);
          make.right.mas_equalTo(0);
          make.height.mas_equalTo(height);
          make.bottom.equalTo(itemV).offset(-14);
        }];
        
        UIImageView *leftIM = [[UIImageView alloc] init];
        leftIM.image = [UIImage imageNamed:@"icon_single"];
        [rentDayBtn addSubview:leftIM];
        [leftIM mas_makeConstraints:^(MASConstraintMaker *make) {
          make.right.mas_equalTo(-13);
          make.width.mas_equalTo(17);
          make.height.mas_equalTo(18);
          make.centerY.mas_equalTo(rentDayBtn.mas_centerY);
        }];
        
        UILabel *chooseL = [[UILabel alloc] init];
        chooseL.textAlignment = NSTextAlignmentRight;
        chooseL.text = @"请选择";
        chooseL.tag = 20000 + j;
        chooseL.textColor = ACOLOR(30, 33, 38, 0.25);
        chooseL.font = [UIFont systemFontOfSize:MutilFont(15)];
        [rentDayBtn addSubview:chooseL];
        [chooseL mas_makeConstraints:^(MASConstraintMaker *make) {
          make.top.mas_equalTo(0);
          make.right.mas_equalTo(-32);
          make.height.mas_equalTo(height);
          make.width.mas_lessThanOrEqualTo(BCWidth - 120 - 18 - 10 - 32);
        }];
       
        [rentDayBtn addtargetBlock:^(UIButton *button) {
          
          NSMutableArray *arr = [NSMutableArray array];
          for (NSString *dic in [itemDic objectForKeyNil:@"sub_titles"]) {
            NSString *name = [NSString stringWithFormat:@"%@",dic];
            [arr addObject:@{@"name":name,@"id":name}];
          }
          
          MABottomSingleSelectAlert *aler = [[MABottomSingleSelectAlert alloc] initWithTitle:@"选择" showKey:@"name" showSearch:NO maskClose:YES andData:arr endChooseItem:nil];
          [aler showModal];
          aler.chooseBlock = ^(NSDictionary *chooseDic) {
            chooseL.text = [NSString stringWithFormat:@"%@",[chooseDic objectForKeyNil:@"name"]];
            chooseL.textColor = COLOR(31, 33, 38);
            
            [weakSelf.changeItemDictionary setObject:chooseL.text forKey:itemId];
            [weakSelf checkItemContions:itemId];
          };
        }];
        
        NSString *str = [NSString stringWithFormat:@"%@",[self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]];
        if (self.draftData && !BCStringIsEmpty(str)) {
          chooseL.text = str;
          chooseL.textColor = COLOR(31, 33, 38);
          [self.changeItemDictionary setObject:chooseL.text forKey:itemId];
        }
        
      }else if ([rightStr isEqualToString:@"MULTI_CHOICE"]) {//多选
      
        if (conditionDisplay == 1) {
          NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
          NSString *parentTag = [NSString stringWithFormat:@"%d",30000 + j + i];
          NSString *superTag = [NSString stringWithFormat:@"%ld", bottomV.tag];
          NSMutableArray *someArr = [NSMutableArray array];
          for (NSDictionary *somed in conditionGroups) {
            NSArray *subArr = [somed objectForKeyNil:@"conditions"];
            if (BCArrayIsEmpty(subArr)) {
              continue;
            }
            
            for (NSDictionary *subD in subArr) {
              [someArr addObject:[subD objectForKeyNil:@"field"]];
            }
          }
          
          if (!BCArrayIsEmpty(someArr)) {
            NSDictionary *conditionDic = [NSDictionary dictionaryWithObjectsAndKeys:parentId,@"parentId",@(height + 28),@"itemH",parentTag,@"parentTag",superTag,@"superTag",someArr,@"subId",conditionGroups,@"conditionGroups", nil];
            [self.changeItemContions addObject:conditionDic];
          }
          
        }
        
        UIButton *rentDayBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [itemV addSubview:rentDayBtn];
        [rentDayBtn mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(leftBtn.mas_right).offset(6);
          make.top.mas_equalTo(14);
          make.right.mas_equalTo(0);
          make.height.mas_equalTo(height);
          make.bottom.equalTo(itemV).offset(-14);
        }];
        
        UIImageView *leftIM = [[UIImageView alloc] init];
        leftIM.image = [UIImage imageNamed:@"icon_single"];
        [rentDayBtn addSubview:leftIM];
        [leftIM mas_makeConstraints:^(MASConstraintMaker *make) {
          make.right.mas_equalTo(-13);
          make.width.mas_equalTo(17);
          make.height.mas_equalTo(18);
          make.centerY.mas_equalTo(rentDayBtn.mas_centerY);
        }];
        
        UILabel *chooseL = [[UILabel alloc] init];
        chooseL.textAlignment = NSTextAlignmentRight;
        chooseL.text = @"请选择";
        chooseL.tag = 20000 + j;
        chooseL.textColor = ACOLOR(30, 33, 38, 0.25);
        chooseL.font = [UIFont systemFontOfSize:MutilFont(15)];
        [rentDayBtn addSubview:chooseL];
        [chooseL mas_makeConstraints:^(MASConstraintMaker *make) {
          make.top.mas_equalTo(0);
          make.right.mas_equalTo(-32);
          make.height.mas_equalTo(height);
          make.width.mas_lessThanOrEqualTo(BCWidth - 120 - 18 - 10 - 32);
        }];
        
        [rentDayBtn addtargetBlock:^(UIButton *button) {
          
          NSMutableArray *arr = [NSMutableArray array];
          for (NSString *dic in [itemDic objectForKeyNil:@"sub_titles"]) {
            NSString *name = [NSString stringWithFormat:@"%@",dic];
            [arr addObject:@{@"name":name,@"id":name,@"isSelect":@"0"}];
          }
          
          MABottomMutilSelectAlert *aler = [[MABottomMutilSelectAlert alloc] initWithTitle:@"多选" showKey:@"name" showSearch:NO maskClose:YES andData:arr];
          [aler showModal];
          aler.chooseBlock = ^(NSArray *chooseArray) {
            if (BCArrayIsEmpty(chooseArray)) {
              return;
            }
            NSMutableArray *arr = [NSMutableArray array];
            for (NSDictionary *dic in chooseArray) {
              [arr addObject:[dic objectForKeyNil:@"name"]];
            }
            
            NSString *commaSeparatedString = [arr componentsJoinedByString:@","];
            chooseL.text = [NSString stringWithFormat:@"%@",commaSeparatedString];
            chooseL.textColor = COLOR(31, 33, 38);
            
            [weakSelf.changeItemDictionary setObject:chooseL.text forKey:itemId];
            [weakSelf checkItemContions:itemId];
          };
          
        }];
        
        if (self.draftData) {
          NSArray *strarr = [self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
          if (!BCArrayIsEmpty(strarr)) {
            
            NSMutableArray *arr = [NSMutableArray array];
            for (NSDictionary *dic in strarr) {
              [arr addObject:[dic objectForKeyNil:@"label"]];
            }
            chooseL.text = [arr componentsJoinedByString:@","];
            chooseL.textColor = COLOR(31, 33, 38);
            [self.changeItemDictionary setObject:chooseL.text forKey:itemId];
          } else {
            
            NSString *str = [NSString stringWithFormat:@"%@",[self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]];
            if (!BCStringIsEmpty(str)) {
              chooseL.text = str;
              chooseL.textColor = COLOR(31, 33, 38);
              [self.changeItemDictionary setObject:chooseL.text forKey:itemId];
            }
            
          }
        }
        
      } else if ([rightStr isEqualToString:@"DATE"]) {//日期选择
      
        if (conditionDisplay == 1) {
          NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
          NSString *parentTag = [NSString stringWithFormat:@"%d",30000 + j + i];
          NSString *superTag = [NSString stringWithFormat:@"%ld", bottomV.tag];
          NSMutableArray *someArr = [NSMutableArray array];
          for (NSDictionary *somed in conditionGroups) {
            NSArray *subArr = [somed objectForKeyNil:@"conditions"];
            if (BCArrayIsEmpty(subArr)) {
              continue;
            }
            
            for (NSDictionary *subD in subArr) {
              [someArr addObject:[subD objectForKeyNil:@"field"]];
            }
          }
          
          if (!BCArrayIsEmpty(someArr)) {
            NSDictionary *conditionDic = [NSDictionary dictionaryWithObjectsAndKeys:parentId,@"parentId",@(height + 28),@"itemH",parentTag,@"parentTag",superTag,@"superTag",someArr,@"subId",conditionGroups,@"conditionGroups", nil];
            [self.changeItemContions addObject:conditionDic];
          }
          
        }
        
        UIButton *rentDayBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [itemV addSubview:rentDayBtn];
        [rentDayBtn mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(leftBtn.mas_right).offset(6);
          make.top.mas_equalTo(14);
          make.right.mas_equalTo(0);
          make.height.mas_equalTo(height);
          make.bottom.equalTo(itemV).offset(-14);
        }];
        
        UIImageView *leftIM = [[UIImageView alloc] init];
        leftIM.image = [UIImage imageNamed:@"icon_single"];
        [rentDayBtn addSubview:leftIM];
        [leftIM mas_makeConstraints:^(MASConstraintMaker *make) {
          make.right.mas_equalTo(-13);
          make.width.mas_equalTo(17);
          make.height.mas_equalTo(18);
          make.centerY.mas_equalTo(rentDayBtn.mas_centerY);
        }];
        
        UILabel *chooseL = [[UILabel alloc] init];
        chooseL.textAlignment = NSTextAlignmentRight;
        chooseL.text = @"请选择";
        chooseL.tag = 20000 + j;
        chooseL.textColor = ACOLOR(30, 33, 38, 0.25);
        chooseL.font = [UIFont systemFontOfSize:MutilFont(15)];
        [rentDayBtn addSubview:chooseL];
        [chooseL mas_makeConstraints:^(MASConstraintMaker *make) {
          make.top.mas_equalTo(0);
          make.right.mas_equalTo(-32);
          make.height.mas_equalTo(height);
          make.width.mas_lessThanOrEqualTo(BCWidth - 120 - 18 - 10 - 32);
        }];
        [rentDayBtn addtargetBlock:^(UIButton *button) {
          
          NSInteger type;
          NSString *typeStr = [itemDic objectForKeyNil:@"format"];
          if ([typeStr isEqualToString:@"YYYY-MM-DD"]) {
            type = 2;
          } else if ([typeStr isEqualToString:@"YYYY-MM"]) {
            type = 1;
          } else {
            type = 0;
          }
          
          MASingleDateView *dateV = [[MASingleDateView alloc] initWithFrame:[UIScreen mainScreen].bounds andCurrent:NO andType:type];
          [dateV showModal];
          dateV.okBlock = ^(NSString *dateStr) {
            chooseL.text = [NSString stringWithFormat:@"%@",dateStr];
            chooseL.textColor = COLOR(31, 33, 38);
          };
        }];
        
        NSString *str = [NSString stringWithFormat:@"%@",[self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]];
        if (self.draftData && !BCStringIsEmpty(str)) {
          chooseL.text = str;
          chooseL.textColor = COLOR(31, 33, 38);
        }
        
        
      }else if ([rightStr isEqualToString:@"LONG_TEXT"]){//文本域
        
        if (conditionDisplay == 1) {
          NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
          NSString *parentTag = [NSString stringWithFormat:@"%d",30000 + j + i];
          NSString *superTag = [NSString stringWithFormat:@"%ld", bottomV.tag];
          NSMutableArray *someArr = [NSMutableArray array];
          for (NSDictionary *somed in conditionGroups) {
            NSArray *subArr = [somed objectForKeyNil:@"conditions"];
            if (BCArrayIsEmpty(subArr)) {
              continue;
            }
            
            for (NSDictionary *subD in subArr) {
              [someArr addObject:[subD objectForKeyNil:@"field"]];
            }
          }
          
          if (!BCArrayIsEmpty(someArr)) {
            NSDictionary *conditionDic = [NSDictionary dictionaryWithObjectsAndKeys:parentId,@"parentId",@(height + 8 + 28 + 92),@"itemH",parentTag,@"parentTag",superTag,@"superTag",someArr,@"subId",conditionGroups,@"conditionGroups", nil];
            [self.changeItemContions addObject:conditionDic];
          }
          
        }
        
        NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
        NSString *titleNameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"title"]];
        CGFloat height = [titleNameStr boundingRectWithSize:CGSizeMake(BCWidth - 32, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        height = ceil(height);
        height = height < 22 ? 22 : height;
        
        //        计算每个选项的高度
        [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
          make.width.mas_lessThanOrEqualTo(BCWidth - 50);
          make.height.mas_equalTo(height);
        }];
        
        UITextView *memoTV = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, BCWidth - 32, 92)];
        memoTV.backgroundColor = [UIColor whiteColor];
        memoTV.layer.borderColor = ACOLOR(31, 33, 38, 0.10).CGColor;
        memoTV.layer.borderWidth = 0;
        memoTV.layer.cornerRadius = 4;
        memoTV.font = [UIFont systemFontOfSize:MutilFont(15)];
        memoTV.delegate = self;
        memoTV.tag = 20000 + j;
        memoTV.textContainerInset = UIEdgeInsetsZero;
        memoTV.textContainer.lineFragmentPadding = 0;
        memoTV.returnKeyType = UIReturnKeyDone;
        memoTV.textColor = COLOR(31, 33, 38);
        memoTV.inputAccessoryView = [self returnDone];
        [itemV addSubview:memoTV];
        [memoTV mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(16);
          make.top.mas_equalTo(leftL.mas_bottom).offset(8);
          make.width.mas_equalTo(BCWidth - 32);
          make.height.mas_equalTo(92);
          make.bottom.equalTo(itemV).offset(-14);
        }];
        
        NSString *str = [NSString stringWithFormat:@"%@",[self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]]];
        if (self.draftData && !BCStringIsEmpty(str)) {
          memoTV.text = str;
          [self.changeItemDictionary setObject:memoTV.text forKey:itemId];
        } else {
          memoTV.placeholder = @"请输入";
        }
        [memoTV addtargetBlock:^(UITextView *tv) {
          [weakSelf.changeItemDictionary setObject:tv.text forKey:itemId];
          [weakSelf checkItemContions:itemId];
        }];
        
        NSInteger maxCount = [[itemDic objectForKeyNil:@"max"] integerValue];
        if (maxCount != 0) {
          memoTV.limitLength = @(maxCount);
        }
        
        NSInteger minCount = [[itemDic objectForKeyNil:@"min"] integerValue];
        if (minCount != 0) {
          [memoTV addEndEditBlock:^(UITextView *tv) {
            if (tv.text.length > 0 && tv.text.length < minCount) {
              NSString *errorResult = [NSString stringWithFormat:@"%@最少输入%ld个字",titleNameStr,(long)minCount];
              [weakSelf.notiflyView showModal:NotiflyFail andTitle:errorResult];
            }
          }];
        }
        
      } else if ([rightStr isEqualToString:@"IMAGE"]){//图片
        
        NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
        NSString *titleNameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"title"]];
        CGFloat height = [titleNameStr boundingRectWithSize:CGSizeMake(BCWidth - 32, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        height = ceil(height);
        height = height < 22 ? 22 : height;
        
        if (conditionDisplay == 1) {
          NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
          NSString *parentTag = [NSString stringWithFormat:@"%d",30000 + j + i];
          NSString *superTag = [NSString stringWithFormat:@"%ld", bottomV.tag];
          NSMutableArray *someArr = [NSMutableArray array];
          for (NSDictionary *somed in conditionGroups) {
            NSArray *subArr = [somed objectForKeyNil:@"conditions"];
            if (BCArrayIsEmpty(subArr)) {
              continue;
            }
            
            for (NSDictionary *subD in subArr) {
              [someArr addObject:[subD objectForKeyNil:@"field"]];
            }
          }
          
          if (!BCArrayIsEmpty(someArr)) {
            NSDictionary *conditionDic = [NSDictionary dictionaryWithObjectsAndKeys:parentId,@"parentId",@(height + 8 + 28 + (BCWidth - 32 - 24) / 4.0),@"itemH",parentTag,@"parentTag",superTag,@"superTag",someArr,@"subId",conditionGroups,@"conditionGroups", nil];
            [self.changeItemContions addObject:conditionDic];
          }
          
        }
        
        [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
          make.width.mas_lessThanOrEqualTo(BCWidth - 50);
          make.height.mas_equalTo(height);
        }];
        
        NSArray *editArr = @[];
        if (self.draftData && !BCArrayIsEmpty([self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]])) {
          editArr = [self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
        }
        
        NSInteger maxCount = [[itemDic objectForKeyNil:@"max"] integerValue];
        if (maxCount <= 0) {
          maxCount = 20;
        }
        
        MAUploadMutilImageView *imageV = [[MAUploadMutilImageView alloc] initWithFrame:CGRectMake(0, 0, BCWidth - 32, (BCWidth - 32 - 24) / 4.0) withUrl:@"kms/hxl.kms.businessplan.file.upload" withMaxCount:maxCount withUploadType:UploadTypeOnlyPhoto withParams:@{@"fid":@"-1",@"fileType":[itemDic objectForKeyNil:@"id"]} endUploadUrl:editArr enable:NO];
        [itemV addSubview:imageV];
        imageV.tag = 20000 + j;
        
        [itemV mas_updateConstraints:^(MASConstraintMaker *make) {
          if (BCArrayIsEmpty(editArr)) {
            make.height.mas_equalTo(height + 8 + 28 + (BCWidth - 32 - 24) / 4.0);
            
          } else {
            CGFloat itemH = (BCWidth - 32 - 24) / 4.0 + 8;
            CGFloat editHeight = (BCWidth - 32 - 24) / 4.0;
            
            if (editArr.count < 4) {
              
              editHeight = itemH - 8;
              
            } else if (editArr.count >= 4 && editArr.count < 8) {
              
              editHeight = 2 * itemH - 8;
              
            } else if (editArr.count >= 8 && editArr.count < 12) {
              
              editHeight = 3 * itemH - 8;
              
              
            } else if (editArr.count >= 12 && editArr.count < 16) {
              
              editHeight = 4 * itemH - 8;
              
              
            } else if (editArr.count >= 16 && editArr.count <= 20) {
              
              editHeight = 5 * itemH - 8;
            }
            
            make.height.mas_equalTo(height + 8 + 28 + editHeight);
            
            if (conditionDisplay == 1) {
              NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
              for (int i = 0; i < self.changeItemContions.count; i ++) {
                
                NSMutableDictionary *tmpContionDic = [NSMutableDictionary dictionaryWithDictionary:[self.changeItemContions objectAtIndexCheck:i]];
                if ([[tmpContionDic objectNilForKey:@"parentId"] isEqualToString:parentId]) {
                  [tmpContionDic setObject:@(height + 8 + 28 + editHeight) forKey:@"itemH"];
                  [self.changeItemContions replaceObjectAtIndex:i withObject:tmpContionDic];
                  break;
                }
              }
            }
          }
          
        }];
        
        [imageV mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(16);
          make.top.mas_equalTo(leftL.mas_bottom).offset(8);
          make.width.mas_equalTo(BCWidth - 32);
          make.bottom.equalTo(itemV).offset(-14);
          if (BCArrayIsEmpty(editArr)) {
            make.height.mas_equalTo((BCWidth - 32 - 24) / 4.0);
          } else {
            
            CGFloat itemH = (BCWidth - 32 - 24) / 4.0 + 8;
            CGFloat editHeight = (BCWidth - 32 - 24) / 4.0;
            
            if (editArr.count >= maxCount) {
              if (editArr.count / 4 == 0) {//就一行
                
              } else {
                editHeight = editArr.count / 4  * itemH - 8;
              }
            } else {
              
              if ((editArr.count + 1) % 4 == 0) {//一行正好放得下
                editHeight = ((editArr.count + 1) / 4 ) * (itemH) - 8;
              } else {
                editHeight = ((editArr.count + 1) / 4  + 1) * (itemH ) - 8;
              }
            }
            
            make.height.mas_equalTo(editHeight);
          }
        }];
        __weak MAUploadMutilImageView *weakUP = imageV;
        imageV.chooseBlock = ^(CGFloat bottom) {
         
          [itemV mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(height + 8 + 28 + bottom);
          }];
         
          [weakUP mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(bottom);
          }];
          
          if (conditionDisplay == 1) {
            NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
            for (int i = 0; i < weakSelf.changeItemContions.count; i ++) {
              NSMutableDictionary *tmpContionDic = [NSMutableDictionary dictionaryWithDictionary:[weakSelf.changeItemContions objectAtIndexCheck:i]];
              if ([[tmpContionDic objectNilForKey:@"parentId"] isEqualToString:parentId]) {
                [tmpContionDic setObject:@(height + 8 + 28 + bottom) forKey:@"itemH"];
                [weakSelf.changeItemContions replaceObjectAtIndex:i withObject:tmpContionDic];
                break;
              }
            }
          }
          
        };
        
      }else if ([rightStr isEqualToString:@"VIDEO"]){//视频
        
        NSDictionary *attributes = @{NSFontAttributeName:[UIFont systemFontOfSize:MutilFont(15)]};
        NSString *titleNameStr = [NSString stringWithFormat:@"%@",[itemDic objectNilForKey:@"title"]];
        CGFloat height = [titleNameStr boundingRectWithSize:CGSizeMake(BCWidth - 32, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil].size.height;
        height = ceil(height);
        height = height < 22 ? 22 : height;
        
        //        条件公式
        if (conditionDisplay == 1) {
          NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
          NSString *parentTag = [NSString stringWithFormat:@"%d",30000 + j + i];
          NSString *superTag = [NSString stringWithFormat:@"%ld", bottomV.tag];
          NSMutableArray *someArr = [NSMutableArray array];
          for (NSDictionary *somed in conditionGroups) {
            NSArray *subArr = [somed objectForKeyNil:@"conditions"];
            if (BCArrayIsEmpty(subArr)) {
              continue;
            }
            
            for (NSDictionary *subD in subArr) {
              [someArr addObject:[subD objectForKeyNil:@"field"]];
            }
          }
          
          if (!BCArrayIsEmpty(someArr)) {
            NSDictionary *conditionDic = [NSDictionary dictionaryWithObjectsAndKeys:parentId,@"parentId",@(height + 8 + 28 + (BCWidth - 32 - 24) / 4.0),@"itemH",parentTag,@"parentTag",superTag,@"superTag",someArr,@"subId",conditionGroups,@"conditionGroups", nil];
            [self.changeItemContions addObject:conditionDic];
          }
          
        }
        
        [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
          make.width.mas_lessThanOrEqualTo(BCWidth - 50);
          make.height.mas_equalTo(height);
        }];
        
        NSArray *editArr = @[];
        if (self.draftData && !BCArrayIsEmpty([self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]])) {
          editArr = [self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
        }
        
        NSInteger maxCount = [[itemDic objectForKeyNil:@"max"] integerValue];
        if (maxCount <= 0) {
          maxCount = 20;
        }
        MAUploadMutilImageView *imageV = [[MAUploadMutilImageView alloc] initWithFrame:CGRectMake(16, leftL.bottom  - 8, BCWidth - 32, (BCWidth - 32 - 24) / 4.0) withUrl:@"kms/hxl.kms.businessplan.file.upload" withMaxCount:maxCount withUploadType:UploadTypeOnlyVideo withParams:@{@"fid":@"-1",@"fileType":[itemDic objectForKeyNil:@"id"]} endUploadUrl:editArr enable:NO];
        imageV.tag = 20000 + j;
        [itemV addSubview:imageV];
        
        [itemV mas_updateConstraints:^(MASConstraintMaker *make) {
          if (BCArrayIsEmpty(editArr)) {
            make.height.mas_equalTo(height + 8 + 28 + (BCWidth - 32 - 24) / 4.0);
            
          } else {
            CGFloat itemH = (BCWidth - 32 - 24) / 4.0 + 8;
            CGFloat editHeight = (BCWidth - 32 - 24) / 4.0;
            
            if (editArr.count < 4) {
              
              editHeight = itemH - 8;
              
            } else if (editArr.count >= 4 && editArr.count < 8) {
              
              editHeight = 2 * itemH - 8;
              
            } else if (editArr.count >= 8 && editArr.count < 12) {
              
              editHeight = 3 * itemH - 8;
              
              
            } else if (editArr.count >= 12 && editArr.count < 16) {
              
              editHeight = 4 * itemH - 8;
              
              
            } else if (editArr.count >= 16 && editArr.count <= 20) {
              
              editHeight = 5 * itemH - 8;
            }
            
            make.height.mas_equalTo(height + 8 + 28 + editHeight);
            
            if (conditionDisplay == 1) {
              NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
              for (int i = 0; i < self.changeItemContions.count; i ++) {
                
                NSMutableDictionary *tmpContionDic = [NSMutableDictionary dictionaryWithDictionary:[self.changeItemContions objectAtIndexCheck:i]];
                if ([[tmpContionDic objectNilForKey:@"parentId"] isEqualToString:parentId]) {
                  [tmpContionDic setObject:@(height + 8 + 28 + editHeight) forKey:@"itemH"];
                  [self.changeItemContions replaceObjectAtIndex:i withObject:tmpContionDic];
                  break;
                }
              }
            }
          }
          
        }];
        
        [imageV mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(16);
          make.top.mas_equalTo(leftL.mas_bottom).offset(8);
          make.width.mas_equalTo(BCWidth - 32);
          make.bottom.equalTo(itemV).offset(-14);
          
          if (BCArrayIsEmpty(editArr)) {
            make.height.mas_equalTo((BCWidth - 32 - 24) / 4.0);
          } else {
            
            CGFloat itemH = (BCWidth - 32 - 24) / 4.0 + 8;
            CGFloat editHeight = (BCWidth - 32 - 24) / 4.0;
            
            if (editArr.count >= maxCount) {
              if (editArr.count / 4 == 0) {//就一行
                
              } else {
                editHeight = editArr.count / 4  * itemH - 8;
              }
            } else {
              
              if ((editArr.count + 1) % 4 == 0) {//一行正好放得下
                editHeight = ((editArr.count + 1) / 4 ) * (itemH) - 8;
              } else {
                editHeight = ((editArr.count + 1) / 4  + 1) * (itemH ) - 8;
              }
            }
            
            make.height.mas_equalTo(editHeight);
          }
        }];
        __weak MAUploadMutilImageView *weakUP = imageV;
        imageV.chooseBlock = ^(CGFloat bottom) {
          
          [itemV mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(height + 8 + 28 + bottom);
          }];
         
          [weakUP mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(bottom);
          }];
          
          if (conditionDisplay == 1) {
            NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
            for (int i = 0; i < weakSelf.changeItemContions.count; i ++) {
              NSMutableDictionary *tmpContionDic = [NSMutableDictionary dictionaryWithDictionary:[weakSelf.changeItemContions objectAtIndexCheck:i]];
              if ([[tmpContionDic objectNilForKey:@"parentId"] isEqualToString:parentId]) {
                [tmpContionDic setObject:@(height + 8 + 28 + bottom) forKey:@"itemH"];
                [weakSelf.changeItemContions replaceObjectAtIndex:i withObject:tmpContionDic];
                break;
              }
            }
          }
          
        };
      } else if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"LOCATION"]) {
        
        //        条件公式
        if (conditionDisplay == 1) {
          NSString *parentId = [NSString stringWithFormat:@"%@",[itemDic objectForKeyNil:@"id"]];
          NSString *parentTag = [NSString stringWithFormat:@"%d",30000 + j + i];
          NSString *superTag = [NSString stringWithFormat:@"%ld", bottomV.tag];
          NSMutableArray *someArr = [NSMutableArray array];
          for (NSDictionary *somed in conditionGroups) {
            NSArray *subArr = [somed objectForKeyNil:@"conditions"];
            if (BCArrayIsEmpty(subArr)) {
              continue;
            }
            
            for (NSDictionary *subD in subArr) {
              [someArr addObject:[subD objectForKeyNil:@"field"]];
            }
          }
          
          if (!BCArrayIsEmpty(someArr)) {
            NSDictionary *conditionDic = [NSDictionary dictionaryWithObjectsAndKeys:parentId,@"parentId",@(height + 28),@"itemH",parentTag,@"parentTag",superTag,@"superTag",someArr,@"subId",conditionGroups,@"conditionGroups", nil];
            [self.changeItemContions addObject:conditionDic];
          }
          
        }
        
      
        UIButton *locationBtn = [[UIButton alloc] init];
        locationBtn.tag = 20000 + j;
        [itemV addSubview:locationBtn];
        
        UILabel *rightL = [[UILabel alloc] init];
        rightL.font = [UIFont systemFontOfSize:MutilFont(15)];
        rightL.numberOfLines = 1;
        rightL.tag = 666;
        rightL.lineBreakMode = NSLineBreakByTruncatingMiddle;
        rightL.textColor = COLOR(26, 106, 255);
        rightL.text = @"选择地址";
        [locationBtn addSubview:rightL];
        [rightL mas_makeConstraints:^(MASConstraintMaker *make) {
          make.right.mas_equalTo(0);
          make.centerY.mas_equalTo(locationBtn.mas_centerY);
          make.width.mas_lessThanOrEqualTo(locationBtn.mas_width).offset(-24);
        }];
        
        UIImageView *leftIM = [[UIImageView alloc] init];
        leftIM.image = [UIImage imageNamed:@"locationchangeitem"];
        [locationBtn addSubview:leftIM];
        [leftIM mas_makeConstraints:^(MASConstraintMaker *make) {
          make.right.mas_equalTo(rightL.mas_left).offset(-4);
          make.height.width.mas_equalTo(16);
          make.centerY.mas_equalTo(locationBtn.mas_centerY);
        }];
        
        [locationBtn mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(leftBtn.mas_right).offset(6);
          make.top.mas_equalTo(14);
          make.right.mas_equalTo(-16);
          make.height.mas_equalTo(height);
          make.bottom.equalTo(itemV).offset(-14);
        }];
        
        [locationBtn addTarget:self action:@selector(clickGetLocation:) forControlEvents:UIControlEventTouchUpInside];
        
        NSDictionary *dic = [self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
        if (self.draftData && !BCDictIsEmpty(dic)) {
          rightL.text = [NSString stringWithFormat:@"%@",[dic objectNilForKey:@"address"]];
          [locationBtn addMoreParams:dic];
         
        }
        
        
      } else if ([[itemDic objectNilForKey:@"component_type"] isEqualToString:@"TABLE"]) {
        
        
        [leftL mas_updateConstraints:^(MASConstraintMaker *make) {
          make.height.mas_equalTo(0);
        }];
        
        NSArray *editArr = @[];
        if (self.draftData && !BCArrayIsEmpty([self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]])) {
          editArr = [self.draftitemChangeDic objectForKeyNil:[itemDic objectForKeyNil:@"id"]];
        }
        
        MACustomBuniessTableView *tableView = [[MACustomBuniessTableView alloc] initWithFrame:CGRectMake(0, leftL.bottom  - 8, BCWidth, 50) andDataSource:itemDic endInputData:editArr isEdit:YES endState:@"制单"];
        tableView.tag = 20000 + j;
        [itemV addSubview:tableView];
        [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(0);
          make.top.mas_equalTo(leftL.mas_bottom).offset(0);
          make.width.mas_equalTo(BCWidth);
          make.height.mas_greaterThanOrEqualTo(50);
          make.bottom.equalTo(itemV).offset(-14);
        }];


        
      } else {
        
        UITextField *rightTF = [[UITextField alloc] init];
        rightTF.text = @"暂不支持该功能";
        rightTF.userInteractionEnabled = NO;
        rightTF.tag = 20000 + j;
        rightTF.keyboardType = UIKeyboardTypeNumberPad;
        rightTF.returnKeyType = UIReturnKeyDone;
        rightTF.textColor = COLOR(134, 144, 156);
        rightTF.textAlignment = NSTextAlignmentRight;
        rightTF.font = [UIFont systemFontOfSize:MutilFont(15)];
        rightTF.delegate = self;
        rightTF.inputAccessoryView = [self returnDone];
        [itemV addSubview:rightTF];
        [rightTF mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(leftBtn.mas_right).offset(6);
          make.top.mas_equalTo(14);
          make.right.mas_equalTo(-16);
          make.height.mas_equalTo(height);
          make.bottom.equalTo(itemV).offset(-14);
        }];
        
      }
      
      
      
      //    分割线
      UIView *lineV = [[UIView alloc] init];
      lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
      [itemV addSubview:lineV];
      [lineV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.top.mas_equalTo(0);
        make.width.mas_equalTo(BCWidth - 16);
        make.height.mas_equalTo(1);
      }];
      
    
      if (conditionDisplay == 1) {//需要条件显示
        if (![self checkDraftContions:conditionGroups]) {//如果草稿里的值不满足条件
          
          [itemV mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
        }
        
      } else {//不需要条件
        if (display != 1) {//控件不显示
          [itemV mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
          }];
        }
      }
      
      
      lastInV = itemV;
    }
    
    lastOutV = bottomV;
    
  }
  
}
  


#pragma mark 获取定位
- (void)clickGetLocation:(UIButton *)sender{
  
  __weak typeof(self)weakSelf = self;
  UILabel *locationL = [sender viewWithTag:666];
  
  if ([locationL.text isEqualToString:@"选择地址"]) {//获取定位
    
    MAChooseLocationViewController *vc = [[MAChooseLocationViewController alloc] init];
    vc.havePosition = NO;
    [self.navigationController pushViewController:vc animated:YES];
    vc.completionHandler = ^(NSDictionary *position) {
      locationL.text = [position objectNilForKey:@"address"];
      [sender addMoreParams:position];
    };
    
  } else {
    NSDictionary *dic = [sender getMoreParams];
    MAChooseLocationViewController *vc = [[MAChooseLocationViewController alloc] init];
    vc.havePosition = YES;
    vc.pointLatitude = [dic objectNilForKey:@"latitude"];
    vc.pointLongitude = [dic objectNilForKey:@"longitude"];
    [self.navigationController pushViewController:vc animated:YES];
    vc.completionHandler = ^(NSDictionary *position) {
      locationL.text = [position objectNilForKey:@"address"];
      [sender addMoreParams:position];
    };
  }
  
  
}

#pragma mark uiscrollview 代理
- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
  if (scrollView == self.homeScrollView) {
    
    if ( !self.changeView || !self.basicView || self.isCLickScroll) {
      return;
    }
    
    UIButton *sender;
    
    if ((scrollView.contentOffset.y + scrollView.height / 2) <= self.basicView.bottom && scrollView.contentOffset.y + scrollView.height < scrollView.contentSize.height) {
        // 基本信息
        sender = [self.topTabV viewWithTag:1014];
      
    } else if ((scrollView.contentOffset.y + scrollView.height / 2) >= self.changeView.top &&
               (scrollView.contentOffset.y + scrollView.height / 2) <= self.changeView.bottom) {
        // 变动项
        for (int i = 0; i < self.itemChangeArr.count; i++) {
            NSDictionary *dic = [self.itemChangeArr objectAtIndexCheck:i];
            // 跳过系统预设的变动项和未启用的变动项
            if ([[dic objectForKeyNil:@"acquiesce"] isEqual:@(1)] || [[dic objectForKeyNil:@"enable"] isEqual:@(0)]) {
                continue;
            }
            
            UIView *bottomV = [self.changeView viewWithTag:10000 + i];
            CGRect tvRect = [bottomV convertRect:bottomV.bounds toView:self.homeScrollView];
            
            // 判断视图是否居中
            CGFloat centerY = scrollView.contentOffset.y + scrollView.height / 2;
            if (centerY >= tvRect.origin.y && centerY < (tvRect.origin.y + tvRect.size.height)) {
                sender = [self.topTabV viewWithTag:1015 + i];
                break;
            }
        }
    } else if (scrollView.contentOffset.y + scrollView.height >= scrollView.contentSize.height) {
      // 滚动到底部的特殊处理
      NSInteger lastIndex = self.itemChangeArr.count - 1;
      for (NSInteger i = lastIndex; i >= 0; i--) {
        NSDictionary *dic = [self.itemChangeArr objectAtIndexCheck:i];
        // 跳过系统预设的变动项和未启用的变动项
        if ([[dic objectForKeyNil:@"acquiesce"] isEqual:@(1)] || [[dic objectForKeyNil:@"enable"] isEqual:@(0)]) {
          continue;
        }
        
        UIView *bottomV = [self.changeView viewWithTag:10000 + i];
        CGRect tvRect = [bottomV convertRect:bottomV.bounds toView:self.homeScrollView];
        
        // 如果滚动到底部且最后一个视图的高度不足以滚动到屏幕中央，选中最后一个
        if (tvRect.origin.y + tvRect.size.height <= scrollView.contentOffset.y + scrollView.height) {
          sender = [self.topTabV viewWithTag:1015 + i];
          break;
        }
      }
    } else {
      return;
    }
    
    if (sender == nil) {
      return;
    }
    if (sender != _selectTabButton) {
      UILabel *titleL = [_selectTabButton viewWithTag:222];
      titleL.font = [UIFont systemFontOfSize:MutilFont(14)];
      titleL.textColor = ACOLOR(30, 33, 38, 0.7);
      
      UILabel *titleLs = [sender viewWithTag:222];
      titleLs.font = [UIFont systemFontOfSize:MutilFont(14) weight:UIFontWeightMedium];
      titleLs.textColor = COLOR(26, 106, 255);
      
      _selectTabButton = sender;
      
      //    指示条动画
      
      [UIView animateWithDuration:0.1 animations:^{
        self.indicateLayer.frame = CGRectMake(sender.left + (sender.width - 20)/2, sender.bottom - 10, 20, 3);
      }];
      
      
      
      //   计算选项卡滚动位置
      [self scrollSelectedViewToCenter:sender];
      
    }
  }
  
}
#pragma mark 键盘弹出时
- (void)keyboardWillShow:(NSNotification *)noti {
  UIResponder *responder = [self findFirstResponderInView:self.view];
  if ([responder isKindOfClass:[UITextField class]]) {
    UITextField *currentTextField = (UITextField *)responder;
    self.activeTV = currentTextField;
  } else if ([responder isKindOfClass:[UITextView class]]) {
    UITextView *currentTextView = (UITextView *)responder;
    self.activeTV = currentTextView;
  }
  
  if (!self.activeTV) {
    return;
  }
  
  if (self.activeTV == self.nameTF) {
    return;
  }
  
  CGFloat keyboardHeight = [noti.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue].size.height;
  self.homeScrollView.height = self.mainHeight - keyboardHeight + 90;
  CGRect tvRect = [self.activeTV convertRect:self.activeTV.bounds toView:self.homeScrollView];
  tvRect.size.height += 14;
  [self.homeScrollView scrollRectToVisible:tvRect animated:YES];
}

#pragma mark 键盘隐藏时
- (void)keyboardWillHidden:(NSNotification *)noti {
  
  if (self.activeTV == self.nameTF) {
    self.activeTV = nil;
    return;
  }
  
  self.activeTV = nil;
  self.homeScrollView.height =  self.mainHeight;
  
}

- (void)viewDidAppear:(BOOL)animated{
  [super viewDidAppear:animated];
  
  self.navigationController.interactivePopGestureRecognizer.delegate = (id)self;//侧滑手势
  if ([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
  }
}
#pragma mark 导航栏
- (void)initToolBar{
  
  UIView *toolbar = [[UIView alloc] initWithFrame: CGRectMake(0, 0, BCWidth, _heightTop + 44)];
  toolbar.backgroundColor = [UIColor whiteColor];
  [self.view addSubview:toolbar];
  
  //  返回按钮
  UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
  switchMap.frame = CGRectMake(14, self.heightTop ,136, 44);
  [switchMap addTarget:self action:@selector(back) forControlEvents:UIControlEventTouchUpInside];
  [toolbar addSubview:switchMap];
  
  UIImageView *leftIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 12, 20, 20)];
  leftIM.image = [UIImage imageNamed:@"nav_back"];
  [switchMap addSubview:leftIM];
  
  UILabel *titleL = [[UILabel alloc] init];
  titleL.frame = CGRectMake(0, self.heightTop + 4, BCWidth,  25);
  titleL.text = @"新增商圈";
  titleL.textAlignment = NSTextAlignmentCenter;
  titleL.textColor = COLOR(31, 33, 38);
  titleL.font = [UIFont systemFontOfSize:MutilFont(17) weight:UIFontWeightMedium];
  [toolbar addSubview: titleL];
  
  UILabel *saveL = [[UILabel alloc] init];
  saveL.frame = CGRectMake(0, titleL.bottom, BCWidth,  12);
  saveL.text = @"退出时自动保存填写内容";
  saveL.textAlignment = NSTextAlignmentCenter;
  saveL.textColor = ACOLOR(30, 33, 38, 0.7);
  saveL.font = [UIFont systemFontOfSize:MutilFont(9)];
  [toolbar addSubview: saveL];
  
}
- (void)back {
  
  [self.navigationController popViewControllerAnimated:YES];
  
}


- (UIView *)returnDone{
  UIView *customView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, RealSize(42))];
  customView.backgroundColor = COLOR(247, 247, 248);
  UIButton *btn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - 64, 0, 64, RealSize(42))];
  [btn setTitle:@"完成" forState:UIControlStateNormal];
  [btn setTitleColor:COLOR(26, 106, 255) forState:UIControlStateNormal];
  btn.titleLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  [btn addTarget:self action:@selector(btnClickedDone) forControlEvents:UIControlEventTouchUpInside];
  [customView addSubview:btn];
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(0, 0, BCWidth , 1)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [customView addSubview:lineV];
  return customView;
}
#pragma mark 点击键盘上方完成按钮
- (void)btnClickedDone{
  [self.homeScrollView endEditing:YES];
}
#pragma mark uitextfield代理
- (BOOL)textFieldShouldReturn:(UITextField *)textField{
  
  [self.homeScrollView endEditing:YES];
  return YES;
}
- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string{
  
  
  if (string.length <= 0) {
    return YES;
  }
  
  //禁止输入空格
  NSString *tem = [[string componentsSeparatedByCharactersInSet:[NSCharacterSet whitespaceCharacterSet]]componentsJoinedByString:@""];
  
  if (![string isEqualToString:tem]) {
    return NO;
  }
  
  if (textField == self.openStoreTF) {
    NSString * toBeString = [textField.text stringByReplacingCharactersInRange:range withString:string];
    if ([toBeString isEqual:@"0"]){
      return NO;
    }
  }
  
  
  return YES;
}

- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text{
  
  if ([text isEqualToString:@"\n"]){ //判断输入的字是否是回车，即按下return
    
    [self.homeScrollView endEditing:YES];
    return NO;//这里返回NO，不会出现换行，如果为yes，换行
  }
  
  return YES;
}


- (void)dealloc
{
  NSLog(@"新建商圈释放了");
  [[NSNotificationCenter defaultCenter] removeObserver:self];
  _mapView.showsUserLocation = NO;
  [_mapView.layer removeAllAnimations];
  [_mapView removeAnnotations:_mapView.annotations];
  [_mapView removeOverlays:_mapView.overlays];
  [_mapView removeFromSuperview];
  _mapView.delegate = nil;
  _mapView = nil;
  
}
- (MALoadWaveView *)loadingView{
  if (!_loadingView) {
    _loadingView = [[MALoadWaveView alloc] initWithFrame:[UIScreen mainScreen].bounds];
  }
  return _loadingView;
}
- (UIStatusBarStyle)preferredStatusBarStyle{
  if (@available(iOS 13.0, *)) {
    return  UIStatusBarStyleDarkContent;
  } else {
    return UIStatusBarStyleDefault;
  }
}
- (void)Tap{
  [self.view endEditing:YES];
}

- (UIScrollView *)homeScrollView{
  if (!_homeScrollView) {
    _homeScrollView = [[UIScrollView alloc] initWithFrame: CGRectMake(0, self.heightTop + 44 , BCWidth, BCHeight - (self.heightTop + 44  ))];
    _homeScrollView.backgroundColor = COLOR(242, 243, 245);
    _homeScrollView.bounces = NO;
    _homeScrollView.delegate = self;
    _homeScrollView.alpha = 0;
    _homeScrollView.contentInset = UIEdgeInsetsMake(12, 0, 0, 0);
    _homeScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    _homeScrollView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    UITapGestureRecognizer *tableViewTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(Tap)];
    tableViewTap.cancelsTouchesInView = NO;
    [_homeScrollView addGestureRecognizer:tableViewTap];
    
  }
  
  return _homeScrollView;
}

- (MABottomSingleSelectAlert *)levelAlert{
  if (!_levelAlert) {
    _levelAlert = [[MABottomSingleSelectAlert alloc] initWithTitle:@"选择商圈等级" showKey:@"name" showSearch:NO maskClose:YES andData:@[@{@"name":@"S",@"id":@"S"},@{@"name":@"A",@"id":@"A"},@{@"name":@"B",@"id":@"B"},@{@"name":@"C",@"id":@"C"}] endChooseItem:@{}];
  }
  return _levelAlert;
}




- (UIView *)topBackV{
  if (!_topBackV) {
    _topBackV = [[UIView alloc] initWithFrame:CGRectMake(0,self.heightTop + 44, BCWidth, 44)];
    
  }
  return _topBackV;
}

- (UIScrollView *)topTabV{
  if (!_topTabV) {
    _topTabV = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, 44)];
    _topTabV.contentInset = UIEdgeInsetsMake(0, 16, 0, 0);
    _topTabV.backgroundColor = [UIColor whiteColor];
    _topTabV.showsHorizontalScrollIndicator=NO;
    _topTabV.showsVerticalScrollIndicator=NO;
  }
  return _topTabV;
}

- (UIView *)indicateLayer{
  if (!_indicateLayer) {
    _indicateLayer = [[UIView alloc] init];
    _indicateLayer.layer.cornerRadius = 2;
    _indicateLayer.backgroundColor = COLOR(26, 106, 255);
  }
  return _indicateLayer;
}
- (UIView *)topLineV{
  if (!_topLineV) {
    _topLineV = [[UIView alloc] initWithFrame:CGRectMake(0, 43.5,BCWidth , 0.5)];
    _topLineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  }
  
  return _topLineV;
}
- (UIView *)draftView{
  if (!_draftView) {
    _draftView = [[UIView alloc] initWithFrame:CGRectMake(0, self.heightTop + 44 + 44 , BCWidth, 42)];
    _draftView.backgroundColor = COLOR(232, 244, 255);
  }
  
  return _draftView;
}

- (MATopToastView *)statusView{
  if (!_statusView) {
    _statusView = [[MATopToastView alloc] initCustomViewWithDuration:1];
  }
  
  return _statusView;
}

- (MATopNotiflyView *)notiflyView{
  if (!_notiflyView) {
    _notiflyView = [[MATopNotiflyView alloc] initCustomViewWithDuration:1];
  }
  return _notiflyView;
}

- (AMapLocationManager *)locationManager{
  if (!_locationManager) {
    _locationManager = [[AMapLocationManager alloc] init];
    _locationManager.desiredAccuracy = kCLLocationAccuracyNearestTenMeters;
    _locationManager.locationTimeout = 3.5;
    _locationManager.reGeocodeTimeout = 3.5;
  }
  return _locationManager;
}

@end
