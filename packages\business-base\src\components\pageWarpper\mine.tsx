import React, { useEffect, useState, useMemo, useRef } from 'react';
import { XlbText, TOKEN, XlbIconfontNew, XlbCell } from '@xlb/components-rn';
import { Flex } from '@fruits-chain/react-native-xiaoshu';
import FastImage from 'react-native-fast-image';
import { normalize } from '@xlb/common/src/config/theme';
import { authModel, roleAuth } from '@xlb/business-base/src/models/auth';
import Toast from 'react-native-root-toast';
import { useNavigation } from '@react-navigation/native';
import { ErpHttp } from '@xlb/common/src/services/lib/erphttp';
import {
  DeviceEventEmitter,
  Image,
  ImageBackground,
  Linking,
  Pressable,
  ScrollView,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { Routes } from '@xlb/common/src/config/route';

import { useModel } from 'foca';

import useStore from '@xlb/common/src/components/features/xlbStoreText/model';
import useRefreshStore from '@xlb/common/src/models/refresh';
import useSelectStore from '@xlb/business-base/src/screens/home/<USER>/ErpHome/store';
import Loading from '@xlb/common/src/components/RootView/Loading';
import { $modalAlert } from '@xlb/common/src/components/SecondModal';
import useStausBarHeight from '@xlb/business-base/src/hook/useStatusBarHeight';
import { images } from '@xlb/common/src/config/images';
import XlbIcon from '@xlb/common/src/assets/iconfont';
import Clipboard from '@react-native-clipboard/clipboard';
import { useDeviceInfo } from '@/hook/useDeviceInfo';
// import Barcode from 'react-native-barcode-svg'
const { height } = useStausBarHeight();

import { XIcon } from '@xlb/common/src/components';
import AuthAlert from './AuthAlert';
import warpperStore from './store';
import userInfosModal from '../../models/userInfosModal';
import useProductSalesAnalysis from './store1';
const Mine = () => {
  const userInfos = authModel?.state?.userInfos;
  const navigation = useNavigation<any>();
  const setStoreList = useSelectStore((state: any) => state.setStoreList);
  const setRefresh = useRefreshStore((state: any) => state.setHomeRefresh);
  const setStoreLists = useStore((state: any) => state.setStoreList);
  const [electronAuth, setElectronAuth] = useState<any>({});
  const { setUserInfos } = userInfosModal((state: any) => state);
  const { isPDA } = useDeviceInfo();
  const setCell = warpperStore((state: any) => state.setCell);
  const cellRef = useRef(null);
  const model = useProductSalesAnalysis((state: any) => state);

  const auths = useModel(authModel);

  const avatar = useMemo(() => {
    return auths?.userInfos?.avatar_url
      ? { uri: auths?.userInfos?.avatar_url }
      : images['newAvatar'];
  }, [auths]);

  const changeStore = async () => {
    if (authModel?.state?.userInfos?.access_token?.length <= 6) {
      Toast.show('无操作权限');
      return;
    }
    // navigation.navigate('ErpSelectStore', {
    //   isMultiple: false,
    //   postUrl: '/erp/hxl.erp.user.switchstore.page',
    //   storeChange,
    // })

    navigation.navigate('AccountListView', {});
  };

  const storeChange = async (item: any) => {
    Loading.show();
    const res = await ErpHttp.post<CommonResponse>(
      '/erp/hxl.erp.user.store.switch',
      { id: item?.id },
    );
    if (res?.code === 0 && res?.data) {
      authModel?.setUserInfos(res.data);
      authModel?.setSystem(roleAuth(res.data));
      setUserInfos(res.data);
    }
    Loading.hide();
    // 解决首页切换门店问题
    setStoreList([]);
    setStoreLists([]);
    setRefresh();
    DeviceEventEmitter.emit('DepartDocumenRefresh'); //刷新待办事项内的发车单
  };

  useEffect(() => {
    const WMSWaitHandleRefresh = DeviceEventEmitter.addListener(
      'TMSCheckRefresh',
      () => {
        getElectronAuth();
      },
    );
    return () => {
      WMSWaitHandleRefresh.remove();
    };
  }, []);

  useEffect(() => {
    getElectronAuth();
  }, []);

  const getElectronAuth = async () => {
    const id = userInfos?.id;
    if (!id) return;
    const res = await ErpHttp.post<CommonResponse>(
      '/tms/hxl.tms.sign.certification.check',
      {
        tel: userInfos?.tel,
      },
      { timeout: 20000 },
    );
    if (res.code == 0) {
      setElectronAuth(res.data);
    }
  };

  const toCheck = async () => {
    Loading.show();
    const res = await ErpHttp.post<CommonResponse>(
      '/tms/hxl.tms.sign.certification',
      {
        tel: authModel?.state?.userInfos?.tel,
        end_point: 'H5',
      },
    );
    if (res.code == 0) {
      if (res.data.console_url) {
        Loading.hide();
        $modalAlert(
          '是否跳转认证链接?',
          '',
          async () => {
            Linking.openURL(res.data.console_url);
          },
          () => {
            Clipboard.setString(res.data.console_url);
            Toast.show('复制成功，请去浏览器粘贴打开');
          },
          '一键复制',
        );
      } else {
        Loading.hide();
        Toast.show('数据有误');
      }
    }
  };
  const handleLayout = () => {
    if (cellRef.current) {
      cellRef?.current.measure((x, y, width, height, pageX, pageY) => {
        // 减TOKEN.space_4是因为小暑的cell不支持传入ref，在外层包了一个View，页面的Cell有内边距，计算引导图位置需要减去paddingTop
        setCell({
          visable: true,
          x: pageX,
          y: pageY - TOKEN.space_3,
          height: height,
        });
      });
    }
  };

  let isElectronAuth =
    electronAuth?.is_activated && electronAuth.proxy_operator_is_verified;

  const [alertShow, setAlertShow] = useState<boolean>(false);

  return (
    <>
      {/* <Drawer
        open={visible}
        onOpen={() => setVisible(true)}
        onClose={() => setVisible(false)}
        drawerStyle={{
          width: '87.46%',
        }}
        drawerType="slide"
        renderDrawerContent={() => {
          return ( */}
      <ScrollView style={{ flex: 1, marginTop: 50 }}>
        <Flex justify="center" direction="column">
          <View
            style={{
              position: 'relative',
              width: '100%',
              alignItems: 'center',
              marginTop: normalize(12) + height,
            }}>
            <FastImage
              source={avatar}
              style={{
                width: normalize(64),
                height: normalize(64),
                borderRadius: normalize(32),
              }}></FastImage>
            <Pressable
              style={{ position: 'absolute', top: 0, right: normalize(20) }}
              onPress={() => {
                navigation.navigate('RemoteAppBase.UserInfo');
              }}>
              <XlbIconfontNew
                name="bianji"
                color={TOKEN.grey_7}
                size={normalize(20)}></XlbIconfontNew>
            </Pressable>
          </View>

          <XlbText
            style={{
              fontSize: normalize(20),
              marginTop: TOKEN.space_2,
              marginBottom: TOKEN.space_1,
            }}
            grey_10
            semiBold>
            {userInfos?.name}
          </XlbText>
          <XlbText font_size_4 grey_45>
            {' '}
            {userInfos?.tel}
          </XlbText>

          <XlbText
            font_size_4
            grey_10
            style={{ marginTop: TOKEN.space_3, marginBottom: TOKEN.space_5 }}>
            {userInfos?.company_id}｜{userInfos?.company_name}
          </XlbText>

          <TouchableOpacity
            onPress={() => toCheck()}
            activeOpacity={0.5}
            style={{
              paddingHorizontal: normalize(20),
              width: '100%',
              marginBottom: normalize(44),
            }}>
            <ImageBackground
              source={require('@xlb/common/src/assets/images/auth_bg.png')}
              resizeMode="stretch"
              style={{
                // marginHorizontal: normalize(20),
                width: '100%',
                height: normalize(81),
                justifyContent: 'center',
                // alignItems: 'center',
              }}>
              <Flex>
                <FastImage
                  source={
                    isElectronAuth
                      ? require('@xlb/common/src/assets/images/auth1.png')
                      : require('@xlb/common/src/assets/images/auth1.png')
                  }
                  style={{
                    width: normalize(36),
                    height: normalize(36),
                    marginRight: normalize(8),
                    marginLeft: TOKEN.space_3,
                  }}></FastImage>
                <View style={{ flex: 1 }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      paddingRight: 11,
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <XlbText
                      grey_10
                      font_size_5
                      semiBold
                      style={{ marginBottom: TOKEN.space_1 }}>
                      {isElectronAuth ? '已实名认证' : '实名认证'}
                    </XlbText>
                    {electronAuth?.admin && (
                      <TouchableWithoutFeedback
                        style={{ paddingHorizontal: 21 }}
                        onPress={() => {
                          setAlertShow(true);
                        }}>
                        <Image
                          source={require('@xlb/common/src/assets/images/add.png')}
                          style={{
                            width: normalize(20),
                            height: normalize(20),
                          }}
                        />
                      </TouchableWithoutFeedback>
                    )}
                  </View>

                  <XlbText grey_7 font_size_2>
                    {isElectronAuth
                      ? '您已完成实名认证，享受在线签约服务'
                      : '认证电子签，即享在线签约服务'}
                  </XlbText>
                </View>
              </Flex>
            </ImageBackground>
          </TouchableOpacity>

          {/* <Cell
            title="收藏"
            iconName={'shoucang'}
            onPress={() => {
              // setVisible(!visible)
              navigation.navigate('Collect');
            }}></Cell> */}

          <View style={{ width: '100%' }} ref={cellRef} onLayout={handleLayout}>
            <Cell
              title="切换账号"
              iconName={
                // <XIcon name={'iconMine'} style={{ marginRight: TOKEN.space_3, marginTop: 6 }} color={'#9A9B9D'} size={20} />
                <XlbIconfontNew
                  name={'caidan-qiehuanzhanghao'}
                  style={{ marginRight: TOKEN.space_3, marginTop: 6 }}
                  color={'#9A9B9D'}
                  size={20}
                />
              }
              onPress={() => {
                // setVisible(!visible)
                if (authModel?.state?.userInfos?.isTemporarily) return;
                changeStore();
              }}
              valueTextNumberOfLines={1}
              value={
                userInfos?.store_name
                  ? `${userInfos?.store_name}` + '-' + `${userInfos?.account}`
                  : `${userInfos?.account}`
              }></Cell>
          </View>

          {!Boolean(isPDA) && (
            <Cell
              title="切换主题"
              iconName={'iconPifu'}
              onPress={() => {
                // setVisible(!visible)
                navigation.navigate('RemoteAppBase.SwitchTheme');
              }}></Cell>
          )}

          <Cell
            title="客服与帮助"
            iconName={'bangzhu'}
            onPress={() => {
              // setVisible(!visible)
              navigation.navigate('RemoteAppBase.HelpPage');
            }}></Cell>
          <Cell
            title="设置"
            iconName={'shezhi'}
            onPress={() => {
              // setVisible(!visible)
              console.log(*********);
              navigation.navigate('RemoteAppBase.MinSetting');
            }}></Cell>
          <Cell
            title="版本管理"
            iconName={'guanyu'}
            onPress={() => {
              // setVisible(!visible)
              // navigation.navigate(Routes.AboutVersion);
              navigation.navigate('RemoteAppBase.AboutVersion');
            }}></Cell>
          {/* <Cell
            title="老首页"
            iconName={
              <XIcon
                name={'iconHome'}
                style={{marginRight: TOKEN.space_3}}
                color={'#9A9B9D'}
                size={20}
              />
            }
            onPress={() => {
              // setVisible(!visible)
              navigation.navigate('HomeOld');
            }}></Cell> */}

          {__DEV__ && (
            <Cell
              title="BaseWeb"
              iconName={
                <XIcon
                  name={'iconHome'}
                  style={{ marginRight: TOKEN.space_3 }}
                  color={'#9A9B9D'}
                  size={20}
                />
              }
              onPress={() => {
                // setVisible(!visible)
                // navigation.navigate('RemoteAppBase.SelectGoods');
                navigation.navigate('TestComponents');
              }}></Cell>
          )}
        </Flex>
        {/* <View>
          <Barcode
            value="123456789012"  // 确保这里是一个有效的条形码数字字符串
            format="CODE128"
            maxWidth={200}
            height={100}
          />
        </View> */}
        <AuthAlert
          alertShow={alertShow}
          onCancel={() => {
            setAlertShow(false);
          }}
        />
      </ScrollView>
    </>
  );
};

interface CellProps {
  onPress: () => void;
  title: string;
  iconName: string;
}

const Cell: React.FC<CellProps> = ({ onPress, title, iconName, ...rest }) => {
  return (
    <XlbCell
      title={title}
      divider={false}
      titleTextStyle={{
        color: TOKEN.grey_10,
        fontSize: normalize(15),
        paddingVertical: 0,
        alignSelf: 'center',
        alignItems: 'center',
        minHeight: normalize(20),
        lineHeight: normalize(20),
        marginRight: normalize(18),
      }}
      onPress={onPress}
      titleExtra={
        iconName ? (
          typeof iconName === 'string' ? (
            <XlbIcon
              name={iconName}
              size={normalize(20)}
              color="rgba(154, 155, 157, 1)"
              style={{
                alignSelf: 'center',
                marginRight: TOKEN.space_3,
              }}></XlbIcon>
          ) : (
            iconName
          )
        ) : null
      }
      innerStyle={{
        marginHorizontal: TOKEN.space_5,
        paddingVertical: TOKEN.space_4,
      }}
      style={{
        width: '100%',
      }}
      {...rest}></XlbCell>
  );
};

export default Mine;
