/*
 * @Description:
 * @Author: ershu
 * @Date: 2024-04-17 13:44:53
 * @LastEditTime: 2024-05-21 09:27:08
 * @LastEditors: ershu
 */
import { XLBHttp } from '../services/lib/http'
import { ErpHttp } from '../services/lib/erphttp'

/**
 * 通用选择类接口
 */
class Api {
  // 获取门店列表
  getStore = (param: any) => XLBHttp.post('/store/all', param)

  // 获取供应商列表
  getSupplier = (param: any) => XLBHttp.post('/supplier/all', param)

  // 根据门店和商品查询关联供应商信息
  getSupplierByStoreAndProduct = (param: any) => XLBHttp.post('/storeAnalysis/getSupplier', param)

  // 根据门店ID获取配送仓库列表
  getHouseByStore = (param: any) => XLBHttp.post('/store/get/getitemlist', param)

  // 非标 根据门店ID获取配送仓库列表
  getHouse = (param: any) => XLBHttp.post('/house/all', param)

  // 获取商品
  getProduct = (param: any) => XLBHttp.post('/item/get', param)

  // 库存调整原因
  getAdjustReasons = (param: any = null) => XLBHttp.post('/inventory/reason/all', param)

  // 获取批发客户
  getWholesaleCustomers = (param: any = null) => XLBHttp.post('/supplier/all', param)

  // 获取采购员
  getBuyer = (param: any = null) => XLBHttp.post('/user/all', param)

  // 获取配送路线
  getDistributionLine = (param: any = null) => XLBHttp.post('/distributionLine/all', param)

  // 获取分类列表
  getCategories = () => XLBHttp.post('/class/all', { type: 'item' })

  // 获取商品档案
  getCommodity = (param: any) => XLBHttp.post<any>('/item/all', param)

  // 获取车辆
  getVehicle = (param: any) => XLBHttp.post('/car/all', param)

  // 获取采购供应商
  getSupplierPurchase = (param: any) => XLBHttp.post('/storeAnalysis/getSupplier', param)

  // 成分商品调价商品
  getCommodityIndicate = (param: any) => XLBHttp.post<any>('/ingredient/adjust/price/get/item', param)

  // 零售调价商品
  getCommodityRetail = (param: any) => XLBHttp.post<any>('/retail/price/adjust/get/item', param)

  // 成分商品、零售调价门店
  getStoreRetail = (param: any) => XLBHttp.post<any>('/retail/price/adjust/user/all', param)

  // erp新门店接口
  getNewStore = (param: any) => ErpHttp.post<any>('/erp-mdm/hxl.erp.store.short.page', param)
  // erp门店选择仓库接口
  getNewHouse = (param: any) => ErpHttp.post<any>('/erp-mdm/hxl.erp.storehouse.store.find', param)
  // erp门店选择批发客户接口
  getNewWholeClient = (param: any) => ErpHttp.post<any>('/erp-mdm/hxl.erp.client.page', param)
  // erp商品选择新街口
  getNewGoods = (param: any) => ErpHttp.post<any>('/erp-mdm/hxl.erp.requestorder.app.item.page', param)
  // erp采购退货商品选择新街口
  getreturnNewGoods = (param: any) => ErpHttp.post<any>('/erp-mdm/hxl.erp.returnorder.item.page', param)
  // erp供应商新接口
  getNewSupplier = (param: any) => ErpHttp.post<any>('/erp-mdm/hxl.erp.supplier.short.page', param)
  // erp采购订单商品接口
  getPurchaseGoods = (param: any) => ErpHttp.post<any>('/erp-mdm/hxl.erp.purchaseorder.item.page', param)
  // erp根据门店选择仓库
  getStoreHouse = (param: any) => ErpHttp.post<any>('/erp-mdm/hxl.erp.storehouse.store.find', param)
  // wms根据门店选择仓库
  getWmsStoreHouse = (param: any) => ErpHttp.post<any>('/wms/hxl.wms.storehouse.find', param)
  // erp选类别
  getCategory = (param: any) => ErpHttp.post<any>('/erp-mdm/hxl.erp.category.find', param)
  // 获取库位
  getWarehouse = (param: any) => ErpHttp.post<any>('/wms/hxl.wms.storage.page', param)
  // 获取客户
  getClient = (param: any) => ErpHttp.post<any>('/erp-mdm/hxl.erp.client.page', param)
  // 获取客户
  getClients = (param: any) => ErpHttp.post<any>('/wms/hxl.wms.client.find', param)
  // 获取wms商品详情
  getWmsGoodList = (params: any) => ErpHttp.post<CommonResponse>('/wms/hxl.wms.item.page', params)
  // 获取调整原因
  getReasonList = (params: any) => ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.stockadjustmentreason.find', params)
  getKmsOrgList = () => ErpHttp.post<CommonResponse>('/kms/hxl.kms.org.find')
  // 获取新移库任务
  getNewWarehouse = (param: any) => ErpHttp.post<any>('/wms/hxl.wms.storage.stockitem.find', param)
}

export const CommonSelectApi = new Api()
