import {Toast} from 'native-base';
import Config from 'react-native-config';
import {Platform, ToastAndroid} from 'react-native';
import {authModel} from '@xlb/business-base/src/models/auth';
import Const from '@xlb/common/src/utils/const';

const getQuery = (obj: Record<string, any>) => {
  let queryString = '';
  let queryArr: string[] = [];
  for (let key in obj) {
    queryArr = [...queryArr, `${key}=${obj[key]}`];
  }
  queryString = queryArr.join('&');
  return queryArr.length ? '?' + queryString : '';
};

export default class NewXlbUpload {
  /**
   * 上传图片
   * @param image
   */
  static async uploadImage(image: any, id: number, uploadFilesConfig?: any) {
    const formData = new FormData();
    formData.append('file', {
      uri: image.path,
      name: image.filename || 'xlb_default.jpg',
      type: 'multipart/form-data',
    } as any);
    formData.append('refType', uploadFilesConfig?.refType || 'ITEM_PICTURE');
    formData.append('refId', uploadFilesConfig?.refId || id);

    const configUrl =
      uploadFilesConfig?.uploadUrl || '/erp/hxl.erp.file.upload';
    // 调接口 上传
    const urlSplit = configUrl?.split('/')?.filter(item => !!item) || [];

    const urlEnvName = ['ENV', 'DEVELOP', 'TEST'].includes(Config.ENV || '')
      ? 'test'
      : Config.BRANCH_NAME;
    const baseUrl = `https://react-web.react-web.ali-${urlEnvName}.xlbsoft.com/`;

    const requestUrl = baseUrl.replace(/\/$/, '') + configUrl;

    console.log(requestUrl, 'uploadUrl===>new');

    return new Promise((resolve, reject) => {
      fetch(requestUrl, {
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
          'Access-Token': `${authModel?.state?.userInfos?.access_token}`,
          'Api-Version': '1.5.0',
        },
        body: formData,
      })
        .then(res => res.json())
        .then(result => {
          if (result.code === 0) {
            resolve(result.data);
          } else {
            if (Platform.OS === 'android') {
              ToastAndroid.show(result.msg, ToastAndroid.LONG);
            }
            reject(result?.msg || '');
          }
        })
        .catch(error => {
          Toast.show({title: error});
          reject(error);
        });
    });
  }

  static async uploadFile(file: any, photoType: string, params: any) {
    console.log('上传之前', file);
    const mineArr =
      photoType === 'file'
        ? file[file.mime ? 'mime' : 'type']?.split('/') || []
        : [];
    const isIos = Platform.OS === 'ios';
    const fileReg = /^file:\/\//i;

    const path = file?.uri || file?.path;

    const filePath =
      isIos && fileReg.test(path) ? path.replace('file://', '') : path;
    const type =
      mineArr[mineArr.length - 1] || path?.split('/').pop()?.split('.')[1];
    const formData = new FormData();

    const fileName =
      file.name ||
      (decodeURIComponent(
        filePath?.split('/')[filePath?.split('/').length - 1]?.split('.')[0],
      ) || new Date().getTime()) +
        '.' +
        type;

    // const baseUrl = Config.ERP_URL + '/kms/hxl.kms.projectaccountplan.file.upload'
    const configUrl = params?.uploadUrl || '/erp/hxl.erp.file.upload';
    formData.append('file', {
      uri: filePath,
      name: encodeURIComponent(fileName),
      type: 'multipart/form-data',
    });
    formData.append('refType', params?.refType || 'ITEM_FILE');
    formData.append('refId', params?.refId || 'file');

    const urlSplit = configUrl?.split('/')?.filter(item => !!item) || [];

    const urlEnvName = ['ENV', 'DEVELOP', 'TEST'].includes(Config.ENV || '')
      ? 'test'
      : Config.BRANCH_NAME;
    const baseUrl = `https://react-web.react-web.ali-${urlEnvName}.xlbsoft.com/`;

    const requestUrl = baseUrl.replace(/\/$/, '') + configUrl;

    return new Promise((resolve, reject) => {
      fetch(requestUrl, {
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
          'Access-Token': `${authModel?.state?.userInfos?.access_token}`,
          'Api-Version': '1.5.0',
        },
        body: formData,
      })
        .then(res => res?.json())
        .then(result => {
          if (result.code === 0) {
            console.log(result, '上传成功');
            resolve({
              ...result.data,
              name: fileName,
            });
          } else {
            Toast.show(result?.msg || '');
            reject(result?.msg || '');
          }
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  // 上传base64
  static async uploadByBase64(image: any, id: number, uploadFilesConfig?: any) {
    const formData = new FormData();
    formData.append('base64Image', image.data);
    formData.append('refType', uploadFilesConfig?.refType || 'ITEM_PICTURE');
    formData.append('refId', uploadFilesConfig?.refId || id);

    const configUrl =
      uploadFilesConfig?.uploadBase64Url || '/erp/hxl.erp.file.upload';
    // 调接口 上传
    const urlSplit = configUrl?.split('/')?.filter(item => !!item) || [];
    const urlEnvName = ['ENV', 'DEVELOP', 'TEST'].includes(Config.ENV || '')
      ? 'test'
      : Config.BRANCH_NAME;
    const baseUrl = `https://react-web.react-web.ali-${urlEnvName}.xlbsoft.com/`;

    const requestUrl = baseUrl.replace(/\/$/, '') + configUrl;
    return new Promise((resolve, reject) => {
      fetch(requestUrl, {
        method: 'post',
        headers: {
          // 'Content-Type': 'multipart/form-data',
          'Access-Token': `${authModel?.state?.userInfos?.access_token}`,
          'Api-Version': '1.5.0',
        },
        body: formData,
      })
        .then(res => res.json())
        .then(result => {
          if (result.code === 0) {
            resolve(result.data);
          } else {
            if (Platform.OS === 'android') {
              ToastAndroid.show(result.msg, ToastAndroid.LONG);
            }
            console.log(result, 'result');

            reject(result.msg);
          }
        })
        .catch(error => {
          Toast.show({title: error});
          reject(error);
        });
    });
  }
}
