import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'

// 获取数据详情
const getPossSummary = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.possale.summary.find', params, {
    timeout: 20000,
    isHiddenMsg: true,
  })
}
const getMoneypsd = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.possale.moneypsd.find', params, {
    timeout: 20000,
    isHiddenMsg: true,
  })
}

const getMemberMoneypsd = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.possale.member.moneypsd.find', params, {
    timeout: 20000,
    isHiddenMsg: true,
  })
}

const getMemberCountpsd = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.possale.member.countpsd.find', params, {
    timeout: 20000,
    isHiddenMsg: true,
  })
}

const getCountpsd = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.possale.countpsd.find', params, {
    timeout: 20000,
    isHiddenMsg: true,
  })
}
// 获取日商分析
const getDaySummary = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.day.sale.storesummary.find', params, {
    timeout: 20000,
  })
}

// 获取配送毛利详情
const getDeliverySummary = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bi/hxl.bi.app.delivery.summary.find', params, {
    timeout: 20000,
  })
}
// 待办事项
const getWaitHandle = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.homepage.waithandle.find', params, {
    timeout: 20000,
  })
}

// 待办事项
const getAppWaitHandle = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.homepage.waithandle.app.find', params, {
    timeout: 20000,
  })
}
// scm待办事项
const getSCMWaitHandle = (params: any) => {
  return ErpHttp.post<CommonResponse>('/scm/hxl.scm.homepage.waithandle.find', params, {
    timeout: 20000,
  })
}
// ocr识别
const getFileInfo = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.file.ocr', params)
}

//更新门店食品经营许可证
const foodUpdate = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.store.food.update', params)
}
//更新门店营业执照
const licenseUpdate = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.store.license.update', params)
}

//更新门店环境
const updateStoreEnv = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.storefileapply.save', params)
}

//读取门店环境
const readStoreEnv = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.storefileapply.read', params)
}
//待办
const wmsWaitHandle = (params: any) => {
  return ErpHttp.post<CommonResponse>('/wms/hxl.wms.waithandle.find', params)
}

//待办
const uploadLog = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.userlocation.save', params)
}

//截屏上传日志
const uploadPrintScreenLog = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.userlog.printscreen.save', params)
}
//待办
const erpWaitHandle = (params: any) => {
  return ErpHttp.post<CommonResponse>('/bms/hxl.bms.merchant.register.info.read', params)
}

export const erpHomeApi = {
  getDaySummary,
  getPossSummary,
  getDeliverySummary,
  getWaitHandle,
  getSCMWaitHandle,
  getAppWaitHandle,
  getFileInfo,
  licenseUpdate,
  foodUpdate,
  updateStoreEnv,
  readStoreEnv,
  wmsWaitHandle,
  uploadLog,
  uploadPrintScreenLog,
  erpWaitHandle,
  getMoneypsd,
  getMemberMoneypsd,
  getMemberCountpsd,
  getCountpsd,
}
