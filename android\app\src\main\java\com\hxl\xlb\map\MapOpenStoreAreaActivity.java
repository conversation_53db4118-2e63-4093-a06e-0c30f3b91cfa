package com.hxl.xlb.map;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Point;
import android.graphics.drawable.VectorDrawable;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.LocationSource;
import com.amap.api.maps.MapView;
import com.amap.api.maps.Projection;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.LatLngBounds;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.maps.model.MyLocationStyle;
import com.amap.api.maps.model.Polygon;
import com.amap.api.maps.model.PolygonOptions;
import com.amap.api.services.core.AMapException;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.core.PoiItemV2;
import com.amap.api.services.poisearch.PoiResultV2;
import com.amap.api.services.poisearch.PoiSearchV2;
import com.google.gson.Gson;
import com.hxl.xlb.R;
import com.hxl.xlb.adapter.PointSearchContentAdapter;
import com.hxl.xlb.bean.FileBean;
import com.hxl.xlb.bean.OpenStoreAreaBean;
import com.hxl.xlb.bean.WarpmapLocationFlagBean;
import com.hxl.xlb.contract.MapOpenStoreAreaContract;
import com.hxl.xlb.contract.PickUtilResultContract;
import com.hxl.xlb.responsebody.OpenStoreAreaUpdateReponse;
import com.hxl.xlb.utils.AreaOverlapUtil;
import com.hxl.xlb.utils.CustomUploadCollection;
import com.hxl.xlb.utils.MySPTool;
import com.hxl.xlb.utils.PickerUtil;
import com.hxl.xlb.widget.CustomUpload;
import com.xlb.mvplibrary.mvp.MvpActivity;
import com.xlb.mvplibrary.network.errorhandler.ExceptionHandle;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MapOpenStoreAreaActivity extends MvpActivity<MapOpenStoreAreaContract.MapOpenStoreAreaPresenter> implements MapOpenStoreAreaContract.IOpenStoreAreaView, View.OnClickListener, LocationSource,
        AMapLocationListener, AMap.OnCameraChangeListener, PoiSearchV2.OnPoiSearchListener {

    private MapView mapView;
    private AMap aMap;
    private OnLocationChangedListener mListener;
    private AMapLocationClient mlocationClient;
    private AMapLocationClientOption mLocationOption;
    private MyLocationStyle myLocationStyle;

    private int businessId;//商圈id
    // 商圈区域code
    private int areaCode;

    private boolean useMoveToLocationWithMapMode = true;

    //自定义定位小蓝点的Marker
    private Marker locationMarker;
    //坐标和经纬度转换工具
    Projection projection;
    private LatLng latLngLocation;

    //蓝色图标
    private Marker centerMarker;
    private MarkerOptions markerOption;

    //标记点图标
    private Marker pointMarker;

    private Polygon drawPolygon;// 用于保存当前绘制的多边形对象

    private List<LatLng> drawPointsList;

    //可开店区域点位集合
    private List<OpenStoreAreaBean> storeOpenAreaPointList = new ArrayList<>();
    private float mapLevel;

    // 点位弹窗打开弹窗标记
    private boolean isPointSearchSheetDialogOpen = true;
    private LatLng centerlatLng;
    private View pointSelectViewSheet, pointContentView;
    private EditText  nameEt, addressEt;

    private TextView searchEt;
    private RecyclerView contentRv;
    private View loadingView;
    private FrameLayout listContainer;
    private PointSearchContentAdapter adapter;
    private ArrayList<PoiItemV2> allList = new ArrayList<>();
    private String searchContent = "";
    private int pageNumber = 0;
    // 选中的点位数据
    private PoiItemV2 selectedPoiItem;

    // 表示是否从新增或者编辑进入二次修改地址进入当前页面 选择点位的时候也要支持了
    private boolean poiSearchChangeFlag = false;

    // 判断地图滑动时点击搜索的点位还是滑动地图,搜索的点位不滑动
    //  private boolean poiSearchPointClick = false;
    // 选择标记点的经纬度
    private LatLng selectedLatLng;

    private RelativeLayout drawRl;
    private RadioGroup priorityRg;
    private CustomUpload openStoreAreaUpload;
    private ActivityResultLauncher<Intent> filePickerLauncher;
    private PickerUtil pickerUtil;
    private CustomUploadCollection uploadCollection = CustomUploadCollection.getInstance("OPEN_STORE_AREA_POINT_ADD");
    private HashMap<String, Object> dataMap;
    private TextView numberTv;
    private EditText contentEt;

    //可开店区域优先级
    private int storeAreaPriority = 1;

    // 可开店区域id
    private long storeAreaId;

    //点位id 若是存在点位id则表示可开店区域内容 不可修改
    private Integer storePlanId;

    private String storePlanName;

    private OpenStoreAreaBean openStoreAreaBean;
    //是否从商圈新增页面跳转而来
    private boolean MapAddBusinessJumpFlag;
    //是否从商圈新增页面修改功能跳转而来
    private boolean MapAddBusinessJumpEditFlag;
    //是否从商圈编辑页面跳转而来
    private boolean MapEditBusinessJumpFlag;
    //是否从商圈编辑页面修改功能跳转而来
    private boolean MapEditBusinessJumpEditFlag;

    @Override
    protected MapOpenStoreAreaContract.MapOpenStoreAreaPresenter createPresenter() {
        return new MapOpenStoreAreaContract.MapOpenStoreAreaPresenter();
    }

    @Override
    public void initData(Bundle savedInstanceState) {
        getWindow().setWindowAnimations(R.style.AnimBottom);
        getWindow().setGravity(Gravity.BOTTOM);
        mapView = (MapView) findViewById(R.id.map);
        mapView.onCreate(savedInstanceState);// 此方法必须重写

//        name = getIntent().getStringExtra("storePlanName");
//        address = getIntent().getStringExtra("address");
//        latitude = getIntent().getDoubleExtra("latitude", -1);
//        longitude = getIntent().getDoubleExtra("longitude", -1);

        init();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_map_open_store_area;
    }

    /**
     * 对VIewPager进行初始化.
     */
    private void init() {
        //获取默认的EventBus对象(单例)，并把当前对象注册为Subscriber。
        //注意：当销毁当前实例的时候必须注销这个Subscriber。一般与Activity的生命周期绑定
        EventBus.getDefault().register(this);

        if (aMap == null) {
            aMap = mapView.getMap();
            setUpMap();
        }

        findViewById(R.id.back_ll).setOnClickListener(this);
        findViewById(R.id.location_ll).setOnClickListener(this);

        pointSelectViewSheet = findViewById(R.id.point_select_view);
        pointContentView = findViewById(R.id.point_content_view);
        listContainer = findViewById(R.id.point_search_list_container); // 需要先在布局中添加这个容器
        loadingView = LayoutInflater.from(context).inflate(com.xlb.mvplibrary.R.layout.dialog_loading, listContainer, false);
        listContainer.addView(loadingView);
        loadingView.setVisibility(View.GONE);
        // 初始化控件
        contentRv = findViewById(R.id.point_search_list);
        searchEt = findViewById(R.id.search_poi_et);

        drawRl = findViewById(R.id.draw_rl);
        findViewById(R.id.close_rl).setOnClickListener(this);
        findViewById(R.id.open_store_area_close_rl).setOnClickListener(this);
        findViewById(R.id.save_tv).setOnClickListener(this);
        nameEt = findViewById(R.id.name_et);
        addressEt = findViewById(R.id.address_et);
        priorityRg = findViewById(R.id.priority_rg);
        openStoreAreaUpload = findViewById(R.id.open_store_area_upload);
        contentEt = findViewById(R.id.content_et);
        numberTv = findViewById(R.id.number_tv);

        drawPointsList = (List<LatLng>) getIntent().getSerializableExtra("drawPoints");
        storeOpenAreaPointList  =  getIntent().getParcelableArrayListExtra("storeOpenAreaPointList");
        mapLevel = getIntent().getFloatExtra("mapLevel", 15.5f);
        businessId = getIntent().getIntExtra("business_plan_id", -1);
        areaCode = getIntent().getIntExtra("areaCode", -1);
        MapAddBusinessJumpFlag = getIntent().getBooleanExtra("MapAddBusinessJumpFlag", false);
        MapEditBusinessJumpFlag = getIntent().getBooleanExtra("MapEditBusinessJumpFlag", false);
        MapAddBusinessJumpEditFlag = getIntent().getBooleanExtra("MapAddBusinessJumpEditFlag", false);
        MapEditBusinessJumpEditFlag = getIntent().getBooleanExtra("MapEditBusinessJumpEditFlag", false);
        String openStoreAreaBeanStr = getIntent().getStringExtra("OpenStoreAreaBean");
        //  openStoreAreaBean = (OpenStoreAreaBean) getIntent().getBundleExtra("bundle").getSerializable("OpenStoreAreaBean");
        if (null != drawPointsList && !drawPointsList.isEmpty()) {
//            setDrawPolygonDrawable(drawPointsList);
            LatLng polygonCenter = AreaOverlapUtil.calculateCentroid(drawPointsList); // 获取多边形的中心点坐标
            centerlatLng = polygonCenter;
            // 将地图的中心点设置为centerPosition
//            aMap.moveCamera(CameraUpdateFactory.changeLatLng(polygonCenter));
            // 可以选择将缩放级别设置为一个合适的值
//            aMap.moveCamera(CameraUpdateFactory.zoomTo(18.5f));
            addMarkerToMap(new LatLng(polygonCenter.latitude, polygonCenter.longitude));
        }

        priorityRg.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                // 根据选中的RadioButton做相应的操作
                if (checkedId == R.id.priority_one_rb) {
                    storeAreaPriority = 1;
                    addMarkerPointToMap(selectedLatLng, R.drawable.open_store_area_point_priority_one_iv);
                } else if (checkedId == R.id.priority_two_rb) {
                    storeAreaPriority = 2;
                    addMarkerPointToMap(selectedLatLng, R.drawable.open_store_area_point_priority_two_iv);
                } else if (checkedId == R.id.priority_three_rb) {
                    storeAreaPriority = 3;
                    addMarkerPointToMap(selectedLatLng, R.drawable.open_store_area_point_priority_three_iv);
                }
            }
        });
        ImageView etClearIv = findViewById(R.id.et_clear_iv);
//        searchEt.addTextChangedListener(new TextWatcher() {
//            @Override
//            public void afterTextChanged(Editable s) {
//            }
//
//            @Override
//            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
//            }
//
//            @Override
//            public void onTextChanged(CharSequence s, int start, int before, int count) {
//                etClearIv.setVisibility(s.length() > 0 ? View.VISIBLE : View.GONE);
//            }
//        });
//
//        searchEt.setOnEditorActionListener((v, actionId, event) -> {
//            searchContent = searchEt.getText().toString();
//            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
//                performSearch();
//                return true;
//            }
//            return false;
//        });
        searchEt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(context,MapLocationFlagActivity.class);
                intent.putExtra("centerlatLng", centerlatLng);
                startActivity(intent);


            }
        });
        etClearIv.setOnClickListener(v -> searchEt.setText(""));

        filePickerLauncher = registerForActivityResult(
                new PickUtilResultContract(),
                this::onActivityResult
        );

        pickerUtil = new PickerUtil(this, filePickerLauncher, "openStoreArea");
        openStoreAreaUpload.setFilePickerLauncher(filePickerLauncher);
        setupUploadCallback(openStoreAreaUpload, "openStoreArea");

        contentEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // 在文本改变之前调用，这里可以不做处理
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // 在文本改变时调用，这里可以处理字数限制等逻辑
                int length = s.length();
                // 这里可以添加字数限制的逻辑，例如：
                numberTv.setText(length + "/100");
            }

            @Override
            public void afterTextChanged(Editable s) {
                // 在文本改变之后调用，这里可以更新UI显示当前字数等
            }
        });

        if ((MapAddBusinessJumpFlag || MapEditBusinessJumpFlag) && (MapAddBusinessJumpEditFlag || MapEditBusinessJumpEditFlag)) {
            if (!TextUtils.isEmpty(openStoreAreaBeanStr)) {
                openStoreAreaBean = new Gson().fromJson(openStoreAreaBeanStr, OpenStoreAreaBean.class);
                if (null != openStoreAreaBean) {
                    storeAreaId = openStoreAreaBean.getId();
                    String name = openStoreAreaBean.getStore_open_area_name();
                    String address = openStoreAreaBean.getAddress();
                    storePlanId = openStoreAreaBean.getStore_plan_id();
                    storePlanName = openStoreAreaBean.getStore_plan_name();
                    String extensionStrategy = openStoreAreaBean.getExtension_strategy();
                    List<LatLng> LatLngList = openStoreAreaBean.getArea_range();
                    List<FileBean> files = openStoreAreaBean.getFiles();
                    //storePlanId = openStoreAreaBean.getStore_plan_id();
                    if (null != LatLngList && !LatLngList.isEmpty()) {
                        selectedLatLng = LatLngList.get(0);
                    }
                    storeAreaPriority = openStoreAreaBean.getPriority();
                    Log.d("TAG","优先级" + storeAreaPriority);
                    if (1 == storeAreaPriority) {
                        priorityRg.check(R.id.priority_one_rb);
                        addMarkerPointToMap(selectedLatLng, R.drawable.open_store_area_point_priority_one_iv);
                    } else if (2 == storeAreaPriority) {
                        priorityRg.check(R.id.priority_two_rb);
                        addMarkerPointToMap(selectedLatLng, R.drawable.open_store_area_point_priority_two_iv);
                    } else if (3 == storeAreaPriority) {
                        priorityRg.check(R.id.priority_three_rb);
                        addMarkerPointToMap(selectedLatLng, R.drawable.open_store_area_point_priority_three_iv);
                    } else {
                        priorityRg.check(R.id.priority_one_rb);
                        addMarkerPointToMap(selectedLatLng, R.drawable.open_store_area_point_priority_one_iv);
                    }

                    if (!TextUtils.isEmpty(name)) {
                        nameEt.setText(name);
                    }
                    if (!TextUtils.isEmpty(address)) {
                        addressEt.setText(address);
                    }
                    if (!TextUtils.isEmpty(extensionStrategy)) {
                        contentEt.setText(extensionStrategy);
                    }
                    if (null != files && !files.isEmpty()) {
                        openStoreAreaUpload.setFiles(files);
                    }
                    showBottomPointContentView();
                }
            }

        } else {
            showBottomSheet();
        }

    }

    /**
     * 设置一些amap的属性
     */
    private void setUpMap() {
        aMap.setLocationSource(this);// 设置定位监听
        myLocationStyle = new MyLocationStyle();//初始化定位蓝点样式类
        myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE);//连续定位、且将视角移动到地图中心点，定位点依照设备方向旋转，并且会跟随设备移动。（1秒1次定位）如果不设置myLocationType，默认也会执行此种模式。
        myLocationStyle.interval(2000); //设置连续定位模式下的定位间隔，只在连续定位模式下生效，单次定位模式下不会生效。单位为毫秒。
        aMap.setMyLocationStyle(myLocationStyle);//设置定位蓝点的Style
        aMap.getUiSettings().setZoomControlsEnabled(false);//隐藏缩放地图按钮
        aMap.getUiSettings().setTiltGesturesEnabled(false);//关闭倾斜手势 效果
        //aMap.getUiSettings().setMyLocationButtonEnabled(true);//设置默认定位按钮是否显示，非必需设置。
        aMap.setMyLocationEnabled(true);// 设置为true表示启动显示定位蓝点，false表示隐藏定位蓝点并不进行定位，默认是false。
        aMap.setOnCameraChangeListener(this);//监听地图的移动和缩放变化
    }

    /**
     * 在地图上添加marker
     */
    private void addMarkerToMap(LatLng latLng) {
        if (isPointSearchSheetDialogOpen) {
            if (centerMarker == null) {
                VectorDrawable vectorDrawable = (VectorDrawable) ContextCompat.getDrawable(context, R.drawable.map_position_navigation_iv);
                Bitmap bitmap = Bitmap.createBitmap(vectorDrawable.getIntrinsicWidth(), vectorDrawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
                Canvas canvas = new Canvas(bitmap);
                vectorDrawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
                vectorDrawable.draw(canvas);

                markerOption = new MarkerOptions().icon(BitmapDescriptorFactory.fromBitmap(bitmap))
                        .position(latLng)
                        .icon(BitmapDescriptorFactory.fromBitmap(bitmap))
                        .anchor(0.5f, 0.0f);
                centerMarker = aMap.addMarker(markerOption);
                //AMap对象的moveCamera方法或者animateCamera方法来设置地图的中心点。这里有两种方法：直接移动和动画移动。
//                aMap.moveCamera(CameraUpdateFactory.newLatLng(latLng));
                // 可以选择将缩放级别设置为一个合适的值
//                aMap.moveCamera(CameraUpdateFactory.zoomTo(15.5f));
            } else {
                // 更新位置并触发动画
                centerMarker.setPosition(latLng);
            }

        } else if (centerMarker != null) {
            centerMarker.remove();
            centerMarker = null;
        }
    }

    /**
     * 在地图上添加marker
     */
    private void addMarkerPointToMap(LatLng latLng, int drawable) {
        if (null != pointMarker) {
            pointMarker.remove();
            pointMarker = null;
        }
        VectorDrawable vectorDrawable = (VectorDrawable) ContextCompat.getDrawable(context, drawable);
        Bitmap bitmap = Bitmap.createBitmap(vectorDrawable.getIntrinsicWidth(), vectorDrawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        vectorDrawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        vectorDrawable.draw(canvas);

        MarkerOptions markerOption = new MarkerOptions().icon(BitmapDescriptorFactory.fromBitmap(bitmap))
                .position(latLng)
                .icon(BitmapDescriptorFactory.fromBitmap(bitmap))
                .anchor(0.5f, 0.5f);
        pointMarker = aMap.addMarker(markerOption);
        //AMap对象的moveCamera方法或者animateCamera方法来设置地图的中心点。这里有两种方法：直接移动和动画移动。
//                aMap.moveCamera(CameraUpdateFactory.newLatLng(latLng));
//                // 可以选择将缩放级别设置为一个合适的值
//                aMap.moveCamera(CameraUpdateFactory.zoomTo(15.5f));


    }

    private void setDrawPolygonDrawable(List<LatLng> area_range) {
        if (drawPolygon != null) {
            drawPolygon.remove(); // 移除临时绘制的多边形
            drawPolygon = null; // 清空reference
        }
        if (null != area_range && !area_range.isEmpty()) {
            if (area_range.size() > 2) {
                // 声明 多边形参数对象
                PolygonOptions polygonOptions = new PolygonOptions();
// 添加 多边形的每个顶点（顺序添加）
                polygonOptions.addAll(area_range);
                polygonOptions.strokeWidth(4) // 多边形的边框
                        .strokeColor(getResources().getColor(R.color.color_map_draw_red)) // 边框颜色
                        .fillColor(getResources().getColor(R.color.color_map_draw_fill_red)); // 多边形的填充色
                drawPolygon = aMap.addPolygon(polygonOptions);

                // 计算多边形的边界范围
                LatLngBounds.Builder builder = new LatLngBounds.Builder();
                for (LatLng latLng : area_range) {
                    builder.include(latLng);
                }
                LatLngBounds bounds = builder.build();
// 设置地图的中心点和缩放级别以包含多边形
                aMap.moveCamera(CameraUpdateFactory.newLatLngBounds(bounds, 20)); // 20是padding，可以根据需要调整


            }
        }

        //  setDrawPolyLineOthers();
    }

    private void onActivityResult(PickUtilResultContract.ActivityResultWithExtra result) {
        if (result.getResultCode() == Activity.RESULT_OK) {
            Intent data = result.getPhotoFileIntent();
            // 处理返回的数据
//            Log.i("TAG", "------filePickerLauncher-----------onActivityResult data: " + data);
//            Log.i("TAG", "------filePickerLauncher-----------onActivityResult resultCode: " + result.getResultCode());
//            Log.i("TAG", "------filePickerLauncher-----------onActivityResult getExtra: " +  result.getExtra());
//            Log.i("TAG", "------filePickerLauncher-----------onActivityResult getPhotoFileUrl: " +  result.getPhotoFileUrl());
//            Log.i("TAG", "------filePickerLauncher-----------onActivityResult getPhotoFileIntent: " +  result.getPhotoFileIntent());
            Object extra = result.getExtra();
//            Log.i("MapAddBusinessActivity", "ActivityResult received: " + result);
            if (data != null) {
                // Log the entire Intent and its extras
                String receivedId = (String) extra;
                if (receivedId != null) {
                    CustomUpload uploadInstance = uploadCollection.getCustomUpload(receivedId);
                    if (uploadInstance != null) {
                        uploadInstance.onFilePickerResult(result);
                    } else {
                        Log.i("TAG", "No CustomUpload instance found for operatorId: " + receivedId);
                    }
                }
            } else {
                Log.i("TAG", "No data received in ActivityResult");
            }

        }
    }

    private void setupUploadCallback(CustomUpload uploadInstance, String fileType) {
        Map<String, Object> query = new HashMap<>();
        query.put("fileType", fileType);
        query.put("fid", businessId);
        Log.i("CustomUpload", "setupUploadCallback: " + query.toString());
        //  Log.i("CustomUpload", "receivedMap: " + receivedMap.toString());
        uploadInstance.setUploadExtraParams(query);

    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onResume() {
        super.onResume();
        mapView.onResume();
        useMoveToLocationWithMapMode = false;
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onPause() {
        super.onPause();
        mapView.onPause();
        useMoveToLocationWithMapMode = false;
    }


    /**
     * 方法必须重写
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        mapView.onDestroy();
        if (null != mlocationClient) {
            mlocationClient.onDestroy();
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.back_ll) {
            finish();
        } else if (v.getId() == R.id.location_ll) {
            useMoveToLocationWithMapMode = true;
            if (mlocationClient != null) {
                if (null != latLngLocation) {
                    startMoveLocationAndMap(latLngLocation);
                } else {
                    mlocationClient.startLocation();
                }
            } else {
                newLocationClient();
            }
        } else if (v.getId() == R.id.close_rl) {
            hideBottomSheet();
            finish();
        } else if (v.getId() == R.id.open_store_area_close_rl) {
            hideBottomPointContentView();
            finish();
        } else if (v.getId() == R.id.save_tv) {
            saveData();
        }

    }

    // 提交保存
    private void saveData() {
        if (null != drawPolygon && null != selectedLatLng) {
            boolean isInside = drawPolygon.contains(selectedLatLng);
            if (!isInside) {
                // 点不在多边形内
                showMsg("标记点需在可开店区域商圈范围内");
                return;
            }
        }
        String name = nameEt.getText().toString().trim();
        String address = addressEt.getText().toString().trim();
        String content = contentEt.getText().toString().trim();
        CustomUpload uploadInstance = uploadCollection.getCustomUpload("openStoreArea");
        List<String> urls = new ArrayList<>();
        List<LatLng> latLngList = new ArrayList<>();
        List<FileBean> fileList = new ArrayList<>();
        if (uploadInstance != null) {
            fileList = uploadInstance.getFiles();
            for (FileBean bean : fileList) {
                urls.add(bean.getUrl());
            }
        }
        if (TextUtils.isEmpty(name)) {
            showCenterToast("请填写名称");
            return;
        }
        if (TextUtils.isEmpty(address)) {
            showCenterToast("请填写地址");
            return;
        }
        if (null != selectedLatLng) {
            latLngList.add(selectedLatLng);
        }
        showLoadingDialog();
        HashMap body = new HashMap<>();
        body.put("id", storeAreaId);
        body.put("address", address);
        body.put("area_code", areaCode);
        body.put("area_range", latLngList);
        body.put("extension_strategy", content);
        // body.put("store_plan_id",businessId);
        body.put("business_plan_id", businessId);
        body.put("draw_type", "DRAW_POINT");
        body.put("priority", storeAreaPriority);
        body.put("store_open_area_name", name);
        body.put("store_plan_id", storePlanId);
        body.put("store_plan_name", storePlanName);
        body.put("urls", urls);
        mPresenter.openStoreAreaUpdatePOST(body);

    }

    // 显示底部弹窗
    private void showBottomSheet() {
        pointSelectViewSheet.setVisibility(View.VISIBLE);
        pointSelectViewSheet.animate().translationY(0).setDuration(1);
        startSearch("");


        setDrawPolygonDrawable(drawPointsList);
    }

    // 隐藏底部弹窗
    private void hideBottomSheet() {
        int height = pointSelectViewSheet.getHeight();
        pointSelectViewSheet.animate().translationY(height).setDuration(1);
        pointSelectViewSheet.setVisibility(View.GONE);
        isPointSearchSheetDialogOpen = false;
        //poiSearchPointClick = false;

        if (centerMarker != null) {
            centerMarker.remove();
            centerMarker = null;
        }

        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        // 假设你的EditText的id是editTextId
        imm.hideSoftInputFromWindow(searchEt.getWindowToken(), 0);

    }

    // 显示底部弹窗
    private void showBottomPointContentView() {
        pointContentView.setVisibility(View.VISIBLE);
        pointContentView.animate().translationY(0).setDuration(1);
    }

    // 隐藏底部弹窗
    private void hideBottomPointContentView() {
        int height = pointContentView.getHeight();
        pointContentView.animate().translationY(height).setDuration(1);
        pointContentView.setVisibility(View.GONE);

        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        // 假设你的EditText的id是editTextId
        imm.hideSoftInputFromWindow(searchEt.getWindowToken(), 0);

    }

    private void setPointContent() {
        if (null != selectedPoiItem) {
            String name = selectedPoiItem.getTitle();
            String address = selectedPoiItem.getProvinceName() + selectedPoiItem.getCityName() + selectedPoiItem.getAdName() + selectedPoiItem.getSnippet();

            if (!TextUtils.isEmpty(name)) {
                nameEt.setText(name);
            }
            if (!TextUtils.isEmpty(address)) {
                addressEt.setText(address);
            }
            selectedLatLng = new LatLng(selectedPoiItem.getLatLonPoint().getLatitude(), selectedPoiItem.getLatLonPoint().getLongitude());
            // selectedPoiItem.
            addMarkerPointToMap(selectedLatLng, R.drawable.open_store_area_point_priority_one_iv);
        }
    }

    private void performSearch() {
        if (centerlatLng == null) return;
        pageNumber = 0;
        allList.clear();
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
        startSearch(TextUtils.isEmpty(searchContent) ? "" : searchContent);
    }

    public void updateLocation(double newLng, double newLat) {

        centerlatLng = new LatLng(newLat, newLng);
        Log.i("CenterMarker", "updateLocation: " + newLng + "lat:" + newLat);
        pageNumber = 0; // 重置分页
        allList.clear();
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
        performSearch(); // 执行新的搜索
    }


    private void startSearch(String keyword) {
        if (pageNumber < 0) pageNumber = 0;
        if (pageNumber == 0) {
            loadingView.setVisibility(View.VISIBLE);
            contentRv.setVisibility(View.GONE);
        }
        String types = "汽车服务|汽车销售|汽车维修|摩托车服务|餐饮服务|购物服务|生活服务|体育休闲服务|医疗保健服务|住宿服务|风景名胜|商务住宅|政府机构及社会团体|科教文化服务|交通设施服务|金融保险服务|公司企业|道路附属设施|地名地址信息|公共设施";
//        PoiSearchV2.Query query = new PoiSearchV2.Query(keyword, types, "");
        PoiSearchV2.Query query = new PoiSearchV2.Query(keyword, types, "");
        query.setLocation(new LatLonPoint(centerlatLng.latitude, centerlatLng.longitude)); // 使用最新坐标
//        query.setDistanceSort(true);
        query.setPageNum(pageNumber);
        query.setPageSize(15);

        if (centerlatLng != null) {
            query.setLocation(new LatLonPoint(centerlatLng.latitude, centerlatLng.longitude));
        }

        Log.i("onPoiSearched", "query" + new Gson().toJson(query));


        try {
            PoiSearchV2 search = new PoiSearchV2(context, query);

            search.setOnPoiSearchListener(this);
            if (centerlatLng != null) {
                search.setBound(new PoiSearchV2.SearchBound(new LatLonPoint(centerlatLng.latitude, centerlatLng.longitude), 1500));
            }
            search.searchPOIAsyn();
        } catch (AMapException e) {
            Toast.makeText(context, "搜索初始化失败", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onPoiItemSearched(PoiItemV2 poiItem, int code) {
        loadingView.setVisibility(View.GONE);
        contentRv.setVisibility(View.VISIBLE);

    }

    @Override
    public void onPoiSearched(PoiResultV2 result, int code) {
        loadingView.setVisibility(View.GONE);
        contentRv.setVisibility(View.VISIBLE);
        Log.i("onPoiSearched", "onPoiSearched: " + result + "code:" + code);
        Log.i("onPoiSearched", "longitude: " + centerlatLng.longitude + "latitude:" + centerlatLng.latitude);


        if (code == AMapException.CODE_AMAP_SUCCESS && result != null) {
            ArrayList<PoiItemV2> pois = result.getPois();
            Log.i("onPoiSearched", "onPoiSearched: " + pois.toString());
            if (pois != null && !pois.isEmpty()) {
                if (pageNumber == 0) {
                    allList.clear();
                }
                for (PoiItemV2 item : pois) {
                    if (!allList.contains(item)) {
                        allList.add(item);
                    }
                }
//                allList.addAll(pois);
                updateAdapter();
                pageNumber++;
            } else if (pois != null && pois.isEmpty()) {
                if (!TextUtils.isEmpty(searchContent) && pageNumber == 0) {
                    allList.clear();
                    updateAdapter();
                }


            }
        }
    }

    private void updateAdapter() {
        if (adapter == null) {
            adapter = new PointSearchContentAdapter(context, allList, new PointSearchContentAdapter.OnItemListener() {
                @Override
                public void onItem(int position) {
                    selectedPoiItem = allList.get(position);
//                    if (poiSearchChangeFlag) {
//                        handleChangePoiItem();
//                    } else {
//                        findOrganization();
//
//                    }

                    Boolean isAdded = false;
                    for (int i = 0; i < storeOpenAreaPointList.size(); i++) {
                        OpenStoreAreaBean tempBean = storeOpenAreaPointList.get(i);

                        LatLng latlng = tempBean.getArea_range().get(0);

                        if(selectedPoiItem.getLatLonPoint().getLatitude() == latlng.latitude &&
                            selectedPoiItem.getLatLonPoint().getLongitude() == latlng.longitude
                        ){
                            isAdded = true;
                        }
                    }
                    if(isAdded){
                        showCenterToast("该点位已存在，请重新选择");
                        return ;
                    }


                    hideBottomSheet();
                    setPointContent();
                    showBottomPointContentView();

                }

                @Override
                public void onClickItem(int position) {
                    PoiItemV2 curClickPoiItem = allList.get(position);
                    if (curClickPoiItem != null) {

                        if (curClickPoiItem.getLatLonPoint() != null) {
                            //poiSearchPointClick = true;

                            aMap.moveCamera(CameraUpdateFactory.newLatLngZoom(new LatLng(curClickPoiItem.getLatLonPoint().getLatitude(), curClickPoiItem.getLatLonPoint().getLongitude()), aMap.getCameraPosition().zoom));
                        }
                    }

                }

            }, true);
            contentRv.setLayoutManager(new LinearLayoutManager(context));
            contentRv.setAdapter(adapter);
        } else {
            adapter.notifyDataSetChanged();
        }

    }


    @Override
    public void onLocationChanged(AMapLocation aMapLocation) {

        if (mListener != null && aMapLocation != null) {
            if (aMapLocation != null && aMapLocation.getErrorCode() == 0) {
                latLngLocation = new LatLng(aMapLocation.getLatitude(), aMapLocation.getLongitude());
                MySPTool.setDouble(this, "latitude", aMapLocation.getLatitude());
                MySPTool.setDouble(this, "longitude", aMapLocation.getLongitude());
                //展示自定义定位小蓝点
                if (locationMarker == null) {
                    //首次定位
                    locationMarker = aMap.addMarker(new MarkerOptions().position(latLngLocation)
                            .icon(BitmapDescriptorFactory.fromResource(R.drawable.map_location_mark))
                            .anchor(0.5f, 0.5f));

                    //首次定位,选择移动到地图中心点并修改级别到15.5级
//                     aMap.moveCamera(CameraUpdateFactory.newLatLngZoom(latLngLocation, 15.5f));
                } else {

                    if (useMoveToLocationWithMapMode) {
                        //二次以后定位，使用sdk中没有的模式，让地图和小蓝点一起移动到中心点（类似导航锁车时的效果）
                        startMoveLocationAndMap(latLngLocation);
                    } else {
                        startChangeLocation(latLngLocation);
                    }

                }


            } else {
                String errText = "定位失败," + aMapLocation.getErrorCode() + ": " + aMapLocation.getErrorInfo();
                Log.e("AmapErr", errText);
            }
        }
    }

    /**
     * 修改自定义定位小蓝点的位置
     *
     * @param latLng
     */
    private void startChangeLocation(LatLng latLng) {

        if (locationMarker != null) {
            LatLng curLatlng = locationMarker.getPosition();
            if (curLatlng == null || !curLatlng.equals(latLng)) {
                locationMarker.setPosition(latLng);
            }
        }
    }

    /**
     * 同时修改自定义定位小蓝点和地图的位置
     *
     * @param latLng
     */
    private void startMoveLocationAndMap(LatLng latLng) {

        //将小蓝点提取到屏幕上
        if (projection == null) {
            projection = aMap.getProjection();
        }
        if (locationMarker != null && projection != null) {
            LatLng markerLocation = locationMarker.getPosition();
            Point screenPosition = aMap.getProjection().toScreenLocation(markerLocation);
//            locationMarker.setPositionByPixels(screenPosition.x, screenPosition.y);

        }
        //移动地图，移动结束后，将小蓝点放到放到地图上
//        myCancelCallback.setTargetLatlng(latLng);
        //动画移动的时间，最好不要比定位间隔长，如果定位间隔2000ms 动画移动时间最好小于2000ms，可以使用1000ms
        //如果超过了，需要在myCancelCallback中进行处理被打断的情况
        aMap.animateCamera(CameraUpdateFactory.changeLatLng(latLng), 1000, myCancelCallback);

        useMoveToLocationWithMapMode = false;
    }

    @Override
    public void onCameraChange(CameraPosition cameraPosition) {

        if (null != centerMarker && isPointSearchSheetDialogOpen) {
            centerMarker.setPosition(cameraPosition.target);

        }
    }

    @Override
    public void onCameraChangeFinish(CameraPosition cameraPosition) {
        if (isPointSearchSheetDialogOpen) {
            // 获取当前中心点坐标（纬度、经度）
            double lat = cameraPosition.target.latitude;
            double lng = cameraPosition.target.longitude;
//            poiSearchPointClick = true
//            if (!poiSearchPointClick) {
            centerlatLng = cameraPosition.target;
            updateLocation(lng, lat);
//            }
            Log.d("CenterMarker", "当前中心点坐标：" + lat + ", " + lng);
        }

    }

    MyCancelCallback myCancelCallback = new MyCancelCallback();

    @Override
    public void openStoreAreaUpdatePOST(OpenStoreAreaUpdateReponse reponse) {
        hideLoadingDialog();
        if (reponse == null) {
            return;
        }
        int code = reponse.code;
        if (code == 0 && reponse.data != null) {
            openStoreAreaBean = reponse.data;
            EventBus.getDefault().post(openStoreAreaBean);
            finish();
            showMsg("保存成功");
        } else {
            showMsg("保存失败");
        }
    }

    @Override
    public void openStoreAreaUpdatePOSTFailed(Throwable e) {
        hideLoadingDialog();
        if (e != null) {
            //服务器异常
            ExceptionHandle.ResponseThrowable responseThrowable = (ExceptionHandle.ResponseThrowable) e;
            String msg = responseThrowable.msg;
            // String msg = e.getMessage();
            if (!TextUtils.isEmpty(msg)) {
                showMsg(msg);
            }
        } else {
            showMsg("保存失败");
        }

    }


    /**
     * 监控地图动画移动情况，如果结束或者被打断，都需要执行响应的操作
     */
    class MyCancelCallback implements AMap.CancelableCallback {

        LatLng targetLatlng;

        public void setTargetLatlng(LatLng latlng) {
            this.targetLatlng = latlng;
        }

        @Override
        public void onFinish() {
            if (locationMarker != null && targetLatlng != null) {
                locationMarker.setPosition(targetLatlng);
            }
        }

        @Override
        public void onCancel() {
            if (locationMarker != null && targetLatlng != null) {
                locationMarker.setPosition(targetLatlng);
            }
        }
    }

    /**
     * 激活定位
     */
    @Override
    public void activate(OnLocationChangedListener listener) {
        mListener = listener;
        newLocationClient();
    }

    private void newLocationClient() {
        if (mlocationClient == null) {
            try {
                mlocationClient = new AMapLocationClient(this);
                mLocationOption = new AMapLocationClientOption();
                //设置定位监听
                mlocationClient.setLocationListener(this);
                //设置为高精度定位模式
                mLocationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
                //是指定位间隔
                mLocationOption.setInterval(2000);
                //设置定位参数
                mlocationClient.setLocationOption(mLocationOption);
                // 此方法为每隔固定时间会发起一次定位请求，为了减少电量消耗或网络流量消耗，
                // 注意设置合适的定位时间的间隔（最小间隔支持为2000ms），并且在合适时间调用stopLocation()方法来取消定位请求
                // 在定位结束后，在合适的生命周期调用onDestroy()方法
                // 在单次定位情况下，定位无论成功与否，都无需调用stopLocation()方法移除请求，定位sdk内部会移除
                mlocationClient.startLocation();
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

    }

    /**
     * 停止定位
     */
    @Override
    public void deactivate() {
        mListener = null;
        if (mlocationClient != null) {
            mlocationClient.stopLocation();
            mlocationClient.onDestroy();
        }
        mlocationClient = null;
    }

    @Subscribe
    public void onEvent(WarpmapLocationFlagBean event) {
        //选择 点位 后

        PoiItemV2 curClickPoiItem = event.getCurClickPoiItem();
//                ArrayList<PoiItemV2> allList =  data.getParcelableArrayListExtra("pointAllList");

        searchEt.setText(event.getCurClickPoiItem().getTitle());

        aMap.moveCamera(
                CameraUpdateFactory.newLatLngZoom(
                        new LatLng(
                                curClickPoiItem.getLatLonPoint().getLatitude(),
                                curClickPoiItem.getLatLonPoint().getLongitude()
                        ), aMap.getCameraPosition().zoom
                )
        );

    }
}

