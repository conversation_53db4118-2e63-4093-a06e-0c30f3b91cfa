import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'
import {
  BuildTasktStatisticResDTO,
  ClientClueStatisticReqDTO,
  ClientClueStatisticResDTO,
  SignClientReqDTO,
  SignClientStatisticResDTO,
  TargetStatisticResDTO,
  UserNoticePageReqDTO,
  UserNoticeResDTO,
  WaitHandleDetailResDTO,
} from './type'

export default {
  //客户统计
  getCilentStatistic: (data: ClientClueStatisticReqDTO) => ErpHttp.post<Result<ClientClueStatisticResDTO>>('/kms/hxl.kms.report.intentclient.find', data),

  //客户折线图数据
  getChartList: (data: ClientClueStatisticReqDTO) => ErpHttp.post<Result<ClientClueStatisticResDTO>>('/kms/hxl.kms.report.intentlinechart.find', data),
  //客户折线图数据
  getTableList: (data: ClientClueStatisticReqDTO) => ErpHttp.post<Result<ClientClueStatisticResDTO>>('/kms/hxl.kms.report.intentclient.detail.find', data),

  //标地统计
  getTargetStatistic: (data: ClientClueStatisticReqDTO) => ErpHttp.post<Result<TargetStatisticResDTO>>('/kms/hxl.kms.app.target.statistic', data),

  //工程统计
  getBuildtaskStatistic: (data: ClientClueStatisticReqDTO) => ErpHttp.post<Result<BuildTasktStatisticResDTO>>('/kms/hxl.kms.app.buildtask.statistic', data),

  //用户公告
  getNotice: (data: UserNoticePageReqDTO) => ErpHttp.post<Result<TableList<UserNoticeResDTO>>>('/erp-mdm/hxl.erp.usernotice.page', data),

  //待办事项
  getWaitHandle: () => ErpHttp.post<Result<WaitHandleDetailResDTO[]>>('/kms/hxl.kms.join.waithandle.find'),

  // 签约top10 &  签约概况
  getSignClientStatistic: (data: SignClientReqDTO) => ErpHttp.post<Result<SignClientStatisticResDTO>>('/kms/hxl.kms.signclient.statistic', data),

  // 线索
  getClueAddStatistic: (data: any) => ErpHttp.post('/kms/hxl.kms.clientclew.add.statistic', data),
  getClueStatistic: (data: any) => ErpHttp.post('/kms/hxl.kms.clientclew.distribution.statistic', data),
  getClueDetailStatistic: (data: any) => ErpHttp.post('/kms/hxl.kms.clientclew.distribution.detail.statistic', data),
  //意向客户分析卡片
  getIntentclientstore: (data: any) => ErpHttp.post('/kms/hxl.kms.intentclientstore.statistics', data),
  //正式客户分析卡片页
  getNormalclientstore: (data: any) => ErpHttp.post('/kms/hxl.kms.normalclientstore.statistics', data),
  //开店榜单卡片页
  getShopTopList: (data?: any) => ErpHttp.post('/kms/hxl.kms.openstoretop.statistics', data),
  // 开店分布分析卡片页
  getShopAnalysisList: (data?: any) => ErpHttp.post('/kms/hxl.kms.openstoredistribution.statistics', data),
  // 获取组织列表
  gitOrgList: () => ErpHttp.post('/kms/hxl.kms.organization.currentuser.find'),

  // 开店榜单详情页
  getShopTopDetail: (data: any) => ErpHttp.post('/kms/hxl.kms.openstoretop.detail.page', data),
  // 开店分布分析详情页
  getShopAnalysisDetail: (data: any) => ErpHttp.post('/kms/hxl.kms.openstoretop.detail.find', data),
  // 正式客户分析详情页
  getNormalclientstoreDetail: (data: any) => ErpHttp.post('/kms/hxl.kms.normalclientstore.detail.find', data),
  // 意向客户分析详情页
  getIntentclientstoreDetail: (data: any) => ErpHttp.post('/kms/hxl.kms.intentclientstore.detail.find', data),

  // 加盟商开店明细
  getShopDetail: (data: any) => ErpHttp.post('/kms/hxl.kms.clientopenstore.read', data),
  // 加盟商意向金明细
  getEarnestMoney: (data: any) => ErpHttp.post('/kms/hxl.kms.clientintentmoney.read', data),
  getBusinessAuthFun: (data: any) => ErpHttp.post('/kms/hxl.kms.currentuser.read', data),
}
