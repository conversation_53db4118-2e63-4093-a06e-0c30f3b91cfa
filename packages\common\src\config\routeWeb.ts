import Config from 'react-native-config'


export let domin = `https://mobile-erp.erp.ali-${Config.ENV}.xlbsoft.com`
export const hrsDomain = `https://mobile-hrs-h5.hrs.ali-${Config.ENV}.xlbsoft.com`

export const fsmsDomain = `https://xlb-mobile-fsms.fsms.ali-${Config.ENV}.xlbsoft.com`

export const kmsDomain =  `https://xlb-mobile-kms.kms.ali-${Config.ENV}.xlbsoft.com`

// 会员营销
export const memDomain = `https://member-h5.member.ali-${Config.ENV}.xlbsoft.com`

// 店务系统
export const smsDomain = `https://sms-h5.sms-web.ali-${Config.ENV}.xlbsoft.com`

export const oaDomain = `https://mobile-oa.oa.ali-${Config.ENV}.xlbsoft.com`

export let routes = {
  deliveryMargin: `${domin}/deliveryMargin`,
  couponsRefund: `${domin}/couponsRefund`,
  selectStore: `${domin}/selectStore`,
  accountReconciliationSummary: `${domin}/accountReconciliationSummary`,
  storeCompared: `${domin}/storeCompared`,
  shopRunAnalysis: `${domin}/xlb_erp/shopRunAnalysis`,
  postOrder: `${domin}/postOrder`,
  memberAnalysis: `${domin}/xlb_member/memberAnalysis`,
  goodsSalesAnalysis: `${domin}/xlb_erp/goodsSalesAnalysis`,
  dateCheck: `${domin}/dateCheck`,
  goodsRepurchase: `${domin}/goodsRepurchase`,
  stratificationanalysis: `${domin}/stratificationanalysis`,
  // fsms
  qualityReporting: `${fsmsDomain}/xlb_erp/qualityReporting`, //fsms质量提报
  governmentCheck: `${fsmsDomain}/xlb_fsms/spotCheckList`, //fsms政府抽检
  timePeriodAnalysis: `${domin}/timePeriodAnalysis`, // 时段销售分析
  dayBusinessAnalysis: `${domin}/dayBusinessAnalysis`, // 日商分析
  productPerformance: `${domin}/productPerformance`, // 单品业绩
  prepaidCardRefund: `${domin}/prepaidCardRefund`, // 储值卡返款
  installationAnalysis: `${domin}/installationAnalysis`, // 安装进度分析
  daySalesAnalysis: `${domin}/daySalesAnalysis`, // 日销数据分析
  palletAnalysis: `${domin}/xlb_erp/businessPallet`,

  businessRebates: `${domin}/businessRebates`, // 营业返款

  anomalousItems: `${domin}/abnormalItem`,
  storeInformation: `${fsmsDomain}/xlb_fsms/storeInformation`, //fsms证件管理

  salesAnalysis: `${domin}/salesAnalysis`, // 销售分析
  paymentAnalysis: `${domin}/paymentAnalysis`,
  storeHouse: `${fsmsDomain}/xlb_fsms/storeHouse`, //scm商品抽检
  foodSafety: `${fsmsDomain}/xlb_fsms/foodSafety`, //fsms食品安全员
  foodSafetyManagement: `${fsmsDomain}/xlb_fsms/foodSafetyManagement`, //fsms食品安全管理制度
  foodSafetyCheck: `${fsmsDomain}/xlb_fsms/foodSafetyCheck`, //fsms食品安全自查
  dayCheckDetails: `${fsmsDomain}/xlb_fsms/dayDetail`, //fsms食品安全自查日管控详情
  /**scm采购计划 */
  procurementPlan: `${fsmsDomain}/xlb_fsms/procurementPlan`,
  pestManagement: `${fsmsDomain}/xlb_fsms/pestManagement`, //fsms虫害管理
  customerManagement: `${fsmsDomain}/xlb_fsms/customerManagement`,

  newProductSign: `${fsmsDomain}/xlb_fsms/newProductSign`, // 新品申请

  newProductCompanySign: `${fsmsDomain}/xlb_fsms/newProductCompanySign`, // 新品签核
  storeHouseFsms: `${fsmsDomain}/xlb_fsms/storeHouseFsms`, // fsms商品抽检
  warehouseInspection: `${fsmsDomain}/xlb_fsms/warehouseInspection`, // fsms仓库巡检
  storeOpeningCertificate: `${fsmsDomain}/xlb_fsms/storeOpeningCertificate`, // fsms开店证件

  stockingTracking: `${domin}/CNYStockUp`, // 春节备货跟踪

  //   SMS店务
  // ShopPatrolTask: `${smsDomain}/h5SmsTaskList`, // 巡店任务

  //scm营业返款
  scmBusinessRebates: `${domin}/xlb_scm/scmBusinessRebates`,

  shortInsuranceGoodsAnalysis: `${domin}/shortInsuranceGoodsAnalysis`, // 短保商品分析
  //异常单品
  abnormalItem: `${domin}/abnormalItem`,
  // 物质统计 单据详情
  materialStatisticsOrderDetail: `${domin}/xlb_erp/materialStatisticsOrderDetail`,

  // 缺货预警
  shortageWarning: `${domin}/xlb_erp/shortageWarning`,
  // 到货提醒
  arrivalReminder: `${domin}/xlb_erp/arrivalReminder`,
  //商品缺货率
  goodsStockoutRate: `${domin}/xlb_erp/goodsStockoutRate`,
}

//   SMS店务
export const smsWebRoutes = {
  ShopPatrolTask: `${smsDomain}/h5SmsTaskList`, // 巡店任务
  ShopReport: `${smsDomain}/h5SmsStoreReport`, // 门店报备
  TaskOrderManage: `${smsDomain}/h5SmsTaskOrderManage`, // 任务单管理
  SmsStoreAssistant: `${smsDomain}/h5SmsStoreAssistant`, // 店务助手
  SmsSummaryReport: `${smsDomain}/h5SmsSummaryReport`, // 总结报告
  ShopPatrolList: `${smsDomain}/h5SmsPatrolList`, // 巡店记录
  ShopPatrolOnSite: `${smsDomain}/h5SmsPatrolOnSite`, // 现场巡店
  ShopPatrolStatics: `${smsDomain}/h5SmsPatrolStatistics`, // 巡店统计
  ShopCheckItemStatistics: `${smsDomain}/h5SmsCheckItemStatistics`, // 检查项统计
  ShopCheckItemDetail: `${smsDomain}/h5SmsCheckItemDetail`, // 检查项明细
  ShopImageEditor: `${smsDomain}/h5SmsimageEditor`, // 门店拍照
}

export const hrsWebRoutes = {
  attendance: `${hrsDomain}/attendance`, // 考勤
  pm: `${hrsDomain}/pm`, // 人事
  setting: `${hrsDomain}/setting`,
  schedule: `${hrsDomain}/attendance/schedule`, //排班管理
  perventCheat: `${hrsDomain}/attendance/perventCheat`, //防作弊管理
  attanceMechain: `${hrsDomain}/attendance/attanceMechain`, //考勤机管理
  attanceShift: `${hrsDomain}/attendance/attendShift`, //班次管理
  attendanceGroup: `${hrsDomain}/attendance/attendanceGroup`, //考勤组管理
  newCycleShift: `${hrsDomain}/attendance/newCycleShift`, //新建周期排班
  choiceAttendanceShift: `${hrsDomain}/attendance/choiceAttendanceShift`, //选择班次
  applicationApply: `${hrsDomain}/application/apply`, //假勤申请
  applicationIndex: `${hrsDomain}/application`, //假勤申请首页
  pmEmployee: `${hrsDomain}/pm/employee`, //人事管理相关
  classes: `${hrsDomain}/setting/classes`, //人事管理相关
  contract: `${hrsDomain}/contract`, //合同管理相关
  report: `${hrsDomain}/xlb_mobile_hrs/report`, //人事报表
  hrsSalary: `${hrsDomain}/xlb_mobile_hrs/salary`, //工资单
}

export const oaWebRoutes = {
  approval: `${oaDomain}/xlb_mobile_oa/approval`, // 审批中心
  approvalDetail: `${oaDomain}/xlb_mobile_oa/approveCenter/item`, // 审批详情
}
export const kmsWebRoutes = {
  /*********** EMS **************/
  workOrderClient: `${kmsDomain}/workOrderClient`, // 工单管理客户端
  workOrderClientAdd: `${kmsDomain}/workOrderClient/add`, // 工单管理客户端 新增页
  workOrderClientDetail: `${kmsDomain}/workOrderClient/detail`, // 工单管理客户端
  workOrderServer: `${kmsDomain}/workOrderServer`, // 工单管理服务端
  workOrderServerDetail: `${kmsDomain}/workOrderServer/detail`, // 工单管理服务端
  constructionManage: `${kmsDomain}/constructionManagement`,
  constructionManageDetail: `${kmsDomain}/constructionManagement/detail`,
  board: `${kmsDomain}/board`,
  investmentPoster: `${kmsDomain}/marketingApp/investmentPoster`,
  investmentList: `${kmsDomain}/marketingApp/investmentList`,
}

//  全域营销
export const MEMRoutes = {
  pointList: `${memDomain}/xlb_mem/pointList`,
  memberMange: `${memDomain}/xlb_mem/mem-information`, // 会员管理
  // couponAnalysisReport: `${memDomain}/xlb_mem/couponAnalysisReport`, // 消费券分析
}
