import React from 'react';
import {View} from 'react-native';
import Card from './Card';
import {commonStyles, XApplication} from '@xlb/common/src/components';
import {useNavigation} from '@react-navigation/native';
import useHasAuth from '@xlb/common/src/hooks/useHasAuth';
import {images} from '@xlb/common/src/config/images';

const CommonDataApp = (props: any) => {
  const {date} = props;
  const navigation = useNavigation<any>();
  return (
    <Card
      title={'常用数据'}
      content={() => (
        <View
          style={{
            width: '100%',
            ...commonStyles.horBox,
            flexWrap: 'wrap',
            paddingVertical: 10,
          }}>
          {useHasAuth(['营业收款报表', '查询']) ? (
            <View style={{width: '25%'}}>
              <XApplication
                text={'销售分析'}
                image={images.storeAnalysis}
                route={'storeSaleAnalysis'}
                onPress={() =>
                  navigation.navigate('storeSaleAnalysis', {
                    date,
                    name: '销售额',
                  })
                }
              />
            </View>
          ) : null}
          {/* {useHasAuth(['BOSS报表', '查询']) ? (
            <View style={{ width: '25%' }}>
              <XApplication text={'BOSS报表'} image={images.erpStoreBoss} route={'bossTable'} onPress={() => navigation.navigate('bossTable')} />
            </View>
          ) : null} */}
          {useHasAuth(['营业收款报表', '查询']) ? (
            <View style={{width: '25%'}}>
              <XApplication
                text={'营业收款分析'}
                image={images.appCollectionAnlysis}
                route={'CollectionAnalysisIndex'}
                onPress={() => navigation.navigate('CollectionAnalysisIndex')}
              />
            </View>
          ) : null}
          {useHasAuth(['配送分析', '查询']) ? (
            <View style={{width: '25%'}}>
              <XApplication
                text={'配送分析'}
                image={images.deliveryMargins}
                route={'DeliveryMargins'}
                onPress={() => navigation.navigate('DeliveryMargins')}
              />
            </View>
          ) : null}
          {useHasAuth(['商品销售分析', '查询']) ? (
            <View style={{width: '25%'}}>
              <XApplication
                text={'商品销售分析'}
                image={images.goodsQuery}
                route={'productSalesAnalysis'}
                onPress={() =>
                  navigation.navigate('productSalesAnalysis', {
                    date: date,
                    name: '销售额',
                  })
                }
              />
            </View>
          ) : null}
          {useHasAuth(['商品销售分析', '查询']) ? (
            <View style={{width: '25%'}}>
              <XApplication
                text={'商品销售分析新'}
                image={images.goodsQuery}
                route={'productSalesAnalysisH5'}
                onPress={() =>
                  navigation.navigate('productSalesAnalysisH5', {
                    date: date,
                    name: '销售额',
                  })
                }
              />
            </View>
          ) : null}
          {useHasAuth(['区域销售分析', '查询']) ? (
            <View style={{width: '25%'}}>
              <XApplication
                text={'区域销售分析'}
                image={images.areaSaleThink}
                route={'AreaSaleThinkIndex'}
                onPress={() => navigation.navigate('AreaSaleThinkIndex')}
              />
            </View>
          ) : null}
        </View>
      )}
      rightContent={() => <></>}
    />
  );
};

export default CommonDataApp;
