import React, {useRef, useState, useEffect} from 'react';
import type {FC} from 'react';

import {
  View,
  Image,
  Platform,
  Linking,
  TouchableOpacity,
  Alert,
  StatusBar,
  DeviceEventEmitter,
  Text,
  SafeAreaView, NativeModules,
} from 'react-native';

import {useNavigation} from '@react-navigation/native';

import Config from 'react-native-config';
import Toast from 'react-native-root-toast';

import {Space, Divider, Badge, Blank} from '@fruits-chain/react-native-xiaoshu';
// import { TOKEN, XlbText, XlbIconfont } from '@xlb/components-rn'

import {normalize} from '@xlb/common/src/config/theme';
import {Routes} from '@xlb/common/src/config/route';
// import { sendAuthRequest, registerApp } from '@xlb/common/src/utils/WeixinLogin'
// import { AuthHttp } from '@xlb/common/src/services/lib/authHttp'

import Header from '@xlb/common/src/xlb-components-new/Header';

import AgreementModel from './components/agreementModel';
import Footer from './components/Footer';

import Login from './pages/login';
import RoleList from './pages/roleListPage';

import useModel from './model';
import updateConfig from '../../../../../update.json';

import style from './style';
import commonStyles from '@xlb/components-rn/styles';
import {TOKEN, XlbText} from '@xlb/components-rn';
import XlbUpdate from 'src/components/xlbUpdate'

const Index: FC = () => {
  const navigation: any = useNavigation();

  // const { init } = useAliOnepass()

  const isLogin = useModel(state => state.isLogin);
  const agreementModel = useModel(state => state.agreementModel);
  const resetItems = useModel(state => state.resetItems);

  console.log(32, agreementModel);

  const setAgreementModel = useModel(state => state.setAgreementModel);
  const setIsNeedSend = useModel(state => state.setIsNeedSend);
  const [hasUpdate, setHotUpdate] = useState<boolean>(false)
  const codeLoginRef = useRef<{agreeCountdownfn: () => void}>(null);

  // 隐私协议详情页
  const gotoPriviceDetail = (type: 'user' | 'privacy') => {
    setAgreementModel(false);
    navigation.navigate('Agreement', {
      type: type,
    });
  };

  // const initWeixin = () => {
  //   if (registerApp)
  //     registerApp('wxc0590763665be19b', 'universalLink').then((res) => {
  //       console.log('registerApp: ' + res)
  //     })
  // }

  useEffect(() => {
    XlbUpdate.getIsUpdate().then(res=> {
      setHotUpdate(res[0])
    })
    StatusBar?.setTranslucent(true);
  }, []);

  return (
    <>
      <StatusBar hidden={false} translucent />

      <View style={style.content}>
        <Image
          source={require('@xlb/common/src/assets/images/bg.png')}
          style={style.content_bg}
          resizeMode={'cover'}></Image>

        <Header
          detailPage
          hasInputFilter={false}
          leftContent={<View></View>}></Header>

        <View style={style.login_content}>
          {isLogin ? <RoleList></RoleList> : <Login ref={codeLoginRef}></Login>}

          <Space gap={normalize(30)}>
            {/* <Blank left={normalize(24)} right={normalize(24)}>
              <View style={{ alignItems: 'center' }}>
                <Divider type="light" textStyle={{ color: TOKEN.grey_45, fontSize: TOKEN.font_size_2 }}>
                  更多登录方式
                </Divider>
                <TouchableOpacity
                  style={style.weixin_login}
                  onPress={() => {
                    console.log(333)

                    sendAuthRequest('snsapi_userinfo', '')
                      .then((response: any) => {
                        // Alert.alert('登录成功，code: ' + response.code)
                        console.log(127, response.code)

                        AuthHttp.post('/login.app.social', { client_id: 'erp', type: 'WECHAT', code: response.code }).then((res) => {
                          console.log(128, res)
                        })
                      })
                      .catch((error) => {
                        console.log(118, error)
                        let errorCode = Number(error.code)
                        if (errorCode === -2) {
                          Alert.alert('已取消授权登录')
                        } else {
                          Alert.alert('微信授权登录失败')
                        }
                      })
                  }}
                >
                  <XlbIconfont name="weixin" size={TOKEN.space_6} color="#fff"></XlbIconfont>
                </TouchableOpacity>
              </View>
            </Blank> */}
            <Space
              gap={TOKEN.space_2}
              direction="horizontal"
              justify="center"
              align="center">
              <Space direction="horizontal" align="center" gap={normalize(6)}>
                {hasUpdate ? <Badge dot /> : null}
                <XlbText font_size_2 grey_45>
                  版本号：V {Config.VERSION_NAME}
                </XlbText>
              </Space>
              <Divider direction="vertical" color={TOKEN.grey_1}></Divider>
              <XlbText font_size_2 grey_45 onPress={() => Linking.openURL('https://www.pgyer.com/xinlingbangceshi-android')}>
                 最新下载地址
              </XlbText>
              <Divider direction="vertical" color={TOKEN.grey_1}></Divider>
              <XlbText font_size_2 grey_45>
                万辰集团版权所有
              </XlbText>
            </Space>
          </Space>
        </View>
      </View>
      <Footer style={{backgroundColor: '#fff'}}></Footer>

      {/* 隐私协议同意弹框 */}
      <AgreementModel
        setPrivacyAgreement={gotoPriviceDetail}
        sendMeg={() => {
          setIsNeedSend(false);
          if (codeLoginRef.current) codeLoginRef.current.agreeCountdownfn();
        }}
      />
    </>
  );
};

export default Index;
