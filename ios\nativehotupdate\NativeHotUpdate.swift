//
//  NativeHotUpdate.swift
//  a608
//
//  Created by 赵盛 on 2025/7/13.
//
import Foundation
import React

@objc(NativeHotUpdate)
class NativeHotUpdate: NSObject {
  private var cachePath = "RNHotUpdateBundle"
  private let bundleFileName = "index.bundle"
  private var isDownloading = false
  private let oss = URL(
    string: RNCConfig.env(for: "ENV") == "PROD" ? "https://rn-bundle-cdn.xlbsoft.com/" :
      "https://rn-bundle-tmp-just-for-test.oss-cn-hangzhou.aliyuncs.com/"
  )

  // MARK: - 判断是否有更新
  @objc func hasUpdate(_ remoteVersion: String?, resolver resolve: @escaping RCTPromiseResolveBlock,
                       rejecter reject: @escaping RCTPromiseRejectBlock) {
      // 添加空值检查
      guard let remoteVersion = remoteVersion else {
        resolve(false)
        return
      }
      
      let currentVersion = UserDefaults.standard.string(forKey: "current_version")
      let bundleFileURL = getCachedBundleURL()
      let bundleFileExists = bundleFileURL != nil && FileManager.default.fileExists(atPath: bundleFileURL!.path)
      let bundleFileSize = bundleFileExists ? (try? FileManager.default.attributesOfItem(atPath: bundleFileURL!.path)[.size] as? NSNumber)?.intValue ?? 0 : 0
      
      // 更健壮的检查逻辑
      let hasUpdate: Bool
      if currentVersion == nil {
        hasUpdate = true // 没有当前版本，需要更新
      } else if remoteVersion != currentVersion {
        hasUpdate = true // 版本不同，需要更新
      } else if !bundleFileExists {
        hasUpdate = true // 文件不存在，需要更新
      } else if bundleFileSize <= 0 {
        hasUpdate = true // 文件为空，需要更新
      } else {
        hasUpdate = false
      }
      resolve(hasUpdate)
  }

  // MARK: - 触发下载任务
  @objc func runDownload(_ remoteVersion: String, downloadUrl: String, resolver resolve: @escaping RCTPromiseResolveBlock,
                         rejecter reject: @escaping RCTPromiseRejectBlock) {
      if isDownloading {
        DispatchQueue.main.async {
          print("正在下载中...")
        }
        resolve(false)
        return
      }
      
      isDownloading = true
      
      Task {
        await downloadAndCacheBundle(remoteVersion: remoteVersion, downloadUrl: downloadUrl)
        isDownloading = false
        resolve(true)
      }
  }

  // MARK: - 安装 APK（iOS 中无意义，但为了 API 一致性保留）
  @objc func installApk(_ filePath: String, resolver resolve: @escaping RCTPromiseResolveBlock,
                        rejecter reject: @escaping RCTPromiseRejectBlock) {
    // iOS 不支持安装 APK，这里只是为了 API 一致性
    resolve(false)
  }

  // MARK: - 获取当前版本
  @objc func getCurrentVersion(_ resolve: @escaping RCTPromiseResolveBlock,
                               rejecter reject: @escaping RCTPromiseRejectBlock) {
    let currentVersion = UserDefaults.standard.string(forKey: "current_version")
    resolve(currentVersion ?? "")
  }

  /// 获取缓存的 Bundle URL
  func getCachedBundleURL() -> URL? {
    guard let cacheDir = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first
    else {
      return nil
    }
    let versionedCacheDir = cacheDir.appendingPathComponent(self.cachePath)
    let cachedBundlePath = versionedCacheDir.appendingPathComponent(self.bundleFileName)

    // 确保版本目录存在
    if !FileManager.default.fileExists(atPath: versionedCacheDir.path) {
      try? FileManager.default.createDirectory(
        at: versionedCacheDir, withIntermediateDirectories: true)
    }
    
    return cachedBundlePath
  }
  
  /// 检查缓存的 Bundle 是否有效 (新增方法)
  func isCachedBundleValid() -> Bool {
      // 读取当前缓存的版本号
      let userDefaults = UserDefaults.standard
      let currentVersion = userDefaults.string(forKey: "current_version")
      
      guard let bundleURL = getCachedBundleURL() else {
          return false
      }
      
      // 只有当版本号存在且文件真实存在时才是有效缓存
      if currentVersion != nil && FileManager.default.fileExists(atPath: bundleURL.path) {
          // 检查文件是否有效（大小>0）
          if let attributes = try? FileManager.default.attributesOfItem(atPath: bundleURL.path),
             let fileSize = attributes[.size] as? NSNumber,
             fileSize.intValue > 0 {
              return true
          }
      }
      
      return false
  }

  /// 下载 Bundle 并存储到缓存
  private func downloadAndCacheBundle(remoteVersion: String, downloadUrl: String) async {
    let currentVersion = UserDefaults.standard.string(forKey: "current_version")
    let fileManager = FileManager.default
    let bundleFileURL = getCachedBundleURL()
    
    guard let bundleURL = bundleFileURL else {
      print("无法获取缓存目录")
      return
    }
    
    // 检查是否已经是最新版本
    if currentVersion == remoteVersion && fileManager.fileExists(atPath: bundleURL.path) &&
       (try? bundleURL.resourceValues(forKeys: [.fileSizeKey]).fileSize ?? 0) ?? 0 > 0 {
      DispatchQueue.main.async {
        print("已是最新版本")
      }
      return
    }
    
    guard let remoteURL = URL(string: downloadUrl) else {
      print("无效的下载 URL：\(downloadUrl)")
      return
    }
    
    var success = false
    var retryCount = 0
    let maxRetries = 3
    
    while !success && retryCount < maxRetries {
      do {
        // 下载文件
        let tempURL = try await downloadFile(from: remoteURL)
        
        // 保存到缓存
        let savedURL = try saveBundleToCache(tempURL: tempURL)
        success = true
        
        // 存储当前版本号
        UserDefaults.standard.set(remoteVersion, forKey: "current_version")
        print("Bundle 下载成功，保存路径：\(savedURL.path)")
        
      } catch {
        retryCount += 1
        print("下载失败（尝试 \(retryCount)/\(maxRetries)）：\(error.localizedDescription)")
        
        if retryCount < maxRetries {
          try? await Task.sleep(nanoseconds: 1_000_000_000) // 等待 1 秒后重试
        }
      }
    }
    
    if success {
      DispatchQueue.main.async {
        RCTTriggerReloadCommandListeners("热更新重载")
      }
    } else {
      print("下载最终失败")
    }
  }

  /// 下载文件
  func downloadFile(from url: URL) async throws -> URL {
    let config = URLSessionConfiguration.default
    config.timeoutIntervalForRequest = 10.0
    config.timeoutIntervalForResource = 30.0
    config.requestCachePolicy = .reloadIgnoringLocalCacheData
    
    let session = URLSession(configuration: config)
    
    return try await withCheckedThrowingContinuation { continuation in
      let task = session.downloadTask(with: url) { tempURL, response, error in
        if let error = error {
          continuation.resume(throwing: error)
        } else if let tempURL = tempURL, let httpResponse = response as? HTTPURLResponse {
          if httpResponse.statusCode == 200 {
            continuation.resume(returning: tempURL)
          } else {
            let error = NSError(domain: "DownloadError", code: httpResponse.statusCode,
                              userInfo: [NSLocalizedDescriptionKey: "HTTP \(httpResponse.statusCode)"])
            continuation.resume(throwing: error)
          }
        } else {
          continuation.resume(throwing: URLError(.unknown))
        }
      }
      task.resume()
    }
  }

  /// 保存文件到缓存目录
  private func saveBundleToCache(tempURL: URL) throws -> URL {
    guard let cacheDir = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first
    else {
      throw NSError(
        domain: "BundleManager", code: -3,
        userInfo: [NSLocalizedDescriptionKey: "Cache directory not found"])
    }

    let versionedCacheDir = cacheDir.appendingPathComponent(cachePath)
    let destinationURL = versionedCacheDir.appendingPathComponent(bundleFileName)

    // 创建目录（如果不存在）
    if !FileManager.default.fileExists(atPath: versionedCacheDir.path) {
      try FileManager.default.createDirectory(
        at: versionedCacheDir, withIntermediateDirectories: true)
    }

    // 覆盖旧文件（如果有）
    if FileManager.default.fileExists(atPath: destinationURL.path) {
      try FileManager.default.removeItem(at: destinationURL)
    }

    try FileManager.default.moveItem(at: tempURL, to: destinationURL)
    return destinationURL
  }
}
