import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'

// 订单准点率分页查询
const getOntimeData = (params: any) => {
  return ErpHttp.post<CommonResponse>('/scm/hxl.scm.shippingorder.ontime.stats', params)
}

// 订单完成率查询
const getCompleteData = (params: any) => {
  return ErpHttp.post<CommonResponse>('/scm/hxl.scm.prepareorder.complete.stats', params)
}

export const server = {
  getOntimeData,
  getCompleteData,
}
