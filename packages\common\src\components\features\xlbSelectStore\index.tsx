import React, { memo, useEffect, useState } from 'react'
import { commonStyles, XSearchBar, XBottomFixed, XText, FilterDropDown, Header, HeaderTitle, ProText, cs, Switch } from '@xlb/common/src/components'
import { View, StyleSheet, TouchableWithoutFeedback, FlatList, Pressable } from 'react-native'
import { LazyLoad } from '@xlb/common/src/components'
import useSelectStore from './model'
import { useUnmount } from 'ahooks'
import CheckBox from '@xlb/common/src/components/CheckBox'
import { colors } from '@xlb/common/src/config/theme'
import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'

// import XlbFilterStore from '@xlb/business-erp/src/components/xlbFilterStore'
import SiftIcon from '@xlb/common/src/components/others/SiftIcon'
import Loading from '@xlb/common/src/components/RootView/Loading'
// import XlbFilterBox from '@xlb/business-erp/src/components/xlbFilterBox'
import { authModel } from '@xlb/business-base/src/models/auth'
import Toast from 'react-native-root-toast'
// 筛选组件
const Filter = React.memo((props: any) => {
  const { setKeyWord, isFilterShow, setFilterShow, backParams, getQueryList } = props
  const selectStoreModel = useSelectStore((state: any) => state)

  const filterConfirm = () => {
    console.log('请求参数', selectStoreModel.getParams())
    getQueryList(selectStoreModel.getParams())
  }

  const resetConfirm = () => {
    selectStoreModel.resetModel()
    selectStoreModel.setPreParams({
      ...selectStoreModel.preParams,
      LabelList: [],
      ArealList: [],
    })
    getQueryList()
  }

  return (
    <>
      <FilterDropDown onConfirm={filterConfirm} onReset={resetConfirm} isShow={isFilterShow} setIsShow={setFilterShow}>
        <View style={{ height: 'auto', maxHeight: '80%' }}>
          <XlbFilterStore
            title={'门店区域'}
            backRoute={'goBack'}
            model={selectStoreModel}
            params={{
              listName: 'AreaList',
              setListName: 'setAreaList',
              isMultiple: true,
            }}
            postUrl={'/erp/hxl.erp.storearea.find'}
            selectRoute={'ErpSelectStoreArea'}
            backParams={backParams}
          />
          <XlbFilterStore
            title={'门店标签'}
            backRoute={'goBack'}
            model={selectStoreModel}
            params={{
              listName: 'LabelList',
              setListName: 'setLabelList',
              isMultiple: true,
            }}
            postUrl={'/erp/hxl.erp.storelabel.find'}
            selectRoute={'ErpSelectStoreLabel'}
            backParams={backParams}
          />
          <XlbFilterBox
            title={'经营类型'}
            model={selectStoreModel}
            params={{
              listName: 'OperatingTechniques',
              setListName: 'setOperatingTechniques',
            }}
          />
          <XlbFilterBox
            title={'门店状态'}
            model={selectStoreModel}
            isMultiple={true}
            params={{
              listName: 'states',
              setListName: 'setStates',
            }}
          />
        </View>
      </FilterDropDown>
      <View style={[commonStyles.horBox, commonStyles.filterBox, { backgroundColor: colors.primary }]}>
        <XSearchBar fillOpacity onSubmit={(text: string) => setKeyWord(text)} onCancel={() => setKeyWord('')} placeholder={'请输入门店名称/代码查询'} />
        {/* <XSearchBar
          fillOpacity
          placeholder={'请输入单据号查询'}
          value={fid}
          onChangeText={(val) => {
            setFid(val)
          }}
          onCancel={() => {
            filterConfirm('')
          }}
          onSubmit={() => {
            filterConfirm(fid)
          }}
        /> */}
        <SiftIcon onPress={() => setFilterShow(true)} />
        {/* <SiftIcon onPress={() => setFilterShow(true)} color={'#9C9C9C'} /> */}
      </View>
    </>
  )
})

const XlbSelectStore = ({ navigation, route }: any) => {
  const [isFilterShow, setFilterShow] = useState<boolean>(false)
  const list = JSON.parse(JSON.stringify(useSelectStore((state: any) => state.list)))
  const selectStoreModel = useSelectStore((state: any) => state)
  const resetModel = useSelectStore((state: any) => state.resetModel)
  const setTotal = useSelectStore((state: any) => state.setTotal)
  const setList = useSelectStore((state: any) => state.setList)
  const setAllCheck = useSelectStore((state: any) => state.setAllCheck)
  const all = useSelectStore((state: any) => state.all)
  const total = useSelectStore((state: any) => state.total)
  const setCheckedStoreList = route.params.model[route.params.setListName]
  const [keyword, setKeyWord] = useState('')
  // 路由信息传过来的已经选择的数据
  const checkedList = route.params.model[route.params.listName]
  const [curCheckList, setCurCheckList] = useState<any>(route.params.model[route.params.listName])
  const ids = checkedList.map((v: { id: any }) => v.id) //带过来数据的ids

  type filterData = {
    store_label_ids?: any[]
    store_area_ids?: any[]
  }

  const getQueryList = async (filterData: filterData = {}) => {
    Loading.show()
    const postData = {
      ...route.params.postParams,
      keyword,
      ...filterData,
      store_label_ids: selectStoreModel.getParams().store_label_ids.length ? selectStoreModel.getParams().store_label_ids : '',
      store_area_ids: selectStoreModel.getParams().store_area_ids.length ? selectStoreModel.getParams().store_area_ids : '',
    }
    console.log('postData', postData)
    let res
    if (!postData.keyword && !postData.store_area_ids && !postData.store_label_ids && JSON.stringify(filterData) === '{}' && !route.params.postParams) {
      console.log(1)
      res = route.params.returnKey ? { data: [...authModel.state.userInfos.query_stores] } : { data: { content: [...authModel.state.userInfos.query_stores] } }
      const data = route.params.returnKey
        ? res.data.map((v: any) => ({ checked: ids.includes(v.id), ...v }))
        : res.data.content.map((v: any) => ({ checked: ids.includes(v.id), ...v }))
      setTotal(checkedList.length) //设置勾选总数
      setList(data) //设置勾选列表
      setAllCheck(data.every((v: any) => v.checked)) //设置是否全选
    } else {
      console.log(2, postData)
      res = await ErpHttp.post<CommonResponse>(route.params.postUrl, postData)
      const data = route.params.returnKey
        ? res.data.map((v: any) => ({ checked: ids.includes(v.id), ...v }))
        : res.data.content.map((v: any) => ({ checked: ids.includes(v.id), ...v }))
      setTotal(data.filter((e) => e.checked).length) //设置勾选总数
      setList(data) //设置勾选列表
      setAllCheck(data.every((v: any) => v.checked)) //设置是否全选
    }
    Loading.hide()
    // console.log('res', res)
  }

  useEffect(() => {
    getQueryList()
  }, [keyword])

  useUnmount(() => {
    resetModel()
  })

  const renderItem = (props: any) => {
    const { item, index } = props
    const onPress = () => {
      if (!route?.params?.isMultiple) {
        setCheckedStoreList([
          {
            ...item,
            active: true,
            label: item.store_name,
            value: item.id,
            checked: true,
          },
        ])
        if (route.params.backRoute == 'goBack') {
          navigation.goBack()
        } else {
          navigation.navigate(route.params.backRoute)
        }
      } else {
        list[index].checked = !list[index].checked
        setTotal(list.filter((v: any) => v.checked).length)
        setList(list)
        setAllCheck(list.every((v: any) => v.checked))
      }
    }
    return (
      <TouchableWithoutFeedback onPress={onPress}>
        <View style={{ ...styles.itemBox }}>
          {route?.params?.isMultiple ? <CheckBox checked={list[index].checked} onPress={onPress} /> : null}
          <XText style={{ marginLeft: 10, textAlign: 'left', flex: 1 }}>{item.store_name} </XText>
        </View>
      </TouchableWithoutFeedback>
    )
  }

  const Footer = memo(() => {
    const handleConfirm = () => {
      const l = list
        .filter((v: any) => v.checked)
        .map((s: any) => {
          return {
            ...s,
            active: true,
            label: s.store_name,
            value: s.id,
          }
        })
      if (!l.length) {
        Toast.show('请选择门店')
        return false
      }
      setCheckedStoreList([...l])
      if (route.params.backRoute == 'goBack') {
        navigation.goBack()
      } else {
        navigation.navigate(route.params.backRoute)
      }
    }

    const onPressAll = () => {
      setAllCheck(!all)
      setTotal(!all ? list.length : 0)
      setList(list.map((v: any) => ({ ...v, checked: !all })))
    }
    return (
      <View style={{ ...styles.bottomBox, ...commonStyles.verCenBox, ...commonStyles.horBox }}>
        <Pressable onPress={onPressAll} style={{ ...commonStyles.horBox, width: 100 }}>
          <CheckBox checked={all} onPress={onPressAll} />
          <XText style={{ marginLeft: 10 }}>全选</XText>
        </Pressable>
        <XText style={{ width: 'auto' }}>已选门店：{total}家</XText>
        <View style={{ ...commonStyles.buttonStyle, backgroundColor: colors.blue }}>
          <XText onPress={handleConfirm} style={{ color: 'white' }}>
            确定
          </XText>
        </View>
      </View>
    )
  })

  return (
    <>
      <Header centerComponent={<HeaderTitle title="选择门店" />} onBack={() => navigation.goBack()} />
      <Filter setKeyWord={setKeyWord} isFilterShow={isFilterShow} setFilterShow={setFilterShow} backParams={route.params} getQueryList={getQueryList} />
      <XBottomFixed footer={route?.params?.isMultiple && !isFilterShow ? <Footer /> : null}>
        <LazyLoad>
          <View style={{ ...styles.listContainer }}>
            <FlatList data={list} keyExtractor={(item) => item.id} renderItem={renderItem} />
          </View>
        </LazyLoad>
      </XBottomFixed>
    </>
  )
}

const styles = StyleSheet.create({
  listContainer: {
    backgroundColor: '#fff',
  },
  titleText: {
    fontSize: 16,
    fontWeight: '400',
  },
  bottomBox: {
    height: 50,
    borderTopColor: '#F0F0F0',
    borderTopWidth: 1,
    backgroundColor: '#fff',
    padding: 15,
    justifyContent: 'space-between',
  },
  itemBox: {
    height: 40,
    paddingHorizontal: 15,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#F4F4F4',
  },
})

export default XlbSelectStore
