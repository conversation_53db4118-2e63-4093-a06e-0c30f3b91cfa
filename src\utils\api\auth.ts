import { XLBHttp } from '@xlb/common/src/services/lib/http'
import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'

/**
 * auth模块
 */
export class Auth {
  /**
   * 登录
   * @param user
   */
  userLogin = (user: Record<string, any>) => XLBHttp.post<VerifyPasswordSuccessPayload>('/login/login', user)
  // 登录新接口
  userLogins = (user: Record<string, any>) => ErpHttp.post<CommonResponse>('/erp/hxl.erp.user.app.login', user)
  /**
   * 发送验证码
   * @param account
   */
  sendMessage = (account: Record<string, any>) => XLBHttp.post('/login/SendSMS', account)
  /**
   * 获取用户信息
   */
  getUserInfo = () => XLBHttp.post<UserInfoSuccessPayload>('/login/info', {})
  /**
   * 点击卡片登录
   */
  cardLogin = (user: Record<string, any>) =>
    ErpHttp.post<CommonResponse>('/erp/hxl.erp.user.account.app.login', user, {
      timeout: 15000,
      isHiddenMsg:true
    })
  /**
   * 授权码登录
   */
  tempLogin = (user: Record<string, any>) => ErpHttp.post<CommonResponse>('/erp/hxl.erp.user.temp.login', user)
  /**
   * 刷新token
   */
  refreshLoginToken = () => XLBHttp.post('/login/refresh', {})
}
