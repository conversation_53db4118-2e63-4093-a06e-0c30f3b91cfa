import path from 'node:path';
import fs from 'node:fs';
import {fileURLToPath} from 'node:url';
import * as Repack from '@callstack/repack';
import {ReanimatedPlugin} from '@callstack/repack-plugin-reanimated';
import rspack from '@rspack/core';
import TerserPlugin from 'terser-webpack-plugin';
import {RsdoctorRspackPlugin} from '@rsdoctor/rspack-plugin';
import {createRequire} from 'node:module';

// 创建 require 函数用于解析模块路径
const require = createRequire(import.meta.url);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const packageJson = JSON.parse(fs.readFileSync('./package.json').toString());

let hasPodspec = new Set();

function traverse(dir) {
  const fullPath = path.resolve('./node_modules', dir);

  // Check if directory exists to avoid errors
  if (!fs.existsSync(fullPath) || !fs.statSync(fullPath).isDirectory()) {
    return;
  }

  try {
    const children = fs.readdirSync(fullPath);

    for (const child of children) {
      const childPath = path.join(fullPath, child);

      // Skip if not a directory
      if (!fs.statSync(childPath).isDirectory()) {
        continue;
      }

      // Check for .podspec files in this directory
      const childFiles = fs.readdirSync(childPath);
      if (childFiles.some(f => f.endsWith('.podspec'))) {
        hasPodspec.add(path.join(dir, child));
        continue; // No need to go deeper if we found a podspec
      }

      // Handle scoped packages (those starting with @)
      if (child.startsWith('@')) {
        traverse(path.join(dir, child));
      } else if (dir === '') {
        // Only traverse top-level directories if we're at root
        traverse(child);
      }
    }
  } catch (err) {
    console.error(`Error traversing ${fullPath}:`, err.message);
  }
}

traverse('');
// 手动添加没有 podspec 的包到shared
hasPodspec.add('@fruits-chain/react-native-xiaoshu');
hasPodspec.add('@react-native-async-storage/async-storage');
hasPodspec.add('@react-native-camera-roll/camera-roll');
hasPodspec.add('@react-native-clipboard/clipboard');
hasPodspec.add('@react-native-community/slider');
hasPodspec.add('@react-native-documents/picker');
hasPodspec.add('@react-navigation/native');
hasPodspec.add('@sentry/react-native');
hasPodspec.add('@shopify/flash-list');
hasPodspec.add('@xlb/components-rn');
hasPodspec.add('@xlb/react-native-amap3d');
hasPodspec.add('foca');
hasPodspec.add('native-base');
hasPodspec.add('react-native-modal');
hasPodspec.add('react-native-root-toast');
hasPodspec.add('react-native-tab-view');
hasPodspec.add('react-native');
hasPodspec.add('react');
hasPodspec.add('zustand');
hasPodspec.add('@ant-design/react-native');
hasPodspec.add('react-native-root-siblings');

console.log(hasPodspec);

const shared = {};
for (let key of hasPodspec) {
  shared[key] = {
    eager: true,
    singleton: true,
    version: packageJson.dependencies[key],
  };
}
/**
 * Rspack configuration enhanced with Re.Pack defaults for React Native.
 *
 * Learn about Rspack configuration: https://rspack.dev/config/
 * Learn about Re.Pack configuration: https://re-pack.dev/docs/guides/configuration
 */
export default function (env) {
  const curMode = 'development';
  const {
    mode = process.env.CI_COMMIT_REF_NAME?.startsWith('prod') ||
    process.env.CI_COMMIT_REF_NAME?.startsWith('staging')
      ? 'production'
      : 'development',
    context = __dirname,
    entry = './index.js',
    platform = process.env.PLATFORM,
    minimize = mode === 'production',
    devServer = undefined,
  } = env;
  return {
    context: __dirname,
    devServer,
    entry: './index.js',
    resolve: {
      ...Repack.getResolveOptions(),
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@/*': path.resolve(__dirname, './src/*'),
        'src/*': path.resolve(__dirname, './src/*'),
        src: path.resolve(__dirname, './src'),
        '@xlb/common/*': path.resolve(__dirname, './packages/common/*'),
        '@xlb/common': path.resolve(__dirname, './packages/common'),
        '@xlb/business-base/*': path.resolve(
          __dirname,
          './packages/business-base/*',
        ),
        '@xlb/business-base': path.resolve(
          __dirname,
          './packages/business-base',
        ),
        // 修正 Reactotron 兼容性别名 - 使用正确的文件路径
        'react-native/src/private/inspector/XHRInterceptor': require.resolve(
          'react-native/src/private/devsupport/devmenu/elementinspector/XHRInterceptor',
        ),
        'react-native/Libraries/Network/XHRInterceptor': require.resolve(
          'react-native/src/private/devsupport/devmenu/elementinspector/XHRInterceptor',
        ),
      },
      fallback: {
        // 添加 fallback 处理
        'react-native/src/private/inspector/XHRInterceptor': require.resolve(
          'react-native/src/private/devsupport/devmenu/elementinspector/XHRInterceptor',
        ),
      },
    },
    output: {
      clean: true,
      // 开发模式下 如果有.js后缀, 文件获取不到, 待debug
      filename: mode === 'production' ? 'index.bundle.js' : 'index.bundle',
      path: path.resolve(__dirname, 'build/generated', platform),
      uniqueName: 'HostAppXlb',
    },
    module: {
      rules: [
        ...Repack.getJsTransformRules({
          flow: {
            enabled: true,
          },
        }),
        ...Repack.getAssetTransformRules({inline: true}),
        {
          test: /\.[jt]sx?$/,
          include: [
            /node_modules[\\/]@react-native-community[\\/]cameraroll/,
            /node_modules[\\/]@react-native[\\/]virtualized-lists/,
            /node_modules[\\/]react-native-ble-plx/,
          ],
          use: 'babel-loader',
        },
      ],
    },
    optimization: {
      minimize,
      minimizer: [
        new TerserPlugin({
          test: /\.(js)?bundle(\?.*)?$/i,
          extractComments: false,
          terserOptions: {
            format: {
              comments: false,
            },
          },
        }),
      ],
      chunkIds: 'named',
    },
    plugins: [
      new Repack.RepackPlugin({
        extraChunks: [
          {
            include: /.*/,
            type: 'local',
          },
        ],
      }),
      new Repack.plugins.ModuleFederationPluginV2({
        name: 'HostAppXlb',
        filename: 'HostAppXlb.container.js.bundle',
        exposes: {
          './store': './packages/common/src/models/system.ts',
          './webView': './packages/common/src/components/WebViewPage/index.tsx',
          './XlbVisionScanCode':
            './packages/common/src/components/XlbVisionScanCode/index.tsx',
          './ScanCode':
            './packages/common/src/xlb-components-new/ScanCode/indexCode.tsx',
          './PDFViewer': './src/components/pdfView/index.tsx',
          './Provider': './src/shared/XiaoshuProvider.tsx', // 新增：暴露 Provider
          './XlbUpdate': './src/components/xlbUpdate/index.tsx'
        },
        remotes: {
          RemoteAppBase: `RemoteAppBase@dynamic`,
          RemoteAppBi: `RemoteAppBi@dynamic`,
          RemoteAppBms: `RemoteAppBms@dynamic`,
          RemoteAppBpm: `RemoteAppBpm@dynamic`,
          RemoteAppCrm: `RemoteAppCrm@dynamic`,
          RemoteAppEms: `RemoteAppEms@dynamic`,
          RemoteAppErp: `RemoteAppErp@dynamic`,
          RemoteAppFsms: `RemoteAppFsms@dynamic`,
          RemoteAppHrs: `RemoteAppHrs@dynamic`,
          RemoteAppIm: `RemoteAppIm@dynamic`,
          RemoteAppMem: `RemoteAppMem@dynamic`,
          RemoteAppRetail: `RemoteAppRetail@dynamic`,
          RemoteAppScm: `RemoteAppScm@dynamic`,
          RemoteAppSds: `RemoteAppSds@dynamic`,
          RemoteAppSms: `RemoteAppSms@dynamic`,
        },
        dts: false,
        shared,
        runtimePlugins: [path.resolve(__dirname, 'remote-fallback-plugin.ts')],
      }),
      new rspack.IgnorePlugin({
        resourceRegExp: /^@react-native-masked-view/,
      }),
      new rspack.EnvironmentPlugin({
        MF_CACHE: null,
      }),
      new ReanimatedPlugin(),
      process.env.RSDOCTOR &&
        new RsdoctorRspackPlugin({
          // plugin options
        }),

      // 在xcode里build时, 会自动生成Hermes字节码, 仅需要在云效上构建时开启
      !process.env.BUILD_FROM_XCODE &&
        new Repack.plugins.HermesBytecodePlugin({
          enabled: mode === 'production',
          test: /\.bundle/,
        }),
    ],
    devtool: 'source-map',
  };
}
