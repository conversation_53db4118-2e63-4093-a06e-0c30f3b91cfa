// import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'
// import { ErpHttp as BaseErpHttp } from '@xlb/common/src/services/lib/erphttp'

// type Api =
//   /* 门店补货商品详情 */
//   | 'requestorder.app.item.page'
//   /* 配送参数 */
//   | 'deliveryparam.read'

// const server = async (name: Api, param?: any, config?: any) => {
//   return await ErpHttp.post<any>(`/erp-mdm/hxl.erp.${name}`, param, config)
// }

// export default server

import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'

// 商品统计
const getBIData = (params: any) => {
  console.log(params,'resRate')
  return ErpHttp.post<CommonResponse>('/bi-new/app/homepage/item/statistics', params)
}

// 商品缺货率
const getRate = (params: any) => {
  console.log(params,'resRate')
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.outstockwarning.rate', params)
}

export const server = {
  getBIData,
  getRate
}
