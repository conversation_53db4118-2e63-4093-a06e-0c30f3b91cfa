import React, {useEffect} from "react";
import {StyleSheet, Text, TouchableWithoutFeedback, View} from "react-native";
import {colors} from "../../../config/theme";
import {XIcon} from "../../index";
import {useNavigation} from "@react-navigation/native";
import useSelectStoreIcon from "./model";
import {authModel} from "@xlb/business-base/src/models/auth";

export type SelectStoreIconType = {
    isMultiple: boolean
    postParams?: any
}
const SelectStoreIcon = React.memo((props: SelectStoreIconType)=> {

    const navigation = useNavigation<any>()
    const store = useSelectStoreIcon((state: any)=> state)

    const selfStore = authModel.state.userInfos.store || {}

    const {
        isMultiple = true,
        postParams = {}
    } = props

    const onPress = () => {
        navigation.navigate('ErpSelectStore', {
            backRoute: 'goBack',
            model: store,
            listName: 'storeList',
            setListName: 'setStoreList',
            isMultiple: isMultiple,
            postUrl: '/erp/hxl.erp.store.short.page',
            postParams: postParams
        })
    }

    useEffect(()=> {
       if(!store.storeList.length) {
           store.setStoreList([{ ...selfStore }])
       }
       return ()=> store.setStoreList([])
    }, [])

    return <>
        <TouchableWithoutFeedback onPress={onPress}>
            <View style={styles.container}>
                <XIcon name={'store'} color={'#fff'} />
                <Text style={styles.name}>{store.storeList.length === 1 ? store.storeList[0].store_name : '多家门店'}</Text>
            </View>
        </TouchableWithoutFeedback>
    </>
})

const styles = StyleSheet.create({
    container: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center'
    },
    name: { marginLeft: 4, color: colors.white }
})

export default SelectStoreIcon