apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"
/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '../..'
    // root = file("../../")
    //   The folder where the react-native NPM package is. Default is ../../node_modules/react-native
    // reactNativeDir = file("../../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../../node_modules/@react-native/codegen
    // codegenDir = file("../../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../../node_modules/react-native/cli.js
    // cliFile = file("../../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    bundleCommand = "webpack-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]

    /* Autolinking */
    autolinkLibrariesWithApp()
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = io.github.react-native-community:jsc-android-intl:2026004.+`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'io.github.react-native-community:jsc-android:2026004.+'

def env = project.env.get("ENV")

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.hxl.xlb"
    defaultConfig {
        applicationId project.env.get("APPLICATION_ID")
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        manifestPlaceholders = [amapApiKey: project.env.get("MAP_KEY_ANDROID")]
        versionName project.env.get('VERSION_NAME')
        resValue "string", "app_name", project.env.get("APP_NAME")
        resValue "string", "build_config_package", "com.hxl.xlb"    // react-native-config 需要读这里的BuildConfig
    }

    packagingOptions {
        jniLibs {
            useLegacyPackaging = true   // 允许对so压缩
        }
    }

    signingConfigs {
        // dev.keystore = dev; prod.keystore=prod(原新零帮keystore)
        debug {
            storeFile file("dev.keystore")
            storePassword "hxl.xlb"
            keyAlias "mykey-release"
            keyPassword "hxl.xlb"

        }

        release {
            storeFile file("prod.keystore")
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            // 正式的时候打包用release
            if (env == "PROD") {
                signingConfig signingConfigs.release
            } else {
                signingConfig signingConfigs.debug
            }
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }
    buildFeatures {
        viewBinding = true
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar", '*.aar'])
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    implementation("com.jakewharton:process-phoenix:3.0.0")
    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }

    implementation project(':mvplibrary')

    implementation 'com.amap.api:navi-3dmap-location-search:10.0.700_3dmap10.0.700_loc6.4.5_sea9.7.2'
    //第三方时间选择器
    implementation 'io.github.ShawnLin013:number-picker:2.4.13'
    //图片预览 可放大缩小
    implementation 'com.squareup.picasso:picasso:2.5.2'
    //消息机制EventBus引用
    implementation 'org.greenrobot:eventbus:3.1.1'



    // 友盟基础组件库（所有友盟业务SDK都依赖基础组件库）
    implementation 'com.umeng.umsdk:common:+'// 必选
    implementation 'com.umeng.umsdk:asms:+'// 必选
    implementation 'com.umeng.umsdk:apm:+'// U-APM产品包依赖，必选

    implementation 'com.aliyun.ams:alicloud-android-push:3.9.2'
    implementation "com.aliyun.ams:alicloud-android-third-push:3.9.2"
    implementation "com.aliyun.ams:alicloud-android-third-push-meizu:3.9.2"
    implementation "com.aliyun.ams:alicloud-android-third-push-vivo:3.9.2"
    implementation "com.aliyun.ams:alicloud-android-third-push-oppo:3.9.2"
    implementation "com.aliyun.ams:alicloud-android-third-push-xiaomi:3.9.2"
    implementation "com.aliyun.ams:alicloud-android-third-push-huawei:3.9.2"
    implementation "com.aliyun.ams:alicloud-android-third-push-honor:3.9.2"
    implementation 'com.aliyun.ams:alicloud-android-third-push-fcm:3.9.2'

    implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.0'



    implementation "androidx.media3:media3-exoplayer:1.6.1"
    implementation "androidx.media3:media3-ui:1.6.1"



    implementation 'com.github.chrisbanes:PhotoView:1.3.0'

    //沉浸式状态栏框架导入
// 基础依赖包，必须要依赖
    api "com.geyifeng.immersionbar:immersionbar:3.2.2"
// kotlin扩展（可选）
    api "com.geyifeng.immersionbar:immersionbar-ktx:3.2.2"

    implementation "com.facebook.react:react-native:+"  // From node_modules

    implementation 'com.tencent.mm.opensdk:wechat-sdk-android:+'

    implementation "androidx.appcompat:appcompat:1.4.0"

    // 权限请求框架：https://github.com/getActivity/XXPermissions
    implementation 'com.github.getActivity:XXPermissions:25.2'

}

