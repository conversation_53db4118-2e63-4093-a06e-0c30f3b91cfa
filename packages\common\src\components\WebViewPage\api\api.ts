/*
 * @Author: 杜云涛 <EMAIL>
 * @Date: 2025-03-11 17:08:32
 * @LastEditors: 杜云涛 <EMAIL>
 * @LastEditTime: 2025-07-28 20:43:37
 * @FilePath: /host-app-xlb/packages/common/src/components/WebViewPage/api/api.ts
 * @Description:
 *
 */
import {Platform} from 'react-native';
import {createAxiosByinterceptors} from './index';
import ImageCropPicker from 'react-native-image-crop-picker';
import DocumentPicker from '@react-native-documents/picker';
import CryptoJS from 'crypto-js';
import RNFetchBlob from 'react-native-blob-util';
import {authModel} from '@xlb/business-base/src/models/auth';
import Config from 'react-native-config';
import {Toast} from '@fruits-chain/react-native-xiaoshu';
import axios from 'axios';

const baseURL = `https://oa.oa.ali-${Config.ENV}.xlbsoft.com`;
const request = createAxiosByinterceptors({
  headers: {
    'Access-Token': `${authModel?.state?.userInfos?.access_token}`,
  },
});
const requestOA = createAxiosByinterceptors({
  baseURL: baseURL,
  headers: {
    'Access-Token': `${authModel?.state?.userInfos?.access_token}`,
  },
});
export const createRoom = (
  userIds: string[],
  title: string,
  attributes: {[key: string]: any},
) => {
  return request({
    method: 'post',
    url: '/audit/createGroupRoom',
    data: {
      userIds,
      title,
      attributes,
    },
  });
};

export const chatSend = (roomId: string, message: any) => {
  return request({
    method: 'post',
    url: '/chat/send',
    data: {
      id: roomId,
      msg: message,
    },
  });
};
/**
 * 从OA获取用户信息
 * @param userIds
 * @returns
 */
export const getUserInfoByOA = (userIds: string[]) => {
  return requestOA({
    method: 'post',
    url: '/oa/hxl.oa.user.find',
    data: {
      user_ids: userIds,
    },
  }) as any;
};


export const folder = Config.ENV === 'PROD' ? 'prod' : 'test';

export const uploadFile = async (file: any) => {
  try {
    const {AccessKeyId, AccessKeySecret, SecurityToken, Region, Bucket} =
      await getSignature();
    // 2. 构造OSS参数
    const objectKey = `${folder}/${file.name}`; // 生成唯一文件名
    const endpoint = `${Bucket}.${Region}.aliyuncs.com`;
    const ossUrl = `https://${endpoint}/${objectKey}`;

    const filePath = file.path.replace('file://', '');
    // 3. 准备签名参数
    const date = new Date().toUTCString();
    const contentType = file.type || 'application/octet-stream';

    // 4. 构造待签名字符串
    const canonicalizedResource = `/${Bucket}/${objectKey}`;
    const stringToSign = [
      'PUT',
      '', // Content-MD5（可选）
      contentType,
      date,
      `x-oss-security-token:${SecurityToken}`,
      canonicalizedResource,
    ].join('\n');

    // 5. 计算签名
    const signature = CryptoJS.HmacSHA1(stringToSign, AccessKeySecret).toString(
      CryptoJS.enc.Base64,
    );

    // 6. 构造请求头
    const headers = {
      Authorization: `OSS ${AccessKeyId}:${signature}`,
      'x-oss-security-token': SecurityToken,
      'Content-Type': contentType,
      Date: date,
    };

    // 6. 使用RNFetchBlob上传
    const res = await RNFetchBlob.fetch(
      'PUT',
      ossUrl,
      headers,
      RNFetchBlob.wrap(filePath),
    );
    if (res.respInfo.status === 200) {
      console.log('上传成功', {
        ossFileName: objectKey,
        ossFileUrl: res.respInfo.redirects[0],
        name: file.name,
        size: file.size,
        type: file.type,
      });
      return {
        ossFileName: objectKey,
        ossFileUrl: res.respInfo.redirects[0],
        name: file.name,
        size: file.size,
        type: file.type,
      };
    } else {
      console.error('上传失败1', res, res.data);
      return Promise.reject();
    }
  } catch (error) {
    console.error('上传失败:', error);
    return Promise.reject();
  }
};

const MAX_FILE_SIZE = 200 * 1024 * 1024; // 200MB 的字节数
/**
 * 选取图片
 * @returns 图片路径[]
 */
export const handleImagePicker = async () => {
  try {
    const res: any =
      Platform.OS === 'ios'
        ? await ImageCropPicker.openPicker({
            multiple: true,
            mediaType: 'photo',
            includeBase64: true,
            cropping: false,
            maxFiles: 9,
            forceJpg: true,
            compressImageMaxWidth: 1000,
            compressImageQuality: 0.7,
          }).catch(() => {
            return null;
          })
        : await DocumentPicker.pick({
            type: [DocumentPicker.types.images],
            allowMultiSelection: true,
          });

    let urls: string[] = [];
    if (res.length > 9) {
      Toast({
        message: '最多可选择9张图片',
        forbidPress: true,
      });
      return Promise.reject();
    }

    return await Promise.all(
      res.map(async (image: any) => {
        const type = image.mime ? 'mime' : 'type';
        if (
          ![
            '.jpg',
            '.jpeg',
            '.png',
            '.bmp',
            '.gif',
            '.webp',
            '.JPG',
            '.JPEG',
            '.PNG',
            '.BMP',
            '.GIF',
            '.WEBP',
            '.heic',
            '.HEIC',
          ].includes(image[type]?.replace(/.*\//, '.'))
        ) {
          return Promise.reject(
            '请选择 .jpg、.jpeg、.png、.bmp、.gif 或 .webp 格式的图片',
          );
        }
        if (image.size > MAX_FILE_SIZE) {
          Toast({
            message: '不得大于200MB',
            forbidPress: true,
          });
          return Promise.reject(
            '请选择 .jpg、.jpeg、.png、.bmp、.gif 或 .webp 格式的图片',
          );
        }
        image.type = image[type];
        image.name = image.name || image.filename;
        image.path = image.path || image.uri;

        return image;
      }),
    );
  } catch (e) {
    return Promise.reject(e);
  }
};
export const handleUploadImage = async () => {
  try {
    const res: any =
      Platform.OS === 'ios'
        ? await ImageCropPicker.openPicker({
            multiple: true,
            mediaType: 'photo',
            includeBase64: true,
            cropping: false,
            maxFiles: 9,
            forceJpg: true,
            compressImageMaxWidth: 1000,
            compressImageQuality: 0.7,
          }).catch(() => {
            return null;
          })
        : await DocumentPicker.pick({
            type: [DocumentPicker.types.images],
            allowMultiSelection: true,
          });

    let urls: string[] = [];

    return await Promise.all(
      res.map(async (image: any) => {
        const type = image.mime ? 'mime' : 'type';
        if (
          ![
            '.jpg',
            '.jpeg',
            '.png',
            '.bmp',
            '.gif',
            '.webp',
            '.JPG',
            '.JPEG',
            '.PNG',
            '.BMP',
            '.GIF',
            '.WEBP',
            '.heic',
            '.HEIC',
          ].includes(image[type]?.replace(/.*\//, '.'))
        ) {
          return Promise.reject(
            '请选择 .jpg、.jpeg、.png、.bmp、.gif 或 .webp 格式的图片',
          );
        }
        image.type = image[type];
        image.name = image.name || image.filename;
        image.path = image.path || image.uri;

        return await uploadFile(image);
      }),
    );

    return Promise.resolve(urls);
  } catch (e) {
    return Promise.reject(e);
  }
};
// const FileWhitelist = [
//     'video/mp4',
//     'image/png',
//     'image/jpeg',
//     "application/msword",
//     "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
//     "application/vnd.ms-excel",
//     "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
//     "application/vnd.ms-powerpoint",
//     "application/vnd.openxmlformats-officedocument.presentationml.presentation",
//     "application/pdf"
// ]
/**
 * 文件选择
 * @returns file[]
 */
const FileWhitelist = Platform.select({
  ios: [
    'public.movie', // video/mp4
    'public.png', // image/png
    'public.jpeg', // image/jpeg
    'com.microsoft.word.doc', // application/msword
    'org.openxmlformats.wordprocessingml.document', // application/vnd.openxmlformats-officedocument.wordprocessingml.document
    'com.microsoft.excel.xls', // application/vnd.ms-excel
    'org.openxmlformats.spreadsheetml.sheet', // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    'com.microsoft.powerpoint.ppt', // application/vnd.ms-powerpoint
    'org.openxmlformats.presentationml.presentation', // application/vnd.openxmlformats-officedocument.presentationml.presentation
    'com.adobe.pdf', // application/pdf
  ],
  android: [
    'video/mp4',
    'image/png',
    'image/jpeg',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/pdf',
  ],
});

export const handleFilePicker = async () => {
  const res = await DocumentPicker.pick({
    type: FileWhitelist,
    allowMultiSelection: true,
  });
  if (!!res.length) {
    try {
      if (res.length > 9) {
        Toast({
          message: '最多可选择9个文件',
          forbidPress: true,
        });
        return Promise.reject();
      }
      const urls = await Promise.all(
        res.map((file: any) => {
          file.path = file.uri;
          if (file.size > MAX_FILE_SIZE) {
            Toast({
              message: '不得大于200MB',
              forbidPress: true,
            });
            return Promise.reject(
              '请选择 .jpg、.jpeg、.png、.bmp、.gif 或 .webp 格式的图片',
            );
          }
          return file;
        }),
      );
      return urls;
    } catch (e) {
      return Promise.reject();
    }
  }
};
export const handleUploadFile = async () => {
  const res = await DocumentPicker.pick({
    type: FileWhitelist,
    allowMultiSelection: true,
  });
  if (!!res.length) {
    try {
      const uploadPromises = res.map(file => {
        file.path = file.uri;
        return uploadFile(file);
      });
      const urls = await Promise.all(uploadPromises);
      return urls;
    } catch (e) {
      return Promise.reject();
    }
  }
};

/**
 * 手机号灰度
 */
export const getGrayByPhone = async (phone: string) => {
  // 不是生产环境 直接返回true
  if (Config.ENV !== 'PROD') {
    return true;
  }
  // 简单手机号校验
  if (!/^1\d{10}$/.test(phone)) return false;
  const {data, status} = await axios.get(
    'https://prod--im--oss.oss-cn-hangzhou.aliyuncs.com/icon/GrayConfiguration.JSON',
  );

  if (status === 200) {
    if (data?.Whitelist && Array.isArray(data?.Whitelist)) {
      return data?.Whitelist.includes(phone);
    }
    // data.Percentage 单位为 ‰（千分之几）
    if (data.Percentage <= 0) return false;
    const lastThree = parseInt(phone.slice(-3), 10); // 最后三位

    return lastThree % 1000 < data.Percentage; // 命中率为 data.Percentage‰
  }
  return false;
};
