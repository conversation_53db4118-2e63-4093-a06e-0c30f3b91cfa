#!/bin/bash
set -euo pipefail

green() { echo -e "\033[32m$1\033[0m"; }
red()   { echo -e "\033[31m$1\033[0m"; }

# 检查 IPA 路径
IPA_PATH="ios/build/xlb.ipa"
if [ ! -f "$IPA_PATH" ]; then
  red "❌ 未找到 IPA 文件：$IPA_PATH"
  red "请先使用 Xcode 或脚本打包生成 IPA 文件"
  exit 1
fi
green "✅ 找到 IPA 文件：$IPA_PATH"

# 上传到蒲公英
green "🚀 正在上传 IPA 到蒲公英..."
trap 'rm -f response.json' EXIT

curl --progress-bar -F "file=@$IPA_PATH" \
     -F "_api_key=f0556d58371d7154f1de33a3848ee83c" \
     https://www.pgyer.com/apiv2/app/upload > response.json

ERROR_CODE=$(jq -r '.code' response.json)
if [ "$ERROR_CODE" != "0" ]; then
  red "❌ 上传失败，错误码：$ERROR_CODE"
  cat response.json
  exit 1
fi

buildQRCodeURL=$(jq -r '.data.buildQRCodeURL' response.json)
buildInstallURL=$(jq -r '.data.buildShortcutUrl' response.json)

if [ -z "$buildQRCodeURL" ] || [ -z "$buildInstallURL" ]; then
  red "❌ 返回数据中缺少二维码或链接"
  cat response.json
  exit 1
fi

green "🎉 上传成功！"
echo "📱 安装地址: https://www.pgyer.com/${buildInstallURL}"
echo "🧾 二维码地址: ${buildQRCodeURL}"