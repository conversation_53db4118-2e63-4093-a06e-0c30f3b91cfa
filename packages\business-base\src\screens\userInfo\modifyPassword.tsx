import React, {useState, useRef, useEffect} from 'react';
import type {FC} from 'react';

import {
  View,
  TextInput,
  StyleSheet,
  TouchableWithoutFeedback,
  Keyboard,
  Pressable,
} from 'react-native';
import {useRoute, useNavigation} from '@react-navigation/native';

import Toast from 'react-native-root-toast';
import trim from 'lodash/trim';

import {Dialog, Space} from '@fruits-chain/react-native-xiaoshu';
import {XlbText, TOKEN, XlbButton, XlbIconfontNew} from '@xlb/components-rn';

import Loading from '@xlb/common/src/components/RootView/Loading';
import {XlbHeader} from '@xlb/common/src/xlb-components-new';
import {normalize} from '@xlb/components-rn/styles';

import {authModel} from '@xlb/business-base/src/models/auth';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttp';

import {modifyPasswordStyle} from './style';

const Index: FC = () => {
  const routeParams: any = useRoute().params;
  const navigation = useNavigation();

  const [passwordVisible, setPasswordVisible] = useState(true);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(true);

  const [passwordErr, setPasswordErr] = useState(false);

  const password = useRef('');
  const passwordConfirm = useRef('');

  const onPasswordChange = (str: string) => {
    const reg = /^(?![a-zA-Z]+$)(?!\d+$)(?![^\da-zA-Z\s]+$).{8,20}$/;
    const bool = !str || reg.test(str);
    password.current = bool ? str : '';
    setPasswordErr(!bool);
  };

  // 保存
  const save = async () => {
    if (passwordErr) {
      Toast.show('密码格式错误，请检查', {position: Toast.positions.CENTER});
      return;
    } else if (password.current !== passwordConfirm.current) {
      Toast.show('两次输入的密码不一致', {position: Toast.positions.CENTER});
      return;
    }

    Dialog.confirm({
      title: '保存提示',
      message: '请确认是否保存',
    }).then(action => {
      if (action === 'confirm') {
        Loading.show();
        ErpHttp.post<CommonResponse>('/erp/hxl.erp.user.pwd.reset', {
          account: authModel?.state?.userInfos?.account,
          pwd: password.current,
          confirm_pwd: passwordConfirm.current,
          code: routeParams?.code || '',
          tel: routeParams?.tel || '',
        }).then(res => {
          if (res?.code === 0) {
            navigation.navigate('SuccessPage', {
              backRoute: 'UserInfo',
              backRouteName: '个人资料',
            });
          }
        });
      }
    });
  };

  return (
    <TouchableWithoutFeedback
      onPress={() => {
        Keyboard.dismiss();
      }}>
      <View style={modifyPasswordStyle.body}>
        <XlbHeader title="设置新密码" hasInputFilter={false} />

        <View style={modifyPasswordStyle.content}>
          <XlbText font_size_4 grey_7 style={{marginBottom: TOKEN.space_3}}>
            8-20位密码，至少包括英文字母、数字、特殊符号中的2种
          </XlbText>

          <Space gap={TOKEN.space_3} style={{marginBottom: normalize(48)}}>
            <View
              style={StyleSheet.flatten([
                modifyPasswordStyle.input_item,
                {borderColor: TOKEN.red_10, borderWidth: passwordErr ? 0.5 : 0},
              ])}>
              <TextInput
                autoFocus
                secureTextEntry={passwordVisible}
                placeholder="输入新的密码"
                placeholderTextColor={TOKEN.grey_25}
                style={{fontSize: normalize(15), padding: 0, flex: 1}}
                onChangeText={str => {
                  onPasswordChange(trim(str));
                }}
              />
              <Pressable
                style={{paddingRight: TOKEN.space_3}}
                onPress={() => {
                  setPasswordVisible(!passwordVisible);
                }}>
                <XlbIconfontNew
                  name={passwordVisible ? 'zhengyan' : 'biyan'}
                  color={TOKEN.grey_45}></XlbIconfontNew>
              </Pressable>
            </View>
            <View style={modifyPasswordStyle.input_item}>
              <TextInput
                secureTextEntry={confirmPasswordVisible}
                placeholder="再次输入密码"
                placeholderTextColor={TOKEN.grey_25}
                style={{fontSize: normalize(15), padding: 0, flex: 1}}
                onChangeText={str => {
                  passwordConfirm.current = trim(str);
                }}
              />
              <Pressable
                style={{paddingRight: TOKEN.space_3}}
                onPress={() => {
                  setConfirmPasswordVisible(!confirmPasswordVisible);
                }}>
                <XlbIconfontNew
                  name={confirmPasswordVisible ? 'zhengyan' : 'biyan'}
                  color={TOKEN.grey_45}></XlbIconfontNew>
              </Pressable>
            </View>
          </Space>

          <XlbButton
            type="primary"
            round={false}
            style={{borderRadius: TOKEN.space_1}}
            onPress={save}>
            保存
          </XlbButton>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default Index;
