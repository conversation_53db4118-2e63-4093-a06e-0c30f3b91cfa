import {Navigation<PERSON>ontainer, StackActions} from '@react-navigation/native';
import * as React from 'react';
import {
  StatusBar,
  DeviceEventEmitter,
  AppState,
  NativeEventSubscription,
} from 'react-native';

import Login from '@xlb/business-base/src/screens/login';
import HomeTabs from './HomeTabs';
import {navigationRef} from './NavigationService';
import {getHeaderScreens} from './HeaderScreens';
import {useModel} from 'foca';
import {authModel} from '@xlb/business-base/src/models/auth';
import Entrance from '@xlb/business-base/src/screens/entrance';
import {PageSelector} from '../components';
import OrganizationTreeSelect from '../components/organizationTreeSelect';
import DepartmentTreeSelect from '../components/DepartmentTreeSelect';
import FilterTempatePage from '../hooks/useFilterTemplate/template';
import useRefreshStore from '@xlb/common/src/models/refresh';
import useSystemStore from '@xlb/common/src/models/system';
import {NoticeMessage} from '@xlb/business-base/src/screens/noticeMessage';
import getApprovalCenterRoute from '@xlb/business-base/src/screens/approvalCenter/route';
import {
  NativeStackNavigationOptions,
  createNativeStackNavigator,
} from '@react-navigation/native-stack';
import PrivacyDetail from '@xlb/business-base/src/screens/auth/privacyDetail';
import Config from 'react-native-config';
import LoginInfo from '@xlb/business-base/src/screens/auth/loginInfo';
import ForgetPassWord from '@xlb/business-base/src/screens/auth/forgetPassWord';
import ResetPassword from '@xlb/business-base/src/screens/auth/resetPassword';
import {Watermark} from '../components';

import XLBStorage from '../utils/storage';
import Board from './Board';

import Dayjs from 'dayjs';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttp';
import {cloneDeep} from 'lodash';
import FastImage from 'react-native-fast-image';
// 最小化导航
import MiniNaviModal from '@xlb/business-base/src/screens/mileageSubsidy/commpoents/miniNaviModal';
import useStore from '@xlb/common/src/components/Amap/store';
import {createDrawerNavigator} from '@react-navigation/drawer';
import DrawerMine from '@xlb/business-base/src/components/pageWarpper/mine';
import {useNetInfo, fetch} from '@react-native-community/netinfo';
import {useState} from 'react';
import dayjs from 'dayjs';
import Agreement from '@xlb/business-base/src/screens/agreement';
import {ApproveManageRoute} from '@xlb/business-base/src/screens/approveManage/route';
import {RightDrawerScreen} from './preLoadRouter';
import TableDemoV2 from '../../../../src/TableDemoV2';
import {getRemoteComponent} from 'src/getRemoteComponent';
import {AppFieldsProps, applicationFields, AppModule} from '../config/fields';

const Drawer = createDrawerNavigator();

export const DrawerNavigator = () => {
  return (
    <Drawer.Navigator
      initialRouteName="Home"
      drawerContent={DrawerMine}
      screenOptions={{
        drawerType: 'slide',
        drawerStyle: {
          width: '87.46%',
        },
      }}>
      <Drawer.Screen
        name="Home"
        component={HomeTabs}
        options={{
          headerShown: false,
        }}
      />
    </Drawer.Navigator>
  );
};

export const defaultScreenOptions: NativeStackNavigationOptions = {
  animation: 'slide_from_right',
  headerShadowVisible: false,
  headerTitleAlign: 'center',
  headerTitleStyle: {
    fontSize: 18,
    color: '#061B17FF',
    fontWeight: '500',
  },
  // statusBarColor: 'red',
  headerBackButtonMenuEnabled: false,
  headerTintColor: '#000E18FF',
  headerBackVisible: false,
  // headerLeft: ({ canGoBack }) => <NavBack canGoBack={canGoBack} />,
  contentStyle: {backgroundColor: '#F4F5F7'},
};

const Stack = createNativeStackNavigator();
const AuthStack = createNativeStackNavigator();

const AuthNavigator = () => {
  return (
    <AuthStack.Navigator screenOptions={{gestureDirection: 'horizontal'}}>
      <Stack.Screen
        name="login"
        key={'login'}
        component={Login}
        options={{
          headerShown: false,
        }}
      />
    </AuthStack.Navigator>
  );
};
const lightStatusBarRoutes = [
  'NoNetWork',
  'Collect',
  'Entrance',
  'HelpPage',
  'baseWebView',
  'AboutVersion',
  'MinSetting',
  'HardwareSetting',
  'NoticeSetting',
  'LabelPrintPage',
  'Message',
  'Notice',
  'Details',
  'Search',
  'bossTable', //boss报表
  'storeSaleAnalysis', //销售分析
  'NisshoAnalysis', // 日商分析
  'storeBusinessAnalysis', //门店经营分析
  'StoreCompared', //店横对比
  'productSalesAnalysisH5', //商品销售分析
  // ErpRoutes.ConsumerRollRebate, // 消费卷返款
  // ErpRoutes.DeliveryMargins, ///配送分析
  // ErpRoutes.TimePeriodAnalysis, //时段分析
  // ErpRoutes.productPerformance, //单品业绩
  // ErpRoutes.GoodsRepurchase, // 商品复购分析
  'Stratificationanalysis', //分层分析
  'CollectionAnalysisIndex', //营业收款分析
  'CollectionAnalysisDetail', // 营业收款分析详情
  'StoreSaleAnasysis', // 门店销售分析
  'AreaSaleThinkIndex', // 区域销售分析
  'AbnormalCashAnalysis', // 异常收银分析
  'AbnormalCashAnalysisDetail', // 异常收银分析详情
  // UserApplyRoute.UserApply, // 用户申请
  // GoodsReportQueryRoute.List, //商品资质查询
  // SupplierManageRoutes.SupplierManageIndex, // 供应商管理
  // SupplierManageRoutes.SupplierManageDetail, //供应商管理详情
  'PurchaseOrder', // 采购订单
  'PurchaseOrderAdd', // 采购订单新增
  'PurchaseOrderDetail', // 采购订单详情
  'ReceiveOrderIndex', //采购收货单
  'ReceiveOrderAdd', //添加采购收货单
  'ReceiveOrderDetail', //采购收货单详情
  'ReceiveOrderBindDetail', // 点击采购单收货按钮跳转的详情页和已经绑定采购单的采购收货单跳转的详情页
  'ReturnOrderIndex', //采购退货单
  'ReturnOrderAdd', //添加采购退货单
  'ReturnOrderDetail', //采购退货单详情
  'ReturnOrderGoods', //商品详情
  'PurchasePlan', //采价计划
  'PurchasePlanAdd', //添加采价计划
  'PurchasePlanDetail', //采价详情

  'MultipleSelect', // 多单合并
  'StoreReplenishment', // 门店补货
  'StoreReplenishmentDetail', // 门店补货详情页
  'StoreReplenishmentAuditDetail', // 门店补货审核详情页
  'StoreReplenishmentAdd', // 门店补货新增页面
  'StoreReplenishmentGoodsDetail', // 门店补货商品详情

  'StoreOrder', // 门店订单
  'StoreOrderDetail', // 门店订单详情
  'StoreOrderAdd', // 门店订单新增
  'StoreOrderGoodsDetail', // 门店订单商品详情
  'StoreOrderAuditDetail', // 门店订单审核详情

  'DeliveryInOrder', //调入单
  'DeliveryInOrderDetail', //调入单详情

  'DeliveryOrder', // 调出单
  'DeliveryOrderAdd', // 调出单新增
  'DeliveryOrderDetail', // 调出单详情

  'StoreApplicationOrder', // 门店申请单
  'StoreApplicationOrderAdd', // 门店申请单新增
  'StoreApplicationOrderChoseDeliveryInOrder',

  'WholeSaleOrder', //批发订单
  'WholeSaleOrderAdd', //批发订单新增
  'SelectWholeClient', //选择批发客户

  'TransferBasketInOut', //中转筐
  'TransferBasketInOutAdd',
  'TransferBasketDetail',
  'TransferBasketAdd',
  'TransferBasketDirection',
  'TransferBasketName',
  'TransSelectStore',

  'StockCheckOrder', //库存盘点
  'StockCheckOrderDetail', //库存盘点详情
  'StockCheckOrderAdd', // 库存盘点新增

  'NewStockAdjustOrder', // 库存调整
  'NewStockAdjustOrderAdd', // 库存调整新增
  'NewStockAdjustOrderDetail', // 库存调整详情

  'stockQuery', //库存查询
  'stockQueryDetail', //库存查询详情

  'ItemQuery', //商品查询
  'ItemQueryDetail', //商品查询详情

  'MerchantRegister', // 商户进件
  // ErpRoutes.postOrderH5,
  'Home',
  'CRMBorad', //招商看板
  'SDSBorad', // 标地看板
  'operatingIncome', //营业外收入
  'operatingIncomeDetail',
  'StoreFee', // 门店费用
  'StoreFeeDetail',
  'IngredientProductIndex', // 成分商品调价
  'IngredientProductAdd',
  'IngredientProductDetail',
  'IngredientDetail',
  'RetailPriceAdjustIndex', //零售价调整
  'RetailPriceAdjustAdd', //零售价调整新增
  'RetailPriceAdjustDetail', //零售价调整详情
  'DiscountCodeIndex', // 折扣码
  'StoreRetailPriceSale', // 门店零售价
  'StoreRetailDetail', // 门店零售价详情
  'PriceTagApply', //价签申请
  'PriceTagApplyDetail', //价签申请详情
  'OnlineTransactionIndex', // 线上交易记录
  'SettlementEntityReconciliationIndex', // 结算实体对账
  'storeAccountPage', // 往来对账
  'newCloudMoney', // 贷款账户
  'PalletAnalysis', //经营货盘分析
  // AttendanceRoute.ATTENDANCE_PM,
  // EmsWebRoute.WORK_ORDER_CLIENT,
  // EmsWebRoute.WORK_ORDER_SERVER,
  // EmsWebRoute.CONSTRUCTION_MANAGE,
  // RouteDefine.confirmInOrderNew.index,
  // RouteDefine.confirmInOrderNew.detail,
  // RouteDefine.confirmInOrderNew.itemDetail,
  // RouteDefine.confirmInOrderNew.items,
  // AttendanceRoute.DETAIL,
  'ApprovalCenterList',
  'ApprovalCenterAdd',
  'ApprovalCenterSearchList',
  'ApprovalCenterMemo',
  'FormTableListPage',
  'UserApplyDetail',
  'UserManage',
  'UserManageDetail',
  'UserApplyDetail',
  'UserInfo',
  'ModifyPassword',
  'ModifyPhone',
  'VerifyPhone',
  'SuccessPage',
  'RECALL',
  'URGING_PAGE',
  'ModifyRecord',
  'Jumpout',
  'ClientManageStoreInfoDetailNew',
  'ClientMangeDetailNew',
  'componentCenter',
  // RouteDefine.pointManagement.index,
  // RouteDefine.pointManagement.detail,
  // RouteDefine.confirmInOrder_new.index,
  // RouteDefine.confirmInOrder_new.detail,
  // RouteDefine.confirmInOrder_new.itemDetail,
  'login',
  'Agreement',
  'ForgetPassWord',
  'ResetPassword',
  ApproveManageRoute.ApproveIndex, //审批首页
  // RouteDefine.InventoryCounts.index,
  // ErpRoutes?.stockQueryNew,
  // RouteDefine.InventoryCounts.detail,
  // RouteDefine.InventoryCounts.item,
  // RouteDefine.ProcurementReceive.index,
  // RouteDefine.ProcurementReceive.detail,
  // RouteDefine.ProcurementReturn.index,
  // RouteDefine.ProcurementReturn.detail,
  'SwitchTheme',
  // ErpRoutes.storeOrderLogisticsDetail,
  // ErpRoutes.ShortageWarning,
  // ErpRoutes.GoodsStockoutRate,
  // ErpRoutes.ArrivalReminder,
  'StoreDespoit',
  // ErpRoutes.StoreOrderDeliveryStatisticsDetail,
];

const App: React.FC = () => {
  const {isLoggedIn} = useModel(authModel);

  const [checkLogin, setCheckLogin] = React.useState(!isLoggedIn);

  const refresh = useRefreshStore((state: any) => state.homeRefresh);
  const setHomeRefresh = useRefreshStore((state: any) => state.setHomeRefresh);

  const subscription = React.useRef<NativeEventSubscription>(undefined);
  const appState = React.useRef(AppState.currentState);
  const userInfoRef = React.useRef<any>(null);
  const isLoggedInRef = React.useRef(false);
  const {type, isConnected} = useNetInfo();
  // const route = useRoute()
  const timer = React.useRef<any>(null);
  const [imageLoaded, setImageLoaded] = useState(true);
  // 最小化导航

  const setVisible = useStore((state: any) => state.setVisible);
  const setShowMiniNavi = useStore((state: any) => state.setShowMiniNavi);
  const showMiniNavi = useStore((state: any) => state.showMiniNavi);
  const {themeName} = useSystemStore((state: any) => state);

  const pointData = React.useRef([]);
  const historyRoutes = React.useRef([]);
  React.useEffect(() => {
    //更新导航的起终点数据
    const pointEmit = DeviceEventEmitter.addListener(
      'naviPointUpdate',
      (data: any) => {
        pointData.current = data?.points || [];
        // setPointsData(data?.points || [])
      },
    );
    return () => {
      pointEmit.remove();
    };
  }, []);

  React.useEffect(() => {
    isLoggedInRef.current = isLoggedIn;
  }, [isLoggedIn]);

  /**
   * 路由变动了
   */
  async function onStateChange(prevState, currentState) {
    let res = fetch();

    const currentRoute = navigationRef?.current?.getCurrentRoute();
    // statusbar 黑字路由列表

    if (lightStatusBarRoutes.includes(currentRoute.name)) {
      StatusBar.setBarStyle(
        themeName === 'default' ? 'dark-content' : 'light-content',
      );
    } else {
      setTimeout(() => {
        StatusBar.setBarStyle(
          themeName === 'default' ? 'light-content' : 'dark-content',
        );
      }, 900);
    }
    if (!isConnected && currentRoute.name !== 'NoNetWork') {
      // if (isEqual(historyRoutes.current, prevState.routes)) return false
      if (currentRoute.name === 'Home') return false;
      // navigationRef.current?.relpace('NoNetWork')
      navigationRef.current.dispatch(StackActions.replace('NoNetWork'));
      historyRoutes.current = prevState.routes
        .filter(e => e.name !== 'NoNetWork')
        .map(e => e.name);
    }
    // 你也可以设置更多的上下文信息，例如：
    // Sentry.setExtra('routeParams', state.routes[state.index].params);
  }

  /** 初始化获取登录信息 */
  const initUserInfo = async () => {
    const userInfo = await XLBStorage.getItem('loginInfo');
    if (userInfo) userInfoRef.current = userInfo;
    if (isLoggedIn) loginCheck();
  };

  const setLoginState = () => {
    // console.log('imageLoaded', imageLoaded)
    timer.current = setTimeout(() => {
      setCheckLogin(true);
    }, 0);
  };

  /** 刷新token */
  const refreshToken = async (userInfo: any) => {
    if (!authModel.state?.userInfos?.company_id) return;

    const res: any = await ErpHttp.post(
      '/erp/hxl.erp.user.token.refresh',
      {
        refresh_token: userInfo.refresh_token,
        company_id: authModel.state.userInfos.company_id,
      },
      {timeout: 30000},
    );
    if (res.code === 0) {
      authModel.setUserInfos({
        ...cloneDeep(authModel.state.userInfos),
        token: res.data.access_token,
        access_token: res.data.access_token,
        refresh_token: res.data.refresh_token,
        authorities: res.data.authorities,
      });
      XLBStorage.setItem('loginInfo', {
        loginTime: Dayjs().format('YYYY-MM-DD HH:mm:ss'),
        refresh_token: res.data.refresh_token,
      });
      setHomeRefresh();
    } else {
      DeviceEventEmitter.emit('logOut');
    }

    setLoginState();
  };

  /** 登录过期检测，大于5天刷新token */
  const loginCheck = async (msg?: boolean) => {
    let obj = userInfoRef.current;

    if (msg) {
      obj = await XLBStorage.getItem('loginInfo');
    }

    if (obj) {
      const loginTime = Dayjs(obj?.loginTime);
      const now = Dayjs();
      const diff = now.diff(loginTime, 'day');
      if (diff > 5) {
        refreshToken(obj);
      } else {
        setLoginState();
      }
    } else {
      if (isLoggedIn) {
        refreshToken({refresh_token: authModel.state.userInfos.refresh_token});
      } else {
        setLoginState();
      }
    }
  };

  /** 从后台进入前台检测token */
  const appStateCheck = () => {
    subscription.current = AppState.addEventListener('change', nextAppState => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active' &&
        isLoggedInRef.current
      )
        loginCheck(true);
      appState.current = nextAppState;
    });
  };

  React.useEffect(() => {
    initUserInfo();
    appStateCheck();
    return () => {
      clearTimeout(timer.current);
      subscription.current?.remove();
    };
  }, []);

  return (
    <NavigationContainer ref={navigationRef} onStateChange={onStateChange}>
      {/* {isVConsole && (
        <VConsole
          // 使用 'react-native-config-reader' 库获获取额外信息
          appInfo={{
            info: '额外信息',
            // deviceId: DeviceInfo.getUniqueId(),
            deviceName: result,
            systemName: DeviceInfo.getSystemName(),
            systemVersion: DeviceInfo.getSystemVersion(),
            model: DeviceInfo.getModel(),
            brand: DeviceInfo.getBrand(),
            userAgent: DeviceInfo.getUserAgentSync(),
          }}
          // console.time 可辨别是否开启 debug 网页
          console={__DEV__ ? !console.time : true}
        />
      )} */}
      <Watermark />
      {/* <StatusBar
        translucent
        hidden={false}
        animated={true}
        barStyle={'dark-content'}
        backgroundColor={'transparent'}
      /> */}

      {checkLogin ? (
        <Stack.Navigator screenOptions={defaultScreenOptions} key={refresh}>
          {isLoggedIn ? (
            <>
              {/* 跳转页面进行多选页面，以页面为组件使用 */}
              <Stack.Screen
                name="Home"
                component={RightDrawerScreen}
                options={{headerShown: false}}
              />
              <Stack.Screen
                name="Entrance"
                component={Entrance}
                // @ts-ignore
                options={{headerShown: false, animationEnabled: false}}
              />
              {/* <Stack.Screen options={{ headerShown: false }} name="HomeOld" component={HomeTabsOld} /> */}
              <Stack.Screen
                name="pageSelector"
                component={PageSelector}
                // @ts-ignore
                options={{headerShown: false}}
              />
              <Stack.Screen
                name="organizationSelect"
                component={OrganizationTreeSelect}
                // @ts-ignore
                options={{headerShown: false}}
              />
              <Stack.Screen
                name="departmentSelect"
                component={DepartmentTreeSelect}
                // @ts-ignore
                options={{headerShown: false}}
              />
              {/* 筛选模板 */}
              <Stack.Screen
                name="filterTemplatePage"
                component={FilterTempatePage}
                // @ts-ignore
                options={{headerShown: false}}
              />

              {/* <Stack.Screen name={'HistoryPage'} component={HistoryPage} options={{ headerShown: false }} />
              <Stack.Screen name={'SearchResult'} component={SearchResult} options={{ headerShown: false }} /> */}
              <Stack.Screen
                name={'Board'}
                component={Board}
                options={{headerShown: false}}
              />
              {getHeaderScreens().map(getScreen => getScreen())}

              {/* {getApprovalTaskRoute()} */}
              {getApprovalCenterRoute()}

              {/* TODO */}
              {Object.keys(applicationFields).map((key: any) => (
                <Stack.Group key={key}>
                  {applicationFields[key as keyof AppFieldsProps]
                    .filter(d => d.route && !d.hiddenStack)
                    .map((appModule: AppModule) => {
                      return (
                        <Stack.Screen
                          key={appModule.route}
                          name={appModule.route}
                          children={props => {
                            let [AppName, ComponentName] =
                              appModule.route.split('.');
                            let Component = getRemoteComponent(
                              AppName,
                              ComponentName,
                            );

                            return <Component {...props} />;
                          }}
                          options={{
                            headerShown: false,
                          }}
                        />
                      );
                    })}
                </Stack.Group>
              ))}
              <Stack.Screen
                name="noticeMessage"
                component={NoticeMessage}
                // @ts-ignore
                options={{headerShown: false}}
              />
              <Stack.Screen
                name="SmartTableDemoV2"
                component={TableDemoV2}
                // @ts-ignore
                options={{headerShown: false}}
              />
            </>
          ) : (
            <>
              <Stack.Screen
                name="Login"
                component={AuthNavigator}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                options={{
                  headerShown: false,
                }}
                name={'Agreement'}
                key={'Agreement'}
                component={Agreement}
              />
              <Stack.Screen
                options={{
                  headerShown: false,
                }}
                name={'PrivacyDetail'}
                key={'PrivacyDetail'}
                component={PrivacyDetail}
              />
              <Stack.Screen
                name={'LoginInfo'}
                key={'LoginInfo'}
                component={LoginInfo}
                options={{headerShown: false}}
              />
              <Stack.Screen
                name={'ForgetPassWord'}
                key={'ForgetPassWord'}
                component={ForgetPassWord}
                options={{headerShown: false}}
              />
              <Stack.Screen
                name={'ResetPassword'}
                key={'ResetPassword'}
                component={ResetPassword}
                options={{headerShown: false}}
              />
            </>
          )}
        </Stack.Navigator>
      ) : (
        <>
          <FastImage
            source={
              imageLoaded
                ? {
                    cache: FastImage.cacheControl.web,
                    uri: `https://hzlapp.oss-cn-hangzhou.aliyuncs.com/jieqi/${dayjs().format('YYYYMMDD')}.jpg`,
                  }
                : require('../assets/images/3.png')
            }
            // onLoad={() => setImageLoaded(true)} // 当图片成功加载时调用
            onError={() => {
              setImageLoaded(false);
              setTimeout(() => {
                setCheckLogin(true);
              }, 0);
            }}
            resizeMode="cover"
            // defaultSource={require('../assets/images/3.png')} // or use require('./path/to/local/image.png') for local images
            style={{width: '100%', height: '100%'}}></FastImage>
        </>

        // <Stack.Screen
        //   name="Login"
        //   component={Login}
        //   options={{
        //     headerShown: false,
        //   }}
        // />
      )}

      {/* 最小化地图 */}
      {
        <>
          <MiniNaviModal
            showModal={showMiniNavi}
            clickClose={() => {
              setShowMiniNavi(false);
              setVisible(true);
            }}
          />

          {/* <AmapNativePage
            onBackdropPress={() => {
              console.log('back')
            }}
            onCalculateRoutBack={(data) => {
              DeviceEventEmitter.emit('naviMapDone', { points: data })
            }}
            onDidStartNavi={() => {
              DeviceEventEmitter.emit('naviMapStart')
            }}
            onDidStoptNavi={() => {
              DeviceEventEmitter.emit('naviMapStop')
            }}
            onBackGroundModel={() => {
              setShowMiniNavi(true)
            }}
          /> */}
        </>
      }
    </NavigationContainer>
  );
};

export default React.memo(App);
