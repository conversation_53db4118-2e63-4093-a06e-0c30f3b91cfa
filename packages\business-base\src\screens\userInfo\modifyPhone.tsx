import React, {useState, useRef} from 'react';
import type {FC} from 'react';

import {
  View,
  StyleSheet,
  Pressable,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import {useRoute, useNavigation} from '@react-navigation/native';

import Toast from 'react-native-root-toast';
import trim from 'lodash/trim';

import {Dialog, TextInput} from '@fruits-chain/react-native-xiaoshu';
import {XlbText, TOKEN, XlbButton} from '@xlb/components-rn';

import Loading from '@xlb/common/src/components/RootView/Loading';
import {XlbHeader} from '@xlb/common/src/xlb-components-new';
import {normalize} from '@xlb/components-rn/styles';

import {authModel} from '@xlb/business-base/src/models/auth';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttp';

import {reg, numReg} from './define';

import {modifyPhoneStyle, ComponentStyle} from './style';
import {DeviceEventEmitter} from 'react-native';

const Index: FC = () => {
  const route = useRoute();
  const navigation = useNavigation();

  const tel = route.params?.tel ? route.params?.tel.replace(reg, ' ') : '';

  const [data, setData] = useState('');
  const [visible, setVisible] = useState(false);

  const [loading, setLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60); // 时间
  const [captcha, setCaptcha] = useState(''); // 验证码

  const inputRef = useRef<TextInput>(null);
  const phoneNumberRef = useRef('');

  const formatPhoneNumber = (input: string) => {
    let str = '';

    // 获取输入框中的内容并去除空格
    let phoneNumber = input.replace(/\s+/g, '');

    if (!phoneNumber.length) {
      setData('');
      return;
    }
    if (!numReg.test(phoneNumber)) return;

    phoneNumberRef.current = phoneNumber;

    // 限制输入框中的内容长度为11位
    if (phoneNumber.length >= 11) {
      Keyboard.dismiss();
      inputRef.current?.focus();
    }

    // 判断输入框中的内容是否需要添加空格
    if (phoneNumber.length > 3 && phoneNumber.length < 8) {
      str = phoneNumber.slice(0, 3) + ' ' + phoneNumber.slice(3);
    } else if (phoneNumber.length >= 8) {
      str =
        phoneNumber.slice(0, 3) +
        ' ' +
        phoneNumber.slice(3, 7) +
        ' ' +
        phoneNumber.slice(7, 11);
    } else {
      str = phoneNumber;
    }

    setVisible(phoneNumber.length >= 11);
    setData(str);
  };

  const btnOnPress = async () => {
    if (loading) return;
    if (phoneNumberRef.current === authModel.state?.userInfos?.tel) {
      Toast.show('手机号一致，请重新输入', {position: Toast.positions.CENTER});
      return;
    }

    setLoading(true);
    Loading.show();

    const res = await ErpHttp.post<CommonResponse>(
      '/erp/hxl.erp.user.changetelcode.send',
      {
        tel: phoneNumberRef.current,
      },
    );

    if (res?.code === 0) {
      let time = 59;
      const interval = setInterval(() => {
        setTimeLeft(time);
        time -= 1;
        if (time < 0) {
          clearInterval(interval);
          setLoading(false);
          setTimeLeft(60);
        }
      }, 1000);

      Toast.show(
        `验证码已发送到尾号为${phoneNumberRef.current.substring(phoneNumberRef.current.length - 4)}的手机上`,
        {position: Toast.positions.CENTER},
      );
    } else {
      Toast.show(`验证码发送失败`, {position: Toast.positions.CENTER});
    }
  };

  // 保存
  const save = async () => {
    Keyboard.dismiss();
    Dialog.confirm({
      title: '保存提示',
      message: '请确认是否保存',
    }).then(action => {
      if (action === 'confirm') {
        ErpHttp.post<CommonResponse>('/erp/hxl.erp.user.tel.change', {
          code: captcha,
          tel: phoneNumberRef.current,
        }).then(res => {
          if (res?.code === 0) {
            Toast.show('保存成功');
            // navigation.navigate('UserInfo', { tel: phoneNumberRef.current })
            // authModel.setTel(phoneNumberRef.current)
            // authModel?.setUserInfos({ ...authModel.state.userInfos, tel: phoneNumberRef.current })
            console.log('退出登录发起');
            DeviceEventEmitter.emit('logOut');
          }
        });
      }
    });
  };

  return (
    <TouchableWithoutFeedback
      onPress={() => {
        Keyboard.dismiss();
      }}>
      <View style={modifyPhoneStyle.body}>
        <XlbHeader title="修改手机号" hasInputFilter={false} />

        <View style={modifyPhoneStyle.content}>
          <XlbText font_size_4 grey_7 style={{marginBottom: TOKEN.space_3}}>
            当前手机号{tel}
          </XlbText>

          <View
            style={StyleSheet.flatten([
              ComponentStyle.card,
              {marginBottom: normalize(48)},
            ])}>
            <View style={{paddingLeft: TOKEN.space_3}}>
              <View style={ComponentStyle.card_item}>
                <TextInput
                  keyboardType="numeric"
                  clearable={true}
                  clearTrigger="focus"
                  value={data}
                  placeholder="新的手机号"
                  placeholderTextColor={TOKEN.grey_25}
                  style={{
                    fontSize: normalize(15),
                    padding: 0,
                    flex: 1,
                    color: TOKEN.grey_10,
                  }}
                  onChangeText={formatPhoneNumber}
                />
              </View>
              <View
                style={StyleSheet.flatten([
                  ComponentStyle.card_item,
                  {borderBottomWidth: 0},
                ])}>
                <View style={{paddingRight: 20, flex: 1}}>
                  <TextInput
                    keyboardType="numeric"
                    clearable={true}
                    clearTrigger="focus"
                    ref={inputRef}
                    placeholder="验证码"
                    placeholderTextColor={TOKEN.grey_25}
                    style={{
                      fontSize: normalize(15),
                      padding: 0,
                      flex: 1,
                      color: TOKEN.grey_10,
                    }}
                    onChangeText={text => {
                      setCaptcha(trim(text));
                    }}
                  />
                </View>

                {visible ? (
                  <Pressable
                    style={{height: '100%', justifyContent: 'center'}}
                    onPress={btnOnPress}>
                    <XlbText
                      font_size_5
                      style={{
                        color: loading ? TOKEN.grey_25 : TOKEN.primary_10,
                      }}>
                      {loading ? `${timeLeft}秒后重试` : '获取验证码'}
                    </XlbText>
                  </Pressable>
                ) : null}
              </View>
            </View>
          </View>

          <XlbButton
            disabled={!trim(captcha).length}
            type="primary"
            round={false}
            style={{borderRadius: TOKEN.space_1}}
            onPress={save}>
            保存
          </XlbButton>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default Index;
