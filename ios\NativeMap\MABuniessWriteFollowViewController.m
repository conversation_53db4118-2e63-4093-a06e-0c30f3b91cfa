//
//  MABuniessWriteFollowViewController.m
//  xlb
//
//  Created by 莱昂纳多·迪卡普里奥 on 2025/2/17.
//

#import "MABuniessWriteFollowViewController.h"
#import "MAHttpTool.h"
#import "MALoadWaveView.h"
#import "UIView+Common.h"
#import "MANativeAlert.h"
#import "UIView+Toast.h"
#import "NSDictionary+Common.h"
#import "NSArray+Common.h"
#import "UIImageView+WebCache.h"
#import "UITextView+Common.h"
#import "MASingleDateView.h"
#import "MABottomSingleSelectAlert.h"
#import "Masonry.h"
#import "MAUploadFileView.h"
#import "MATopToastView.h"
#import "MATopNotiflyView.h"

// 判断字符串是否为空
#define BCStringIsEmpty(str) ([str isKindOfClass:[NSNull class]] || str == nil || ![str isKindOfClass:[NSString class]] || [str length]< 1 ? YES : NO || [str isEqualToString:@"null"] || [str isEqualToString:@"<null>"] || [str isEqualToString:@"(null)"])
// 判断数组是否为空
#define BCArrayIsEmpty(array) (array == nil || [array isKindOfClass:[NSNull class]] || ![array isKindOfClass:[NSArray class]] || [array count] == 0)
// 判断字典是否为空
#define BCDictIsEmpty(dic) (dic == nil || [dic isKindOfClass:[NSNull class]] || ![dic isKindOfClass:[NSDictionary class]] || dic.allKeys.count == 0)

#define BCWidth   [UIScreen mainScreen].bounds.size.width
#define BCHeight  [UIScreen mainScreen].bounds.size.height
#define COLOR(R, G, B) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:1]
#define ACOLOR(R, G, B,A) [UIColor colorWithRed:R/255.0 green:G/255.0 blue:B/255.0 alpha:A]

#define RealSize(value)  MAX(round(value * [UIScreen mainScreen].bounds.size.width / 400.0), value)
#define MutilFont(value)  [UIScreen mainScreen].bounds.size.width > 420 ? (value + 2) : value

@interface MABuniessWriteFollowViewController ()<UITextViewDelegate>

@property (nonatomic, assign) CGFloat heightTop;
@property (nonatomic, strong) MALoadWaveView *loadingView;//加载
@property (nonatomic, strong) UIButton *submitButton;//提交按钮
@property (nonatomic, strong) UITextView *memoTV;//备注
@property (nonatomic, strong) UILabel *wordCountLabel;
@property (nonatomic, strong) MAUploadFileView *uploadFileView;
@property (nonatomic, strong) NSArray *uploadArr;
@property (nonatomic, strong) CAShapeLayer *shapeLayer;
@property (nonatomic, strong) UIScrollView *homeScrollView;//首页scrollview
@property (nonatomic, strong) MATopToastView *statusView;//加载
@property (nonatomic, strong) MATopNotiflyView *notiflyView;//加载

@end

@implementation MABuniessWriteFollowViewController

- (void)viewDidLoad {
  [super viewDidLoad];
  
  self.view.backgroundColor = COLOR(242, 243, 245);
  if (@available(iOS 13.0, *)) {
    self.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
  }
  
  _heightTop = 54;

  if (@available(iOS 13.0, *)) {
    NSSet *set = [UIApplication sharedApplication].connectedScenes;
    UIWindowScene *windowScene = [set anyObject];
    if (windowScene) {
      UIWindow *window = windowScene.windows.firstObject;
     
      if (window.safeAreaInsets.top > 0) {
        _heightTop = window.safeAreaInsets.top;
       
      } else {
        _heightTop = 54;
       
      }
    }
    
  } else {
    UIWindow *window = [[UIApplication sharedApplication].windows firstObject];
    if (window.safeAreaInsets.top > 0) {
      _heightTop = window.safeAreaInsets.top;
    
    } else {
      _heightTop = 54;
     
    }
  }
  
  [self initToolBar];
  
  [self loadBottomView];
  
  [self.view addSubview:self.homeScrollView];
  
  [self loadMemoView];
  
}

- (void)initToolBar{
  
  UIView *toolbar = [[UIView alloc] initWithFrame: CGRectMake(0, 0, BCWidth, _heightTop + 44)];
  toolbar.backgroundColor = [UIColor whiteColor];
  [self.view addSubview:toolbar];
  
  //  返回按钮
  UIButton *switchMap = [UIButton buttonWithType:UIButtonTypeCustom];
  switchMap.frame = CGRectMake(14, _heightTop ,136, 44);
  [switchMap addTarget:self action:@selector(back) forControlEvents:UIControlEventTouchUpInside];
  [toolbar addSubview:switchMap];
  
  UIImageView *leftIM = [[UIImageView alloc] initWithFrame:CGRectMake(0, 12, 20, 20)];
  leftIM.image = [UIImage imageNamed:@"nav_back"];
  [switchMap addSubview:leftIM];
  
  UILabel *titleL = [[UILabel alloc] init];
  titleL.frame = CGRectMake(0, self.heightTop, BCWidth,  44);
  titleL.text = @"跟进录入";
  titleL.textAlignment = NSTextAlignmentCenter;
  titleL.textColor = COLOR(31, 33, 38);
  titleL.font = [UIFont systemFontOfSize:MutilFont(17) weight:UIFontWeightMedium];
  [toolbar addSubview: titleL];
  
}

#pragma mark 底部保存按钮
- (void)loadBottomView{
  
  UIView *bottomV = [[UIView alloc] initWithFrame:CGRectMake(0, BCHeight , BCWidth, 90)];
  bottomV.backgroundColor = [UIColor whiteColor];
  [self.view addSubview:bottomV];
  
  UIButton *sureBtn = [[UIButton alloc] initWithFrame:CGRectMake(16, 12, BCWidth - 32, 44)];
  sureBtn.layer.cornerRadius = 4;
  [sureBtn setTitle:@"提交" forState:UIControlStateNormal];
  sureBtn.backgroundColor = ACOLOR(26, 106, 255, 1);
  sureBtn.userInteractionEnabled = YES;
  sureBtn.titleLabel.font = [UIFont systemFontOfSize:MutilFont(16) weight:UIFontWeightMedium];
  [sureBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
  [sureBtn addTarget:self action:@selector(clickSubmit) forControlEvents:UIControlEventTouchUpInside];
  [bottomV addSubview:sureBtn];
  self.submitButton = sureBtn;
  
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(0,0, BCWidth , 1)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [bottomV addSubview:lineV];
  
  [UIView animateWithDuration:0.3 delay:0.5 options:UIViewAnimationOptionCurveEaseInOut animations:^{
    bottomV.top = BCHeight - 90;
  } completion:^(BOOL finished) {
    
  }];
}

- (void)loadMemoView{
  
  __weak typeof(self)weakSelf = self;
  
  UIView *memoV = [[UIView alloc] init];
  memoV.backgroundColor = [UIColor whiteColor];
  [self.homeScrollView addSubview:memoV];
  [memoV mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.mas_equalTo(12);
    make.left.mas_equalTo(0);
    make.height.mas_equalTo(228);
    make.width.mas_equalTo(BCWidth);
  }];
  
  UILabel *timeStartL = [[UILabel alloc] initWithFrame:CGRectMake(8, 14, 8, 21)];
  timeStartL.text = @"*";
  timeStartL.font = [UIFont systemFontOfSize:MutilFont(15)];
  timeStartL.textColor = COLOR(255, 33, 33);
  [memoV addSubview:timeStartL];
  
  UILabel *timeL = [[UILabel alloc] initWithFrame:CGRectMake(16, 14, 100, 21)];
  timeL.text = @"跟进详情";
  timeL.font = [UIFont systemFontOfSize:MutilFont(15)];
  timeL.textColor = COLOR(31, 33, 38);
  [memoV addSubview:timeL];
  
  self.memoTV = [[UITextView alloc] initWithFrame:CGRectMake(16, 43 , BCWidth - 32, 92)];
  self.memoTV.backgroundColor = [UIColor whiteColor];
  self.memoTV.font = [UIFont systemFontOfSize:MutilFont(15)];
  self.memoTV.delegate = self;
  self.memoTV.textContainerInset = UIEdgeInsetsZero;
  self.memoTV.returnKeyType = UIReturnKeyDone;
  self.memoTV.textContainer.lineFragmentPadding = 0;
  self.memoTV.textColor = COLOR(31, 33, 38);
  self.memoTV.inputAccessoryView = [self returnDone];
  [memoV addSubview:self.memoTV];
  self.memoTV.placeholder = @"请输入";
  
  self.wordCountLabel = [[UILabel alloc] initWithFrame:CGRectMake(BCWidth - 16 - 60, self.memoTV.bottom + 8, 60, 20)];
  self.wordCountLabel.textAlignment = NSTextAlignmentRight;
  self.wordCountLabel.textColor = ACOLOR(30, 33, 38, 0.45);
  self.wordCountLabel.font = [UIFont systemFontOfSize:MutilFont(14)];
  self.wordCountLabel.text = [NSString stringWithFormat:@"%lu/%@",(unsigned long)self.memoTV.text.length,@(200)];
  [memoV addSubview:self.wordCountLabel];
  
  UIView *lineV1 = [[UIView alloc] initWithFrame:CGRectMake(16, self.wordCountLabel.bottom + 14, BCWidth - 16, 0.5)];
  lineV1.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [memoV addSubview:lineV1];
  
  self.uploadFileView = [[MAUploadFileView alloc] initWithFrame:CGRectMake(0,lineV1.bottom, BCWidth, 50) withUrl:@"kms/hxl.kms.businessplan.follow.upload" withTitle:@"附件" isRequire:NO uploadParams:@{@"fid":self.pointId,@"fileType":@"followRecord"} endUpload:@[] showLine:NO];
  [memoV addSubview:self.uploadFileView];
  self.uploadFileView.uploadDataBlock = ^(NSArray *uploadData) {
    weakSelf.uploadArr = uploadData;
    [memoV mas_updateConstraints:^(MASConstraintMaker *make) {
       make.height.mas_equalTo(228 + uploadData.count * 52);
    }];
  };
  
  self.uploadFileView.chooseBlock = ^(CGFloat bottom) {
   
      weakSelf.homeScrollView.contentSize = CGSizeMake(BCWidth, bottom + 180);
    
  };

}

- (void)viewDidAppear:(BOOL)animated{
  [super viewDidAppear:animated];
  self.navigationController.interactivePopGestureRecognizer.delegate = (id)self;//侧滑手势
  if ([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
  }
}
#pragma mark 点击底部保存按钮
- (void)clickSubmit{
  
  __weak typeof(self)weakSelf = self;
  
  if (BCStringIsEmpty(self.memoTV.text)) {
    [self.notiflyView showModal:NotiflyFail andTitle:@"请填写跟进详情"];
    return;
  }
  
  MANativeAlert *alertV = [[MANativeAlert alloc] initWithTitle:@"确定要提交吗?" content:@"" cancelButton:@"取消" endSureButton:@"确定"];
  [alertV showModal];
  alertV.okBlock = ^(NSString *dateStr) {
    
    
    [weakSelf startAnimation];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.6 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [weakSelf submitData];
    });
    
  };
}

#pragma mark 提交数据到服务器端
- (void)submitData{
  
  NSMutableDictionary *pamas = [NSMutableDictionary dictionary];
  [pamas setObject:self.memoTV.text forKey:@"memo"];
  [pamas setObject:self.pointId forKey:@"id"];
  if (!BCArrayIsEmpty(self.uploadArr)) {
    
    NSMutableArray *upArr = [NSMutableArray array];
    for (NSDictionary *dic in self.uploadArr) {
      [upArr addObject:[dic objectForKeyNil:@"url"]];
    }
    
    [pamas setObject:upArr forKey:@"urls"];
  }
  
  [[MAHttpTool defaultManagerTool] postWithURL:@"kms/hxl.kms.businessplan.followrecord.save" Params:pamas success:^(NSDictionary *successResult) {
    
    [self.statusView showModal:ToastSuccess andTitle:@"提交成功"];
    [self stopAnimation];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"REFRESHBUNIESSDETAILDATA" object:nil userInfo:nil];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self.navigationController popViewControllerAnimated:YES];
    });
    
  } failure:^(NSString *errorResult) {
    
    [self stopAnimation];
    [[UIApplication sharedApplication].keyWindow makeToast:errorResult duration:1 position:CSToastPositionCenter];
  }];
  
}

-(void)startAnimation{
  CGPoint center = self.submitButton.center;
  CGFloat width = self.submitButton.frame.size.height;
  CGFloat height = self.submitButton.frame.size.height;
  CGRect desFrame = CGRectMake(center.x - width / 2, center.y - height / 2, width, height);
  
  self.submitButton.userInteractionEnabled = NO;
  
  [UIView animateWithDuration:0.3 animations:^{
    self.submitButton.titleLabel.alpha = .0f;
    self.submitButton.frame = desFrame;
    self.submitButton.layer.cornerRadius = self.submitButton.frame.size.height/2;
    
  } completion:^(BOOL finished) {
    
    UIBezierPath* path = [[UIBezierPath alloc] init];
    [path addArcWithCenter:CGPointMake(22,22) radius:(44/2 - 5) startAngle:0 endAngle:M_PI_2 * 2 clockwise:YES];
    self.shapeLayer = [[CAShapeLayer alloc] init];
    self.shapeLayer.lineWidth = 2.5;
    self.shapeLayer.strokeColor = [UIColor whiteColor].CGColor;
    self.shapeLayer.fillColor = COLOR(26, 106, 255).CGColor;
    self.shapeLayer.path = path.CGPath;
    self.shapeLayer.lineCap = kCALineCapRound;
    [self.submitButton.layer addSublayer:self.shapeLayer];
    
    
    CABasicAnimation *baseAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    baseAnimation.duration = 0.4;
    baseAnimation.fromValue = @(0);
    baseAnimation.toValue = @(2 * M_PI);
    baseAnimation.repeatCount = MAXFLOAT;
    baseAnimation.removedOnCompletion = NO;
    baseAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
    [self.submitButton.layer addAnimation:baseAnimation forKey:nil];
  }];
}

- (void)stopAnimation{
  [self.shapeLayer removeFromSuperlayer];
  [self.submitButton.layer removeAllAnimations];
  self.shapeLayer = nil;
  [UIView animateWithDuration:0.3 animations:^{
    
    self.submitButton.frame = CGRectMake(16, 12, BCWidth - 32, 44);
    self.submitButton.layer.cornerRadius = 4;
    self.submitButton.titleLabel.alpha = 1.0f;
  } completion:^(BOOL finished) {
    self.submitButton.userInteractionEnabled = YES;
    
  }];
}


- (UIView *)returnDone{
  UIView *customView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, BCWidth, 42)];
  customView.backgroundColor = COLOR(247, 247, 248);
  UIButton *btn = [[UIButton alloc] initWithFrame:CGRectMake(BCWidth - 64, 0, 64, 42)];
  [btn setTitle:@"完成" forState:UIControlStateNormal];
  [btn setTitleColor:COLOR(26, 106, 255) forState:UIControlStateNormal];
  btn.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
  [btn addTarget:self action:@selector(btnClicked) forControlEvents:UIControlEventTouchUpInside];
  [customView addSubview:btn];
  UIView *lineV = [[UIView alloc] initWithFrame:CGRectMake(0, 0, BCWidth , 0.5)];
  lineV.backgroundColor = ACOLOR(31, 33, 38, 0.10);
  [customView addSubview:lineV];
  return customView;
}
#pragma mark 点击键盘上方完成按钮
- (void)btnClicked{
  [self.view endEditing:YES];
}

- (void)textViewDidChange:(UITextView *)textView{
  if ([textView.text length] > 200) {
      
      textView.text = [textView.text substringToIndex:200];
  }
  
  self.wordCountLabel.text = [NSString stringWithFormat:@"%lu/%@",(unsigned long)self.memoTV.text.length,@(200)];
}

- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text{
  
  if ([text isEqualToString:@"\n"]){ //判断输入的字是否是回车，即按下return
    
    [self.view endEditing:YES];
    return NO;//这里返回NO，不会出现换行，如果为yes，换行
  }
  
  return YES;
}
- (void)back {
  [self.navigationController popViewControllerAnimated:YES];
}
- (void)dealloc{
  NSLog(@"可视区域释放了");
  
}

- (UIStatusBarStyle)preferredStatusBarStyle{
  if (@available(iOS 13.0, *)) {
    return  UIStatusBarStyleDarkContent;
  } else {
    return UIStatusBarStyleDefault;
  }
}

#pragma mark 懒加载
- (MALoadWaveView *)loadingView{
  if (!_loadingView) {
    _loadingView = [[MALoadWaveView alloc] initWithFrame:[UIScreen mainScreen].bounds];
  }
  return _loadingView;
}
- (UIScrollView *)homeScrollView{
  if (!_homeScrollView) {
    _homeScrollView = [[UIScrollView alloc] initWithFrame: CGRectMake(0, self.heightTop + 44  , BCWidth, BCHeight - (self.heightTop + 44) - 90)];
    _homeScrollView.backgroundColor = COLOR(242, 243, 245);
    _homeScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    _homeScrollView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
    _homeScrollView.showsVerticalScrollIndicator = NO;
    _homeScrollView.showsHorizontalScrollIndicator = NO;
    UITapGestureRecognizer *tableViewTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(Tap)];
    tableViewTap.cancelsTouchesInView = NO;
    [_homeScrollView addGestureRecognizer:tableViewTap];
    _homeScrollView.contentSize = CGSizeMake(BCWidth, BCHeight - (self.heightTop + 44) - 40);
  }
  
  return _homeScrollView;
}
- (void)Tap{
  [self.view endEditing:YES];
}
- (MATopToastView *)statusView{
  if (!_statusView) {
    _statusView = [[MATopToastView alloc] initCustomViewWithDuration:1];
  }
  
  return _statusView;
}

- (MATopNotiflyView *)notiflyView{
  if (!_notiflyView) {
    _notiflyView = [[MATopNotiflyView alloc] initCustomViewWithDuration:1];
  }
  return _notiflyView;
}
@end
