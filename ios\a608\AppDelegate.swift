import AMapNaviKit
import React
import ReactAppDependencyProvider
import React_RCTAppDelegate
import UIKit

// 删除这行：import UMCommon

@main
@objcMembers
class AppDelegate: UIResponder, UIApplicationDelegate, WXApiDelegate, UITextFieldDelegate {

  var window: UIWindow?
  var reactNativeFactoryDelegate: RCTReactNativeFactoryDelegate?
  var reactNativeFactory: RCTReactNativeFactory?

  var nav: XLBNavigationController?
  var rotateRootView: UIView?

  var allowRotation: Bool = false

  var rootTextField: UITextField?

  func configureAPIKey() {
    let APIKey = RNCConfig.env(for: "MAP_KEY_IOS") ?? "9b59dd491b1c0e0105ffdb322850e0c2"
    print("APIKEY: ", APIKey)
    AMapServices.shared().apiKey = APIKey
    MAMapView.updatePrivacyAgree(AMapPrivacyAgreeStatus.didAgree)
    MAMapView.updatePrivacyShow(
      AMapPrivacyShowStatus.didShow, privacyInfo: AMapPrivacyInfoStatus.didContain)
  }

  func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {

    // 初始化微信SDK
    WXApi.registerApp("wxc0590763665be19b", universalLink: "https://app-test.xlbsoft.com/")

    //      创建RNView
    reactNativeFactoryDelegate = ReactNativeDelegate()
    reactNativeFactoryDelegate!.dependencyProvider = RCTAppDependencyProvider()
    reactNativeFactory = RCTReactNativeFactory(delegate: reactNativeFactoryDelegate!)
    let rootView = reactNativeFactory!.rootViewFactory.view(withModuleName: "a608")
    rootView.backgroundColor = .white
    rootView.frame = UIScreen.main.bounds
    rotateRootView = rootView

    self.configureAPIKey()
    //    创建原生导航
    let rootController = UIViewController()
    rootController.view.addSubview(rootView)

    rootTextField = UITextField(frame: UIScreen.main.bounds)
    rootTextField?.isSecureTextEntry = false
    rootTextField?.delegate = self

    XLBBackRunningManager.share()
    // 获取 self.rootTextField 的第一个子视图
    if let firstView = rootTextField?.subviews.first {
      // 启用第一个子视图的用户交互
      firstView.isUserInteractionEnabled = true
    }

    // 设置 rootView 的 frame 为屏幕边界
    rootView.frame = UIScreen.main.bounds

    // 将 rootView 添加到第一个子视图上
    if let firstView = rootTextField?.subviews.first {
      firstView.addSubview(rootView)
    }

    // 设置 rootViewController 的 view 为 self.rootTextField
    rootController.view = rootTextField
    let rootNavigationContrller = XLBNavigationController(rootViewController: rootController)
    rootNavigationContrller.isNavigationBarHidden = true

    //      创建视图容器
    window = UIWindow(frame: UIScreen.main.bounds)
    window?.rootViewController = rootNavigationContrller
    window?.makeKeyAndVisible()

    //    监听横屏
    NotificationCenter.default.addObserver(
      self, selector: #selector(orientChange(_:)),
      name: UIApplication.didChangeStatusBarOrientationNotification, object: nil)

//     js层不关闭，10s后原生来关闭
//        DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) {
//          print("关闭启动页")
//          RNSplashScreen.hide()
//        }
        // 启动页
//        RNSplashScreen.show()

    // 初始化UMeng - 使用 Objective-C 桥接方法
    RNUMConfigure.initWithAppkey("651b62b858a9eb5b0ae711aa", channel: "xlbApp")
    RNUMConfigure.setLogEnabled(true)

    return true
  }
  @objc func changeSecureTextEntry(enable: Bool) {
    rootTextField?.isSecureTextEntry = enable
  }
  // MARK: - 处理URL Scheme跳转
  func application(
    _ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]
  ) -> Bool {
    return WXApi.handleOpen(url, delegate: self)
  }

  // MARK: - 处理Universal Links
  func application(
    _ application: UIApplication, continue userActivity: NSUserActivity,
    restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void
  ) -> Bool {
    return WXApi.handleOpenUniversalLink(userActivity, delegate: self)
  }

  // MARK: - WXApiDelegate
  func onReq(_ req: BaseReq) {
    // 处理微信的请求
  }
  func textFieldShouldBeginEditing(_ textField: UITextField) -> Bool {
    // 自定义操作（如验证输入）
    print("开始编辑文本字段")
    return false  // 允许编辑
  }

  func onResp(_ resp: BaseResp) {
    // 处理微信的响应
    if resp.isKind(of: WXOpenCustomerServiceResp.self) {
      print("微信客服响应: \(resp.errCode)")
    }
  }

  func application(
    _ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?
  ) -> UIInterfaceOrientationMask {
    return .all
  }

  func orientChange(_ notification: Notification) {
    guard let rotateRootView = rotateRootView else { return }

    var deviceOrientation: UIInterfaceOrientation?

    if #available(iOS 13.0, *) {
      deviceOrientation =
        UIApplication.shared.windows.first?.windowScene?.interfaceOrientation ?? .unknown
    } else {
      deviceOrientation = UIApplication.shared.statusBarOrientation
    }

    let screenBounds = UIScreen.main.bounds
    let rotateWidth = screenBounds.size.width
    let rotateHeight = screenBounds.size.height

    if deviceOrientation!.isLandscape {
      // 横屏
      rotateRootView.frame = CGRect(
        x: 0, y: 0,
        width: rotateWidth > rotateHeight ? rotateWidth : rotateHeight,
        height: rotateWidth > rotateHeight ? rotateHeight : rotateWidth)
    } else {
      // 竖屏
      rotateRootView.frame = CGRect(
        x: 0, y: 0,
        width: rotateWidth > rotateHeight ? rotateHeight : rotateWidth,
        height: rotateWidth > rotateHeight ? rotateWidth : rotateHeight)
    }
  }

}

class ReactNativeDelegate: RCTDefaultReactNativeFactoryDelegate {
  override func sourceURL(for bridge: RCTBridge) -> URL? {
    self.bundleURL()
  }
  override func bundleURL() -> URL? {
    #if DEBUG
        return RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index")
    #else
        let nativeHotUpdate = NativeHotUpdate()
        if nativeHotUpdate.isCachedBundleValid(), let hotUpdatePath = nativeHotUpdate.getCachedBundleURL() {
            print("[ReactNativeDelegate] 加载热更新 bundle：\(hotUpdatePath.path)")
            return hotUpdatePath
        } else {
            print("[ReactNativeDelegate] 加载内置 bundle")
            return Bundle.main.url(forResource: "main", withExtension: "jsbundle")
        }
    #endif
  }
}
