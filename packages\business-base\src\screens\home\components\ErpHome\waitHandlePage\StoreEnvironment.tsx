import React, { useEffect, useState } from 'react';
import { c<PERSON>, <PERSON><PERSON>, <PERSON>er<PERSON><PERSON><PERSON>, Item, FormItems, ProFormDate, ProButton, FilterTags, ProText } from '@xlb/common/src/components';
import { ScrollView, Text, View } from 'react-native';
import { useProRoute } from '@xlb/common/src/hooks';
import { FormProvider, useForm } from 'react-hook-form';
import useRefreshStore from "@xlb/common/src/models/refresh";
import { erpHomeApi } from '../api';
import Toast from 'react-native-root-toast';
import FormUpload from '@xlb/business-kms/src/screens/pointApply/components/FormUplod';
import { IconTS } from '@xlb/common/src/assets/iconfont';

// 门店经营环境的待办
export const StoreEnvironment: React.FC = () => {
    const { navigation, route } = useProRoute();
    const [reason, setReason] = useState(null)
    const setRefresh = useRefreshStore((state: any) => state.setRefresh)
    const store_id = route?.params.store_id
    const baseForm = useForm({
        mode: 'onBlur',
    })

    enum fileType {
        门头照 = 'DOOR',
        收银台照 = 'CASHIER',
        室内卖场照 = 'INDOOR'
    }
    const handleSave = async () => {
        const isNoError = await baseForm?.trigger();
        const { file1, file2, file3 } = baseForm.getValues()
        const arr1 = file1.map(v => ({ file_id: v.id || v.file_id, type: fileType['门头照'] }))
        const arr2 = file2.map(v => ({ file_id: v.id || v.file_id, type: fileType['收银台照'] }))
        const arr3 = file3.map(v => ({ file_id: v.id || v.file_id, type: fileType['室内卖场照'] }))

        if (isNoError) {
            const params = {
                store_id,
                type_urls: [...arr1, ...arr2, ...arr3]
            }

            const res: any = await erpHomeApi.updateStoreEnv(params)
            if (res?.code === 0) {
                Toast.show('保存成功')
                setRefresh()
                navigation.goBack()
            }
        } else {
            const errorMsg: any = baseForm.formState.errors
            let arr: any = []
            Object.keys(errorMsg).forEach((key) =>
                arr.push({
                    name: key,
                    value: errorMsg[key],
                })
            )
            Toast.show('请填写必填项')
        }
    }

    const getData = async () => {
        const res: any = await erpHomeApi.readStoreEnv({ id: store_id })
        if (res?.code === 0) {
            setReason(res.data.reason)
            baseForm.setValue('file1', res.data.type_urls.filter(v => v.type === fileType['门头照']))
            baseForm.setValue('file2', res.data.type_urls.filter(v => v.type === fileType['收银台照']))
            baseForm.setValue('file3', res.data.type_urls.filter(v => v.type === fileType['室内卖场照']))
        }
    }

    useEffect(() => {
        getData()
    }, [])

    const uploadFormItems = [
        {
            label: '门头照',
            mode: 'vertical',
            require: true,
            comp: <FormUpload
                name="file1"
                max={10}
                rules={{ validate: (value: []) => { return Boolean(value?.length) || '请上传' } }}
                url="/erp/hxl.erp.file.upload"
                mode={'photo'}
                extraParams={{ refType: fileType['门头照'], refId: store_id }}
            />
        },
        {
            label: '收银台照',
            mode: 'vertical',
            require: true,
            comp: <FormUpload
                name="file2"
                max={10}
                rules={{ validate: (value: []) => { return Boolean(value?.length) || '请上传' } }}
                url="/erp/hxl.erp.file.upload"
                mode={'photo'}
                extraParams={{ refType: fileType['收银台照'], refId: store_id }}
            />
        },
        {
            label: '室内卖场照',
            mode: 'vertical',
            require: true,
            comp: <FormUpload
                name="file3"
                max={10}
                rules={{ validate: (value: []) => { return Boolean(value?.length) || '请上传' } }}
                url="/erp/hxl.erp.file.upload"
                mode={'photo'}
                extraParams={{ refType: fileType['门头照'], refId: store_id }}
            />
        },
    ]




    return (
        <>
            <Header centerComponent={<HeaderTitle title={route?.params.type} />} onBack={() => navigation.goBack()} />
            <ScrollView showsVerticalScrollIndicator={false}>
                <FormProvider {...baseForm}>
                    {reason ? <View style={{ width: '100%', backgroundColor: '#FFECE8', height: 28, flexDirection: "row", alignItems: "center", paddingLeft: 8 }}>
                        <IconTS color={'#F53F3F'} size={12} />
                        <ProText style={{ fontSize: 12, color: '#F53F3F', marginLeft: 4 }}>拒绝原因：{reason}</ProText>
                    </View> : null}
                    <Text style={{ marginLeft: 8, marginTop: 8 }}>*每项最多可上传十张照片</Text>
                    {uploadFormItems.map(v =>
                        <Item showIcon={false} containerStyle={{ paddingVertical: 0 }}>
                            <FormItems data={[v]} />
                        </Item>
                    )}
                </FormProvider>
            </ScrollView>
            <View style={[
                cs.p10,
                cs.bgWhite,
                {
                    borderTopColor: '#E5E6EA',
                    borderTopWidth: 1,
                    paddingTop: 0,
                    paddingBottom: 0,
                },
            ]}>
                <ProButton onPress={handleSave}>提交</ProButton>
            </View>
        </>
    );
}


