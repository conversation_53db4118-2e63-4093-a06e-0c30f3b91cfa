// import { Routes } from './route'
import {icons, images} from './images';
// import { systemEnum, deliveryInStateEnum } from './enum'
// import { pickingStatusDetailEnum, pickingStatusEnum, platoonStatusEnum, verificationStatusEnum } from '@xlb/business-non-wms/src/config/enum'
// import { WMSRoutes } from '@xlb/business-non-wms/src/config/route'
// import { BmsRoutes } from '../../../bussiness-bms/src/config/route'
// import { ErpRoutes } from '@xlb/business-erp/src/config/route'
// import { IMRoutes } from '@xlb/business-im/src/config/route'

// import { storeReplementOrderType } from '@xlb/business-erp/src/config/enum'

// import { ConfirmInOrderRoutes } from '@xlb/business-erp/src/screens/product/confirmInOrder'
// import { GoodsQueryRoutes } from '@xlb/business-non-wms/src/screens/goodsQuery/route'
// import { CarAuthRoute } from '@xlb/business-non-wms/src/screens/carAuth/routes'
// import { InvoiceRoutes } from '@xlb/business-scm/src/screens/invoice'
// import { NewProductApplicationRoutes } from '@xlb/business-scm/src/screens/newProductApplication/routes'
// import { StockQueryRoutes } from '../../../business-scm/src/screens/stockQuery/routes'
// import { SupplyPrepareRoutes } from '../../../business-scm/src/screens/supplyPrepare/routes'
// import { SampleDeliveryRoutes } from '@xlb/business-scm/src/screens/sampleDelivery/routes'
// import { ReturnStorageRoutes } from '@xlb/business-non-wms/src/screens/returnStorage'
// import { BuildTaskRoute } from '../../../business-kms/src/screens/buildTask/routes'
// import { BuildProcessBoard } from '@xlb/business-kms/src/screens/buildProcessBoard/route'
// import { PointApplyRoute } from '../../../business-kms/src/screens/pointApply/route'

// import { RelocationRoute } from '../../../business-kms/src/screens/storeRelocation/route'
// import { StoreForSale } from '../../../business-kms/src/screens/storeForSale/routes'
// import { BusinessDistrictRoute } from '../../../business-kms/src/screens/businessDistrict/route'
// import { PointPlanRoute } from '../../../business-kms/src/screens/pointPlan/route'
// import { StockAdjustOrderRoutes } from '@xlb/business-erp/src/screens/stockAdjust/routes'
// import { NewStockAdjustOrderRoutes } from '@xlb/business-erp/src/screens/newStockAdjust/routes'
// import { PlatoonCarRoute } from '@xlb/business-non-wms/src/screens/platoonCar/routes'
// import { DateCheckRoute } from '@xlb/business-erp/src/screens/dateCheck/routes'
// import { ClientMangeRoute } from '../../../business-kms/src/screens/clientMange/routes'
// import { ClientMangeRoute as ClientMangeNewRoute } from '../../../business-kms/src/screens/clientMangeNew/routes'
// import { UserApplyRoute } from '@xlb/business-erp/src/screens/userApply/routes'
// import { UserApplyRoute as UserManageRoute } from '@xlb/business-erp/src/screens/userManage/routes'
// import { RentPayRoute } from '@xlb/business-kms/src/screens/rentPay/routes'
// import { ProjectAccountPlanRoute } from '@xlb/business-kms/src/screens/projectAccountPlan/routes'
// import { ClueRoute } from '@xlb/business-kms/src/screens/clue/routes'
// import { potentialClientRoute } from '@xlb/business-kms/src/screens/potentialClient/routes'
// import { lookApplicationRoute } from '@xlb/business-kms/src/screens/lookApplication/routes'
// import { lookTaskRoute } from '@xlb/business-kms/src/screens/lookTask/routes'
// import { DeliverRoute } from '@xlb/business-kms/src/screens/buildtaskDeliver/routes'
// import { ProjectAccountRoute } from '@xlb/business-kms/src/screens/projectAccount/routes'
// import { BuildWaitHandle } from '@xlb/business-kms/src/screens/buildWaitHandle/routes'

// import { FlagPlantRoute } from '@xlb/business-kms/src/screens/newFlagPlant/routes'
// import { CompetitorMarkRoute } from '@xlb/business-kms/src/screens/competitorMark/routes'
// import { SupplySamplingRoute } from '@xlb/business-scm/src/screens/supplierSampling/routes'
// import { GoodsCheckReportRoute } from '@xlb/business-scm/src/screens/goodsCheckReport/routes'
// import { ApprovalTakRoute } from '@xlb/business-kms/src/screens/approvalTask/route'
// import { LeaseAgreementRoute } from '@xlb/business-kms/src/screens/leaseAgreement/routes'

// import { landMarkRoute } from '@xlb/business-kms/src/screens/landMark/routes'

// import { MerchantRegisterRoute } from '@xlb/business-bms/src/screens/merchantRegister/routes'
// import { GoodsReportQueryRoute } from '@xlb/business-erp/src/screens/goodsReportQuery/route'
// import { buildFeeRoute } from '@xlb/business-kms/src/screens/buildFee/routes'
// import { CollaborativeWork } from '@xlb/business-kms/src/screens/collaborativeWork/routes'
// import { SettlementEntityReconciliationRoute } from '@xlb/business-bms/src/screens/routes'
// import { cloudMoneyRoute } from '@xlb/business-erp/src/screens/newCloudMoney/router'
// import { FsmsRoute } from '../../../business-fsms/src/router'
// import { AttendanceRoute } from '@xlb/business-hrs/src/screens/attendance/route'

// import { RouteDefine as ErpRouteDefine } from '@xlb/business-erp/src/utils/define'
// import { EmsWebRoute } from '@xlb/business-ems/src/screens/route/emsWebRoute'
// import { DataCenterRoute } from '@xlb/business-kms/src/screens/dataCenter/route'
// import { SCMRoute } from '@xlb/business-scm/src/routes'
// import { InvestmentRoute } from '@xlb/business-kms/src/screens/investment/routes'

import useHasAuth from '@xlb/common/src/hooks/useHasAuth';
import {FsmsRouteKeys} from '../navigation/fsmsRoute';
import {ErpRouteKeys} from '@xlb/common/src/navigation/erpRoute';
import {ScmRouteKeys} from '../navigation/scmRoute';
import {BmsRouteKeys} from '../navigation/bmsRoute';
import {SdsRouteKeys} from '@xlb/common/src/navigation/sdsRoute';
import {baseRoute} from './baseRoute';
// import { PointPollRoute } from '@xlb/business-kms/src/screens/pointPool/route'

// export const entranceSystemFields = [
//   {
//     key: systemEnum.WMS,
//     name: 'WMS',
//     image: 'systemWms',
//     permission: true,
//   },
//   {
//     key: systemEnum.ERP,
//     name: 'ERP',
//     image: 'systemErp',
//     permission: true,
//   },
//   {
//     key: systemEnum.SCM,
//     name: 'SCM',
//     image: 'systemWcs',
//     permission: true,
//   },
//   {
//     key: systemEnum.KMS,
//     name: systemEnum.KMS,
//     image: 'systemKms',
//     permission: true,
//   },
//   // {
//   //   key: systemEnum.TMS,
//   //   name: 'TMS',
//   //   image: 'systemTms',
//   //   permission: false,
//   // },
//   // {
//   //   key: systemEnum.BMS,
//   //   name: 'BMS',
//   //   image: 'systemBms',
//   //   permission: false,
//   // },
//   // {
//   //   key: systemEnum.DMS,
//   //   name: 'DMS',
//   //   image: 'systemDms',
//   //   permission: false,
//   // },
//   // {
//   //   key: systemEnum.XPAY,
//   //   name: 'X-PAY',
//   //   image: 'systemXPay',
//   //   permission: false,
//   // },
//   // {
//   //   key: systemEnum.OMS,
//   //   name: 'OMS',
//   //   image: 'systemOms',
//   //   permission: false,
//   // },
//   {
//     key: systemEnum.CRM,
//     name: 'CRM',
//     image: 'systemOms',
//     permission: false,
//   },
//   {
//     key: systemEnum.SDS,
//     name: 'SDS',
//     image: 'systemOms',
//     permission: false,
//   },
//   {
//     key: systemEnum.EMS,
//     name: 'EMS',
//     image: 'systemOms',
//     permission: false,
//   },
//   {
//     key: systemEnum.HRS,
//     name: 'HRS',
//     image: 'systemOms',
//     permission: false,
//   },
// ]

type AuthLogicNode = {
  op: 'and' | 'or';
  children: (AuthLogicNode | string[])[];
};

export type AppModule = {
  moduleName: string; // 模块名称
  name: string; // 路由中文名称
  image?: string; // 图片路由地址
  route: string; // 路由英文名称
  isHasAuth?: string[] | number | boolean | AuthLogicNode; // 权限数组
  createTime?: string; // 创建时间
  permission?: boolean; // 应删除的字段
  appType?: string;
  hiddenMenu?: boolean; // 是否显示在工作台
  RemoteAppKey?: keyof AppFieldsProps; // 具体实现在那个子包
  hiddenStack?: boolean; // 是否注册路由false注册true不注册
};
export type AppFieldsProps = {
  BPM: AppModule[];
  // WMS: AppModule[];
  ERP: AppModule[];
  FSS: AppModule[];
  BMS: AppModule[];
  SCM: AppModule[];
  KMS: AppModule[];
  XPAY: AppModule[];
  TMS: AppModule[];
  FSMS: AppModule[];
  CRM: AppModule[];
  SDS: AppModule[];
  EMS: AppModule[];
  HRS: AppModule[];
  MEM: AppModule[];
  SMS: AppModule[];
  IM: AppModule[];
  BASE: AppModule[];
};
const _applicationFields: AppFieldsProps = {
  BPM: [
    {
      moduleName: '企业办公',
      name: '审批',
      route: 'OA_H5_PAGE',
      image: images.approveManage,
      isHasAuth: ['审批', '编辑'],
    },
  ],
  ERP: [
    // {
    //   moduleName: '档案',
    //   name: 'IM',
    //   route: IMRoutes.ChatHome,
    //   image: images.userLogo,
    //   isHasAuth: ['APP用户管理', '查询'],
    // },
    {
      moduleName: '档案',
      name: '供应商管理',
      route: 'SupplierManageIndex',
      image: images.suppplierManage,
      isHasAuth: ['供应商管理', '查询'],
      // isHasAuth: false,
    },
    {
      moduleName: '档案',
      name: '供应商管理详情',
      route: 'SupplierManageDetail',
      image: images.suppplierManage,
      isHasAuth: ['供应商管理', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '数图空间',
      route: 'ShuTool',
      image: images.shuTool,
      isHasAuth: ['数图', '查询'],
      // isHasAuth: false,
    },
    {
      moduleName: '档案',
      name: '用户申请',
      route: 'userApply',
      image: images.userApplication,
      isHasAuth: ['用户申请', '查询'],
      // isHashAuth: false,
    },
    {
      moduleName: '档案',
      name: '用户申请详情',
      route: 'userApplyDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '用户申请修改',
      route: 'userApplyEdit',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '用户管理',
      route: 'userManage',
      image: images.userApplication,
      isHasAuth: ['APP用户管理-ERP', '查询'],
      // isHashAuth: false,
    },
    {
      moduleName: '档案',
      name: '用户管理详情',
      route: 'userManageDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '门店管理',
      route: 'StoreManagement',
      image: images.storeManagement,
      isHasAuth: ['门店管理', '查询'],
    },
    {
      moduleName: '档案',
      name: '门店管理详情',
      route: 'StoreManageDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '门店更改历史',
      route: 'StoreManagementHistory',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '商品资质查询',
      route: 'GoodsReportQueryList',
      image: images.goodsReportQuery,
      isHasAuth: ['商品资质查询', '查询'],
    },
    {
      moduleName: '档案',
      name: '证件管理',
      route: 'StoreInformation',
      image: images.storeInformation,
      isHasAuth: ['证件管理', '查询'],
    },
    {
      moduleName: '档案',
      name: '组织商品查询',
      route: 'ItemQuery',
      image: images.goodsQuery,
      isHasAuth: 966,
    },
    {
      moduleName: '档案',
      name: '组织商品查询详情',
      route: 'ItemDetailQuery',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '合同管理',
      route: 'ContractManagement',
      image: images.storeInformation,
      isHasAuth: ['合同管理', '查询'],
    },
    {
      moduleName: '档案',
      name: '合同管理详情',
      route: 'ContractManagementDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '合同管理PDF预览',
      route: 'ContractManagementPdf',
      hiddenMenu: true,
    },

    {
      moduleName: '档案',
      name: '商品档案',
      route: 'ProductFileInquiry',
      image: images.goodsQuery,
      isHasAuth: ['商品档案', '查询'],
    },
    {
      moduleName: '档案',
      name: '商品档案详情',
      route: 'ProductFileInquiryDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '采购管理',
      name: '采购订单',
      route: 'PurchaseOrder',
      image: images.appPurchaseOrder,
      isHasAuth: ['采购订单', '查询'],
    },
    {
      moduleName: '采购管理',
      name: '采购订单详情',
      route: 'PurchaseOrderDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '采购管理',
      name: '采购订单新增',
      route: 'PurchaseOrderAdd',
    },
    {
      moduleName: '库存管理',
      name: '库存调整',
      route: 'StockAdjustOrder',
      image: images.stockAdjust,
      isHasAuth: ['库存调整', '查询'],
      // isHasAuth: false,
    },
    {
      moduleName: '库存管理',
      name: '库存调整详情',
      route: 'StockAdjustOrderDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '库存管理',
      name: '库存调整商品详情',
      route: 'StockAdjustOrderItemDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '库存管理',
      name: '日期检查',
      route: 'DateCheckH5',
      image: images.DateCheck,
      isHasAuth: ['门店补货单', '查询'],
    },
    // {
    //   moduleName: '库存管理',
    //   name: '新库存调整',
    //   route: NewStockAdjustOrderRoutes.StockAdjustOrder,
    //   image: images.stockAdjust,
    //   isHasAuth: ['库存调整', '查询'],
    //   // isHasAuth: false,
    // },
    {
      moduleName: '采购管理',
      name: '采购收货单',
      route: 'PurchaseReceive',
      image: images.appPurchaseReceipt,
      isHasAuth: ['采购收货单', '查询'],
    },
    {
      moduleName: '采购管理',
      name: '采购收货单详情',
      route: 'PurchaseReceiveDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '采购管理',
      name: '采购收货单商品详情',
      route: 'PurchaseReceiveItemDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '采购管理',
      name: '采购退货单',
      route: 'PurchaseReturn',
      image: images.appPurchaseReturn,
      isHasAuth: ['采购退货单', '查询'],
    },
    {
      moduleName: '采购管理',
      name: '采购退货单详情',
      route: 'PurchaseReturnDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '采购管理',
      name: '采购退货单商品详情',
      route: 'PurchaseReturnItemDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '采购管理',
      name: '采价计划',
      route: 'PurchasePlan',
      image: images.purchasePlan,
      isHasAuth: ['采价计划', '查询'],
    },
    {
      moduleName: '采购管理',
      name: '采价计划新增',
      route: 'PurchasePlanAdd',
      image: images.purchasePlan,
      isHasAuth: ['采价计划', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '采购管理',
      name: '采价计划详情',
      route: 'PurchasePlanDetail',
      image: images.purchasePlan,
      isHasAuth: ['采价计划', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '库存管理',
      name: '库存查询',
      route: 'InventoryInquire',
      image: images.appInventoryCheck,
      isHasAuth: ['库存查询', '查询'],
      appType: 'ERP',
    },
    {
      moduleName: '库存管理',
      name: '库存盘点',
      route: 'StockCheckOrder',
      image: images.appInventoryCount,
      isHasAuth: ['库存盘点', '查询'],
    },
    {
      moduleName: '库存管理',
      name: '库存盘点',
      route: 'StockCheckOrderDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '库存管理',
      name: '库存盘点商品详情',
      route: 'StockCheckOrderItemDetail',
      hiddenMenu: true,
    },

    // {
    //   moduleName: '库存管理',
    //   name: '日期检查',
    //   route: DateCheckRoute.DateCheck,
    //   image: images.DateCheck,
    //   isHasAuth: ['日期检查', '查询'],
    // },
    {
      moduleName: '库存管理',
      name: '日期检查',
      route: '',
      image: images.DateCheck,
      isHasAuth: ['日期检查', '查询'],
    },
    {
      moduleName: '商品管理',
      name: '门店零售价',
      route: 'StoreRetailPriceSaleIndex',
      image: images.storeRetailSale,
      isHasAuth: ['门店零售价', '查询'],
      RemoteAppKey: 'MEM',
    },
    {
      moduleName: '商品管理',
      name: '门店零售价详情',
      route: 'StoreRetailPriceSaleDetail',
      image: images.storeRetailSale,
      isHasAuth: ['门店零售价', '查询'],
      RemoteAppKey: 'MEM',
      hiddenMenu: true,
    },
    // {
    //   moduleName: '商品管理',
    //   name: '零售调价',
    //   route: '',
    //   image: images.appRetailPriceAdjust,
    //   isHasAuth: ['零售调价', '查询'],
    // },
    {
      moduleName: '商品管理',
      name: '零售调价',
      route: 'RetailPriceAdjustIndex',
      image: images.appRetailPriceAdjust,
      isHasAuth: ['零售调价', '查询'],
      RemoteAppKey: 'MEM',
    },
    {
      moduleName: '商品管理',
      name: '零售调价新增',
      route: 'RetailPriceAdjustAdd',
      image: images.appRetailPriceAdjust,
      isHasAuth: ['零售调价', '查询'],
      RemoteAppKey: 'MEM',
      hiddenMenu: true,
    },
    {
      moduleName: '商品管理',
      name: '零售调价详情',
      route: 'RetailPriceAdjustDetail',
      image: images.appRetailPriceAdjust,
      isHasAuth: ['零售调价', '查询'],
      RemoteAppKey: 'MEM',
      hiddenMenu: true,
    },
    {
      moduleName: '商品管理',
      name: '零售调价详情',
      route: 'RetailPriceAdjustItemConfig',
      image: images.appRetailPriceAdjust,
      isHasAuth: ['零售调价', '查询'],
      RemoteAppKey: 'MEM',
      hiddenMenu: true,
    },
    {
      moduleName: '商品管理',
      name: '门店调价申请',
      route: 'StorePriceAdjustmentIndex',
      image: images.discountCode,
      isHasAuth: ['门店调价申请', '查询'],
      RemoteAppKey: 'MEM',
    },
    {
      moduleName: '商品管理',
      name: '门店调价新增',
      route: 'StorePriceAdjustmentAdd',
      image: images.discountCode,
      isHasAuth: ['门店调价申请', '查询'],
      RemoteAppKey: 'MEM',
      hiddenMenu: true,
    },
    {
      moduleName: '商品管理',
      name: '门店调价详情',
      route: 'StorePriceAdjustmentDetail',
      image: images.discountCode,
      isHasAuth: ['门店调价申请', '查询'],
      RemoteAppKey: 'MEM',
      hiddenMenu: true,
    },
    {
      moduleName: '商品管理',
      name: '门店调价详情',
      route: 'StorePriceAdjustmentItemConfig',
      image: images.discountCode,
      isHasAuth: ['门店调价申请', '查询'],
      RemoteAppKey: 'MEM',
      hiddenMenu: true,
    },
    {
      moduleName: '商品管理',
      name: '门店调价详情',
      route: 'StorePriceAdjustmentRecord',
      image: images.discountCode,
      isHasAuth: ['门店调价申请', '查询'],
      RemoteAppKey: 'MEM',
      hiddenMenu: true,
    },
    {
      moduleName: '商品管理',
      name: '折扣码管理',
      route: 'DiscountCodeIndex',
      image: images.discountCode,
      RemoteAppKey: 'MEM',
      isHasAuth: ['零售调价', '查询'],
    },
    {
      moduleName: '商品管理',
      name: '折扣码编辑',
      route: 'DiscountCodeEditor',
      RemoteAppKey: 'MEM',
      hiddenMenu: true,
    },
    {
      moduleName: '零售管理',
      name: '价签申请',
      route: 'PriceTagApplyIndex',
      RemoteAppKey: 'MEM',
      image: images.priceTagApply,
      isHasAuth: ['价签申请', '查询'],
    },
    {
      moduleName: '零售管理',
      name: '价签申请详情',
      route: 'PriceTagApplyDetail',
      RemoteAppKey: 'MEM',
      hiddenMenu: true,
      image: images.priceTagApply,
      isHasAuth: ['价签申请', '查询'],
    },
    // {
    //   moduleName: '商品管理',
    //   name: '成分商品调价',
    //   route: '',
    //   image: images.appIngredientProductAdjust,
    //   isHasAuth: ['成分商品调价', '查询'],
    // },
    {
      moduleName: '配送管理',
      name: '门店补货',
      route: 'StoreReplenishment',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店补货单', '查询'],
    },
    {
      moduleName: '配送管理',
      name: '门店补货详情',
      route: 'StoreReplenishmentDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '门店补货新增',
      route: 'StoreReplenishmentAdd',
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '门店补货审核详情',
      route: 'StoreReplenishAuditDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '门店补货配送信息',
      route: 'StoreOrderLogisticsDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '门店补货订单统计',
      route: 'StoreOrderDeliveryStatisticsDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '门店补货多单合并',
      route: 'MultipleSelect',
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '调入单',
      route: 'DeliveryInOrder',
      image: images.appCallIn,
      isHasAuth: ['调入单', '查询'],
    },
    {
      moduleName: '配送管理',
      name: '调入单详情页',
      route: 'DeliveryInOrderDetail',
    },
    // {
    //   moduleName: '配送管理',
    //   name: '点位申请',
    //   route: PointApplyRoute.POINT_APPLY,
    //   image: images.pointApply,
    //   isHasAuth: ['调出单', '查询'],
    // },
    // {
    //   moduleName: '配送管理',
    //   name: '调入确认单',
    //   route: ConfirmInOrderRoutes.confirmInOrder,
    //   image: images.app_ware_confirmation,
    //   isHasAuth: ['调入确认单', '查询'],
    // },
    {
      moduleName: '配送管理',
      name: '调入确认单',
      route: 'ConfirmInOrder',
      image: images.app_ware_confirmation,
      isHasAuth: ['调入确认单', '查询'],
    },
    {
      moduleName: '配送管理',
      name: '调入确认单详情',
      route: 'ConfirmInOrderDetail',
      image: images.app_ware_confirmation,
      isHasAuth: ['调入确认单', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '调入确认单商品详情',
      route: 'ConfirmInOrderItemDetail',
      image: images.app_ware_confirmation,
      isHasAuth: ['调入确认单', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '调入确认单商品列表',
      route: 'ConfirmInOrderItemsList',
      image: images.app_ware_confirmation,
      isHasAuth: ['调入确认单', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '调入确认单评价',
      route: 'EvaluatePage',
      image: images.app_ware_confirmation,
      isHasAuth: ['调入确认单', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '调入确认单评价详情',
      route: 'EvaluateDetail',
      image: images.app_ware_confirmation,
      isHasAuth: ['调入确认单', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '调入确认单电子签',
      route: 'ConfirmInOrderWebView',
      image: images.app_ware_confirmation,
      isHasAuth: ['调入确认单', '查询'],
      hiddenMenu: true,
    },

    {
      moduleName: '配送管理',
      name: '调入确认单-新',
      route: 'ConfirmInOrderNew',
      image: images.app_ware_confirmation,
      isHasAuth: ['调入确认单', '查询'],
    },
    {
      moduleName: '配送管理',
      name: '调入确认单-新详情',
      route: 'ConfirmInOrderDetailNew',
      image: images.app_ware_confirmation,
      isHasAuth: ['调入确认单', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '调入确认单-新商品详情',
      route: 'ConfirmInOrderItemDetailNew',
      image: images.app_ware_confirmation,
      isHasAuth: ['调入确认单', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '调入确认单-新商品列表',
      route: 'ConfirmInOrderItemsListNew',
      image: images.app_ware_confirmation,
      isHasAuth: ['调入确认单', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '调入确认单-电子签列表',
      route: 'EvaluatePageNew',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店订单', '查询'],
      hiddenMenu: true,
      appType: 'ERP',
    },
    {
      moduleName: '配送管理',
      name: '调入确认单-电子签详情',
      route: 'EvaluateDetailNew',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店订单', '查询'],
      hiddenMenu: true,
      appType: 'ERP',
    },
    {
      moduleName: '配送管理',
      name: '调入确认单-电子签页面',
      route: 'ConfirmInOrderWebViewNew',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店订单', '查询'],
      hiddenMenu: true,
      appType: 'ERP',
    },

    // {
    //   moduleName: '配送管理',
    //   name: '调入确认单-新',
    //   route: ErpRouteDefine.confirmInOrder_new.index,
    //   image: images.app_ware_confirmation,
    //   isHasAuth: ['调入确认单', '查询'],
    // },
    {
      moduleName: '配送管理',
      name: '调出单',
      route: 'DeliveryOrder',
      image: images.appCallOut,
      isHasAuth: ['调出单', '查询'],
      // isHasAuth: false,
    },
    {
      moduleName: '配送管理',
      name: '调出单新增',
      route: 'DeliveryOrderAdd',
      image: images.appCallOut,
      isHasAuth: ['调出单', '查询'],
      hiddenMenu: true,
      // isHasAuth: false,
    },
    {
      moduleName: '配送管理',
      name: '调出单详情',
      route: 'DeliveryOrderDetail',
      image: images.appCallOut,
      isHasAuth: ['调出单', '查询'],
      hiddenMenu: true,
      // isHasAuth: false,
    },
    // {
    //   moduleName: '配送管理',
    //   name: '点位申请',
    //   route: PointApplyRoute.POINT_APPLY,
    //   image: images.pointApply,
    //   isHasAuth: ['调出单', '查询'],
    // },
    {
      moduleName: '配送管理',
      name: '门店申请单',
      route: 'StoreApplicationOrder',
      image: images.appDistributionInspection,
      isHasAuth: ['门店申请单', '查询'],
    },
    {
      moduleName: '配送管理',
      name: '门店申请单详情',
      route: 'StoreApplicationOrderDetail',
      image: images.appDistributionInspection,
      isHasAuth: ['门店申请单', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '新增门店申请单',
      route: 'StoreApplicationOrderAdd',
      image: images.appDistributionInspection,
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '门店申请单选择调入单',
      route: 'StoreApplicationOrderChoseDeliveryInOrder',
      image: images.appDistributionInspection,
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '门店申请单商品详情',
      route: 'StoreApplicationOrderEditItem',
      image: images.appDistributionInspection,
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '选择仓库',
      route: 'SelectApplicationHouse',
      image: images.appDistributionInspection,
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '门店预订单',
      route: 'StoreReserverOrderIndex',
      image: images.appWholesaleOrder,
      isHasAuth: ['门店预订单', '查询'],
    },
    {
      moduleName: '配送管理',
      name: '门店预订单详情',
      route: 'StoreReserverOrderDetail',
      hiddenMenu: true,
    },

    {
      moduleName: '配送管理',
      name: '物资进出单',
      route: 'TransferBasketInOut',
      image: images.appTransferBasketInOut,
      isHasAuth: ['物资进出单', '查询'],
    },
    {
      moduleName: '配送管理',
      name: '物资进出单新增',
      route: 'TransferBasketAdd',
      image: images.appTransferBasketInOut,
      isHasAuth: ['物资进出单', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '物资进出单详情',
      route: 'TransferBasketDetail',
      image: images.appTransferBasketInOut,
      isHasAuth: ['物资进出单', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '物资进出单',
      route: 'TransferBasketName',
      image: images.appTransferBasketInOut,
      isHasAuth: ['物资进出单', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '物资进出单-选择门店',
      route: 'TransSelectStore',
      image: images.appTransferBasketInOut,
      isHasAuth: ['物资进出单', '查询'],
      hiddenMenu: true,
    },

    {
      moduleName: '配送管理',
      name: '物资统计',
      route: 'MaterialStatistics',
      image: images.appTransferBasketInOut,
      isHasAuth: ['物资统计', '查询'],
    },
    {
      moduleName: '配送管理',
      name: '物资统计详情',
      route: 'MaterialStatisticsDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '批发订单',
      route: 'wholeSaleOrder',
      image: images.appNewWholesaleOrder,
      isHasAuth: ['批发订单', '查询'],
    },
    {
      moduleName: '配送管理',
      name: '批发订单新增',
      route: 'wholeSaleOrderAdd',
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '批发订单详情',
      route: 'wholeSaleOrderDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '配送管理',
      name: '经营货盘分析',
      route: '',
      image: images.appTransferPalletAnalysis,
      isHasAuth: ['经营货盘分析', '查询'],
    },
    {
      moduleName: '配送管理',
      name: '门店订单',
      route: 'StoreOrder',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店订单', '查询'],
      appType: 'ERP',
    },
    {
      moduleName: '配送管理',
      name: '门店订单新增',
      route: 'StoreOrderAdd',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店订单', '查询'],
      hiddenMenu: true,
      appType: 'ERP',
    },
    {
      moduleName: '配送管理',
      name: '门店订单-物流信息',
      route: 'StoreOrderDistributionMsg',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店订单', '查询'],
      hiddenMenu: true,
      appType: 'ERP',
    },
    {
      moduleName: '配送管理',
      name: '门店订单详情',
      route: 'StoreOrderDetail',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店订单', '查询'],
      hiddenMenu: true,
      appType: 'ERP',
    },
    {
      moduleName: '配送管理',
      name: '门店订单详情-编辑',
      route: 'StoreOrderAuditDetail',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店订单', '查询'],
      hiddenMenu: true,
      appType: 'ERP',
    },
    {
      moduleName: '配送管理',
      name: '门店订单详情-多单合并',
      route: 'StoreOrderMerge',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店订单', '查询'],
      hiddenMenu: true,
      appType: 'ERP',
    },
    {
      moduleName: '配送管理',
      name: '门店订单详情-商品详情',
      route: 'StoreOrderGoodsDetail',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店订单', '查询'],
      hiddenMenu: true,
      appType: 'ERP',
    },
    {
      moduleName: '配送管理',
      name: '门店订单详情-发货明细',
      route: 'DistributionGoods',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店订单', '查询'],
      hiddenMenu: true,
      appType: 'ERP',
    },
    {
      moduleName: '配送管理',
      name: '经营货盘分析',
      route: 'PalletAnalysis',
      image: images.appTransferPalletAnalysis,
      isHasAuth: ['经营货盘分析', '查询'],
    },
    {
      moduleName: '数据',
      name: '销售分析',
      route: 'RemoteAppBi.SaleAnalysisH5',
      image: images.storeAnalysis,
      isHasAuth: ['营业收款报表', '查询'],
      hiddenMenu: true,
    },
    // {
    //   moduleName: '数据',
    //   name: '区域销售分析',
    //   route: 'AreaSaleThinkIndex',
    //   image: images.areaSaleThink,
    //   isHasAuth: ['区域销售分析', '查询'],
    // },

    //     import ShortageWarning from '../pages/shortageWarningH5';
    // import ArrivalReminder from '../pages/arrivalReminderH5';
    // import GoodsStockoutRate from '../pages/goodsStockoutRateH5';
    {
      moduleName: '数据',
      name: '缺货预警',
      route: 'RemoteAppErp.ShortageWarning',
      image: images.salesAnalysis,
      isHasAuth: ['缺货预警', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '订单准点率',
      route: 'punctualityRateH5',
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '订单完成率',
      route: 'completionRateH5',
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '到货提醒',
      route: 'RemoteAppErp.ArrivalReminder',
      image: images.salesAnalysis,
      isHasAuth: ['到货提醒', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '商品缺货率',
      route: 'RemoteAppErp.GoodsStockoutRate',
      isHasAuth: ['商品销售分析', '查询'],
      hiddenMenu: true,
    },

    {
      moduleName: '数据',
      name: '商品销售分析',
      route: 'RemoteAppBi.ProductSalesAnalysis',
      image: images.salesAnalysis,
      isHasAuth: ['商品销售分析', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '配送分析',
      route: 'RemoteAppBi.DeliveryMargins',
      image: images.deliveryMargins,
      isHasAuth: ['配送分析', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '营业收款分析',
      route: 'RemoteAppBi.CollectionAnalysis',
      image: images.appCollectionAnlysis,
      isHasAuth: ['营业收款报表', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '营业收款分析明细',
      route: 'RemoteAppBi.CollectionAnalysisDetail',
      image: images.appCollectionAnlysis,
      isHasAuth: ['营业收款报表', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '门店销售统计',
      route: 'RemoteAppBi.StoreSaleAnalysis',
      image: images.storeSaleRank,
      isHasAuth: ['门店销售分级统计', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '备货跟踪',
      route: 'RemoteAppBi.StockingTracking',
      image: icons.stocking_tracking,
      isHasAuth: useHasAuth(['备货跟踪', '查询']),
      hiddenMenu: true,
    },

    // {
    //   moduleName: '数据',
    //   name: '门店贷款明细',
    //   route: 'storeLoansPage',
    //   image: images.storeLoans,
    //   isHasAuth: ['门店货款明细', '查询']
    // },
    // {
    //   moduleName: '数据',
    //   name: '已结账单查询',
    //   route: 'RemoteAppBi.PostOrder',
    //   createTime: '2023-08-15',
    //   isHasAuth: ['已结账单查询', '查询'],
    // },
    {
      moduleName: '数据',
      name: 'BOSS报表',
      route: 'RemoteAppBi.BossTable',
      image: images.erpStoreBoss,
      isHasAuth: ['BOSS报表', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '单品业绩',
      route: 'RemoteAppBi.ProductPerformance',
      image: images.singleProduct,
      isHasAuth: useHasAuth(['单品业绩', '查询']),
      hiddenMenu: true,
    },
    // {
    //   moduleName: '数据',
    //   name: '门店销售分析',
    //   route: 'storeSaleAnalysis',
    //   image: images.deliveryMargins,
    //   isHasAuth: ['门店销售分析', '查询'],
    // },

    {
      moduleName: '数据',
      name: '异常收银分析',
      route: 'RemoteAppBi.AbnormalCashAnalysis',
      image: images.AbnormalCashAnalysis,
      isHasAuth: ['异常收银分析', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '异常收银分析详情',
      route: 'RemoteAppBi.AbnormalCashAnalysisDetail',
      image: images.AbnormalCashAnalysis,
      isHasAuth: ['异常收银分析', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '店横对比',
      route: 'RemoteAppBi.StoreCompared',
      image: images.StoreCompared,
      isHasAuth: ['店横对比', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '商品复购分析',
      route: 'RemoteAppBi.GoodsRepurchase',
      image: images.GoodsRepurchase,
      isHasAuth: ['商品复购分析', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '商品分层分析',
      route: 'RemoteAppBi.Stratificationanalysis',
      image: images.Stratificationanalysis,
      isHasAuth: ['商品分层分析', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '已结账单查询',
      route: 'RemoteAppBi.PostOrder',
      image: images.postOrder,
      isHasAuth: ['已结账单查询', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '异常单品',
      route: 'RemoteAppBi.AbnormalItem',
      image: images.memberAnalysis,
      isHasAuth: ['异常单品', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '会员分析',
      route: '',
      image: images.memberAnalysis,
      isHasAuth: ['会员分析', '查询'],
    },
    {
      moduleName: '数据',
      name: '时段销售分析',
      route: 'RemoteAppBi.TimePeriodAnalysis',
      image: images.timePeriodAnalysis,
      isHasAuth: ['时段销售分析', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '短保商品报表',
      route: 'RemoteAppBi.ShortInsuranceGoodsAnalysis',
      image: images.shortInsuranceAnalysis,
      isHasAuth: useHasAuth(['短保商品报表', '查询']),
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '日商分析',
      route: 'RemoteAppBi.DayBusinessAnalysis',
      image: images.memberAnalysis,
      isHasAuth: ['日商分析', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '门店经营分析',
      route: 'RemoteAppBi.storeBusinessAnalysis',
      image: images.storeBusinessAnalysis,
      isHasAuth: useHasAuth(['门店经营状况分析', '查询']),
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '门店经营分析详情',
      route: 'RemoteAppBi.storeBusinessAnalysisDetail',
      isHasAuth: useHasAuth(['门店经营状况分析', '查询']),
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '门店综合分析',
      route: 'RemoteAppBi.ShopRunAnalysis',
      image: images.storeBusinessAnalysis,
      isHasAuth: ['门店经营状况分析', '查询'],
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '区域销售分析',
      route: 'RemoteAppBi.AreaSaleThinkIndex',
      image: images.areaSaleThink,
      isHasAuth: useHasAuth(['区域销售分析', '查询']),
      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '支付分析',
      route: 'RemoteAppBi.PaymentAnalysis',
      image: images.appCollectionAnlysis,
      isHasAuth: useHasAuth(['营业收款报表', '查询']),
      hiddenMenu: true,
    },
    // TODO 消费卷返款权限没设置
    {
      moduleName: '零售管理',
      name: '消费券返款',
      route: 'RemoteAppBi.consumerRollRebateH5',
      image: images.ConsumerRollRebate,
      isHasAuth: ['APP消费券返款', '查询'],
    },
    {
      moduleName: '零售管理',
      name: '卡返款',
      route: '',
      image: images.prepaidCardRefund,
      isHasAuth: ['卡返款', '查询'],
    },
    {
      moduleName: '零售管理',
      name: '在线订单',
      route: 'PointsManagementIndex',
      RemoteAppKey: 'MEM',
      image: images.pointsChangeManage,
      isHasAuth: ['在线订单', '查询'],
      appType: 'ERP',
    },
    {
      moduleName: '零售管理',
      name: '在线订单详情',
      route: 'PointsManagementDetail',
      RemoteAppKey: 'MEM',
      image: images.pointsChangeManage,
      isHasAuth: ['在线订单', '查询'],
      appType: 'ERP',
      hiddenMenu: true,
    },
    {
      moduleName: '零售管理',
      name: '在线订单列表',
      route: 'PointsManagementGoodList',
      RemoteAppKey: 'MEM',
      image: images.pointsChangeManage,
      isHasAuth: ['在线订单', '查询'],
      appType: 'ERP',
      hiddenMenu: true,
    },
    {
      moduleName: '零售管理',
      name: '在线订单详情',
      route: 'PointsManagementItemDetail',
      RemoteAppKey: 'MEM',
      image: images.pointsChangeManage,
      isHasAuth: ['在线订单', '查询'],
      appType: 'ERP',
      hiddenMenu: true,
    },
    {
      moduleName: '零售管理',
      name: '营业返款',
      route: '',
      image: images.ScmBusinessRebates,
      isHasAuth: ['营业返款', '查询'],
    },
    {
      moduleName: '经营',
      name: '门店经营分析',
      route: 'storeBusinessAnalysis',
      image: images.storeBusinessAnalysis,
      isHasAuth: ['门店经营状况分析', '查询'],
    },
    {
      moduleName: '经营',
      name: '门店经营分析详情',
      route: 'storeBusinessAnalysisDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '经营',
      name: '门店费用',
      route: 'storeFee',
      image: images.operatingIncomeIcon,
      isHasAuth: ['门店费用', '查询'],
    },
    {
      moduleName: '经营',
      name: '营业外收入',
      route: 'operatingIncome',
      image: images.StoreFeeIcon,
      isHasAuth: ['营业外收入', '查询'],
    },
    {
      moduleName: '经营',
      name: '营业外收入详情',
      route: 'operatingIncomeDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '支付宝',
      name: '日销数据分析',
      route: 'daySalesAnalysis',
      image: images.daySalesAnalysis,
      isHasAuth: ['日销数据分析', '查询'],
    },
    {
      moduleName: '效率工具',
      name: '效期检查',
      route: 'ExpirationDateCheck',
      image: images.expirationDateCheck,
      isHasAuth: ['效期检查', '查询'],
    },
    {
      moduleName: '效率工具',
      name: '效期打印',
      route: 'ExpirationDatePrinting',
      image: images.ExpirationDatePrinting,
      isHasAuth: ['效期打印', '查询'],
    },
  ],
  FSS: [
    {
      moduleName: '门店结算',
      name: '门店费用',
      route: 'storeFee',
      RemoteAppKey: 'BMS',
      image: images.operatingIncomeIcon,
      isHasAuth: ['门店费用', '查询'],
    },
    {
      moduleName: '门店结算',
      name: '详情',
      RemoteAppKey: 'BMS',
      route: 'storeFeeDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '门店返款',
      name: '消费券返款',
      route: 'consumerRollRebate',
      RemoteAppKey: 'BMS',
      image: images.ConsumerRollRebate,
      isHasAuth: ['APP消费券返款', '查询'],
    },
    {
      moduleName: '门店返款',
      name: '卡返款',
      route: 'prepaidCardRefund',
      RemoteAppKey: 'BMS',
      image: images.prepaidCardRefund,
      isHasAuth: ['卡返款查询', '查询'],
    },
    {
      moduleName: '门店返款',
      name: '营业返款',
      route: 'businessRebates',
      RemoteAppKey: 'BMS',
      image: images.ScmBusinessRebates,
      isHasAuth: ['营业返款', '查询'],
      appType: 'FSS',
    },
  ],
  BMS: [
    {
      moduleName: '档案',
      name: '商户进件',
      route: BmsRouteKeys.merchantRegister,
      image: images.merchantRegister,
      isHasAuth: ['商户进件', '查询'],
    },
    {
      moduleName: '档案',
      name: '账户信息',
      route: BmsRouteKeys.accountInfo,
      image: images.moneyCount,
      isHasAuth: {
        op: 'and',
        children: [
          ['账户信息', '查询'],
          {
            op: 'or',
            children: [
              ['账户信息/转账指引', '查询'],
              ['账户信息/组织范围', '查询'],
            ],
          },
        ],
      },
    },
    {
      moduleName: '档案',
      name: '收单认证',
      route: BmsRouteKeys.acquiringAuthentication,
      image: images.acquiringAuthentication,
      isHasAuth: ['收单认证', '查询'],
    },
    {
      moduleName: '收单',
      name: '商品收单明细',
      route: BmsRouteKeys.onlineTransaction,
      image: images.appOnlineTransaction,
      isHasAuth: ['商品收单明细', '查询'],
    },
    {
      moduleName: '收单',
      name: '商品收单汇总',
      route: BmsRouteKeys.transactionSummary,
      image: images.transactionSummary,
      isHasAuth: ['商品收单汇总', '查询'],
    },
    {
      moduleName: '收单',
      name: '卡收单明细',
      route: BmsRouteKeys.onlineTransactionCard,
      image: images.appCardOnlineTransaction,
      isHasAuth: ['卡收单明细', '查询'],
    },
    {
      moduleName: '收单',
      name: '卡收单汇总',
      route: BmsRouteKeys.transactionSummaryCard,
      image: images.appCardTransactionSummary,
      isHasAuth: ['卡收单汇总', '查询'],
    },
    {
      moduleName: '账户往来',
      name: '记账户',
      route: BmsRouteKeys.accounting,
      image: images.moneyCount,
      isHasAuth: ['记账户', '查询'],
    },
    {
      moduleName: '账户往来',
      name: '账户明细',
      route: BmsRouteKeys.transactionDetails,
      image: images.summaryTable,
      isHasAuth: {
        op: 'and',
        children: [
          ['账户明细', '查询'],
          {
            op: 'or',
            children: [
              ['账户明细/交易明细', '查询'],
              ['账户明细/余额明细', '查询'],
            ],
          },
        ],
      },
    },
    {
      moduleName: '账户往来',
      name: '月度对账单',
      route: BmsRouteKeys.monthlyReconciliation,
      image: images.monthlyReconciliation,
      isHasAuth: ['月度对账单/APP查询', '查询'],
    },
    {
      moduleName: '账户往来',
      name: '充值',
      route: 'moneyIn',
      hiddenMenu: true,
    },
    {
      moduleName: '账户往来',
      name: '提现',
      route: 'payOut',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '商户进件修改',
      route: 'checkInfo',
      hiddenMenu: true,
    },
    {
      moduleName: '收单',
      name: '订单表格',
      route: 'orderDetailTable',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '订单详细',
      route: 'orderDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '商户明细',
      route: 'shopDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '收支统计',
      route: 'chartDisplay',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '交易详情',
      route: 'onlineTransactionDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '交易详情',
      route: 'onlineTransactionDetailCard',
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '提现记录',
      route: 'payOutRecord',
      hiddenMenu: true,
    },
  ],
  SMS: [
    {
      moduleName: '店务管理',
      name: '任务待办',
      route: 'ShopPatrolTask',
      image: images.smsTaskPending,
      isHasAuth: ['APP任务待办', '查询'],
      appType: 'SMS',
    },
    {
      moduleName: '店务管理',
      name: '门店报备',
      route: 'ShopReport',
      image: images.smsStoreReport,
      isHasAuth: ['APP门店报备', '查询'],
      appType: 'SMS',
    },
    {
      moduleName: '店务管理',
      name: '任务单管理',
      route: 'TaskOrderManage',
      image: images.smsTaskOrderMange,
      isHasAuth: ['APP任务单管理', '查询'],
      appType: 'SMS',
    },
    {
      moduleName: '店务管理',
      name: '店务助手',
      route: 'SmsStoreAssistant',
      image: images.smsStoreAssistant,
      isHasAuth: ['APP店务助手', '查询'],
      appType: 'SMS',
    },
    {
      moduleName: '店务管理',
      name: '总结报告',
      route: 'SmsSummaryReport',
      image: images.SmsSummaryReport,
      isHasAuth: ['APP总结报告', '查询'],
      appType: 'SMS',
    },
    {
      moduleName: '巡店',
      name: '巡店记录',
      route: 'ShopPatrolList',
      image: images.smsShopPatrolList,
      isHasAuth: ['APP巡店记录', '查询'],
      appType: 'SMS',
    },
    {
      moduleName: '巡店',
      name: '现场巡店',
      route: 'ShopPatrolOnSite',
      image: images.smsPatrolOnSite,
      isHasAuth: ['APP现场巡店', '查询'],
      appType: 'SMS',
    },
    {
      moduleName: '巡店',
      name: '门店拍照',
      route: 'ShopPhoto',
      image: images.smsPatrolOnSite,
      isHasAuth: true,
      hiddenMenu: true,
      appType: 'SMS',
    },
    // {
    //   moduleName: '巡店',
    //   name: '总结报告',
    //   route: 'SmsSummaryReport',
    //   image: images.smsPatrolOnSite,
    //   isHasAuth: true,
    //   hiddenMenu: true,
    //   appType: 'SMS',
    // },
    // {
    //   moduleName: '巡店',
    //   name: '门店拍照',
    //   route: 'ShopPhoto',
    //   image: images.smsPatrolOnSite,
    //   isHasAuth: true,
    //   hiddenMenu: true,
    //   appType: 'SMS',
    // },
    // {
    //   moduleName: '巡店',
    //   name: '门店拍照',
    //   route: 'ShopPhoto',
    //   image: images.smsPatrolOnSite,
    //   isHasAuth: true,
    //   hiddenMenu: true,
    //   appType: 'SMS',
    // },
  ],
  SCM: [
    {
      moduleName: '业务管理',
      name: '门店订单',
      route: 'StoreOrder',
      image: images.appStoreReplenishment,
      isHasAuth: ['门店订单', '查询'],
      appType: 'SCM',
      RemoteAppKey: 'ERP',
      hiddenStack: true,
    },
    {
      moduleName: '业务管理',
      name: '门店申请单',
      route: 'SCMStoreApplicationOrder',
      RemoteAppKey: 'ERP',
      image: images.appDistributionInspection,
      isHasAuth: ['门店申请单', '查询'],
      appType: 'SCM',
    },
    {
      moduleName: '业务管理',
      name: '门店申请单详情',
      route: 'SCMStoreApplicationOrderDetail',
      hiddenMenu: true,
      RemoteAppKey: 'ERP',
    },
    {
      moduleName: '业务管理',
      name: '门店申请单新增',
      route: 'SCMStoreApplicationOrderAdd',
      hiddenMenu: true,
      RemoteAppKey: 'ERP',
    },
    {
      moduleName: '业务管理',
      name: '门店申请单选择调入单',
      route: 'SCMStoreApplicationOrderChoseDeliveryInOrder',
      hiddenMenu: true,
      RemoteAppKey: 'ERP',
    },
    {
      moduleName: '业务管理',
      name: '门店申请单商品详情',
      route: 'SCMStoreApplicationOrderEditItem',
      hiddenMenu: true,
      RemoteAppKey: 'ERP',
    },
    {
      moduleName: '业务管理',
      name: '用户申请',
      route: 'userApply',
      RemoteAppKey: 'ERP',
      image: images.userApplication,
      isHasAuth: ['用户申请', '查询'],
      // isHashAuth: false,
      hiddenStack: true,
    },
    {
      moduleName: '业务管理',
      name: '发货单',
      route: 'InvoiceOrder',
      image: images.appInvoice,
      isHasAuth: ['发货单', '查询'],
    },
    {
      moduleName: '业务管理',
      name: '发货单详情',
      route: 'InvoiceOrderDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '发货单新增预约',
      route: 'Advance',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '发货预约',
      route: 'AppointmentRecordsNew',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '发货预约选择商品',
      route: 'AddProduct',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '新品申请',
      route: 'NewProductApplication',
      image: images.appNewProduction,
      isHasAuth: ['新品申请', '查询'],
    },
    {
      moduleName: '业务管理',
      name: '新品申请详情',
      route: 'NewProductApplicationDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '备货单',
      route: 'SupplyPrepare',
      image: images.supplyPrepare,
      isHasAuth: ['备货单', '查询'],
    },
    {
      moduleName: '业务管理',
      name: '备货单详情',
      route: 'SupplyPrepareDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '样品寄送',
      route: 'SampleDelivery',
      image: images.appSampleDelivery,
      isHasAuth: ['样品寄送', '查询'],
    },
    {
      moduleName: '业务管理',
      name: '样品寄送详情',
      route: 'SampleDeliveryDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '新增样品寄送',
      route: 'SampleDeliveryDetailAdd',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '样品寄送选择快递',
      route: 'SampleDeliveryDetailExpress',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '样品寄送添加样品',
      route: 'SampleDeliveryGoodList',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '商品检测报告',
      route: 'GoodsCheckReport',
      image: images.appGoodsCheckReport,
      isHasAuth: ['商品检测报告', '查询'],
      // isHasAuth: false,
    },
    {
      moduleName: '业务管理',
      name: '商品检测报告详情',
      route: 'GoodsCheckReportDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '商品检测报告变更记录',
      route: 'GoodsCheckReportRecords',
      hiddenMenu: true,
    },
    {
      moduleName: '库存管理',
      name: '库存查询',
      route: 'ScmStockQuery',
      image: images.appInventoryCheck,
      isHasAuth: ['库存查询', '查询'],
      appType: 'SCM',
    },
    {
      moduleName: '库存管理',
      name: '供应商抽检',
      route: 'SupplierSampling',
      image: images.supplierImage,
      isHasAuth: ['供应商抽检', '查询'],
    },
    {
      moduleName: '库存管理',
      name: '供应商抽检详情',
      hiddenMenu: true,
      route: 'SupplierSamplingDetail',
    },
    {
      moduleName: '库存管理',
      name: '供应商抽检审核页',
      hiddenMenu: true,
      route: 'SupplierSamplingAudit',
    },
    {
      moduleName: '库存管理',
      name: '供应商抽检详情选择供应商',
      hiddenMenu: true,
      route: 'SupplierSamplingSelectSupplier',
    },
    {
      moduleName: '库存管理',
      name: '商品抽检',
      route: FsmsRouteKeys.StoreHouse,
      RemoteAppKey: 'FSMS',
      image: images.storeHouse,
      isHasAuth: ['仓库商品抽检', '查询'],
    },
    {
      moduleName: '业务管理',
      name: '采购计划',
      route: FsmsRouteKeys.ProcurementPlan,
      RemoteAppKey: 'FSMS',
      image: images.storeHouse,
      isHasAuth: ['采购计划', '查询'],
      appType: 'SCM',
    },
    {
      moduleName: '业务管理',
      name: '新品审核',
      route: FsmsRouteKeys.NewProductSign,
      RemoteAppKey: 'FSMS',
      image: images.newProductSign,
      isHasAuth: ['新品申请/集团总经理审核', '审核'],
    },
    {
      moduleName: '业务管理',
      name: '新品签核',
      route: FsmsRouteKeys.NewProductCompanySign,
      RemoteAppKey: 'FSMS',
      image: images.newProductSign, // 和集团总经理审核用同一个图标
      isHasAuth: ['新品申请/公司总经理审核', '审核'],
    },
    {
      moduleName: '数据',
      name: '营业返款',
      route: 'ScmBusinessRebates',
      image: images.ScmBusinessRebates,
      isHasAuth: ['营业返款', '查询'],
      appType: 'SCM',
    },
    {
      moduleName: '业务管理',
      name: '打包台',
      route: 'AppPackagingDesk',
      image: images.appPackagingDesk,
      isHasAuth: ['打包中心', '查询'],
    },
    {
      moduleName: '业务管理',
      name: '打包台详情',
      route: 'AppPackagingDeskDetail',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '包裹详情',
      route: 'AppPackagingDeskDetailPrd',
      hiddenMenu: true,
    },
    {
      moduleName: '业务管理',
      name: '打包发货',
      route: 'ScmPackage',
      image: images.ScmScmPackageIcon,
      isHasAuth: ['打包发货', '查询'],
      appType: 'SCM',
    },
  ],
  CRM: [
    {
      moduleName: '客户',
      name: '线索管理',
      route: 'ClueHome',
      image: images.Clue,
      isHasAuth: ['线索管理', '查询'],
      RemoteAppKey: 'SDS',
    },
    {
      moduleName: '客户',
      name: '线索管理',
      route: 'Clue',
      image: images.Clue,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
    },

    {
      moduleName: '客户',
      name: '线索管理',
      route: 'ClueDetail',
      image: images.Clue,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
    },

    {
      moduleName: '客户',
      name: '线索管理',
      route: 'BusinessClue',
      image: images.Clue,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
    },

    {
      moduleName: '客户',
      name: '线索管理',
      route: 'InvaildClue',
      image: images.Clue,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
    },

    {
      moduleName: '客户',
      name: '线索管理',
      route: 'ClueAdd',
      image: images.Clue,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
    },

    {
      moduleName: '客户',
      name: '线索管理',
      route: 'ClueEdit',
      image: images.Clue,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
    },

    {
      moduleName: '客户',
      name: '线索管理',
      route: 'ClueTransfer',
      image: images.Clue,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
    },

    {
      moduleName: '客户',
      name: '线索管理',
      route: 'DiffPage',
      image: images.Clue,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
    },

    // {
    //   moduleName: '客户',
    //   name: '线索池',
    //   route: ClueRoute.Clue,
    //   image: images.CluePool,
    //   isHasAuth: ['线索管理', '查询'],
    // },
    // {
    //   moduleName: '客户',
    //   name: '招商线索',
    //   route: ClueRoute.BusinessClue,
    //   image: images.BusinessClue,
    //   isHasAuth: ['线索管理', '查询'],
    // },
    // {
    //   moduleName: '客户',
    //   name: '无效线索',
    //   route: ClueRoute.InvaildClue,
    //   image: images.InvaildClue,
    //   isHasAuth: ['线索管理', '查询'],
    // },
    {
      moduleName: '客户',
      name: '潜在客户',
      route: 'PotentialClient',
      image: images.PotentialClient,
      isHasAuth: ['潜在客户', '查询'],
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '潜在客户',
      route: 'PotentialClientAdd',
      image: images.PotentialClient,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '潜在客户',
      route: 'PotentialClientEdit',
      image: images.PotentialClient,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '潜在客户',
      route: 'PotentialClientDetail',
      image: images.PotentialClient,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '潜在客户',
      route: 'PotentialClientTurnNormal',
      image: images.PotentialClient,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '潜在客户',
      route: 'PotentialClientTurnNormalDetail',
      image: images.PotentialClient,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '潜在客户',
      route: 'IntentionClient',
      image: images.PotentialClient,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '潜在客户',
      route: 'PotentialClientDiffPage',
      image: images.PotentialClient,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '潜在客户',
      route: 'PotentialClientAddFollow',
      image: images.PotentialClient,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
      // isHashAuth: false,
    },
    // {
    //   moduleName: '客户',
    //   name: '客户管理',
    //   route: ClientMangeRoute.ClientMange,
    //   image: images.clientMange,
    //   isHasAuth: true,
    // hiddenMenu: true,
    //   // isHashAuth: false,
    // },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ClientMangeNew',
      image: images.clientMange,
      isHasAuth: ['客户管理', '查询'],
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ClientMangeDetailNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ClientMangeStoreDetailNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ClientTurnNormalNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ClientTurnNormalDetailNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'SignDetailNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'UploadSignDetailNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'OfficialCustomerDetail',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ClientManageAddFollowNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ClientManageIntentionNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ClientManageEditNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ClientManageStoreInfoDetailNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'WUserNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ContractSignNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ContractInvalidNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ContractInvalidOfflineNew',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'CancelContract',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'CancelContractDetail',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'RegularFeeJoin',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'RegularFeeBond',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ContractInvalidOfflineRelocation',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },
    {
      moduleName: '客户',
      name: '客户管理',
      route: 'ClientTreeSelect',
      image: images.clientMange,
      isHasAuth: true,
      hiddenMenu: true,
      RemoteAppKey: 'SDS',

      // isHashAuth: false,
    },

    {
      moduleName: '客户',
      name: '招商看板',
      route: 'CRMBorad',
      image: images.storeAnalysis,
      isHasAuth: ['首页招商看板', '查询'],
      RemoteAppKey: 'SDS',
    },
    {
      moduleName: '客户',
      name: '营销活动',
      route: 'Investment',
      image: images.investment,
      isHasAuth: ['营销活动', '查询'],
      RemoteAppKey: 'SDS',

      // isHasAuth: false,
    },
    {
      moduleName: '客户',
      name: '营销活动',
      route: 'InvestmentPoster',
      image: images.investment,
      isHasAuth: ['营销活动', '查询'],
      RemoteAppKey: 'SDS',
      hiddenMenu: true,

      // isHasAuth: false,
    },
    {
      moduleName: '客户',
      name: '营销活动',
      route: 'InvestmentList',
      image: images.investment,
      isHasAuth: ['营销活动', '查询'],
      RemoteAppKey: 'SDS',
      hiddenMenu: true,

      // isHasAuth: false,
    },
    {
      moduleName: '档案',
      name: '员工管理',
      route: 'StaffManageList',
      image: images.staffManage,
      RemoteAppKey: 'SDS',
      isHasAuth: 993,
    },
    {
      moduleName: '档案',
      name: '员工管理',
      route: 'StaffManageSearch',
      image: images.staffManage,
      RemoteAppKey: 'SDS',
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '员工管理',
      route: 'StaffManageEdit',
      image: images.staffManage,
      RemoteAppKey: 'SDS',
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '招商看板',
      route: 'WishesAnalysisDetail',
      image: images.storeAnalysis,
      RemoteAppKey: 'SDS',
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '招商看板',
      route: 'ShopAnalysisDetail',
      image: images.storeAnalysis,
      RemoteAppKey: 'SDS',
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '招商看板',
      route: 'ShopRelocationDetail',
      image: images.storeAnalysis,
      RemoteAppKey: 'SDS',
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '招商看板',
      route: 'ClueAddDetail',
      image: images.storeAnalysis,
      RemoteAppKey: 'SDS',
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '招商看板',
      route: 'ClueToPotential',
      image: images.storeAnalysis,
      RemoteAppKey: 'SDS',
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '招商看板',
      route: 'CrmHomeCardDetail',
      image: images.storeAnalysis,
      RemoteAppKey: 'SDS',
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '招商看板',
      route: 'DoubleSignDetail',
      image: images.storeAnalysis,
      RemoteAppKey: 'SDS',
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '招商看板',
      route: 'WhiteCustomerDetail',
      image: images.storeAnalysis,
      RemoteAppKey: 'SDS',
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '档案',
      name: '招商看板',
      route: 'SessionsDetail',
      image: images.storeAnalysis,
      RemoteAppKey: 'SDS',
      isHasAuth: true,
      hiddenMenu: true,
    },
  ],
  SDS: [
    {
      moduleName: '标地',
      name: '商圈规划',
      route: 'BusinessDistrictList',
      image: images.pointPlan,
      isHasAuth: ['商圈规划', '查询'],
    },
    {
      moduleName: '标地',
      name: '商圈规划',
      route: 'BusinessDistrictAdd',
      image: images.pointPlan,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '商圈规划',
      route: 'BusinessDistrictDetail',
      image: images.pointPlan,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '商圈规划',
      route: 'mapPolygon',
      image: images.pointPlan,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '商圈规划',
      route: 'BusinessDistrictOtherInfo',
      image: images.pointPlan,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '商圈规划',
      route: 'BusinessDistrictAddFollow',
      image: images.pointPlan,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '商圈规划',
      route: 'BusinessSelectCompetitor',
      image: images.pointPlan,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'PointPlanList',
      image: images.businessDistrict,
      isHasAuth: ['点位规划', '查询'],
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'PointPlanEdit',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'PointPlanDetail',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'PointPlanAdd',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'AddFollow',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'AddPassEnger',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },

    {
      moduleName: '标地',
      name: '点位规划',
      route: 'AuditLog',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'PointPlanBusinessDistrictSelect',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'PointPlanOtherInfo',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'LandlordLog',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'ORG_GROUP',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'POINT_FOLLOW_DETAIL',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'PointExplorationClock',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '点位规划',
      route: 'PointPlanCollaborativeBrandSelect',
      image: images.businessDistrict,
      isHasAuth: true,
      hiddenMenu: true,
    },

    // {
    //   moduleName: '标地',
    //   name: '带看任务',
    //   route: lookTaskRoute.LookTask,
    //   image: images.lookTask,
    //   isHasAuth: ['带看任务', '查询'],
    // },
    // {
    //   moduleName: '标地',
    //   name: '带看申请',
    //   route: lookApplicationRoute.LookApplication,
    //   image: images.lookApplication,
    //   isHasAuth: ['带看申请', '查询'],
    // },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'applyList',
      image: images.pointApply,
      isHasAuth: ['建店申请', '查询'],
    },

    {
      moduleName: '标地',
      name: '建店申请',
      route: 'pointApply',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'pointApplyDetail',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'applyInfo',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'applyInfoDetail',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: '',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'chooseApplyer',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'approvalFlow',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'applyInfoRemark',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'webPage',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'LandlordLogApply',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'MapView',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'locationPage',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'firstCommitDetail',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'JDLookLog',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'leaseAgreementDetail',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'contractDetail',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },

    {
      moduleName: '标地',
      name: '建店申请',
      route: 'PointApplyOtherInfo',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'PointApplyAddRemark',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'PointApplyClientListSelector',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'pointApplyJoinFeeDetail',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'pointApplyMoveApply',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'pointApplyMoveStoreErrorDetail',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'reviewReport',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '建店申请',
      route: 'PointApplyAddReviewReport',
      image: images.pointApply,

      isHasAuth: true,
      hiddenMenu: true,
    },

    {
      moduleName: '标地',
      name: '待分配点位池',
      route: 'applyListPool',
      image: images.pointPool,
      isHasAuth: ['待分配点位池', '查询'],
      // isHasAuth: false,
    },
    {
      moduleName: '标地',
      name: '待分配点位池',
      route: 'poolDetail',
      image: images.pointPool,
      isHasAuth: true,
      hiddenMenu: true,
      // isHasAuth: false,
    },

    {
      moduleName: '标地',
      name: '待售店铺',
      route: 'storeForSale_applyList',
      image: images.storeForSale,
      isHasAuth: ['待售店铺', '查询'],
      // isHasAuth: false,
    },
    {
      moduleName: '标地',
      name: '竞品标记',
      route: 'CompetitorMark',
      image: images.competitorMark,
      isHasAuth: ['竞品标记', '查询'],
      // isHasAuth: false,
    },
    {
      moduleName: '标地',
      name: '竞品标记',
      route: 'CompetitorMarkDetail',
      image: images.competitorMark,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '竞品标记',
      route: 'CompetitorMarkNewDetail',
      image: images.competitorMark,
      hiddenMenu: true,
      isHasAuth: true,
    },
    {
      moduleName: '标地',
      name: '协同品牌',
      route: 'CollaborativeIndex',
      image: images.collaborativeWork,
      isHasAuth: ['协同品牌', '查询'],
      // isHasAuth: false,
    },

    {
      moduleName: '标地',
      name: '协同品牌',
      route: 'CollaborativeMarkDetail',
      image: images.collaborativeWork,
      isHasAuth: true,
      hiddenMenu: true,
    },

    {
      moduleName: '标地',
      name: '协同品牌',
      route: 'CollaborativeMarkEdit',
      image: images.collaborativeWork,
      isHasAuth: true,
      hiddenMenu: true,
    },

    {
      moduleName: '标地',
      name: '插旗系统',
      route: 'FlagPlant',
      image: images.flagPlant,
      isHasAuth: ['插旗系统', '查询'],
      // isHasAuth: false,
    },
    {
      moduleName: '标地',
      name: '插旗系统',
      route: 'FlagPlantTemplate',
      image: images.flagPlant,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '租赁合约',
      route: 'LeaseAgreement',
      image: images.leaseAgreement,
      isHasAuth: ['租赁合约', '查询'],
    },
    {
      moduleName: '标地',
      name: '租赁合约',
      route: 'LeaseAgreementDetail',
      image: images.leaseAgreement,
      isHasAuth: true,
      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '租赁合约',
      route: 'ContractDetail',
      image: images.leaseAgreement,
      isHasAuth: true,

      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '标地看板',
      route: 'SDSBorad',
      image: images.storeAnalysis,
      isHasAuth: ['点位规划', '查询'],
    },
    {
      moduleName: '标地',
      name: '店铺搬迁',
      route: 'RelocationList',
      image: images.storeRelocation,
      isHasAuth: ['店铺搬迁', '查询'],
    },
    {
      moduleName: '标地',
      name: '店铺搬迁',
      route: 'RelocationEdit',
      image: images.storeRelocation,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '标地',
      name: '店铺搬迁',
      route: 'RelocationDetail',
      image: images.storeRelocation,
      isHasAuth: true,

      hiddenMenu: true,
    },
    {
      moduleName: '标地',
      name: '店铺搬迁',
      route: 'RelocationContractSign',
      image: images.storeRelocation,
      isHasAuth: true,

      hiddenMenu: true,
    },
    {
      moduleName: '结算',
      name: '租金支付',
      route: 'RentPay',
      image: images.rentPay,
      isHasAuth: ['租金支付', '查询'],
      createTime: '2023-06-25',
      // isHashAuth: false,
    },
    {
      moduleName: '结算',
      name: '租金支付',
      route: 'RentPayDetail',
      image: images.rentPay,
      isHasAuth: true,

      hiddenMenu: true,
    },
    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'MileageSubsidy',
      image: images.mileageSubsidy,
      // isHasAuth: ['考勤', '查询'],
      isHasAuth: ['里程补贴', '查询'],
    },
    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'ReceiveAccount',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },
    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'SubsidyScreen',
      image: images.mileageSubsidy,
      isHasAuth: true,
      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'ClockScreen',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'BankAccountDetail',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'AdjustPointMap',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'TrancetMap',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'TranceList',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'ExpensesList',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'AddExpenses',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'ChooseTranceList',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'AddReimbursement',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'ReimbursementDetail',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'EditReimbursement',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'ExitNavi',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'ReimbursementList',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'ExpensesDetail',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'TranceListDetail',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'SelectDepartment',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'ExitExpenses',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '结算',
      name: '里程补贴',
      route: 'TrancetNaviMap',
      image: images.mileageSubsidy,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '数据',
      name: '标地绩效',
      route: 'landMark',
      image: images.landMark,
      isHasAuth: ['标地绩效', '查询'],
      hiddenMenu: true,
      createTime: '2023-09-27',
    },
    {
      moduleName: '数据',
      name: '跟进统计表',
      route: 'PointFollowTable',
      image: images.clientMange,
      isHasAuth: ['点位跟进统计', '查询'],
    },
    {
      moduleName: '数据',
      name: '点位日报',
      route: 'PointFollowDailyReport',
      image: images.pointFollowDailyReportNew,
      isHasAuth: ['点位日报', '查询'],
    },
    {
      moduleName: '数据',
      name: '数据中心',
      route: 'DataCenter',
      image: images.sdsDataCenter,
      isHasAuth: ['APP数据中心', '查询'],
    },
    {
      moduleName: '数据',
      name: '数据中心',
      route: 'PersonnelDetails',
      image: images.sdsDataCenter,
      isHasAuth: true,

      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '搜索记录',
      route: 'HistoryPage',
      image: images.sdsDataCenter,
      isHasAuth: true,

      hiddenMenu: true,
    },

    {
      moduleName: '数据',
      name: '搜索结果',
      route: 'SearchResult',
      image: images.sdsDataCenter,
      isHasAuth: true,

      hiddenMenu: true,
    },
    {
      moduleName: '数据',
      name: '目标详情',
      route: 'KmsTargetItem',
      image: images.sdsDataCenter,
      isHasAuth: true,

      hiddenMenu: true,
    },
  ],
  EMS: [
    {
      moduleName: '装修',
      name: '工单管理',
      route: 'emsWebRouteWorkOrderServer',
      image: images.workOrder,
      isHasAuth: ['工单管理', '查询'],
    },
    {
      moduleName: '装修',
      name: '施工项目',
      route: 'emsWebRouteWorkOrderClient',
      image: images.buildtaskDeliver,
      isHasAuth: true,

      hiddenMenu: true,
    },
    {
      moduleName: '装修',
      name: '施工项目',
      route: 'emsWebRouteConstructionManage',
      image: images.buildtaskDeliver,
      isHasAuth: ['施工项目', '查询'],
    },
    {
      moduleName: '装修',
      name: '施工项目',
      route: 'emsWebRouteConstructionManageDetail',
      image: images.buildtaskDeliver,
      isHasAuth: true,

      hiddenMenu: true,
    },
    {
      moduleName: '装修',
      name: '看板',
      route: 'emsWebRouteBoard',
      image: images.emsboard,
      isHasAuth: ['施工项目', '查询'],
    },
  ],
  HRS: [
    {
      moduleName: '应用',
      name: '考勤',
      route: 'ATTENDANCE',
      image: images.ateendance,
      isHasAuth: ['APP考勤', '查询'],
      // isHasAuth: true,
    },

    {
      moduleName: '应用',
      name: '人事',
      route: 'ATTENDANCE_PM',
      image: images.pm,
      isHasAuth: ['APP人事模块', '查询'],
      // isHasAuth: true,
    },
    {
      moduleName: '应用',
      name: '人事',
      route: 'AttendanceDetail',
      image: images.pm,
      isHasAuth: true,

      hiddenMenu: true,
      // isHasAuth: true,
    },
    {
      moduleName: '应用',
      name: '人事',
      route: 'HRS_H5_PAGE',
      image: images.pm,
      isHasAuth: true,

      hiddenMenu: true,
      // isHasAuth: true,
    },
  ],
  KMS: [
    // {
    //   moduleName: '标地',
    //   name: '点位日报',
    //   route: PointPlanRoute.POINT_FOLLOW_DAILY_REPORT,
    //   image: images.pointFollowDailyReportNew,
    //   isHasAuth: ['点位日报', '查询'],
    // },
    // {
    //   moduleName: '标地',
    //   name: '审批任务',
    //   route: ApprovalTakRoute.List,
    //   image: images.approvalTask,
    //   isHasAuth: true,
    // },
  ],
  XPAY: [],
  TMS: [
    {
      moduleName: '档案',
      name: '用户管理',
      route: '',
      image: images.userLogo,
      isHasAuth: ['APP用户管理', '查询'],
    },
    // {
    //   moduleName: '调度',
    //   name: '调度管理',
    //   route: Routes.DisPatchManange,
    //   image: images.dispatchLogo,
    //   isHasAuth: ['APP调度管理', '查询'],
    // },
    {
      moduleName: '调度',
      name: '调度管理',
      route: '',
      image: images.dispatchLogo,
      isHasAuth: ['APP调度管理', '查询'],
    },
    {
      moduleName: '调度',
      name: '装车管理',
      route: '',
      image: images.carLogo,
      isHasAuth: ['APP装车管理', '查询'],
    },
    {
      moduleName: '调度',
      name: '配送管理',
      route: '',
      image: images.deliveryLogo,
      isHasAuth: ['APP配送管理', '查询'],
    },
    {
      moduleName: '调度',
      name: '司机评价',
      route: '',
      image: images.evaluateLogo,
      isHasAuth: ['APP司机评价', '编辑'],
    },
    {
      moduleName: '配送',
      name: '运单管理',
      route: '',
      image: images.waybillManageIcon,
      isHasAuth: ['APP运单管理', '查询'],
    },
    {
      moduleName: '调度',
      name: '退货取件',
      route: '',
      image: images.returnPack,
      isHasAuth: ['APP退货取件', '查询'],
    },
    // {
    //   moduleName: '签到',
    //   name: '签到',
    //   route: Routes.SignManager,
    //   image: images.signLogo,
    //   isHasAuth: ['APP签到', '查询'],
    // },
    {
      moduleName: '签到',
      name: '签到',
      route: '',
      image: images.signLogo,
      isHasAuth: ['APP签到', '查询'],
    },
    {
      moduleName: '对账',
      name: '对账',
      route: '',
      image: images.billLogo,
      isHasAuth: ['对账单', '查询'],
    },
  ],
  FSMS: [
    {
      moduleName: '业务',
      name: '政府抽检',
      route: FsmsRouteKeys.GovernmentCheck,
      image: images.governmentCheck,
      isHasAuth: ['政府抽检', '查询'],
      appType: 'FSMS',
    },
    {
      moduleName: '业务',
      name: '质量提报',
      route: FsmsRouteKeys.QualityReporting,
      image: images.qualityReporting,
      isHasAuth: ['质量提报', '查询'],
      appType: 'FSMS',
    },
    {
      moduleName: '业务',
      name: '食品安全员',
      route: FsmsRouteKeys.FoodSafety,
      image: images.foodSafety,
      isHasAuth: ['食品安全员', '查询'],
      appType: 'FSMS',
    },
    {
      moduleName: '业务',
      name: '管理制度',
      route: FsmsRouteKeys.FoodSafetyManagement,
      image: images.foodSafetyManagement,
      isHasAuth: ['食品安全管理制度', '查询'],
      appType: 'FSMS',
    },
    {
      moduleName: '业务',
      name: '食品安全自查',
      route: FsmsRouteKeys.FoodSafetyCheck,
      image: images.foodSafetyCheck,
      isHasAuth: ['食品安全自查', '查询'],
      appType: 'FSMS',
    },
    {
      moduleName: '业务',
      name: '虫害管理',
      route: FsmsRouteKeys.PestManagement,
      image: images.pestManagement,
      isHasAuth: ['虫害管理', '查询'],
      appType: 'FSMS',
    },
    {
      moduleName: '业务',
      name: '客诉管理',
      route: FsmsRouteKeys.CustomerManagement,
      image: images.customerManagement,
      isHasAuth: ['客诉管理', '查询'],
      appType: 'FSMS',
    },
    {
      moduleName: '业务',
      name: '商品抽检',
      route: FsmsRouteKeys.StoreHouseFsms,
      image: images.storeHouse,
      isHasAuth: ['仓库商品抽检(新)', '查询'],
      appType: 'FSMS',
    },
    {
      moduleName: '业务',
      name: '仓库巡检',
      route: FsmsRouteKeys.WarehouseInspection,
      image: images.ckxj,
      isHasAuth: ['仓库巡检管理', '查询'],
      appType: 'FSMS',
    },
    {
      moduleName: '业务',
      name: '开店证件',
      route: FsmsRouteKeys.StoreOpeningCertificate,
      image: images.kdzj,
      isHasAuth: ['开店证件管理', '查询'],
      appType: 'FSMS',
    },
    {
      moduleName: '业务',
      name: '商品溯源',
      route: FsmsRouteKeys.ProductTraceability,
      image: images.suyuan,
      isHasAuth: ['商品溯源', '查询'],
      appType: 'FSMS',
    },
  ],
  MEM: [
    {
      moduleName: '积分',
      name: '积分变动管理',
      route: 'PointsChangeManagementIndex',
      image: images.pointsChangeManage,
      isHasAuth: ['积分审批管理', '查询'],
      appType: 'MEM',
    },

    {
      moduleName: '门店储值激励',
      name: '储值激励活动',
      route: 'StoreDepositIndex',
      image: images.pointsChangeManage,
      isHasAuth: ['门店储值激励报表', '查询'],
      appType: 'MEM',
    },
    {
      moduleName: '门店储值激励',
      name: '储值激励活动',
      route: 'StoreDepositDetail',
      image: images.pointsChangeManage,
      isHasAuth: ['门店储值激励报表', '查询'],
      appType: 'MEM',
      hiddenMenu: true,
    },

    // {
    //   moduleName: '商品管理',
    //   name: '门店零售价',
    //   route: 'StoreRetailPriceSaleIndex',
    //   image: images.storeRetailSale,
    //   isHasAuth: ['门店零售价', '查询'],
    //   appType: 'ERP',
    // },
    // {
    //   moduleName: '商品管理',
    //   name: '门店零售价详情',
    //   route: 'StoreRetailPriceSaleDetail',
    //   image: images.storeRetailSale,
    //   isHasAuth: ['门店零售价', '查询'],
    //   appType: 'ERP',
    //   hiddenMenu: true,
    // },

    // {
    //   moduleName: '商品管理',
    //   name: '零售调价',
    //   route: 'RetailPriceAdjustIndex',
    //   image: images.appRetailPriceAdjust,
    //   isHasAuth: ['零售调价', '查询'],
    //   appType: 'ERP',
    // },
    // {
    //   moduleName: '商品管理',
    //   name: '零售调价新增',
    //   route: 'RetailPriceAdjustAdd',
    //   image: images.appRetailPriceAdjust,
    //   isHasAuth: ['零售调价', '查询'],
    //   appType: 'ERP',
    //   hiddenMenu: true,
    // },
    // {
    //   moduleName: '商品管理',
    //   name: '零售调价详情',
    //   route: 'RetailPriceAdjustDetail',
    //   image: images.appRetailPriceAdjust,
    //   isHasAuth: ['零售调价', '查询'],
    //   appType: 'ERP',
    //   hiddenMenu: true,
    // },
    // {
    //   moduleName: '商品管理',
    //   name: '零售调价详情',
    //   route: 'RetailPriceAdjustItemConfig',
    //   image: images.appRetailPriceAdjust,
    //   isHasAuth: ['零售调价', '查询'],
    //   appType: 'ERP',
    //   hiddenMenu: true,
    // },
    // {
    //   moduleName: '商品管理',
    //   name: '门店调价申请',
    //   route: 'StorePriceAdjustmentIndex',
    //   image: images.discountCode,
    //   isHasAuth: ['门店调价申请', '查询'],
    //   appType: 'ERP',
    // },
    // {
    //   moduleName: '商品管理',
    //   name: '零售调价新增',
    //   route: 'StorePriceAdjustmentAdd',
    //   image: images.discountCode,
    //   isHasAuth: ['门店调价申请', '查询'],
    //   appType: 'ERP',
    //   hiddenMenu: true,
    // },
    // {
    //   moduleName: '商品管理',
    //   name: '零售调价详情',
    //   route: 'StorePriceAdjustmentDetail',
    //   image: images.discountCode,
    //   isHasAuth: ['门店调价申请', '查询'],
    //   appType: 'ERP',
    //   hiddenMenu: true,
    // },
    // {
    //   moduleName: '商品管理',
    //   name: '零售调价详情',
    //   route: 'StorePriceAdjustmentItemConfig',
    //   image: images.discountCode,
    //   isHasAuth: ['门店调价申请', '查询'],
    //   appType: 'ERP',
    //   hiddenMenu: true,
    // },
    // {
    //   moduleName: '商品管理',
    //   name: '零售调价详情',
    //   route: 'StorePriceAdjustmentRecord',
    //   image: images.discountCode,
    //   isHasAuth: ['门店调价申请', '查询'],
    //   appType: 'ERP',
    //   hiddenMenu: true,
    // },
  ],
  BASE: baseRoute,
  IM: [
    {
      moduleName: 'ChatHome',
      name: 'ChatHome',
      route: 'RemoteAppIm.ChatHome',
      hiddenMenu: true,
    },
    {
      moduleName: 'ChatScreen',
      name: 'ChatScreen',
      route: 'RemoteAppIm.ChatScreen',
      hiddenMenu: true,
    },
    {
      moduleName: 'ChatDetail',
      name: 'ChatDetail',
      route: 'RemoteAppIm.ChatDetail',
      hiddenMenu: true,
    },
    {
      moduleName: 'UserList',
      name: 'UserList',
      route: 'RemoteAppIm.UserList',
      hiddenMenu: true,
    },
    {
      moduleName: 'UserScreen',
      name: 'UserScreen',
      route: 'RemoteAppIm.UserScreen',
      hiddenMenu: true,
    },
    {
      moduleName: 'VideoRecorder',
      name: 'VideoRecorder',
      route: 'RemoteAppIm.VideoRecorder',
      hiddenMenu: true,
    },
  ],
};

for (let key in _applicationFields) {
  _applicationFields[key] = _applicationFields[key]
    .filter(d => d.route)
    .map(d => {
      const RemoteKey = d?.RemoteAppKey || key;
      return {
        ...d,
        route: d.route.startsWith('RemoteApp')
          ? d.route
          : 'RemoteApp' +
            RemoteKey[0] +
            RemoteKey.substring(1).toLocaleLowerCase() +
            '.' +
            d.route,
      };
    });
}
console.log('applicationFields', _applicationFields);
// 确保能json化, 后续放oss
export const applicationFields = JSON.parse(JSON.stringify(_applicationFields));

// applicationFields的展开数组
export const applicationsArr = Object.values(applicationFields).reduce(
  (acc, arr) => {
    return acc.concat(arr);
  },
  [],
);

export const tabBarFields: any = {
  BPM: {
    application: [{key: 'oa', title: '企业办公'}],
  },
  ERP: {
    application: [
      {key: 'files', title: '档案'},
      {key: 'purchase', title: '采购管理'},
      {key: 'delivery', title: '配送管理'},
      // {key: 'xpay', title: '数据'},
      {key: 'manage', title: '经营'},
      {key: 'inventory', title: '库存管理'},
      {key: 'product', title: '商品管理'},
      {key: 'retail', title: '零售管理'},
      {key: 'zijin', title: '资金管理'},
      {key: 'alipay', title: '支付宝'},
      {key: 'quality', title: '质量管理'},
      {key: 'efficiency', title: '效率工具'},
    ],
  },
  FSS: {
    application: [
      {key: 'acquiring', title: '门店结算'},
      {key: 'refund', title: '门店返款'},
    ],
  },
  BMS: {
    application: [
      {key: 'files', title: '档案'},
      {key: 'acquiring', title: '收单'},
      {key: 'accountTransactions', title: '账户往来'},
    ],
  },
  SMS: {
    application: [
      {key: 'shopManage', title: '店务管理'},
      {key: 'ShopPatrol', title: '巡店'},
    ],
  },
  WMS: {
    application: [
      {key: 'wmsFiles', title: '档案'},
      {key: 'orderManage', title: '订单管理'},
      {key: 'warehousing', title: '入库管理'},
      {key: 'outOfStock', title: '出库管理'},
      {key: 'inLibrary', title: '库内管理'},
      {key: 'goods', title: '物资'},
    ],
  },
  SCM: {
    application: [
      {key: 'business', title: '业务管理'},
      {key: 'inventory', title: '库存管理'},
      {key: 'scmData', title: '数据'},
    ],
  },
  TMS: {
    application: [
      {key: 'files', title: '档案'},
      {key: 'dispatch', title: '调度'},
      {key: 'delivery', title: '配送'},
      {key: 'sign', title: '签到'},
      {key: 'bill', title: '对账'},
    ],
  },
  HRS: {
    application: [{key: 'application', title: '应用'}],
  },
  FSMS: {
    application: [{key: 'business', title: '业务'}],
  },
  KMS: {
    application: [
      {key: 'files', title: '档案'},
      {key: 'data', title: '数据'},
    ],
  },
  CRM: {
    application: [
      {key: 'clientClue', title: '客户'},
      {key: 'files', title: '档案'},
    ],
  },
  SDS: {
    application: [
      {key: 'markLand', title: '标地'},
      {key: 'business', title: '结算'},
      {key: 'data', title: '数据'},
    ],
  },
  EMS: {
    application: [
      {key: 'build', title: '装修'},
      {key: 'business', title: '结算'},
    ],
  },
  XPAY: {
    application: [{key: 'pay', title: '交易'}],
  },

  // 会员营销
  MEM: {
    application: [
      {key: 'points', title: '积分'},
      {key: 'StoreDepositIndex', title: '门店储值激励'},
      {key: 'retail', title: '零售管理'},
      {key: 'retailGoods', title: '商品管理'},
    ],
  },
};

// /**
//  * 标签选择
//  */
// export const tagSelectFields: any = {
//   // 调入状态
//   callInStatus: [
//     {
//       name: '不限',
//       value: null,
//     },
//     {
//       name: '未调入',
//       value: 1,
//     },
//     {
//       name: '部分调入',
//       value: 2,
//     },
//     {
//       name: '全部调入',
//       value: 3,
//     },
//   ],

//   // 调出状态
//   callOutStatus: [
//     {
//       name: '不限',
//       value: null,
//     },
//     {
//       name: '未调出',
//       value: 1,
//     },
//     {
//       name: '部分调出',
//       value: 2,
//     },
//     {
//       name: '全部调出',
//       value: 3,
//     },
//   ],

//   // 时间范围：value 单位为：天
//   timeRange: [
//     {
//       name: '一天内',
//       value: 1,
//     },
//     {
//       name: '三天内',
//       value: 3,
//     },
//     {
//       name: '一周内',
//       value: 7,
//     },
//     {
//       name: '最近一月',
//       value: 30,
//     },
//   ],
//   timeRanges: [
//     //gyb
//     {
//       name: '今日',
//       value: 1,
//     },
//     {
//       name: '昨日',
//       value: 2,
//     },
//     {
//       name: '最近三天',
//       value: 3,
//     },
//     {
//       name: '最近一周',
//       value: 7,
//     },
//     {
//       name: '最近一月',
//       value: 30,
//     },
//     {
//       name: '本周',
//       value: 7,
//     },
//     {
//       name: '本月',
//       value: 30,
//     },
//     {
//       name: '上月',
//       value: 60,
//     },
//   ],
//   deliveryInState: [
//     {
//       name: '不限',
//       value: null,
//     },
//     {
//       name: '未调入',
//       value: deliveryInStateEnum.unin,
//     },
//     {
//       name: '部分调入',
//       value: deliveryInStateEnum.partin,
//     },
//     {
//       name: '全部调入',
//       value: deliveryInStateEnum.all,
//     },
//   ],
//   sOrderStoreType: [
//     {
//       name: '不限',
//       value: 0,
//     },
//     {
//       name: '调往门店',
//       value: 1,
//     },
//     {
//       name: '申请门店',
//       value: 2,
//     },
//   ],
//   // 收货状态
//   receiptStatus: [
//     {
//       name: '不限',
//       value: null,
//     },
//     {
//       name: '未收货',
//       value: 0,
//     },
//     {
//       name: '部分收货',
//       value: 1,
//     },
//     {
//       name: '全部收货',
//       value: 2,
//     },
//   ],
//   // 结算状态
//   settleStatus: [
//     {
//       name: '不限',
//       value: null,
//     },
//     {
//       name: '未结算',
//       value: 0,
//     },
//     {
//       name: '部分结算',
//       value: 2,
//     },
//     {
//       name: '已结算',
//       value: 1,
//     },
//   ],

//   // 调出状态
//   completeStatus: [
//     {
//       name: '不限',
//       value: null,
//     },
//     {
//       name: '未调入',
//       value: 0,
//     },
//     {
//       name: '部分调入',
//       value: 1,
//     },
//     {
//       name: '全部调入',
//       value: 2,
//     },
//   ],

//   // 采购状态
//   purchaseStatus: [
//     {
//       name: '全部',
//       value: null,
//     },
//     {
//       name: '未采购',
//       value: 0,
//     },
//     {
//       name: '部分采购',
//       value: 2,
//     },
//     {
//       name: '采购完成',
//       value: 3,
//     },
//     {
//       name: '作废',
//       value: 1,
//     },
//   ],

//   /**
//    * WMS 非标
//    */
//   // 拣货状态
//   pickingStatus: [
//     {
//       name: '不限',
//       value: null,
//     },
//     {
//       name: '未拣货',
//       value: pickingStatusEnum.none,
//     },
//     {
//       name: '拣货中',
//       value: pickingStatusEnum.processing,
//     },
//     {
//       name: '拣货完成',
//       value: pickingStatusEnum.complete,
//     },
//   ],
//   // 收货状态
//   nonWmsReceiptStatus: [
//     {
//       name: '不限',
//       value: null,
//     },
//     {
//       name: '未收货',
//       value: 0,
//     },
//     {
//       name: '部分收货',
//       value: 2,
//     },
//     {
//       name: '收货完成',
//       value: 3,
//     },
//   ],
//   // 是否生成收货单
//   generateReceiptStatus: [
//     {
//       name: '全部',
//       value: null,
//     },
//     {
//       name: '是',
//       value: 1,
//     },
//     {
//       name: '否',
//       value: 0,
//     },
//   ],
//   // 明细的拣货状态
//   detailPickingStatus: [
//     {
//       name: '不限',
//       value: null,
//     },
//     {
//       name: '未拣货',
//       value: pickingStatusDetailEnum.none,
//     },
//     {
//       name: '已拣货',
//       value: pickingStatusDetailEnum.complete,
//     },
//     {
//       name: '已忽略',
//       value: pickingStatusDetailEnum.ignored,
//     },
//   ],
//   // 核验状态
//   verificationStatus: [
//     {
//       name: '不限',
//       value: null,
//     },
//     {
//       name: '未核验',
//       value: verificationStatusEnum.none,
//     },
//     {
//       name: '核验中',
//       value: verificationStatusEnum.processing,
//     },
//     {
//       name: '已核验',
//       value: verificationStatusEnum.already,
//     },
//   ],
//   // 发车状态
//   platoonStatus: [
//     {
//       name: '不限',
//       value: null,
//     },
//     {
//       name: '制单',
//       value: platoonStatusEnum.create,
//     },
//     {
//       name: '审核',
//       value: platoonStatusEnum.audit,
//     },
//     {
//       name: '已发车',
//       value: platoonStatusEnum.departed,
//     },
//   ],
//   storeReplenishOrderType: [
//     {
//       name: '不限',
//       value: null,
//     },
//     {
//       name: storeReplementOrderType[0].label,
//       value: storeReplementOrderType[0].value,
//     },
//     {
//       name: storeReplementOrderType[1].label,
//       value: storeReplementOrderType[1].value,
//     },
//     {
//       name: storeReplementOrderType[2].label,
//       value: storeReplementOrderType[2].value,
//     },
//   ],
// }

// /**
//  * 模态框选择
//  */
// export const modalSelectFields = {
//   // 盘点维度
//   inventoryDimension: [
//     { name: '按商品', value: 0 },
//     { name: '按库位', value: 1 },
//   ],
//   // 盘点范围
//   inventoryScope: [
//     { name: '单品盘点', value: 1 },
//     { name: '类别盘点', value: 2 },
//     { name: '全场盘点', value: 3 },
//     { name: '部门盘点', value: 4 },
//     { name: '异动盘点', value: 5 },
//   ],
//   // 盘点单位
//   inventoryUnit: [
//     { name: '基本单位', value: 'sale_unit' },
//     { name: '采购单位', value: 'pur_unit' },
//     { name: '库存单位', value: 'stock_unit' },
//     { name: '批发单位', value: 'batch_unit' },
//     { name: '配送单位', value: 'dev_unit' },
//   ],
//   applicationType: [
//     { name: '退货申请', value: 'RETURN', alias: '退货' },
//     { name: '直调申请', value: 'DELIVERY', alias: '直调' },
//     { name: '门店调拨', value: 'TRANSFER' },
//   ],
//   canTwoSellType: [
//     { name: '是', value: true },
//     { name: '否', value: false },
//   ],
//   stockCheckOrderRangeType: [
//     {
//       name: '单品盘点',
//       value: 'ITEM',
//     },
//     {
//       name: '全场盘点',
//       value: 'ALL',
//     },
//     {
//       name: '库存记录盘点',
//       value: 'STOCK',
//     },
//     {
//       name: '负库存盘点',
//       value: 'NEGATIVESTOCK',
//     },
//     {
//       name: '零库存盘点',
//       value: 'ZEROSTOCK',
//     },
//     /*{
//       name: '类别盘点',
//       value: "CATEGORY"
//     },
//     {
//       name: '部门盘点',
//       value: "DEPT"
//     },

//     {
//       name: '库存记录盘点',
//       value: "STOCK"
//     },
//      */
//   ],
//   stockCheckOrderUnitType: [
//     {
//       name: '基本单位',
//       value: 'BASIC',
//     },
//     {
//       name: '配送单位',
//       value: 'DELIVERY',
//     },
//     {
//       name: '采购单位',
//       value: 'PURCHASE',
//     },
//     {
//       name: '库存单位',
//       value: 'STOCK',
//     },
//     {
//       name: '批发单位',
//       value: 'WHOLESALE',
//     },
//   ],
//   // 物流方式 value 英文命名
//   logisticsType: [
//     // { name: '上门取件', value: '上门取件' }, // TODO 2025-04-22 暂时关闭上门取件
//     { name: '自行寄回', value: '自行寄回' },
//   ],
// }
