#!/bin/bash
set -euo pipefail

shopt -s nullglob
ENV_FILES=(.env.*)
shopt -u nullglob

if [ ${#ENV_FILES[@]} -eq 0 ]; then
  echo "❌ 当前目录下没有找到任何 .env.* 文件"
  exit 1
fi

print_env_files() {
  echo "请选择要本地要使用的环境配置文件："
  for i in "${!ENV_FILES[@]}"; do
    printf "  [%d] %s\n" "$i" "${ENV_FILES[$i]}"
  done
}

print_env_files

while true; do
  read -rp "test(测试) staging(灰度) prod(正式) 输入编号（默认 0）: " SELECTED_INDEX
  SELECTED_INDEX="${SELECTED_INDEX:-0}"
  if [[ "$SELECTED_INDEX" =~ ^[0-9]+$ ]] && [ "$SELECTED_INDEX" -ge 0 ] && [ "$SELECTED_INDEX" -lt "${#ENV_FILES[@]}" ]; then
    break
  fi
  echo "❌ 输入不合法，请输入 0 ~ $(( ${#ENV_FILES[@]} - 1 )) 之间的数字"
done

SELECTED_ENV="${ENV_FILES[$SELECTED_INDEX]}"
echo "✅ 你选择的是: $SELECTED_ENV"

cp "$SELECTED_ENV" .env
echo "📋 已复制 $SELECTED_ENV 到 .env"

DST_XCCONFIG="ios/tmp.xcconfig"
> "$DST_XCCONFIG"

KEYS=("APP_NAME" "VERSION_NAME" "BUNDLE_ID")

for KEY in "${KEYS[@]}"; do
  VALUE=$(grep -v '^#' "$SELECTED_ENV" | grep -m1 -E "^$KEY=" | cut -d '=' -f 2-)
  VALUE=$(echo "$VALUE" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
  if [ -n "$VALUE" ]; then
    echo "$KEY = $VALUE" >> "$DST_XCCONFIG"
  else
    echo "⚠️  Warning: 未在 $SELECTED_ENV 中找到 $KEY"
  fi
done

echo "✅ xcconfig 已生成成功: $DST_XCCONFIG.请在xcode中继续运行"
