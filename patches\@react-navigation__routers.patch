diff --git a/lib/module/CommonActions.js b/lib/module/CommonActions.js
index b3f7cbbd4218bb83ff516c9f45b8b9bc4199a1a1..eedac1cdd7a974ba884af250f4f8de5cdec18356 100644
--- a/lib/module/CommonActions.js
+++ b/lib/module/CommonActions.js
@@ -17,7 +17,7 @@ export function navigate(...args) {
         name,
         params,
         merge: typeof options === 'boolean' ? options : options?.merge,
-        pop: options?.pop
+        pop: typeof options?.pop === 'boolean' ? options.pop : true
       }
     };
   } else {
diff --git a/src/CommonActions.tsx b/src/CommonActions.tsx
index f75298fa6c99042d4a98d8438c36364add64906e..e9816eb3820d6c793f6be0bd7bf1c85b65d0d63f 100644
--- a/src/CommonActions.tsx
+++ b/src/CommonActions.tsx
@@ -114,7 +114,7 @@ export function navigate(...args: any): Action {
         name,
         params,
         merge: typeof options === 'boolean' ? options : options?.merge,
-        pop: options?.pop,
+        pop: typeof options?.pop === 'boolean' ? options.pop : true,
       },
     };
   } else {
