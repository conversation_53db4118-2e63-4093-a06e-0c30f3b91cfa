declare interface UserLocationInfo {
  country: string;
  province: string;
  city: string;
  formattedAddress: string;
  district: string;
  citycode: string;
  adcode: string;
  street: string;
  number: string;
  poiName: string;
  aoiName: string;
  latitude: number;
  longitude: number;
}

declare type WithParamsNonReturnFunc<T> = (params: T) => void;

declare type NonReturnFunc = () => void;

declare interface BusinessDistrict {
  distance?: number;
  // fid
  id: number;
  // 公司id
  company_id: number;
  // 商圈名称
  name: string;
  // 商圈区域
  area_code: number;
  // 区域名称
  area_name: string;
  // 商圈状态
  state: Status;
  // 商圈规格id
  structure_id: number;
  // 商圈规格名称
  structure_name: string;
  // 标签
  label_names: string[];
  // 标签id
  label_ids: number[];
  // 商圈等级id
  // levels: number[];
  // 商圈等级名称
  level_name: string;
  // 商圈类型id
  type_ids: number[];
  // 商圈类型名称
  type_names: string[];
  // 同行品牌
  competitor: string;
  // 人口数（万元）
  population: number;
  // 可开门店数
  open_store: number;
  // 跟进人id
  head_id: number;
  // 跟进人
  head_by: string;
  // 开发进度
  schedule: DevelopStatus;
  // 开发进度名称
  schedule_name: string;
  // 范围
  area_range: string[];
  // 附件
  urls: string[];
  // 制单时间
  create_time: string;
  // 制单人
  create_by: string;
  // 更新时间
  update_time: string;
  // 更新人
  update_by: string;
  // 初评人
  first_audit_by: string;
  // 初评时间
  first_audit_time: string;
  // 复评人
  second_audit_by: string;
  // 复评时间
  second_audit_time: string;
  // 否决人
  reject_by: string;
  // 否决时间
  reject_time: string;
  // 自定义内容
  content: string;
  // 跟进人id
  follow_id: number;
  // 跟进人
  follow_by: string;
  files: FileItem[];
  memo: string;
}

declare interface PointPlan {
  store_valuation?: string;
  //点位位置
  address: string;
  invalid_time: string;
  invalid_by: string;
  //是否允许带看
  allow_look: boolean;
  //点位区域
  area_code: number;
  //区域名称
  area_name: string;
  //商圈id
  business_plan_id: number;
  //商圈名称
  business_plan_name: string;
  //公司id
  company_id: number;
  //是否竞争点位
  competition: boolean;
  //自定义内容
  content: {id: number; name: 'string'; details: object}[];
  //制单人
  create_by: string;
  //制单时间
  create_time: string;

  //制单人
  init_by: string;
  //制单时间
  init_time: string;
  //初评人
  first_audit_by: string;
  //初评时间
  first_audit_time: string;
  //跟进人
  follow_by: string;
  //跟进人id
  follow_id: number;
  //跟进状态,可用值:CLOSE,FINISH,FOLLOW,SHELVE,SUSTAIN
  follow_state: Follow_State;
  //详细地址
  full_address: string;
  //id
  id: number;
  //点位名称
  name: string;
  //否决人
  reject_by: string;
  //否决时间
  reject_time: string;
  //复评人
  second_audit_by: string;
  //复评时间
  second_audit_time: string;
  //点位状态,可用值:FIRST_AUDIT,INIT,REJECT,SECOND_AUDIT
  state: Status;
  //更新人
  update_by: string;
  //更新时间
  update_time: string;
  //等级
  store_level: string;
  //来源
  source: string;
  memo: string;
  /** 签约方式 */
  sign_type: Sign_Type;
  /** 是否签约 */
  sign: boolean;
  /** 文件 */
  files: FileItem[];
  /** 点位信息 */
  info?: {
    latitude?: number;
    longitude?: number;
    point_name?: string;
    keyword?: string;
  };
  /** 签约客户 */
  client_name?: string;
  /** 店铺分组 */
  group_number?: Number;
  /** 商圈可开门店数 */
  open_store?: Number;
  //跟进结果
  result?: string;
  // 截止时间
  deadline_date: string;
  // 是否超过180天
  outside: boolean;
  distance?: number;
}

declare interface BusinessDistrict {
  distance?: number;
  // fid
  id: number;
  // 公司id
  company_id: number;
  // 商圈名称
  name: string;
  // 商圈区域
  area_code: number;
  // 区域名称
  area_name: string;
  // 商圈状态
  state: Status;
  // 商圈规格id
  structure_id: number;
  // 商圈规格名称
  structure_name: string;
  // 标签
  label_names: string[];
  // 标签id
  label_ids: number[];
  // 商圈等级id
  // levels: number[];
  // 商圈等级名称
  level_name: string;
  // 商圈类型id
  type_ids: number[];
  // 商圈类型名称
  type_names: string[];
  // 同行品牌
  competitor: string;
  // 人口数（万元）
  population: number;
  // 可开门店数
  open_store: number;
  // 跟进人id
  head_id: number;
  // 跟进人
  head_by: string;
  // 开发进度
  schedule: DevelopStatus;
  // 开发进度名称
  schedule_name: string;
  // 范围
  area_range: string[];
  // 附件
  urls: string[];
  // 制单时间
  create_time: string;
  // 制单人
  create_by: string;
  // 更新时间
  update_time: string;
  // 更新人
  update_by: string;
  // 初评人
  first_audit_by: string;
  // 初评时间
  first_audit_time: string;
  // 复评人
  second_audit_by: string;
  // 复评时间
  second_audit_time: string;
  // 否决人
  reject_by: string;
  // 否决时间
  reject_time: string;
  // 自定义内容
  content: string;
  // 跟进人id
  follow_id: number;
  // 跟进人
  follow_by: string;
  files: FileItem[];
  memo: string;
}

declare interface ClueTemplate {
  content: {
    title?: string;
    content: string;
    details: ClueTemplateItem[];
  };
  api_name: string;
  details: ClueTemplateItem[];
  acquiesce: boolean;
  type: ModuleType;
  enable: boolean;
  name: string;
  id: number;
  store_apply_required?: boolean;
  store_plan_required?: boolean;
}

export enum ComponentType {
  /** 文本 */
  TEXT = 'TEXT',
  /** 数字 */
  NUMBER = 'NUMBER',
  /** 单选 */
  SINGLE_CHOICE = 'SINGLE_CHOICE',
  // 多选
  /** 多选 */

  MULTI_CHOICE = 'MULTI_CHOICE',
  // 图片
  /** 图片 */

  IMAGE = 'IMAGE',
  // 视频
  /** 视频 */

  VIDEO = 'VIDEO',
  // 日期
  /** 日期 */

  DATE = 'DATE',
  // DATE_TIME = 'DATE_TIME',
  // 长文本
  /** 长文本 */

  LONG_TEXT = 'LONG_TEXT',
  // 金额
  /** 金额 */

  AMOUNT = 'AMOUNT',
  // 下拉单选
  /** 下拉单选 */

  SINGLE_RADIO = 'SINGLE_RADIO',

  // 表单
  TABLE = 'TABLE',
  // 点位地图
  /** 点位地图 */

  LOCATION = 'LOCATION',
  // 关联单选
  /** 关联单选 */
  DATA_ASSOCIATION = 'DATA_ASSOCIATION',
}

export enum ConditionRelation {
  //等于
  EQUALS = 'EQUALS',
  //不等于
  NOT_EQUALS = 'NOT_EQUALS',
  //包含
  CONTAINS = 'CONTAINS',
  //不包含
  NOT_CONTAINS = 'NOT_CONTAINS',
  //为空
  IS_NULL = 'IS_NULL',
  //不为空
  NOT_NULL = 'NOT_NULL',
  //大于
  GREATER_THAN = 'GREATER_THAN',
  //大于等于
  GREATER_THAN_OR_EQUALS = 'GREATER_THAN_OR_EQUALS',
  //小于
  LESS_THAN = 'LESS_THAN',
  //小于等于
  LESS_THAN_OR_EQUALS = 'LESS_THAN_OR_EQUALS',
  //属于
  IN = 'IN',
  //不属于
  NOT_IN = 'NOT_IN',
}
export interface ConditionItem {
  field: string;
  operator: ConditionRelation;
  value: string;
  parent_id: number;
  xkey: ComponentType;
  originKey: ComponentType;
}

export interface ConditionGroupItem {
  id: number;
  name: string;
  type: string;
  conditions: ConditionItem[];
}

export interface ClueTemplateItem {
  format?: string;
  max?: number;
  min?: number;
  min_time?: number;
  max_time?: number;
  required?: boolean;
  sub_titles?: string[];
  details?: ClueTemplateItem[];
  title: string;
  id: string;
  memo?: string;
  component_type: ComponentType;
  display?: boolean;
  edit?: boolean;
  condition_display?: boolean;
  uppercase_number_display?: boolean;
  thousands_separator_display?: boolean;
  condition_groups: ConditionGroupItem[];
  init_required?: boolean;
  store_apply_required?: boolean;
  examples?: {
    files: [FilesItem];
    memo: string;
  }[];
}

declare interface PointPlan {}

declare interface FileItem {
  ref_id: string;
  file_type: string;
  suffix_type: string;
  detail_num: number;
  name: string;
  id: number;
  url: string;
  png_url: string;
  ref_sub_type: string;
  ref_img: string;
}
