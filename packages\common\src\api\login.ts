import { ErpHttp } from '@xlb/common/src/services/lib/erphttpnew'
import Config from "react-native-config";

// 获取账户验证码
const getCheckCode = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.user.checkcode.send', params)
}
// 验证码登录验证码
const getLoginCode = (params: any) => {
  // 灰度换erp-mdm
  const prefix = Config.ENV === 'STAGING' ? '/erp-mdm' : '/erp'
  return ErpHttp.post<CommonResponse>(`${prefix}/hxl.erp.user.logincode.send`, params)
}
// 验证码登录验证码
const getAccountList = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.account.codelogin.user.find', params)
}
// 验证码登录验证码
const verifyQrCode = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.user.app.qrcode.verify', params)
}
// 密码登录
const passLogin = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.user.login', params)
}
// 密码登录
const passwordLogin = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.account.pwd.user.find', params)
}

// 获取语言验证码
const getVoiceCode = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.user.voicecode.send', params)
}

// 获取重置密码验证码
const getResetpwdcode = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.user.resetpwdcode.send', params)
}

// 获取重置密码验证码
const getResetpwdvoicecode = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.user.resetpwdvoicecode.send', params)
}
// 获取重置获取用户列表
const getResetpwd = (params: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.user.resetpwd.find', params)
}
// 更新密码
const resetpwdConfirm = (params: any, config: any) => {
  return ErpHttp.post<CommonResponse>('/erp-mdm/hxl.erp.user.resetpwd.confirm', params, config)
}

export const LoginApi = {
  getCheckCode,
  getLoginCode,
  getAccountList,
  getVoiceCode,
  verifyQrCode,
  passLogin,
  passwordLogin,
  getResetpwdcode,
  getResetpwdvoicecode,
  getResetpwd,
  resetpwdConfirm,
}
