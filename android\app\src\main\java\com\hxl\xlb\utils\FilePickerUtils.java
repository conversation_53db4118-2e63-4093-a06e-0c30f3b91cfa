package com.hxl.xlb.utils;

import android.Manifest;
import android.app.Activity;
import android.content.ClipData;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.XXPermissions;
import com.hjq.permissions.permission.PermissionLists;
import com.hjq.permissions.permission.base.IPermission;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FilePickerUtils {

    public interface FilePickerCallback {
        void onFilePicked(List<Map<String,String>> filePaths);
        void onError(String errorMsg);
    }

    private final Context context;
    private final Activity activity;
    private final ActivityResultLauncher<Intent> activityResultLauncher;
    private FilePickerCallback callback;
    private String currentPhotoPath;
    private String currentPhotoName;

    private static final int PERMISSION_REQUEST_CODE = 101;

    public FilePickerUtils(Context context, Activity activity, ActivityResultLauncher<Intent> launcher) {
        this.context = context;
        this.activity = activity;
        this.activityResultLauncher = launcher;
    }

    public void pickFile(String option,boolean isMutil, FilePickerCallback callback) {
        this.callback = callback;

        List<IPermission> p = new ArrayList<>();
        p.add(PermissionLists.getCameraPermission());
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            p.add(PermissionLists.getManageExternalStoragePermission());
        } else {
            p.add(PermissionLists.getReadExternalStoragePermission());
            p.add(PermissionLists.getWriteExternalStoragePermission());
        }

        XXPermissions.with(context).permissions(p).request(new OnPermissionCallback(){

            @Override
            public void onGranted(@NonNull List<IPermission> permissions, boolean allGranted) {

                switch (option) {
                    case "拍照":
                        takePhoto();
                        break;
                    case "从相册中选择":
                        chooseFromGallery(isMutil);
                        break;
                    case "从文件中选择":
                        chooseFromFileManager();
                        break;
                    default:
                        callback.onError("不支持的选项");
                        break;
                }
            }
        });



    }



    private void takePhoto() {
        Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        if (takePictureIntent.resolveActivity(context.getPackageManager()) != null) {
            File photoFile = null;
            try {
                photoFile = createImageFile();
            } catch (IOException ex) {
                callback.onError("创建图片文件失败: " + ex.getMessage());
                return;
            }

            if (photoFile != null) {
                Uri photoURI = FileProvider.getUriForFile(context,
                        context.getPackageName() + ".fileprovider",
                        photoFile);
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI);
                activityResultLauncher.launch(takePictureIntent);
            }
        } else {
            callback.onError("无法启动相机应用");
        }
    }

    private void chooseFromGallery(boolean isMutil) {
      if (isMutil){//多选
          Intent galleryIntent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
          galleryIntent.setType("image/*"); // 仅允许选择图片类型
          galleryIntent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true); // 支持多选
          activityResultLauncher.launch(galleryIntent);

      } else {
          Intent galleryIntent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
          galleryIntent.setType("image/*"); // 仅允许选择图片类型
          activityResultLauncher.launch(galleryIntent);
      }
    }



    private void chooseFromFileManager() {
//        "text/plain",image/*
        Intent fileIntent = new Intent(Intent.ACTION_GET_CONTENT);
        fileIntent.setType("*/*");
        String[] mimeTypes = {"application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation"};
        fileIntent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes);
        fileIntent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true); // 支持多选
        activityResultLauncher.launch(fileIntent);



    }

    private File createImageFile() throws IOException {
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        File image = File.createTempFile(imageFileName, ".jpg", storageDir);
        currentPhotoPath = image.getAbsolutePath();
        currentPhotoName = image.getName();

        return image;
    }

    public void handleActivityResult(ActivityResult result) {
        if (result.getResultCode() == Activity.RESULT_OK) {
            Intent data = result.getData();

            if (data == null || data.getData() == null && data.getClipData() == null) { // For camera use case
                Log.d("TAG","拍照的文件路径"+currentPhotoPath);
                String filePath = currentPhotoPath;
                String fileName =  currentPhotoName;
                String fileType = "image/jpeg";
                if (filePath != null && fileName != null && fileType != null) {
                    List<Map<String,String>> filePaths = new ArrayList<>();
                    Map<String, String> fileDetails = new HashMap<>();
                    fileDetails.put("filePath", filePath);
                    fileDetails.put("fileName", fileName);
                    fileDetails.put("fileType", fileType);
                    filePaths.add(fileDetails);
                    callback.onFilePicked(filePaths);
                } else {
                    callback.onError("无法解析文件路径或文件名或文件类型");
                }

            } else { // For gallery or file manager use case

//                多选
                ClipData clipData = data.getClipData();
                if (clipData != null)
                {

                    List<Map<String,String>> filePaths = new ArrayList<>();
                    for (int i = 0; i < clipData.getItemCount(); i++) {
                        Uri selectedUri = clipData.getItemAt(i).getUri();
                        String filePath = PathUtils.getPathFromUri(context, selectedUri);
                        String fileName =  PathUtils.getFileName(context, selectedUri);
                        String fileType =  PathUtils.getFileType(context, selectedUri);
                        if (filePath != null && fileName != null && fileType != null) {
                            Map<String, String> fileDetails = new HashMap<>();
                            fileDetails.put("filePath", filePath);
                            fileDetails.put("fileName", fileName);
                            fileDetails.put("fileType", fileType);
                            filePaths.add(fileDetails);
                        } else {
                            callback.onError("无法解析文件路径");
                            return;
                        }
                    }

                    callback.onFilePicked(filePaths);
                }

                //      单选
                Uri selectedUri = data.getData();
                if (selectedUri != null){

                    String filePath;
                    String fileName = PathUtils.getFileName(context, selectedUri);
                    String fileType = PathUtils.getFileType(context, selectedUri);
                    if (fileType.startsWith("image/")){
                        filePath = PathUtils.getPathFromUri(context, selectedUri);
                    } else  {
                        filePath = PathUtils.getFileFromUri(context, selectedUri);
                    }

                    Log.i("jjjj","文件路径-----" + filePath + "文件名=====" + fileName + "====xin===");
                    if (filePath != null && fileName != null && fileType != null) {
                        List<Map<String,String>> filePaths = new ArrayList<>();
                        Map<String, String> fileDetails = new HashMap<>();
                        fileDetails.put("filePath", filePath);
                        fileDetails.put("fileName", fileName);
                        fileDetails.put("fileType", fileType);
                        filePaths.add(fileDetails);
                        callback.onFilePicked(filePaths);
                    } else {
                        callback.onError("无法解析文件路径或文件名或文件类型");
                    }
                }

            }
        } else {
            callback.onError("操作已取消");
        }
    }
}


