import React, {useEffect, useState} from 'react';
import type {FC} from 'react';
import {View, Text, FlatList, StyleSheet} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {TOKEN, XlbCard, XlbText} from '@xlb/components-rn';
import Header from '@xlb/common/src/xlb-components-new/Header';
import {normalize} from '@xlb/components-rn/styles';
import {EmptyBox, XLoading} from '@xlb/common/src/components';
import XlbIcon from '@xlb/common/src/assets/iconfont';
import {Tag, Space} from '@fruits-chain/react-native-xiaoshu';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttpnew';
import {authModel} from '../../models/auth';
import dayjs from 'dayjs';
// import { businessDeptListType } from '@xlb/business-erp/src/config/utils'
const Index: FC = () => {
  const navigation = useNavigation();
  const system = !!authModel?.state?.userInfos.supplier;
  const [noticeDate, setNoticeDate] = useState<any>([]); //公告信息
  const [loading, setLoading] = useState(false);
  const [keyword, setKeyword] = useState('');
  // 获取用户公告
  const getUserNotice = async (type: any) => {
    setLoading(true);
    const res = await ErpHttp.post<any>(
      `/${type}/hxl.${type}.usernotice.page`,
      {
        order_mode: 3,
        keyword: keyword,
      },
    );
    if (res?.code == 0) {
      setNoticeDate(res.data.content);
    }
    setLoading(false);
  };
  useEffect(() => {
    if (keyword) {
      getUserNotice(`${system ? 'scm' : 'erp'}`);
    } else {
      setNoticeDate([]);
    }
  }, [keyword]);
  // useEffect(() => {
  //   // getUserNotice(`${system ? 'scm' : 'erp'}`)
  //   return () => {}
  // }, [])
  return (
    <View style={{flex: 1}}>
      <Header
        title="公告"
        hasFilter
        inputFilterPlaceholder="请输入公告标题/内容"
        onInputChange={val => {
          setKeyword(val);
        }}
      />

      <XLoading loading={loading} />
      <FlatList
        style={{marginBottom: normalize(8)}}
        data={noticeDate}
        renderItem={({item, index}: {item: any; index: number}) => {
          let {
            create_time,
            title,
            type,
            read,
            digest,
            id,
            read_time,
            create_business_dept,
            file_count,
            level,
          } = item;
          return (
            <XlbCard
              title={
                <View style={{alignItems: 'center', flexDirection: 'row'}}>
                  {!read && (
                    <View
                      style={{
                        backgroundColor: TOKEN.red_10,
                        width: normalize(6),
                        height: normalize(6),
                        borderRadius: normalize(6),
                        marginRight: TOKEN.space_1,
                      }}></View>
                  )}

                  <XlbText font_size_6 semiBold>
                    {title}
                  </XlbText>
                  {file_count ? (
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <XlbIcon
                        name={'fujian'}
                        size={normalize(16)}
                        color={'rgba(97, 99, 103, 1)'}></XlbIcon>
                      <Text style={{fontSize: 12, color: '#86909C'}}>
                        {file_count}
                      </Text>
                    </View>
                  ) : null}
                </View>
              }
              onPress={() => {
                navigation.navigate('RemoteAppBase.Details', {id: id});
              }}
              footer={
                <>
                  <Space
                    direction="horizontal"
                    align="center"
                    justify="space-between">
                    <Space
                      direction="horizontal"
                      align="center"
                      gapHorizontal={2}>
                      <Tag type="ghost" color={TOKEN.primary_10}>
                        {type}
                      </Tag>
                      {level == 0 && (
                        <Tag
                          type="ghost"
                          color={'#FF2121'}
                          style={{marginLeft: 8}}>
                          {'置顶'}
                        </Tag>
                      )}

                      <Text
                        style={[styles['item-footer__text1'], {marginLeft: 8}]}>
                        {/* {create_business_dept && businessDeptListType[create_business_dept]} */}
                      </Text>
                    </Space>
                    <Space
                      direction="horizontal"
                      align="center"
                      justify="space-between">
                      <Text style={styles['item-footer__text2']}>
                        {dayjs(create_time).format('YYYY-MM-DD')}
                      </Text>
                    </Space>
                  </Space>
                </>
              }>
              <View
                style={{
                  marginTop: normalize(4),
                }}>
                <Text style={styles['item-text']} numberOfLines={3}>
                  {digest}
                </Text>
              </View>
            </XlbCard>
          );
        }}
        ListEmptyComponent={() => <EmptyBox></EmptyBox>}></FlatList>
    </View>
  );
};
const styles = StyleSheet.create({
  'operate-warp': {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: TOKEN.space_3,
    paddingVertical: TOKEN.space_2,
  },
  'operate-hd': {
    flexDirection: 'row',
    alignItems: 'center',
  },
  'select-icon': {
    position: 'absolute',
    right: normalize(12),
    top: '50%',
    marginTop: normalize(4),
  },
  'item-footer': {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  'item-footer__text1': {
    fontSize: normalize(12),
    color: 'rgba(30,33,38,0.45)',
  },
  'item-footer__text2': {
    fontSize: normalize(12),
    color: 'rgba(30, 33, 38, 0.45)',
  },
  'item-text': {
    color: 'rgba(30, 33, 38, 0.70)',
    fontSize: normalize(13),
    marginTop: normalize(4),
  },
});
export default Index;
