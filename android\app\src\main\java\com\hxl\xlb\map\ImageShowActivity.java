package com.hxl.xlb.map;

import android.content.Context;
import android.graphics.Bitmap;
import android.media.Image;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;
import androidx.media3.common.MediaItem;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.ui.PlayerView;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.hxl.xlb.R;
import com.hxl.xlb.utils.DownloadPhotoUtil;
import com.hxl.xlb.widget.HackyViewPager;
import com.squareup.picasso.Callback;
import com.squareup.picasso.Picasso;
import com.xlb.mvplibrary.base.BaseActivity;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import io.reactivex.annotations.NonNull;
import io.reactivex.annotations.Nullable;
import uk.co.senab.photoview.PhotoView;
import uk.co.senab.photoview.PhotoViewAttacher;

public class ImageShowActivity extends BaseActivity implements View.OnClickListener {

    private static final int PERMISSION_REQUEST_CODE = 1;
    private File downloadFile;
    private Context mContext;
    ViewPager mViewPager;
    private TextView numberTv;
    private PlayerView videoView;
    private ExoPlayer player;
    private int imagePosition;
    private String videoUrl;
    private boolean videoFlag;
    private int total;
    private List<String> imgUrls = new ArrayList<>();

    @Override
    public void initData(Bundle savedInstanceState) {
        mContext = this;
        init();
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_image_show;
    }

    /**
     * 对VIewPager进行初始化.
     */
    private void init() {
        mViewPager = (HackyViewPager) findViewById(R.id.iv_photo);
        numberTv = (TextView) findViewById(R.id.number_tv);
        videoView = (PlayerView) findViewById(R.id.videoView);
        player = new ExoPlayer.Builder(this).build();


        findViewById(R.id.back_ll).setOnClickListener(this);
        findViewById(R.id.save_tv).setOnClickListener(this);
        imagePosition = getIntent().getIntExtra("position", 0);
        videoUrl = getIntent().getStringExtra("videoUrl");
        videoFlag = getIntent().getBooleanExtra("videoFlag", false);
        imgUrls = getIntent().getStringArrayListExtra("imgUrls");
        if (videoFlag) {
            Log.i("TAG", "-----------videoUrl-------------------" + videoUrl);
            // 播放网络视频
//            getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);   //全屏
//            try {
//                play_rtsp();
//            } catch (IOException e){
//                e.printStackTrace();
//            }// 尝试调用 play_rtsp(); 方法来播放RTSP流，并捕获可能抛出的 IOException。
            if (!TextUtils.isEmpty(videoUrl)) {
                videoView.setPlayer(player);
                Uri uri = Uri.parse(videoUrl); // 替换为您的视频URL
                MediaItem mediaItem = MediaItem.fromUri(uri);
                player.setMediaItem(mediaItem);
                player.prepare();
                player.play();

                mViewPager.setVisibility(View.GONE);
                videoView.setVisibility(View.VISIBLE);
            }

//// 设置暂停和开始
//            videoView.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
//                @Override
//                public void onCompletion(MediaPlayer mp) {
//                    // 视频播放完成后的操作
//                }
//            });
//            // 添加媒体控制器（可选），它提供了播放/暂停、音量控制等功能
//            MediaController mediaController1 = new MediaController(this);
//            videoView1.setMediaController(mediaController1);
//            // 监听视频准备播放事件（可选）
//            videoView1.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
//                @Override
//                public void onPrepared(MediaPlayer mp) {
//                    // 视频准备完成后可以执行的操作，例如开始播放
//                }
//            });

        }

        if (null != imgUrls && !imgUrls.isEmpty()) {
            Log.i("TAG", "-----------imgUrls-------size------------" + imgUrls.size());
            mViewPager.setVisibility(View.VISIBLE);
            videoView.setVisibility(View.GONE);
            mViewPager.setAdapter(new SamplePagerAdapter());
            //设置默认的坐标，实际情况不要写死，只需点击哪张图片的position，并传过来接受就可以了，就默认显示点击图片的大图。
            mViewPager.setCurrentItem(imagePosition);

            if (imgUrls.size() > 1) {
                total = imgUrls.size();
                if (imagePosition > imgUrls.size()) {
                    numberTv.setText(total + "/" + total);
                } else {
                    numberTv.setText((imagePosition + 1) + "/" + total);
                }
                numberTv.setVisibility(View.VISIBLE);
            } else {
                numberTv.setVisibility(View.GONE);
            }

            mViewPager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

                }

                @Override
                public void onPageSelected(int position) {
                    imagePosition = position;
                    Log.i("TAG", "imagePosition: ---onPageSelected--" + imagePosition);
                    numberTv.setText(position + 1 + "/" + total);
                }

                @Override
                public void onPageScrollStateChanged(int state) {

                }
            });
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.back_ll) {
            finish();
        } else if (v.getId() == R.id.save_tv) {
            if(imgUrls!=null){
                downloadImage(imgUrls.get(imagePosition));
            }else{
                downloadMP4(videoUrl );
            }
        }

    }


    class SamplePagerAdapter extends PagerAdapter {
        //这里暂时写死了，实际情况中要从服务端获取图片地址结合，传过来

        @Override
        public int getCount() {
            return imgUrls.size();
        }

        @Override
        public View instantiateItem(ViewGroup container, int position) {
            PhotoView photoView = new PhotoView(container.getContext());
            final PhotoViewAttacher attacher = new PhotoViewAttacher(photoView);
            Picasso.with(container.getContext())
                    .load(imgUrls.get(position))
                    .into(photoView, new Callback() {
                        @Override
                        public void onSuccess() {
                            attacher.update();
                        }

                        @Override
                        public void onError() {

                        }
                    });

            container.addView(photoView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

            return photoView;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView((View) object);
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }

    }

    private void downloadImage(String imageUrl) {
        Glide.with(this)
                .asBitmap()
                .load(imageUrl)
                .into(new SimpleTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                        // saveImage(resource);
                        DownloadPhotoUtil.requestPermission(ImageShowActivity.this, resource);
                    }
                });

    }

    private ExecutorService executor = Executors.newSingleThreadExecutor();
    private Future<?> taskFuture;

    private void downloadMP4(String videoUrl) {

        ImageShowActivity.this.getLifecycle().addObserver(new LifecycleObserver(){

            @OnLifecycleEvent(Lifecycle.Event.ON_START)
            void onStart() {
                // 启动任务
                taskFuture = executor.submit(() -> {
                    try {
                        DownloadPhotoUtil.requestPermissionMP4(ImageShowActivity.this, videoUrl, new DownloadPhotoUtil.DownloadListener() {
                            @Override
                            public void onProgress(int downloadedBytes, int totalBytes) {
                                Log.e("onProgress","downloadedBytes"+downloadedBytes);
                            }

                            @Override
                            public void onComplete(String filePath) {
                                new Handler(Looper.getMainLooper()).post(() -> {
                                    Toast.makeText(ImageShowActivity.this,"下载成功："+filePath,Toast.LENGTH_LONG).show();
                                });

                            }

                            @Override
                            public void onError(String errorMessage) {
                                new Handler(Looper.getMainLooper()).post(() -> {
//                                    Toast.makeText(ImageShowActivity.this,"下载失败："+errorMessage,Toast.LENGTH_LONG).show();
                                });

                            }
                        });


                    } catch (Exception e) {
                        Log.e("ImageShowActivity", "Error: " + e.getMessage());
                    }
                });
            }

            @OnLifecycleEvent(Lifecycle.Event.ON_STOP)
            void onStop() {
                // 取消任务（如果还在运行）
                if (taskFuture != null && !taskFuture.isDone()) {
                    taskFuture.cancel(true);
                }
            }

        });




    }

//    private void play_rtsp() throws IOException {
//        String videoUrl = "视频地址URL" ;
//        Uri uri = Uri.parse( videoUrl );
//
//        videoView.setVideoURI(uri);
//        videoView.requestFocus();
//    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (null != player) {
            player.release();
        }
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onPause() {
        super.onPause();
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        executor.shutdown(); // 关闭 Executor
    }


}

