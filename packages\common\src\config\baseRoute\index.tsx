export const baseRoute = [
  {
    moduleName: '基础webView',
    name: '基础业务 webView',
    route: 'BaseWebView',
    hiddenMenu: true,
  },
  {
    moduleName: '关于版本',
    name: '关于版本',
    route: 'AboutVersion',
    hiddenMenu: true,
  },
  {
    moduleName: '用户信息',
    name: '用户信息-编辑',
    route: 'UserInfo',
    hiddenMenu: true,
  },
  {
    moduleName: '我的',
    name: '我的设置',
    route: 'MinSetting',
    hiddenMenu: true,
  },
  {
    moduleName: '我的',
    name: '切换账户',
    route: 'AccountListView',
    hiddenMenu: true,
  },
  {
    moduleName: '我的',
    name: '切换主题',
    route: 'SwitchTheme',
    hiddenMenu: true,
  },
  {
    moduleName: '我的',
    name: '客服与帮助',
    route: 'HelpPage',
    hiddenMenu: true,
  },
  {
    moduleName: '我的',
    name: 'H5调试页面',
    route: 'H5Page',
    hiddenMenu: true,
  },
  {
    moduleName: '我的',
    name: '验证手机号',
    route: 'VerifyPhone',
    hiddenMenu: true,
  },
  {
    moduleName: '我的',
    name: '修改密码',
    route: 'ModifyPassword',
    hiddenMenu: true,
  },
  {
    moduleName: '我的',
    name: '修改手机号',
    route: 'ModifyPhone',
    hiddenMenu: true,
  },
  {
    moduleName: '我的',
    name: '修改成功',
    route: 'SuccessPage',
    hiddenMenu: true,
  },
  {
    moduleName: '选择商品',
    name: '选择商品',
    route: 'DeprecatedSelectGoods',
    hiddenMenu: true,
  },
  {
    moduleName: '选择商品详情',
    name: '选择商品详情',
    route: 'DeprecatedSelectGoodsDetail',
    hiddenMenu: true,
  },
  {
    moduleName: '选择商品详情',
    name: '选择商品详情',
    route: 'DeprecatedSelectGoodsDetails',
    hiddenMenu: true,
  },
  {
    moduleName: '选择商品',
    name: '选择商品',
    route: 'DeprecatedNewSelectGoods',
    hiddenMenu: true,
  },
  {
    moduleName: '商品详情',
    name: '商品详情',
    route: 'DeprecatedNewSelectGoodsDetail',
    hiddenMenu: true,
  },
  {
    moduleName: '商品详情',
    name: '商品详情',
    route: 'DeprecatedNewSelectGoodsDetails',
    hiddenMenu: true,
  },
  {
    moduleName: '选择门店',
    name: '选择门店',
    route: 'DeprecatedSelectStoreH5',
    hiddenMenu: true,
  },
  {
    moduleName: '选择门店',
    name: '选择门店',
    route: 'DeprecatedSelectStore',
    hiddenMenu: true,
  },
  {
    moduleName: '选择门店',
    name: '选择门店',
    route: 'DeprecatedSelectStoreNew',
    hiddenMenu: true,
  },
  {
    moduleName: '选择门店区域',
    name: '选择门店区域',
    route: 'DeprecatedSelectStoreArea',
    hiddenMenu: true,
  },
  {
    moduleName: '选择门店标签',
    name: '选择门店标签',
    route: 'DeprecatedSelectStoreLabel',
    hiddenMenu: true,
  },
  {
    moduleName: '选择仓库',
    name: '选择仓库',
    route: 'DeprecatedSelectStoreHouse',
    hiddenMenu: true,
  },
  {
    moduleName: '选择仓库',
    name: '选择仓库',
    route: 'DeprecatedSelectStoreHouseNew',
    hiddenMenu: true,
  },
  {
    moduleName: '选择供应商',
    name: '选择供应商',
    route: 'DeprecatedSelectSupplier',
    hiddenMenu: true,
  },
  {
    moduleName: '选择分类',
    name: '选择分类',
    route: 'DeprecatedSelectCategory',
    hiddenMenu: true,
  },
  {
    // SelectGoods/NewSelectGoods -> Details 里面跳转用到， 具体作用不详
    moduleName: '选择商品',
    name: '选择商品',
    route: 'DeprecatedSelectGoodsData',
    hiddenMenu: true,
  },
  {
    moduleName: '首页',
    name: '公告',
    route: 'Notice',
    hiddenMenu: true,
  },
  {
    moduleName: '首页',
    name: '公告详情',
    route: 'Details',
    hiddenMenu: true,
  },
  {
    moduleName: '首页',
    name: '公告搜索',
    route: 'NoticeSearch',
    hiddenMenu: true,
  },
];
