import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'
import { ErpHttp as BaseErpHttp } from '@xlb/common/src/services/lib/erphttp'

type Api =
  /* 门店补货商品详情 */
  | 'requestorder.app.item.page'
  /* 配送参数 */
  | 'deliveryparam.read'

const server = async (name: Api, param?: any, config?: any) => {
  return await ErpHttp.post<any>(`/erp-mdm/hxl.erp.${name}`, param, config)
}

export default server
