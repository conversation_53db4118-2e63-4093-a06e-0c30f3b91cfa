import {PermissionsAndroid, Platform, ToastAndroid} from 'react-native'
// import CameraR<PERSON> from '@react-native-community/cameraroll'
import {CameraRoll} from '@react-native-camera-roll/camera-roll'
import Toast from 'react-native-root-toast'
import RN<PERSON><PERSON>bUtil from 'react-native-blob-util'
import last from 'lodash/last'

async function hasAndroidPermission() {
  const getCheckPermissionPromise = async () => {
    if (Number(Platform.Version) >= 33) {
      const res = await Promise.all([PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE)])
      if (!res[0]) {
        try {
          const r = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE)
          if (r === 'granted') return true
          return false
        } catch (error) {
          console.error('requestPermissions: [ERROR] =>', error);
        }
      }
      return true
    } else {
      return PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE)
    }
  }

  const hasPermission = await getCheckPermissionPromise()
  if (hasPermission) {
    return true
  }
  // const getRequestPermissionPromise = () => {
  //   if (Number(Platform.Version) >= 33) {
  //     return PermissionsAndroid.requestMultiple([PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES, PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO]).then(
  //       (statuses) =>
  //         statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES] === PermissionsAndroid.RESULTS.GRANTED &&
  //         statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO] === PermissionsAndroid.RESULTS.GRANTED
  //     )
  //   } else {
  //     console.log(444)
  //     return PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE).then((status) => status === PermissionsAndroid.RESULTS.GRANTED)
  //   }
  // }

  // return await getRequestPermissionPromise()
}

const isLocalFile = (path: string) => {
  const localFilePrefix = ['file://', '/']
  for (const prefix of localFilePrefix) {
    if (path.startsWith(prefix)) {
      return true
    }
  }
  return false
}

const saveBase64Image = (imageUrl: string) => {
  const fs = RNBlobUtil.fs

  const splitData = imageUrl.split(',')
  const contentType = splitData[0]?.match?.(/:(.*?);/)[1]
  const fileExtension = contentType?.split?.('/')[1]
  const imageName = `${Date.now()}.${fileExtension}`
  const base64Image = splitData[1]

  const path = fs.dirs.CacheDir + '/' + imageName

  fs.writeFile(path, base64Image, 'base64')
    .then(() => {
      if (Platform.OS === 'android') {
        CameraRoll.save(path, { type: 'photo' })
          .then(() => {
            ToastAndroid.show('图片已经成功保存到相册', ToastAndroid.SHORT)
          })
          .catch(() => {
            ToastAndroid.show('图片保存失败', ToastAndroid.SHORT)
          })
      } else {
        CameraRoll.save(path, { type: 'photo' })
          .then(() => {
            Toast.show('图片已经成功保存到相册')
          })
          .catch(() => {
            Toast.show('图片保存失败')
          })
      }
    })
    .catch((error) => {
      console.error('Error writing file:', error)
    })
}

export const savePicture = async (imageUrl: string) => {
  if (Platform.OS === 'android' && !(await hasAndroidPermission())) {
    Toast.show('暂无权限,请开启系统权限!')
    return
  }

  const base64Reg = /^data:image\/(.+);base64,/

  if (base64Reg.test(imageUrl)) {
    saveBase64Image(imageUrl)

    // saveBase64Image(imageUrl)
  } else if (!isLocalFile(imageUrl)) {
    const pathName = last(imageUrl.split('/'))!
    const dirs = RNBlobUtil.fs.dirs
    RNBlobUtil.config({
      path: dirs.DocumentDir + `/${pathName}`,
    })
      .fetch('GET', imageUrl)
      .then(async (res) => {
        const path = res.path()

        if (Platform.OS === 'android') {
          CameraRoll.save(`file://${path}`, { type: 'photo' })
            .then(() => {
              ToastAndroid.show('图片已经成功保存到相册', ToastAndroid.SHORT)
            })
            .catch(() => {
              ToastAndroid.show('图片保存失败', ToastAndroid.SHORT)
            })
        } else {
          CameraRoll.save(path, { type: 'photo' })
            .then(() => {
              Toast.show('图片已经成功保存到相册')
            })
            .catch(() => {
              Toast.show('图片保存失败')
            })
        }
      })
  } else {
    CameraRoll.saveToCameraRoll(imageUrl, 'photo')
      .then(() => {
        ToastAndroid.show('图片已经成功保存到相册', ToastAndroid.SHORT)
      })
      .catch(() => {
        ToastAndroid.show('图片保存失败', ToastAndroid.SHORT)
      })
  }
}
