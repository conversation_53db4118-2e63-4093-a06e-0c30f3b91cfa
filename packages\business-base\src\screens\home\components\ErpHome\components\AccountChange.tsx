import {
  BottomUp,
  commonStyles,
  cs,
  Space,
  XIcon,
  XText,
} from '@xlb/common/src/components';
import {
  Dimensions,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import React, {useEffect, useMemo, useState} from 'react';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttp';
import {authModel} from '../../../../../models/auth';
import {Divider} from 'native-base';
import useSelectStore from '../store';
import useStore from '@xlb/common/src/components/features/xlbStoreText/model';
import useRefreshStore from '@xlb/common/src/models/refresh';
// import * as AliyunPush from 'aliyun-react-native-push'
import useAliCloudPush from '@xlb/common/src/hooks/useAliCloudPush';
import {$modalAlert} from '@xlb/common/src/components/SecondModal';

type XlbAccountType = {
  show: boolean;
  setShow: any;
  callBack?: () => void;
};
const screenWidth = Dimensions.get('screen').width;
const screenHeight = Dimensions.get('screen').width;
const AccountChange = (props: XlbAccountType) => {
  const {initPush} = useAliCloudPush();
  const {show, setShow, callBack} = props;
  const setStoreList = useSelectStore((state: any) => state.setStoreList);
  const setStoreLists = useStore((state: any) => state.setStoreList);
  const setRefresh = useRefreshStore((state: any) => state.setHomeRefresh);
  const [accountList, setAccountList] = useState<any>([]);

  const getAccount = async () => {
    const res = await ErpHttp.post<CommonResponse>(
      '/erp/hxl.erp.account.user.find',
      {tel: authModel.state.userInfos?.tel},
    );
    if (res?.code == 0) {
      setAccountList(res.data.account_user_list);
    }
  };

  const accountChange = async (account: any, company_id: any, v: any) => {
    setAccountList([]);
    // 解决首页切换门店问题
    setStoreList([]);
    setStoreLists([]);
    authModel
      .onLogin(account, company_id, undefined, undefined)
      .then(async () => {
        setRefresh();
      });
    setShow(false);
    callBack && callBack();
    // 切换账户时更换推送绑定id
    // AliyunPush.unbindAccount().then(() => initPush())
  };

  useEffect(() => {
    getAccount().then();
  }, []);
  const listHeight = useMemo(() => {
    if (accountList.length > Math.ceil(screenHeight / 60)) {
      return Math.ceil(screenHeight / 50) * 75;
    } else {
      // 1-180,2-190,3-250
      const standard: any = {
        1: 180,
        2: 190,
        3: 250,
      };
      return accountList.length < 4
        ? standard[accountList.length]
        : accountList.length * 80;
    }
  }, [accountList]);
  return (
    <BottomUp
      boxStyle={{paddingRight: 0, height: listHeight}}
      type={'modal'}
      percent={1}
      isVisible={show}
      onBackdropPress={() => setShow(false)}>
      <Space>
        <TouchableWithoutFeedback
          onPress={() => {
            setShow(false);
          }}>
          <Text style={[cs.second, cs.fz14]}>取消</Text>
        </TouchableWithoutFeedback>

        <Text style={[cs.bold, cs.fz16, cs.black]}>切换账户</Text>
        <Text style={[cs.bold, cs.first, cs.fz14, {opacity: 0}]}>确定</Text>
      </Space>
      <ScrollView
        style={{flexWrap: 'wrap', ...commonStyles.horBox, height: '80%'}}>
        {accountList.map((v: any) => {
          return (
            <Pressable
              key={v.company_id + v.name}
              onPress={() => accountChange(v.account, v.company_id, v)}
              style={{width: screenWidth - 14}}>
              <View style={{...styles.modalItemBox, height: 60}}>
                <View style={styles.modalItemBoxLeft}>
                  <XText
                    style={{
                      color:
                        authModel.state.userInfos?.company_id +
                          authModel.state.userInfos?.account ===
                        v.company_id + v.account
                          ? '#1A6AFF'
                          : '#333',
                    }}>
                    {v.company_id}丨{v.company_name}
                  </XText>
                  <XText
                    style={{
                      color:
                        authModel.state.userInfos?.company_id +
                          authModel.state.userInfos?.account ===
                        v.company_id + v.account
                          ? '#1A6AFF'
                          : '#999',
                    }}
                    size12>
                    {v.store_name}-{v.account}
                  </XText>
                </View>
                {authModel.state.userInfos?.company_id +
                  authModel.state.userInfos?.account ===
                  v.company_id + v.account && (
                  <XIcon
                    name={'iconPitchOn'}
                    size={20}
                    color={'#1A6AFF'}
                    style={{marginRight: 28}}
                  />
                )}
              </View>
              <Divider />
            </Pressable>
          );
        })}
      </ScrollView>
    </BottomUp>
  );
};

const styles = StyleSheet.create({
  modalItemBox: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalItemBoxLeft: {
    alignItems: 'flex-start',
  },
});

export default AccountChange;
