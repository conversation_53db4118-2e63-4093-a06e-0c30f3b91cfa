import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import {
  Linking,
  Pressable,
  View,
  Text,
  StyleSheet,
  Dimensions,
  Platform,
} from 'react-native';
import {Flex, Button, ProModal, XIcon} from '../..';
import WebView from 'react-native-webview';
// import { savePicture } from '@xlb/business-kms/src/screens/newPointApply/utils/saveToLocal'
import ImageViewer from 'react-native-image-zoom-viewer';
import {IImageInfo} from 'react-native-image-zoom-viewer/built/image-viewer.type';
// import { FileItem } from '@xlb/business-kms/src/screens/businessDistrict/type'
import PDF from 'react-native-pdf';
import {savePicture} from '../utils/saveToLocal';

export interface XlbPreViewProps {
  type: 'file' | 'image' | 'video';
  url: string;
  /**
   * 预览图片需要传过来的预览列表
   */
  urlList?: FileItem[];
  open?: boolean;

  onClose?: NonReturnFunc;
}

export interface XlbPreViewRef {
  open: NonReturnFunc;
  close: NonReturnFunc;
  preview: (params: Omit<XlbPreViewProps, 'open' | 'onClose'>) => void;
}

export const XlbPreview = forwardRef<XlbPreViewRef, Partial<XlbPreViewProps>>(
  ({type: pType, url: pUrl, urlList: pUrlList, open, onClose}, ref) => {
    const [visible, setVisible] = useState(false);
    //   const [curIndex] = useState()

    const [type, setType] = useState(pType);
    const [url, setUrl] = useState(pUrl);
    const [urlList, setUrlList] = useState(pUrlList);

    useEffect(() => {
      if (open !== void 0 && open !== null) {
        setVisible(open);
      }
    }, [open]);

    const getUrlMineType = (url: string) => {
      const splitArr = url.split('.');
      return '.' + splitArr[splitArr.length - 1];
    };

    const isImage = (url: string = '') => {
      const imageSuffixs = ['.jpg', '.jpeg', '.png', '.bmp', '.webp', '.heic'];
      const suffix = getUrlMineType(url.toLocaleLowerCase());
      return imageSuffixs.includes(suffix.toLowerCase());
    };

    const isPdf = (url: string = '') => {
      const fileType = ['.pdf'];
      const suffix = getUrlMineType(url.toLowerCase());
      return fileType.includes(suffix);
    };

    const previewFiles = urlList
      ? urlList?.filter(item => isImage(item.url))
      : ([{url}] as unknown as FileItem[]);

    const curIndex = previewFiles?.findIndex(item => item?.url === url) || 0;

    useImperativeHandle(ref, () => ({
      open: () => {
        setVisible(true);
      },
      close: () => {
        setVisible(false);
      },
      preview: params => {
        const type = params.type;
        if (type === 'file' && !isPdf(params.url)) {
          Linking.openURL(params.url);
        } else {
          setType(params.type);
          setUrl(params.url);
          setUrlList(params.urlList);
          setVisible(true);
        }
      },
    }));

    if (!type || !url) return null;

    const renderContent = () => {
      if (type === 'image')
        return (
          <Flex>
            <ImageViewer
              index={curIndex}
              enableSwipeDown
              onClick={() => {
                setVisible(false);
                onClose?.();
              }}
              onSwipeDown={() => {
                setVisible(false);
                onClose?.();
              }}
              onSave={url => {
                if (url) {
                  savePicture(url);
                }
              }}
              onSaveToCamera={index => {
                if (index && previewFiles[index]) {
                  savePicture(previewFiles[index]?.url);
                }
              }}
              imageUrls={
                previewFiles.map(item => ({
                  ...item,
                  url: item?.ref_img || item.url,
                })) as IImageInfo[]
              }
              menuContext={{saveToLocal: '保存到相册', cancel: '取消'}}
            />
          </Flex>
        );
      if (type === 'file' && isPdf(url))
        return (
          <Flex>
            <View style={styles.pdf}>
              <PDF
                trustAllCerts={false}
                source={{uri: encodeURI(url), cache: false}}
                style={styles.pdf}
              />
            </View>
            <Button
              onPress={() => {
                setVisible(false);
                onClose?.();
              }}>
              关闭
            </Button>
          </Flex>
        );
      console.log('url111111111', url);
      return (
        <Flex>
          <WebView
            allowsFullscreenVideo={true}
            mediaPlaybackRequiresUserAction={Platform.OS === 'ios'}
            allowsInlineMediaPlayback={true}
            source={{uri: url}}
          />
          <XIcon
            name={'close'}
            size={26}
            color={'#fff'}
            style={styles.closeButton}
            onPress={() => {
              {
                setVisible(false);
                onClose?.();
              }
            }}
          />
          {/* {type === 'video' && visible ? (
          <View
            style={{
              position: 'absolute',
              bottom: 65,
              height: 40,
              width: '80%',
              backgroundColor: '#fff',
              left: '10%',
              borderRadius: 8,
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 12,
              justifyContent: 'space-between',
            }}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <XIcon
                name={'close'}
                size={16}
                right={4}
                onPress={() => {
                  {
                    setVisible(false)
                    onClose?.()
                  }
                }}
              />
              <Text style={{ fontSize: 14, color: '#1F2126' }}>无法播放11111？试试本地播放</Text>
            </View>

            <Pressable
              style={{ height: 26, paddingHorizontal: 8, justifyContent: 'center', borderColor: '#1C6AFF', borderWidth: 1, borderRadius: 6 }}
              onPress={() => {
                if (url) Linking.openURL(url)
              }}
            >
              <Text style={{ fontSize: 14, fontWeight: '500', color: '#1C6AFF' }}>本地播放</Text>
            </Pressable>
          </View>
        ) : null} */}

          {/* <Button
          title="关闭"
          onPress={() => {
            setVisible(false)
            onClose?.()
          }}
        /> */}
        </Flex>
      );
    };

    return (
      <ProModal
        statusBarTranslucent
        visible={visible}
        onRequestClose={() => {
          setVisible(false);
          onClose?.();
        }}
        animationType="slide">
        {renderContent()}
      </ProModal>
    );
  },
);

const styles = StyleSheet.create({
  pdf: {
    flex: 1,
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height - 100,
  },
  closeButton: {
    position: 'absolute',
    top: 65,
    height: 50,
    left: '5%',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
  },
});
