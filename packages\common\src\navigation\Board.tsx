import {DeviceEventEmitter, Platform, View} from 'react-native';
import React, {useEffect, useRef} from 'react';
// import { WebViewPage } from '@xlb/common/src/components/'
import WebViewPage, {
  WebViewPageRef,
} from '@xlb/common/src/components/WebViewPage';
import {routes} from '@xlb/common/src/config/routeWeb';
import Config from 'react-native-config';
import {authModel} from '@xlb/business-base/src/models/auth';
import useAppState from 'react-native-appstate-hook';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

type ReciveMapType = {
  getAuthList: () => void;
};

type H5MessageParamsType = {
  method: keyof ReciveMapType;
};

export default function Index({route}: any) {
  const webviewRef = useRef<WebViewPageRef>(null);
  // route?.params?.is
  console.log('route?.params?.isNavBar', route?.params);
  const isNavBar = route?.params?.isNavBar;
  useEffect(() => {
    const BoardHandleRefresh = DeviceEventEmitter.addListener(
      'BoardRefresh',
      () => {
        webviewRef?.current?.reload();
      },
    );

    return () => {
      BoardHandleRefresh.remove();
    };
  }, []);

  // 发送消息给h5
  const sendMessageToH5 = (method: any, result: any) => {
    let data = {
      method: method,
      data: result,
    };
    webviewRef.current?.postMessage(JSON.stringify(data)); //发送消息到H5
  };

  // 收到h5的消息
  const reciveH5Message = async (e: any) => {
    let params: H5MessageParamsType = JSON.parse(e.nativeEvent.data);

    let reciveMap: ReciveMapType = {
      getAuthList: () => {
        const user = authModel.state.userInfos;
        const auths = user.authorities;
        let authList: any[] = [];
        [
          100161, 100162, 100178, 100179, 100180, 100181, 100182, 100205,
        ].forEach(item => {
          const find = auths.find(auth => auth.id == item);
          if (find) {
            authList.push(find);
          }
        });
        sendMessageToH5('authList', authList);
      },
    };

    if (reciveMap[params.method]) {
      reciveMap[params.method]();
    }
  };

  const domin = `https://mobile-erp.erp.ali-${Config.ENV}.xlbsoft.com`;

  useAppState({
    onChange: newAppState => {
      if (Platform.OS == 'ios') {
        // if (Platform.OS == 'android') {
        if (newAppState.match(/inactive|background/)) {
        } else {
          console.log('刷新了');
          // webviewRef?.current?.reload()
        }
      }
    },
  });

  const insets = useSafeAreaInsets();

  const baseUrl = `${domin}/xlb_center/centerSalesAnalysis`;
  const urlObj = new URL(baseUrl);

  urlObj.searchParams.set('isNavBar', isNavBar ? 'true' : 'false');
  const url = urlObj.toString();

  return (
    <>
      <View style={{flex: 1}}>
        <WebViewPage
          isStatusBar={false}
          disableBack={true}
          url={url}
          onMessage={reciveH5Message}
          ref={webviewRef}></WebViewPage>
        {/* <WebViewPage url={"http://192.168.8.115:8000/~demos/xlb-button-demo-demo?locale=zh-CN"} onMessage={reciveH5Message} ref={webviewRef}></WebViewPage> */}
      </View>
    </>
  );
}
