<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    >

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- Include this only if you are planning to use the camera roll -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- Include this only if you are planning to use the microphone for video recording -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        tools:remove="android:maxSdkVersion" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"
        android:maxSdkVersion="28" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_CONNECT"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.USES_POLICY_FORCE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <uses-permission-sdk-23
        android:name="android.permission.ACCESS_FINE_LOCATION"
        tools:targetApi="Q" />

    <application
        android:name=".MainApplication"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:allowBackup="false"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup,label"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true">
        <activity
            android:name=".MainActivity"
            android:label="@string/app_name"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustPan"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".map.MapActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan"
            tools:ignore="Instantiatable" />
        <activity
            android:name=".map.MapAddressActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".map.MapTemPlateActivity"
            android:exported="true" />
        <activity
            android:name=".map.MapPointDetailActivity"
            android:exported="true" />
        <activity
            android:name=".map.MapPointSearchActivity"
            android:exported="true" />
        <activity
            android:name=".map.MapListDataActivity"
            android:exported="true" />
        <activity
            android:name=".map.MapStorePlanDetailActivity"
            android:exported="true" />
        <activity
            android:name=".map.MapBusinessDetailActivity"
            android:exported="true" />
        <activity
            android:name=".map.MapPositionNavigationActivity"
            android:exported="true" />
        <activity
            android:name=".map.ImageShowActivity"
            android:exported="true" />
        <activity
            android:name=".map.MapPointDetailTwoActivity"
            android:exported="true"
            android:theme="@style/Theme.AppCompat.Dialog.NoActionBar.NoDecor"></activity>
        <activity
            android:name=".map.MapPoiSearchDialogActivity"
            android:exported="true"
            android:theme="@style/Theme.AppCompat.Dialog.NoActionBar.NoDecor"></activity>
        <activity
            android:name=".map.MapAddBusinessActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".map.MapEditBusinessActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".map.MapAddPointActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".map.MapEditPointActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".map.MapAddLandlordActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".map.MapFollowRecordActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".map.MapOpenStoreAreaActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".map.MapExplorationClockActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".map.WebviewActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <activity
            android:name=".map.MapLocationFlagActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustPan" />

      <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="${amapApiKey}" />

        <!-- 注意替换为你的包名加上".provider" -->
        <provider
            android:name=".utils.MainProvider"
            android:authorities="${applicationId}.mainfileprovider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_main_provider"
                tools:replace="android:resource" /> <!-- file_paths是res/xml/file_paths.xml文件的引用 -->
        </provider>

        <service
            android:name="com.amap.api.location.APSService"
            android:foregroundServiceType="location" />

            <provider
                android:name="androidx.core.content.FileProvider"
                android:authorities="${applicationId}.fileprovider"
                android:exported="false"
                android:grantUriPermissions="true">
                <meta-data
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/update_file" />
            </provider>

        <!-- 告知 XXPermissions 当前项目已经适配了分区存储特性 -->
        <meta-data
            android:name="ScopedStorage"
            android:value="true" />
    </application>
</manifest>
