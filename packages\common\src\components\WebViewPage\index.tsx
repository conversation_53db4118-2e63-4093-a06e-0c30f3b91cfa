import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  BackHandler,
  DeviceEventEmitter,
  StatusBar,
  NativeModules,
  Platform,
  Linking,
  Clipboard,
  Dimensions,
  InteractionManager,
  Keyboard,
  ScrollView,
  Pressable,
} from 'react-native';
import React, {
  ForwardRefRenderFunction,
  PropsWithChildren,
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {navigationRef} from '@xlb/common/src/navigation/NavigationService';
import {useProRoute} from '@xlb/common/src/hooks';
import WebView, {WebViewMessageEvent, WebViewProps} from 'react-native-webview';
import Orientation, {OrientationType} from 'react-native-orientation-locker';
import {authModel} from '@xlb/business-base/src/models/auth';
import {colors, normalize} from '@xlb/common/src/config/theme';
import Config from 'react-native-config';

import {ScreenHeight} from '@dplus/rn-ui';

import useSystemStore from '@xlb/common/src/models/system';
import XlbScanCode from './components/XlbScanCode';
// import XlbUploadImage from '@xlb/business-base/src/components/xlbUploadImage'
import NewXlbUpload from './components/upload';
import Flex from '../Flex/Flex';
// import { Dropdown, Portal, , NoticeBar } from '@fruits-chain/react-native-xiaoshu'
import XlbUploadImage from './components/uploadImage';
import {
  Blank,
  Button,
  Card,
  Checkbox,
  Col,
  Divider,
  NoticeBar,
  Popup,
  Row,
  Search,
  Space as SpaceXiaoshu,
  Skeleton,
  Progress,
  Tag,
  TextInput,
} from '@fruits-chain/react-native-xiaoshu';
import Toast from 'react-native-root-toast';
import NoNetwork from '@xlb/business-base/src/screens/noNetwork';
import EmptyBox from '../EmptyBox/emptyBox';
import {TOKEN, XlbAppProvider, XlbButton} from '@xlb/components-rn';
import {useNetInfo} from '@react-native-community/netinfo';
import {CommonActions} from '@react-navigation/native';

import {WebViewProgressEvent} from 'react-native-webview/lib/WebViewTypes';
import {XlbPreview, XlbPreViewRef} from './components/Preview';
import {XlbOpenMap, XlbOpenMapRef} from './components/openMap';
import {$modalAlert} from '../SecondModal';
import {getBrand} from 'react-native-device-info';
import {XlbUploadFiles, XlbUploadFilesRef} from './components/UploadFile';

import ProModal from '../ProModal';
import PDF from 'react-native-pdf';
//  TODO暂时注释截图功能
// import {ScreenShotCtr} from 'app/src/ScreenShotCtr';
import {XlbUploadKit} from '../../utils/xlbUpload';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {getLocationInfo} from './utils/getLocationInfo';
import dayjs from 'dayjs';
import {OSSUploadFile} from './components/OSSUpload';
import ImPopup from './components/ImPopup';
import {useUnmount} from 'ahooks';
import {handleCode} from '../../utils/commonFeatures';
import {multipartUpload} from './components/OSSMultipartUpload';
// import { KmsHttp } from '../../services/lib/kmshttp'
import {useCreateRoom} from './hooks/useCreatRoom';

import usePrintHook from './hooks/usePrintHook';
import {KmsHttp} from '../../services/lib/kmshttp';
import Loading from '../RootView/Loading';
import {formatRecord} from './utils/index';
const {StatusBarManager, NetworkSettings} = NativeModules;
const rotate = NativeModules.ScreenRotateManager;
export enum BusinessDistrictRoute {
  List = 'BusinessDistrictList',
  ADD = 'BusinessDistrictAdd',
  DETAIL = 'BusinessDistrictDetail',
  MAP_POLYGON = 'mapPolygon',
  OTHER_INFO = 'BusinessDistrictOtherInfo',
  AddFollow = 'BusinessDistrictAddFollow',
  SelectCompetitor = 'BusinessSelectCompetitor',
}
interface filesItem {
  ref_id: string;
  file_type: string;
  suffix_type: string;
  detail_num: number;
  name: string;
  id: number;
  url: string;
  png_url: string;
  ref_sub_type: string;
  ref_img: string;
}

export enum PointApplyRoute {
  MapView = 'MapView',
}

export enum AttendanceRoute {
  HRS_H5_PAGE = 'HRS_H5_PAGE',
  // TEST = 'AttendanceTest',
}

// 通用MessageTS
type ReciveMapType = {
  changeOrientation: () => void;
  backRN: () => void;
  logout: () => void; // 或者直接声明为LogOutEventEmitter类型，如果可用
  scanCode: () => void;
  getPosition: () => void;
  getCurrentPosition: () => void;
  uploadImage: () => void;
  uploadFiles: () => void;
  setStatusBarTranslucent: () => void;
  setStatusBarBackgroundColor: () => void;
  setStatusBarDefault: () => void;
  hrsNavigate: () => void;
  goWlan: () => void;
  selectMap: () => void;
  screenShotEnable: () => void; //设置可以截屏
  screenShotDisEnable: () => void; //设置不可以截屏
  checkUseScreenShotEnable: () => void; //检查当前用户是否在能截、录屏用户白名单中
  checkPageScreenShotEnable: () => void; //检查页面是否允许截、录屏

  preview: NonReturnFunc;

  openMap: () => void;

  closeOpenMap: () => void;

  uploadMixFiles: () => void;

  openTellCall: () => void;
  filePreview: () => void;
  lookPointAmap: () => void;
  jumpToPage: () => void;
  createRoom: () => void;
  showPrintPreview: () => void;
  // ... 更多的方法
};

type H5MessageParamsType = {
  method: keyof ReciveMapType;
  data: any;
};

interface WebViewPageProps extends Omit<Partial<WebViewProps>, 'onLoad'> {
  url: string;
  disableBack?: boolean;
  backFunction?: () => void;
  onMessage?: (event: WebViewMessageEvent) => void;
  isStatusBar?: boolean;
  onLoad?: (event: WebViewProgressEvent) => void;
  isPreLoad?: boolean;
  isDrawerOpen?: boolean;
  isSafeAreaView?: boolean;
  customPickerList?: {label: string; value: string; [p: string]: any}[];
}

export interface WebViewPageRef {
  postMessage: (method: string, result: any) => void;
  reload: () => void;
  goBack: () => void;
}

const WebViewPage: ForwardRefRenderFunction<
  WebViewPageRef,
  PropsWithChildren<WebViewPageProps>
> = (
  {
    url,
    onMessage,
    isSafeAreaView = true,
    disableBack,
    backFunction,
    isStatusBar = true,
    isDrawerOpen,
    isPreLoad,
    onLoad,
    customPickerList,
    ...rest
  },
  ref,
) => {
  console.log(customPickerList, url, 'customPickerList===>', 111111);
  const {navigation} = useProRoute();
  const webviewRef = useRef<WebView>(null);
  const [currentH5, setCurrentH5] = useState<any>(); //当前h5路由
  const [show, setShow] = useState<boolean>(false);
  const {type, isConnected} = useNetInfo();
  const [pdfUrl, setPdfUrl] = useState('');
  const [pageState, setPageState] = useState({
    loading: false,
    percentage: 100,
    // fail: true,
  });
  const [uploadVisible, setUploadVisible] = useState<boolean>(false);
  const [uploadType, setUploadType] = useState<'image' | 'file'>('image');
  const [mediaType, setMediaType] = useState<'photo' | 'video' | 'any'>(
    'photo',
  );
  const [uploadFilesConfig, setUploadFilesConfig] = useState<any>({});

  const [errorType, setErrorType] = useState<'network' | 'other' | null>(null);
  // 在组件状态中添加
  const [webViewReady, setWebViewReady] = useState(false);
  const {
    handleCreateRoom,
    handleFormatCheckboxOptions,
    searchValue,
    setSearchValue,
    checkboxOption,
    setCheckboxOption,
    pageVisible,
    setPageVisible,
    detail,
    setDetail,
    userTel,
  } = useCreateRoom();

  const [safeAreaShow, setSafeAreaShow] = useState(true);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  const areaInsets = useSafeAreaInsets();
  const {theme, themeName} = useContext(XlbAppProvider);

  const [keyboardStatus, setKeyboardStatus] = useState({
    visible: true,
    height: 0,
  }); // 键盘状态

  const loadStartTimeRef = useRef<number>(Date.now());
  const loadEndTimeRef = useRef<number>(0);
  const loadDurationRef = useRef<number>(0);

  const xlbPreviewRef = useRef<XlbPreViewRef>(null);

  const xlbOpenMapRef = useRef<XlbOpenMapRef>(null);

  const xlbUploadFileRef = useRef<XlbUploadFilesRef>(null);

  const positionConfig = useRef<any>(null);

  const {setPosition, position} = useSystemStore((state: any) => state);
  const [isLandscape, setIsLandscape] = useState<boolean>(false);

  const [scanItem, setScanItem] = useState<any>({});
  const [printing] = usePrintHook();

  const handleBusinessDistrictDetail = async (params: any) => {
    Loading.show();
    const authResponse = await KmsHttp.post(
      '/kms/hxl.kms.approvatask.audit.check',
      {
        business_key: params?.business_key,
        process_definition_key: params?.process_definition_key,
        tel: authModel?.state?.userInfos?.tel,
      },
    ).catch(() => null);

    if (authResponse?.code === 0) {
      const res = await KmsHttp.post('/kms/hxl.kms.businessplan.read', {
        id: params?.business_key,
      }).finally(() => {
        Loading.hide();
      });
      if (res?.code === 0) {
        const data = formatRecord(res?.data || {});
        (navigation as any).navigate(BusinessDistrictRoute.DETAIL, {
          ...data,
          ...(authResponse?.data || {}),
          oaParams: params,
        });
      }
    } else {
      Loading.hide();
    }

    const {routeName, paramData} = params;
    if (routeName === BusinessDistrictRoute.DETAIL) {
      (navigation as any).navigate(routeName, {
        ...paramData,
      });
    }
  };

  useEffect(() => {
    const onEvent = DeviceEventEmitter.addListener('onEvent', e => {
      const nowDate = new Date().getTime();
      setScanItem({
        code: e.code,
        date: nowDate,
      });
    });

    return () => {
      onEvent.remove();
      setScanItem({});
    };
  }, []);

  useEffect(() => {
    if (!scanItem.code) return;
    sendMessageToH5('pdaScanCode', {
      code: scanItem.code,
    });
  }, [scanItem]);

  const OpenTelCall = (
    telnumber: number | string | boolean | undefined | null,
  ) => {
    if (!telnumber) return $modalAlert('提示', `电话未设置`, () => {});
    Linking.canOpenURL(`tel:${telnumber.toString()}`).then(res => {
      if (res) {
        Linking.openURL(`tel:${telnumber.toString()}`);
      } else {
        if (getBrand() === 'HUAWEI') {
          Linking.openURL(`tel:${telnumber.toString()}`);
        } else {
          Clipboard.setString(telnumber.toString());
          $modalAlert(
            '提示',
            `该设备不支持跳转，电话号码${telnumber}已复制`,
            () => {},
            () => {},
            '',
            '确认',
            true,
          );
        }
      }
    });
  };
  useImperativeHandle(ref, () => {
    return {
      postMessage: (method: string, result: any) => {
        sendMessageToH5(method, result);
      },
      reload: () => {
        webviewRef.current?.reload();
      },
      goBack: () => {
        webviewRef.current?.goBack();
      },
    };
  });

  const handleUploadImageByCameraResult = async (uploadConfig: any) => {
    const UploadAction = new XlbUploadKit();
    const res = await UploadAction.uploadImagesByCamera({
      ...uploadConfig,
      url: uploadConfig?.uploadUrl || '/erp/hxl.erp.file.upload',
      params: uploadConfig?.params,
      // uploadConfig: {},
    });

    console.log(res, uploadConfig, 'Res===> upload');

    if (res && res?.length > 0) {
      sendMessageToH5('uploadResult', {
        ...res[0],
        ...uploadConfig,
      });
    }
  };

  useEffect(() => {
    //暂时禁用返回手势
    const backAction = () => {
      if (backFunction) {
        navigation.goBack();
      }
      if (!disableBack) {
        sendMessageToH5('goBack');
        if (isPreLoad && !isDrawerOpen) {
          return false;
        }
        return true;
      }
    };
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, [currentH5, isPreLoad, isDrawerOpen]);

  useEffect(() => {
    return () => {
      try {
        // ScreenShotCtr.setScreenShotShouldEnable(true);
      } catch {}
    };
  }, []);

  const handlePointSelect = (val: Record<string, any>) => {
    sendMessageToH5('getSeletPointResult', {...val, ...positionConfig.current});
  };

  // 收到h5的消息
  const reciveH5Message = async (e: any) => {
    let params: H5MessageParamsType = JSON.parse(e.nativeEvent.data);
    console.log(params, 'params===> commom');

    let reciveMap: ReciveMapType = {
      // 横竖屏切换
      changeOrientation: () => {
        console.log(params, 'changeOrientation3333');
        changeOrientation();
      },
      jumpToPage: () => {
        const routeName = params?.data?.routeName;
        const paramData = params?.data?.paramData;
        console.log('oa携带参数', routeName, paramData);

        if (routeName) {
          if (routeName === BusinessDistrictRoute.DETAIL) {
            handleBusinessDistrictDetail(paramData?.params || {});
          } else {
            (navigation as any).navigate(routeName, {
              ...paramData,
            });
          }
        }
      },
      // 查看地图
      lookPointAmap: () => {
        const fromData = params?.data;
        if (Platform.OS === 'ios') {
          var Push = NativeModules.PushNativeMapManager;
          InteractionManager.runAfterInteractions(() => {
            Push.LookAMapPoint(fromData);
          });
        } else {
          //安卓
          var Push = NativeModules.PushNativeMapManager;
          InteractionManager.runAfterInteractions(() => {
            Push.LookAMapPoint(fromData);
          });
        }
      },
      // 返回RN
      backRN: () => {
        try {
          //   ScreenShotCtr.setScreenShotShouldEnable(true);
        } catch {}
        navigation.goBack();
        // setH5Visible(false)
      },
      // 退出登录
      logout: () => {
        console.log('退出登录发起');
        DeviceEventEmitter.emit('logOut');
      },
      scanCode: () => {
        setShow(true);
      },
      getPosition: () => {
        console.log('getPosition11', position);
        sendMessageToH5('positionResult', {...position});
      },
      getCurrentPosition: async () => {
        const res = await getLocationInfo();
        sendMessageToH5('currentPositionResult', res);
      },
      setStatusBarDefault: () => {
        setSafeAreaShow(false);
        // StatusBar.setHidden(true);
        StatusBar.setTranslucent(true);
        StatusBar.setBackgroundColor(colors.primary);
        StatusBar.setBarStyle('light-content');
      },
      setStatusBarTranslucent: () => {
        // StatusBar.setBarStyle(params.data)
        setSafeAreaShow(false);
        // StatusBar.setHidden(true);
        setTimeout(() => {
          StatusBar.setTranslucent(true);
          StatusBar.setBackgroundColor('transparent');
          StatusBar.setBarStyle('dark-content');
        }, 500);
      },
      // 上传图片
      uploadImage: () => {
        // fixme 上传图片直接打开相机，不用选择从相册 参数靠 onlyCamera 控制 默认false
        console.log(params?.data, 'params?.data111111111111111');

        if (params?.data?.onlyCamera) {
          setUploadFilesConfig(params?.data);
          handleUploadImageByCameraResult(params?.data);
        } else {
          setUploadType('image');
          setUploadVisible(true);
          setUploadFilesConfig(params?.data);
          console.log(params?.data, 'params?.data====>');

          if (params?.data?.mediaType) {
            setMediaType(params?.data?.mediaType);
          }
        }
      },
      // uploadFiles上传文件
      uploadFiles: () => {
        console.log(params?.data, 'uploadFiles');
        setUploadType('file');
        setUploadVisible(true);
        setUploadFilesConfig(params.data);
        if (params?.data?.mediaType) {
          setMediaType(params?.data?.mediaType);
        }
      },
      setStatusBarBackgroundColor: () => {
        StatusBar.setBackgroundColor(params?.data);
      },
      preview: () => {
        let {url, type} = params?.data;
        if (type === 'video' && Platform.OS === 'ios') {
          NativeModules?.XLBAVPlayerBridge?.xlbAVPlayerEvent?.(
            'play',
            url,
            (error: any, events: any) => {},
          );
        } else {
          xlbPreviewRef.current?.preview?.(params.data);
        }

        console.log('params.data', params.data);
      },
      openMap: () => {
        const pagePayload = {
          longitude: params?.data?.point?.longitude,
          latitude: params?.data?.point?.latitude,
          selected: {
            name: params.data?.name,
            longitude: params?.data?.point?.longitude,
            latitude: params?.data?.point?.latitude,
            location: params?.data?.name,
            address: params?.data?.address,
          },
          disabled: true,
          flagCenter: true,
        } as any;
        navigation.dispatch(
          CommonActions.navigate(
            'RemoteAppSds.' + PointApplyRoute.MapView,
            pagePayload,
          ),
        );
        // xlbOpenMapRef.current?.openMap?.(params.data)
      },
      closeOpenMap: () => {
        xlbOpenMapRef.current?.close?.();
      },
      openTellCall: () => {
        OpenTelCall(params.data);
      },
      filePreview: () => {
        if (params?.data) {
          if (params?.data?.type === 'pdf') {
            setPdfUrl(params?.data?.url);
          }
        }
      },

      uploadMixFiles: () => {
        setUploadFilesConfig(params?.data);
        xlbUploadFileRef.current?.configUpload(params?.data);
      },
      hrsNavigate: () => {
        navigation.dispatch(
          CommonActions.navigate(
            'RemoteAppHrs.' + AttendanceRoute.HRS_H5_PAGE,
            params?.data || ({} as any),
          ),
        );
      },
      goWlan: () => {
        NetworkSettings.goToNetworkSettings();
      },
      selectMap: () => {
        positionConfig.current = params?.data;
        navigation.dispatch(
          CommonActions.navigate('RemoteAppSds.' + PointApplyRoute.MapView, {
            ...(params?.data || {}),
            onSelect: handlePointSelect,
          }),
        );
      },

      screenShotEnable: () => {
        // ScreenShotCtr.setScreenShotShouldEnable(true);
      },
      screenShotDisEnable: () => {
        // ScreenShotCtr.setScreenShotShouldEnable(false);
      },
      checkUseScreenShotEnable: async () => {
        // let enable = await ScreenShotCtr.checkUseScreenShotEnable();
        // sendMessageToH5('checkUseScreenShotEnableResult', {enable: enable});
      },
      checkPageScreenShotEnable: async () => {
        // let enable = await ScreenShotCtr.checkPageScreenShotEnable(
        //   params?.data,
        // );
        // sendMessageToH5('checkPageScreenShotEnableResult', {enable: enable});
      },
      createRoom: () => {
        const {appreove} = params.data;
        setDetail(params.data);
        handleFormatCheckboxOptions(appreove);
        setPageVisible(true);
      },
      // 预览文件
      // + 新增处理打印预览的逻辑
      showPrintPreview: () => {
        console.log('showPrintPreview data:', params.data);
        // setPrintPreviewData(params.data)

        if (params?.data?.printData && Array.isArray(params.data.printData)) {
          params.data.printData.forEach((element: any) => {
            if (element) {
              printing(element, params?.data?.printType, () => {
                console.log('打印完成');
                sendMessageToH5('printLabelsResult', {
                  printResult: 'success',
                  msg: '打印成功',
                });
              });
            } else {
              console.warn('打印数据项为空');
            }
          });
        } else {
          console.warn('打印数据不是有效数组');
        }
        // setPrintPreviewVisible(true)
      },
    };
    if (reciveMap[params.method]) {
      reciveMap[params.method]();
    } else {
      onMessage?.(e);
    }
  };

  // 发送消息给h5
  const sendMessageToH5 = (method: any, result?: any) => {
    let data = {
      method: method,
      data: result,
    };
    console.log('sendMessageToH5111', JSON.stringify(data));
    console.log('webviewRef', webviewRef);
    if (webviewRef?.current) {
      console.log(111);
      webviewRef.current?.postMessage(JSON.stringify(data)); //发送消息到H5
      console.log('消息已发送到 H5');
    } else {
      console.log(222);
      console.log('WebView ref 不存在，无法发送消息');
    }
  };

  //横竖屏切换
  const changeOrientation = () => {
    if (Platform.OS === 'ios') {
      isLandscape
        ? rotate?.resetScreenDirection()
        : rotate?.rotateScreenToDirection('Right');
    } else {
      isLandscape
        ? Orientation.lockToPortrait()
        : Orientation.lockToLandscapeLeft();
    }
    if (isLandscape) {
      StatusBar.setHidden(false);
    } else {
      StatusBar.setHidden(true);
    }
    setIsLandscape(!isLandscape);
  };

  useEffect(() => {
    Orientation.addOrientationListener(orientation => {
      setIsLandscape(orientation != OrientationType.PORTRAIT);
    });

    // 门店照片上传
    const subscription = DeviceEventEmitter.addListener(
      'SHOP_UPLOAD_IMAGE',
      val => {
        sendMessageToH5('uploadResult', val);
      },
    );

    return () => {
      console.log('webView已经卸载111');
      StatusBar.setHidden(false);
      setIsLandscape(false);
      Platform.OS === 'ios'
        ? rotate?.resetScreenDirection()
        : Orientation.lockToPortrait();
      Orientation.removeOrientationListener(() => {});
      subscription.remove();
    };
  }, []);

  let statusBarHeight;
  if (Platform.OS === 'ios') {
    StatusBarManager.getHeight((height: any) => {
      statusBarHeight = height;
    });
  } else {
    statusBarHeight = StatusBar.currentHeight;
  }

  // 上传图片
  const upload = async (data: {filename: string; size: number}) => {
    const mimeType = data?.mime || data?.type;
    data.filename =
      data?.name ||
      data?.filename ||
      new Date().getTime() + `.${mimeType.split('/')[1] || '.jpg'}`;
    console.log('333333333333333');
    let res: any = {};
    console.log('4444444444444444444');
    try {
      // 此处SMS使用
      if (
        mimeType.includes('video') &&
        uploadFilesConfig.uploadUrl?.startsWith('/sms')
      ) {
        sendMessageToH5('uploadVideoStart', uploadFilesConfig);
        // res = await OSSUploadFile(data, uploadFilesConfig)
        res = await multipartUpload(data, uploadFilesConfig);
      } else {
        console.log('5555555555555555555');
        res = await NewXlbUpload.uploadImage(
          data,
          Date.now(),
          uploadFilesConfig,
        );
        console.log('77777777777777', res);
      }

      // res = await NewXlbUpload.uploadImage(data, Date.now(), uploadFilesConfig)
      // res = await multipartUpload(data)
      // console.log('🚀multipartUpload ~ res:', res)
    } catch (error) {
      console.error('上传错误:', error);
    }
    console.log('6666666666666666666', res, uploadType);
    if (uploadType === 'image') {
      sendMessageToH5('uploadResult', {
        ...res,
        ...uploadFilesConfig,
        size: data?.size,
      });
    } else {
      console.log('2uploadFile-->', {
        ...res,
        ...uploadFilesConfig,
        size: data?.size,
      });
      sendMessageToH5('uploadFileResult', {
        ...res,
        ...uploadFilesConfig,
        size: data?.size,
      });
    }
  };

  const handleUploadFile = (files: filesItem[]) => {
    sendMessageToH5('uploadResultList', {
      files,
      ...uploadFilesConfig,
    });
  };

  // 上传文件
  const uploadFile = async (data: any) => {
    console.log('uploadFile-->', data);
    const url: any = await NewXlbUpload.uploadFile(
      data,
      'file',
      uploadFilesConfig,
    );

    sendMessageToH5('uploadFileResult', {
      ...url,
      ...uploadFilesConfig,
      size: data?.size,
    });
  };

  const reloadWebView = () => {
    setErrorType(null);
    if (webviewRef.current) {
      webviewRef.current.reload();
    }
  };

  // 上报加载时长到后端
  const reportLoadingTime = useCallback(
    (duration: number, pageUrl: string, endTime: number) => {
      // 这里添加上报逻辑，可以使用fetch或其他网络请求方法
      console.log('页面加载时长(ms):', duration, '页面URL:', pageUrl);

      if (!url || Config.ENV !== 'PROD') return;
      // 示例上报代码
      fetch(
        'https://gaor4awyz1u.feishu.cn/base/automation/webhook/event/JX84asBfzwTik9h7tv4cSKm6nFh',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            errorType: 'webView加载',
            duration,
            date: dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'),
            pageUrl: url,
            env: Config.ENV === 'PROD' ? '生产' : '测试',
            content: {
              text: `夭寿啦，加载太慢拉，页面加载完成: ${url}, 时间:${dayjs(endTime).format('YYYY-MM-DD HH:mm:ss')}, 加载时长(ms):, ${duration}`,
            },
          }),
        },
      ).catch(err => console.error('上报加载时长失败:', err));
    },
    [],
  );

  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', e => {
      setKeyboardStatus({visible: true, height: e.endCoordinates.height});
    });
    const hideSubscription = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardStatus({visible: false, height: 0});
      },
    );

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  console.log('正在加载。。。。。。。。');
  console.log('areaInsets', areaInsets.left, areaInsets.right);
  console.log('webviewRef', webviewRef);

  const insets = useSafeAreaInsets();

  return (
    <>
      {pageState.percentage && pageState.percentage < 100 && (
        <View style={styles.container}>
          <View
            style={{
              flex: 1,
              width: '100%',
              backgroundColor: '#fff',
              paddingHorizontal: 16,
              paddingVertical: 16,
            }}>
            <Skeleton
              loading
              active={true}
              paragraph={{
                rows: 30,
                widths: [
                  100, 100, 70, 100, 100, 20, 100, 100, 70, 100, 100, 70, 100,
                  100, 70, 100, 70, 100, 70, 100, 70, 70, 100, 70, 70, 100,
                ],
              }}
            />
          </View>
        </View>
      )}
      {/* <Text>33333355555</Text> */}
      {isStatusBar && safeAreaShow && (
        <StatusBar
          animated
          translucent={false}
          backgroundColor={colors.primary}
          // hidden={true}
          barStyle={'light-content'}></StatusBar>
      )}
      {/* <StatusBar animated backgroundColor={colors.primary} barStyle={'light-content'}></StatusBar> */}
      {/* {Platform.OS === 'ios' && safeAreaShow ? <SafeAreaView style={{ height: statusBarHeight, backgroundColor: colors.primary }}></SafeAreaView> : null} */}
      <View
        style={{
          flex: 1,
          width: '100%',
          // opacity: pageState.percentage < 100 ? 0 : 1,
          paddingLeft: isSafeAreaView ? areaInsets.left : 0,
          paddingRight: areaInsets.right,
          backgroundColor: '#fff',
        }}>
        <WebView
          ref={webviewRef}
          source={{
            uri: `${url}`,
          }}
          onMessage={e => reciveH5Message(e)}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          thirdPartyCookiesEnabled={true}
          mixedContentMode="always"
          cacheEnabled={false}
          // cacheMode="LOAD_CACHE_ELSE_NETWORK"
          overScrollMode="never"
          bounces={false}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          automaticallyAdjustContentInsets={false}
          startInLoadingState={true}
          injectedJavaScriptBeforeContentLoaded={`(() => {
            window.APP_DATA = {
              token: '${authModel?.state?.userInfos?.access_token}',
              userInfo: ${JSON.stringify(authModel?.state?.userInfos)},
              isApp: true,
              themeName: '${themeName}',
              timestamp: Date.now()
            };
            localStorage.setItem('access_token', '${JSON.stringify(authModel?.state?.userInfos?.access_token)}');
            localStorage.setItem('userInfos', '${JSON.stringify(authModel?.state?.userInfos)}');
            localStorage.setItem('isApp', 'true');
            localStorage.setItem('themeName', '${JSON.stringify(themeName)}');
             (function() {
    document.documentElement.style.setProperty('--safe-area-top', '${insets.top}px');
  })();
            console.log('注入代码执行完成', new Date().toISOString());
            true;
          })();`}
          injectedJavaScript={`(() => {
            window.APP_DATA = {
              token: '${authModel?.state?.userInfos?.access_token}',
              userInfo: ${JSON.stringify(authModel?.state?.userInfos)},
              isApp: true,
              themeName: '${themeName}',
              timestamp: Date.now()
            };
            localStorage.setItem('access_token', '${JSON.stringify(authModel?.state?.userInfos?.access_token)}');
            localStorage.setItem('userInfos', '${JSON.stringify(authModel?.state?.userInfos)}');
            localStorage.setItem('isApp', 'true');
            localStorage.setItem('themeName', '${JSON.stringify(themeName)}');
                         (function() {
    document.documentElement.style.setProperty('--safe-area-top', '${insets.top}px');
  })();
             console.log('注入代码执行完成滞后', new Date().toISOString());
            true;
          })();`}
          onContentProcessDidTerminate={() => {
            if (webviewRef?.current) {
              webviewRef?.current.reload();
            }
          }}
          // 添加页面加载开始事件处理
          // onLoadStart={() => {
          //   const startTime = Date.now()
          //   loadStartTimeRef.current = startTime
          //   console.log('页面开始加载:', url, '时间:', startTime)
          // }}
          onLoadEnd={e => {
            if (e.nativeEvent.loading === false && !hasLoadedOnce) {
              console.log(
                'onLoadEnd 触发:',
                url,
                '触发时间:',
                new Date().toLocaleTimeString(),
                '加载状态:',
                e.nativeEvent.loading,
              );
              const endTime = Date.now();
              loadEndTimeRef.current = endTime;

              // 计算加载时长
              const duration = endTime - loadStartTimeRef.current;
              loadDurationRef.current = duration;
              if (duration > 3000) {
                reportLoadingTime(duration, url, endTime);
              }
              setWebViewReady(true); // 设置 WebView 为就绪状态
              console.log(
                '页面加载完成:',
                url,
                '时间:',
                endTime,
                '加载时长(ms):',
                duration,
              );
              // 设置标志位，表示已经执行过一次
              setHasLoadedOnce(true);
            }
          }}
          // onLoadStart={() => {
          onLoadProgress={e => {
            if (pageState.percentage !== 100) {
              onLoad?.(e);
              if (e?.nativeEvent?.progress) {
                setPageState({
                  loading: false,
                  percentage: Math.floor(e?.nativeEvent?.progress * 100),
                });
              }

              setCurrentH5(e?.nativeEvent);
            }
          }}
          renderLoading={() => (
            <View style={styles.container}>
              <View
                style={{
                  flex: 1,
                  width: '100%',
                  backgroundColor: '#fff',
                  paddingHorizontal: 16,
                  paddingVertical: 16,
                }}>
                <Skeleton
                  loading
                  active={false}
                  paragraph={{
                    rows: 30,
                    widths: [
                      100, 100, 70, 100, 100, 20, 100, 100, 70, 100, 100, 70,
                      100, 100, 70, 100, 70, 100, 70, 100, 70, 70, 100, 70, 70,
                      100,
                    ],
                  }}
                />
              </View>
            </View>
          )}
          renderError={(
            errorDomain: string | undefined,
            errorCode: number,
            errorDesc: string,
          ) => {
            return (
              // <View>
              //   <Text>加载失败，请返回后重新打开1</Text>
              // </View>
              <>
                <Progress.Page
                  // loading={state.loading}
                  fail={true}
                  onPressReload={reloadWebView}
                  syncRenderChildren></Progress.Page>
              </>
            );
          }}
          //   dataDetectorTypes={'none'}
          onNavigationStateChange={navState => {
            // setCurrentH5(navState)
          }}
          {...rest}
        />

        {/* {keyboardStatus.visible ? <View style={{ height: keyboardStatus.height }}></View> : null} */}

        {/* <Text onPress={handlePress}>1111</Text> */}
        {/* </SafeAreaView> */}
      </View>

      {/* 扫码 */}
      <XlbScanCode
        show={show}
        setBarCodes={(result: any) => {
          if (!result?.code && !result?.data?.item_id && !result?.item_id)
            return;
          if (result?.code) {
            sendMessageToH5('codeResult', {
              codeType: 'code',
              codeValue: result?.code,
            });
          } else if (result?.data?.item_id) {
            sendMessageToH5('codeResult', {
              codeType: 'data_item_id',
              codeValue: result?.data?.item_id,
            });
          } else if (result?.item_id) {
            sendMessageToH5('codeResult', {
              codeType: 'item_id',
              codeValue: result?.item_id,
            });
          }
        }}
        color={'white'}
        setShow={(show: boolean) => {
          setShow(show);
        }}
      />

      {/* 图片上传 */}
      <XlbUploadImage
        mediaType={mediaType}
        uploadType={uploadType}
        maxFiles={uploadFilesConfig?.maxFiles}
        multiple={uploadFilesConfig?.multiple}
        cameraPick={uploadFilesConfig?.cameraPick}
        series={uploadFilesConfig?.series}
        customPickList={customPickerList}
        // 连续拍照
        onSeriesShoot={() => {
          navigation.dispatch(
            CommonActions.navigate('RemoteAppSms.ShopPhoto', uploadFilesConfig),
          );
          setUploadVisible(false);
        }}
        handleUploadImage={(data: any) => {
          if (data) {
            setUploadVisible(false);
            upload(data);
            // sendMessageToH5('uploadResult', data)
          }
        }}
        handleUploadFile={(data: any) => {
          setUploadVisible(false);
          console.log('文件信息', uploadFilesConfig?.suffix, data);
          if (!uploadFilesConfig?.suffix?.includes(data.suffix.toLowerCase())) {
            console.log('文件格式不正确');
            Toast.show(`请选择${uploadFilesConfig?.suffix.join('、')}文件`, {
              position: 80,
            });
            return false;
          }
          if (data) {
            uploadFile(data);
            // sendMessageToH5('uploadResult', data)
          }
        }}
        visible={uploadVisible}
        setVisible={setUploadVisible}
      />
      <XlbPreview ref={xlbPreviewRef} />

      <XlbOpenMap ref={xlbOpenMapRef} />

      <XlbUploadFiles
        onUpload={handleUploadFile}
        uploadConfig={uploadFilesConfig}
        uploadType={uploadType}
        ref={xlbUploadFileRef}
      />

      <ProModal statusBarTranslucent visible={!!pdfUrl} animationType="slide">
        <Flex>
          <View style={styles.pdf}>
            <PDF
              trustAllCerts={false}
              source={{uri: pdfUrl, cache: false}}
              style={styles.pdf}
            />
          </View>
          <Button onPress={() => setPdfUrl('')}>关闭</Button>
        </Flex>
      </ProModal>

      {isSafeAreaView && (
        <SafeAreaView
          style={{flex: 0, backgroundColor: 'white'}}></SafeAreaView>
      )}

      {/* 创建聊天 */}
      <ImPopup
        visible={pageVisible}
        onClose={() => setPageVisible(false)}
        checkboxOption={checkboxOption}
        setCheckboxOption={setCheckboxOption}
        userTel={userTel}
        onConfirm={selectedUsers => handleCreateRoom(detail)}
        detail={detail}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
    width: '100%',
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
    // backgroundColor: '#000',
  },
  loading: {
    marginTop: ScreenHeight * 0.2,
    alignItems: 'center',
    marginBottom: 215,
  },
  pdf: {
    flex: 1,
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height - 100,
  },
});
const ForwardedWebViewPage = forwardRef(WebViewPage);

// 更新全局导出和默认导出
global.__XLB_COMPONENT__.WebViewPage = ForwardedWebViewPage;

export default ForwardedWebViewPage;
