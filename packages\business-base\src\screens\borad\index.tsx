import React, { useEffect, useRef, useState } from 'react'
import {
  DeviceEventEmitter,
  ImageBackground,
  NativeModules,
  Platform,
  StatusBar,
  Text,
  View,
  StyleSheet,
  Image,
  Pressable,
  ScrollView,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native'

import { authModel, roleAuth } from '@xlb/business-base/src/models/auth'
import { BottomUp, commonStyles, cs, Header, Item, Space, XText } from '@xlb/common/src/components'
import useAliCloudPush from '@xlb/common/src/hooks/useAliCloudPush'

import { useNavigation } from '@react-navigation/native'
import { XlbStoreText, XlbToggleChange } from '@xlb/common/src/components/features'

import { colors, normalize } from '@xlb/common/src/config/theme'
import useStausBarHeight from '@xlb/business-base/src/hook/useStatusBarHeight'
import SwiperCard from '@xlb/common/src/components/Swiper'
import PageWarpper from '@xlb/business-base/src/components/pageWarpper'
import SelectDate from '@xlb/business-base/src/components/selectDate'
import { XlbIconfont, XlbButton, XlbTabs, XlbSegmentedControl, XlbDatePickerView, XlbCard } from '@xlb/components-rn'
import useStore from '@xlb/common/src/components/features/xlbStoreText/model'
import { images } from '@xlb/common/src/config/images'
import SalesAnalysis from './salesAnalysis'
import DatePicker from 'react-native-date-picker'
import dayjs from 'dayjs'
import { DatePickerView, Picker, PickerView } from '@ant-design/react-native'
import RNEChartsPro from 'react-native-echarts-pro'
import useBoradStore from './store'
import { dateMap } from '@xlb/business-base/src/components/selectDate/config'
import { ScreenWidth } from '@dplus/rn-ui'
import XlbIcon from '@xlb/common/src/assets/iconfont'
const { height } = useStausBarHeight()

interface FastProps {
  value: string | number
  onChange: (value: string | number) => void
}
const Fast: React.FC<FastProps> = ({ value, onChange }) => {
  const [show, setShow] = useState(false)
  const [pickerValue, setPickerValue] = useState<any>([value])
  return (
    <>
      <TouchableOpacity
        onPress={() => {
          if (authModel?.state?.userInfos?.isTemporarily) return
          setShow(true)
        }}
        style={{ width: 32, height: 20, display: 'flex', justifyContent: 'center', position: 'absolute', right: 0, top: normalize(10) }}
      >
        <Image style={{ width: 32, height: 20 }} resizeMode="cover" source={images.selectSystem} />
      </TouchableOpacity>
      <BottomUp percent={0.35} isVisible={show} onBackdropPress={() => setShow(false)}>
        <Space>
          <TouchableWithoutFeedback
            onPress={() => {
              setShow(false)
            }}
          >
            <Text style={[cs.second, cs.fz14]}>取消</Text>
          </TouchableWithoutFeedback>

          <Text style={[cs.bold, cs.fz16, cs.black]}>{'选择报表'}</Text>
          <Text
            style={[cs.bold, cs.first, cs.fz14]}
            onPress={() => {
              setShow(false)
              onChange?.(pickerValue)
            }}
          >
            确定
          </Text>
        </Space>
        <PickerView
          data={[
            {
              value: '1',
              label: '销售分析',
            },
            {
              value: '2',
              label: 'BOSS报表',
            },
            {
              value: '3',
              label: '营业收款分析',
            },
            {
              value: '4',
              label: '配送分析',
            },
            {
              value: '5',
              label: '商品销售分析',
            },
            {
              value: '6',
              label: '区域销售分析',
            },
          ]}
          value={pickerValue}
          onChange={(value) => {
            console.log('value', value)
            setPickerValue(value)
            // handleChange(value)
            // setValue(value)
          }}
          // cascade={true}
          style={{ height: 200, marginTop: 10 }}
          itemHeight={44}
          itemStyle={{
            padding: 0,
          }}
        />
      </BottomUp>
    </>
  )
}

/**
 * 首页
 * @constructor
 */
const Home: React.FC = () => {
  const navigation: any = useNavigation()
  const bizday = useBoradStore((state: any) => state.bizday)
  const dateType = useBoradStore((state: any) => state.dateType)
  const setDateType = useBoradStore((state: any) => state.setDateType)
  const setBizDay = useBoradStore((state: any) => state.setBizDay)
  const store = useStore((state: any) => state)
  const [tabValue, setTabValue] = useState('1')
  const userInfos = authModel?.state?.userInfos
  // 监听路由，如果门店用户没有进件，需要弹窗提示
  useEffect(() => {}, [navigation])

  const renderDateText = () => {
    return (
      <View style={{ flex: 1, alignItems: 'center', flexDirection: 'row', justifyContent: 'center' }}>
        <XText
          size12
          style={{
            color: 'rgba(30, 33, 38, 0.45)',
          }}
        >
          {dateMap?.[dateType]?.getText(bizday) || ''}
        </XText>
      </View>
    )
  }

  let isNext = dayjs(bizday[1]).format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD') || dayjs(bizday[1]).isAfter(dayjs())

  const addRoute = () => {
    // navigation.navigate('ErpSelectStore', {
    //   backRoute: 'goBack',
    //   model: store,
    //   listName: 'storeList',
    //   setListName: 'setStoreList',
    //   isMultiple: true,
    //   postUrl: '/erp/hxl.erp.store.short.page',
    // })
    navigation.navigate('selectStoreH5', {
      isMultiple: true,
      params: {
        storeIds: store.storeList.map((v: any) => v.id),
      },
      storeIds: store.storeList.map((v: any) => v.id),
      onSelect: (data) => {
        // console.log('当前选择门店', data)
        store.setStoreList(data.list)
      },
    })
  }

  return (
    <PageWarpper>
      {/* 头部日期切换 选择门店 */}
      <View style={styles.header}>
        <SelectDate
          type={dateType}
          date={bizday}
          onChange={(date, dateType) => {
            setBizDay(date)
            setDateType(dateType)
          }}
        >
          <View style={{ alignItems: 'center', flexDirection: 'row' }}>
            <XText semiBold style={{ marginRight: normalize(4) }}>
              {dateMap[dateType].getDateTypeText(bizday)}
            </XText>
            <XlbIconfont name="zhankai" size={normalize(12)} />
          </View>
        </SelectDate>

        <View style={{ flex: 1 }}></View>

        <XlbButton style={{ height: normalize(32), backgroundColor: '#fff', borderWidth: 0 }} onPress={() => addRoute()}>
          <View style={{ alignItems: 'center', flexDirection: 'row' }}>
            <XlbIcon name="dianpu" size={normalize(16)} style={{ marginRight: normalize(4) }} />
            <XText semiBold>{store.storeList?.length > 1 ? '多家门店' : store.storeList?.[0]?.store_name}</XText>
          </View>
        </XlbButton>
      </View>

      <View style={{ paddingHorizontal: normalize(12), flex: 1, position: 'relative' }}>
        <XlbTabs
          defaultTheme={false}
          paddingWidth={normalize(24)}
          onPress={(value) => {
            setTabValue(value)
          }}
          value={tabValue}
          data={[
            {
              key: '1',
              title: '销售分析',
            },
            {
              key: '2',
              title: 'BOSS报表',
            },
            {
              key: '3',
              title: '营业收款分析',
            },
            {
              key: '4',
              title: '配送分析',
            },
            {
              key: '5',
              title: '商品销售分析',
            },
            {
              key: '6',
              title: '区域销售分析',
            },
          ]}
          renderTab={() => {
            return <SalesAnalysis></SalesAnalysis>
          }}
        />
        <Fast
          value={tabValue}
          onChange={(value) => {
            console.log('tabValue', value)
            setTabValue(value?.[0])
          }}
        ></Fast>
      </View>

      {/*  底部时间切换 */}
      <View style={styles.footer}>
        <XlbButton
          style={{ height: normalize(32), minWidth: normalize(88), borderWidth: 0 }}
          onPress={() => {
            // setBizDay(dateMap[dateType].prev(bizday))
            setBizDay(dateMap[dateType].prev(bizday))
          }}
        >
          <View style={{ alignItems: 'center', flexDirection: 'row' }}>
            <XlbIcon name="sanjiaoxiala" size={normalize(16)} style={{ marginLeft: -8, transform: [{ rotate: '90deg' }] }} />
            <XText semiBold>{dateMap[dateType].prevText(bizday)}</XText>
          </View>
        </XlbButton>
        {renderDateText()}
        <XlbButton
          style={{ height: normalize(32), minWidth: normalize(88), borderWidth: 0 }}
          // type="primary"
          disabled={isNext}
          onPress={() => {
            console.log('dateMap[dateType].next(bizday)', dateMap[dateType].next(bizday))
            setBizDay(dateMap[dateType].next(bizday))
          }}
        >
          <View style={{ alignItems: 'center', flexDirection: 'row' }}>
            <XText
              semiBold
              style={{
                opacity: isNext ? 0.35 : 1,
              }}
            >
              {dateMap[dateType].nextText(bizday)}
            </XText>
            <XlbIcon
              name="sanjiaoxiala"
              size={normalize(16)}
              style={{
                opacity: isNext ? 0.35 : 1,
                marginRight: -8,
                transform: [{ rotate: '-90deg' }],
              }}
            />
          </View>
        </XlbButton>
      </View>
    </PageWarpper>
  )
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    paddingTop: normalize(4),
    paddingBottom: normalize(4),
    paddingHorizontal: normalize(12),
    // backgroundColor: 'red',
  },
  footer: {
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(8),
    flexDirection: 'row',
    backgroundColor: '#fff',
  },
})

export default Home
