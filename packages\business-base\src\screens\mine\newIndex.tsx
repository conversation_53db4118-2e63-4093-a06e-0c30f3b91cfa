import React, { useEffect, useState } from 'react'
import { LazyLoad, XIcon, XText } from '@xlb/common/src/components'
import Toast from 'react-native-root-toast'
import { images } from '@xlb/common/src/config/images'
import { normalize } from '@xlb/common/src/config/theme'
import { Routes } from '@xlb/common/src/config/route'
import { authModel, roleAuth } from '../../models/auth'
import { useNavigation } from '@react-navigation/native'
import { DeviceEventEmitter, Image, Linking, Pressable, StyleSheet, View } from 'react-native'
import { getAuth } from '@xlb/common/src/utils/combineDeliverOrderInAuth'
import useEntranceStore from '../entrance/useEntranceStore'
import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'
import Loading from '@xlb/common/src/components/RootView/Loading'
import useSelectStore from '../home/<USER>/ErpHome/store'
import useStore from '@xlb/common/src/components/features/xlbStoreText/model'
import useRefreshStore from '@xlb/common/src/models/refresh'
import { $modalAlert } from '@xlb/common/src/components/SecondModal'
import PageWarpper from '../../components/pageWarpper'
import Item from './components/Item'
import Clipboard from '@react-native-clipboard/clipboard'

/**
 * 我的页面
 */
const Mine: React.FC = () => {
  const setStoreList = useSelectStore((state: any) => state.setStoreList)
  const setRefresh = useRefreshStore((state: any) => state.setHomeRefresh)
  const setStoreLists = useStore((state: any) => state.setStoreList)
  const navigation = useNavigation<any>()

  const [tmsCheckData, setTmsCheckData] = useState<any>({})

  const _goTo = (route: string, params: any = null) => navigation.navigate(route, params)

  const system = authModel?.state?.userInfos.supplier ? 'SCM' : useEntranceStore((state: any) => state.system)

  const getTmsCheckStates = async () => {
    const id = authModel?.state?.userInfos?.id
    if (!id) return
    const res = await ErpHttp.post<CommonResponse>(
      '/tms/hxl.tms.sign.certification.check',
      {
        tel: authModel?.state?.userInfos?.tel,
      },
      { timeout: 20000 }
    )
    if (res.code == 0) {
      setTmsCheckData(res.data)
    }
  }

  const changeStore = async () => {
    if (authModel?.state?.userInfos?.access_token?.length <= 6) {
      Toast.show('无操作权限')
      return
    }

    navigation.navigate('ErpSelectStore', {
      isMultiple: false,
      postUrl: '/erp/hxl.erp.user.switchstore.page',
      storeChange,
    })
  }
  const storeChange = async (item: any) => {
    const res = await ErpHttp.post<CommonResponse>('/erp/hxl.erp.user.store.switch', { id: item?.id })
    if (res?.code === 0 && res?.data) {
      authModel?.setUserInfos(res.data)
      authModel?.setSystem(roleAuth(res.data))
    }
    // 解决首页切换门店问题
    setStoreList([])
    setStoreLists([])
    setRefresh()
    DeviceEventEmitter.emit('DepartDocumenRefresh') //刷新待办事项内的发车单
  }

  const getCombineAuth = async () => {
    await getAuth()
  }

  const toCheck = async () => {
    Loading.show()
    const res = await ErpHttp.post<CommonResponse>('/tms/hxl.tms.sign.certification', {
      tel: authModel?.state?.userInfos?.tel,
      end_point: 'H5',
    })
    if (res.code == 0) {
      if (res.data.console_url) {
        Loading.hide()
        $modalAlert(
          '是否跳转认证链接?',
          '',
          async () => {
            Linking.openURL(res.data.console_url)
          },
          () => {
            Clipboard.setString(res.data.console_url)
            Toast.show('复制成功，请去浏览器粘贴打开')
          },
          '一键复制'
        )
      } else {
        Loading.hide()
        Toast.show('数据有误')
      }
    }
  }

  useEffect(() => {
    const WMSWaitHandleRefresh = DeviceEventEmitter.addListener('TMSCheckRefresh', () => {
      getTmsCheckStates()
    })
    getTmsCheckStates()
    return () => {
      WMSWaitHandleRefresh.remove()
    }
  }, [])
  useEffect(() => {
    getCombineAuth()
    getTmsCheckStates()
  }, [])

  return (
    <PageWarpper>
      <View style={styles.view_content}>
        <View style={[styles.xShadowBox, { backgroundColor: '#E6EEFF' }]}>
          <View
            style={[
              styles.xShadowBox,
              {
                paddingTop: normalize(24),
                paddingLeft: normalize(20),
                paddingRight: normalize(20),
              },
            ]}
          >
            <View style={[styles.flexDirectionRow, styles.edit_content]}>
              {authModel.state.userInfos?.avatar_url ? (
                <Image style={styles.avatar} source={{ uri: authModel.state.userInfos?.avatar_url }} />
              ) : (
                <Image source={images['newAvatar']} style={styles.avatar} />
              )}
              <View style={[{ flex: 1 }]}>
                <View style={[styles.flexDirectionRow, { justifyContent: 'space-between', marginBottom: 8 }]}>
                  <XText size18 style={{ color: '#1F2126', flex: 1 }} ellipsizeMode={'tail'} numberOfLines={1}>
                    {authModel.state.userInfos?.name || '--'}
                  </XText>
                  {!authModel?.state?.userInfos?.isTemporarily && (
                    <View style={[styles.editBtn, styles.jac]}>
                      <XText size12 style={{ color: 'rgba(30, 33, 38, 0.70)' }} onPress={() => _goTo(Routes.Profile)}>
                        编辑资料
                      </XText>
                    </View>
                  )}
                </View>
                <XText size14 style={{ color: 'rgba(30,33,38,0.45)' }} ellipsizeMode={'middle'} numberOfLines={1}>
                  {authModel.state.userInfos?.tel}
                </XText>
              </View>
            </View>
            <Pressable
              onPress={() => navigation.navigate('SelectRole')}
              style={{
                borderTopColor: 'rgba(31, 33, 38, 0.10)',
                borderTopWidth: 0.5,
                marginTop: 24,
                height: 52,
                alignItems: 'center',
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}
            >
              <XText size15 style={{ color: 'rgba(31, 33, 38, 1)' }}>
                {authModel.state.userInfos?.company_id}｜{authModel.state.userInfos?.company_name}
              </XText>
              <XIcon name={'iconleft'} size={16} />
            </Pressable>
          </View>

          <Pressable
            onPress={() => toCheck()}
            style={[
              styles.xShadowBox,
              {
                height: 60,
                backgroundColor: '#E6EEFF',
                paddingVertical: normalize(12),
                paddingHorizontal: normalize(20),
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              },
            ]}
          >
            <Image
              source={images[tmsCheckData?.is_activated && tmsCheckData.proxy_operator_is_verified ? 'proxyOperated' : 'proxyOperator']}
              style={{
                width: 32,
                height: 32,
                marginRight: normalize(8),
              }}
            />
            <View style={{ flex: 1 }}>
              {tmsCheckData?.is_activated && tmsCheckData.proxy_operator_is_verified ? (
                <XText size14 style={{ color: 'rgba(30, 33, 38, 0.45)', textAlign: 'right' }}>
                  已认证
                </XText>
              ) : (
                <>
                  <XText size15 style={{ color: 'rgba(31, 33, 38, 1)' }}>
                    电子签认证
                  </XText>
                  <View style={{ marginBottom: 2 }}></View>
                  <XText size12 style={{ color: 'rgba(30, 33, 38, 0.45)' }}>
                    认证电子签，即享在线签约服务
                  </XText>
                </>
              )}
            </View>
            {!(tmsCheckData?.is_activated && tmsCheckData.proxy_operator_is_verified) && <XIcon name={'iconleft'} size={16} />}
          </Pressable>
        </View>

        <Item label={'收藏'} iconName={'shoucang'} onPress={() => navigation.navigate('Collect')} />

        <Item
          label={'门店'}
          iconName={'mendian'}
          subLabel={authModel.state.userInfos?.store?.store_name || ''}
          onPress={() => {
            if (authModel?.state?.userInfos?.isTemporarily) return
            changeStore()
          }}
        />

        <View style={{ borderRadius: 8, backgroundColor: 'rgba(255, 255, 255, 1)', marginTop: normalize(12) }}>
          <Item label={'客服与帮助'} iconName={'bangzhu'} marginTop={0} onPress={() => navigation.navigate('HelpPage')} />
          <View style={{ borderTopColor: 'rgba(31, 33, 38, 0.10)', borderTopWidth: 0.5, marginHorizontal: 20 }}></View>
          <Item label={'设置'} iconName={'shezhi'} marginTop={0} onPress={() => navigation.navigate('MinSetting')} />
        </View>

        {/*<Item label={'登录设备'} iconName={'shouji'} onPress={()=>{}}/>*/}

        <Item label={'关于新零帮'} iconName={'guanyu'} onPress={() => _goTo(Routes.AboutVersion)} />
      </View>
    </PageWarpper>
  )
}

const styles = StyleSheet.create({
  view_content: {
    paddingLeft: normalize(12),
    paddingRight: normalize(12),
  },
  edit_content: {
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  avatar: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: normalize(64),
    height: normalize(64),
    marginRight: normalize(6),
    borderRadius: 32,
  },
  editBtn: {
    // backgroundColor: '#F2F3F5',
    paddingLeft: 10,
    paddingRight: 14,
    borderRadius: 12,
    height: 24,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.10)',
  },
  ids: {
    marginRight: 14,
    marginTop: 14,
  },
  flexDirectionRow: {
    flexDirection: 'row',
  },
  row: {
    marginBottom: 6,
  },
  xShadowBox: {
    backgroundColor: '#FFF',
    borderRadius: normalize(8),
  },
  jac: {
    justifyContent: 'center',
    alignItems: 'center',
  },
})

const LazyMine = () => {
  return (
    <LazyLoad>
      <Mine />
    </LazyLoad>
  )
}

export default LazyMine
