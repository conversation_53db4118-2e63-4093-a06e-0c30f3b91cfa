import Config from "react-native-config";

const CONST = {
  ENVIRONMENT: {
    DEV: 'development',
    PRODUCTION: 'production',
  },
  // 范围
  RANGE: {
    // 最大值
    MAX: {
      MONEY: 999999999.99,
      PRICE: 999999999.9999,
      QUANTITY: 999999999.999,
      // 备注最大长度
      MEMO: 200,
    },
    // 最小值
    MIN: {
      MONEY: -999999999.99,
      PRICE: 0,
      QUANTITY: -999999999.999,
    },
  },
  ZERO: {
    // 固定的位数
    FIXED: {
      QUANTITY: 3,
      MONEY: 2,
      PRICE: 4,
    },
    // 格式化 数量 金额 价格
    FORMAT: {
      QUANTITY: '0.000',
      MONEY: '0.00',
      PRICE: '0.0000',
    },
  },
  // 通用正则常量
  REGEX: {
    // 数字校验
    NUMBER: /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/,
    // 非负数
    NONNEGATIVE: /^\d+(\.\d+)?$/,
    //正整数
    POSITIVEINTNUMBER: /^[1-9]+[0-9]*$/,
    // 长宽高两位小数 不大于1000
    VOLUME:
      /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
    // 手机号
    TEL: /^1[3456789]\d{9}$/,
    // 身份证
    IDCARD: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  },
  NewApiService: {
    bi: 'http://bi.bi.ali-' + Config.ENV + '.xlbsoft.com/',
    'bi-new': 'https://bi-new.bi.ali-' + Config.ENV + '.xlbsoft.com/',
    bms: 'http://bms.bms.ali-' + Config.ENV + '.xlbsoft.com/',
    center: 'http://center.center.ali-' + Config.ENV + '.xlbsoft.com/',
    erp_app: 'http://erp-app.erp.ali-' + Config.ENV + '.xlbsoft.com/',
    erp_export: 'http://erp-export.erp.ali-' + Config.ENV + '.xlbsoft.com/',
    erp: 'https://react_web.react_web.ali-' + Config.ENV + '.xlbsoft.com/',
    kms: 'http://kms.kms.ali-' + Config.ENV + '.xlbsoft.com/',
    member: 'http://member.member.ali-' + Config.ENV + '.xlbsoft.com/',
    oa: 'http://oa.oa.ali-' + Config.ENV + '.xlbsoft.com/',
    retail: 'http://retail.retail.ali-' + Config.ENV + '.xlbsoft.com/',
    retail_new:
      'http://retail-new.retail-new.ali-' + Config.ENV + '.xlbsoft.com/',
    scm: 'http://scm.scm.ali-' + Config.ENV + '.xlbsoft.com/',
    tms: 'http://tms.tms.ali-' + Config.ENV + '.xlbsoft.com/',
    wms_export: 'http://wms-export.wms.ali-' + Config.ENV + '.xlbsoft.com/',
    wms: 'http://wms.wms.ali-' + Config.ENV + '.xlbsoft.com/',
    'xlb-wms': 'http://react-web.react-web.ali-' + Config.ENV + '.xlbsoft.com/',
    // 'xlb-wms':"http://10.2.0.54:8081/",
    allowance: 'http://allowance.allowance.ali-' + Config.ENV + '.xlbsoft.com/',
    auth: 'http://auth.auth.ali-' + Config.ENV + '.xlbsoft.com/',
    contract: 'http://contract.contract.ali-' + Config.ENV + '.xlbsoft.com/',
    dmc: 'http://dmc.dmc.ali-' + Config.ENV + '.xlbsoft.com/',
    ems: 'http://ems.ems.ali-' + Config.ENV + '.xlbsoft.com/',
    export: 'http://export.export.ali-' + Config.ENV + '.xlbsoft.com/',
    fsms: 'http://fsms.fsms.ali-' + Config.ENV + '.xlbsoft.com/',
    gateway: 'http://gateway.gateway.ali-' + Config.ENV + '.xlbsoft.com/',
    hrs: 'http://hrs.hrs.ali-' + Config.ENV + '.xlbsoft.com/',
    kingdee: 'http://kingdee.kingdee.ali-' + Config.ENV + '.xlbsoft.com/',
    milkyway: 'http://milkyway.milkyway.ali-' + Config.ENV + '.xlbsoft.com/',
    open_api: 'http://open-api.open-api.ali-' + Config.ENV + '.xlbsoft.com/',
    wechat: 'http://wechat.wechat.ali-' + Config.ENV + '.xlbsoft.com/',
    wmshandle: 'http://wmshandle.wmshandle.ali-' + Config.ENV + '.xlbsoft.com/',
    workorder: 'http://workorder.workorder.ali-' + Config.ENV + '.xlbsoft.com/',
    storemanager:
      'http://storemanager.storemanager.ali-' + Config.ENV + '.xlbsoft.com/',
    fss: 'http://fss.bms.ali-' + Config.ENV + '.xlbsoft.com/',
    xpay: 'http://xpay.xpay.ali-' + Config.ENV + '.xlbsoft.com/',
    workimg: 'http://workimg.workimg.ali-' + Config.ENV + '.xlbsoft.com/',
    purchase: 'http://purchase.purchase.ali-' + Config.ENV + '.xlbsoft.com/',
    oms: 'http://retail-oms-c.retail-new.ali-' + Config.ENV + '.xlbsoft.com/',
    sms: 'http://sms.sms.ali-' + Config.ENV + '.xlbsoft.com/',
    hxl: 'http://retail-oms-c.retail-new.ali-' + Config.ENV + '.xlbsoft.com/',
    mdm: 'http://mdm.mdm.ali-' + Config.ENV + '.xlbsoft.com/',
    logistics: 'http://logistics.logistics.ali-' + Config.ENV + '.xlbsoft.com/',
  },
};

export default CONST;
