<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>zh-<PERSON></string>
	<key>CFBundleDisplayName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(VERSION_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wxc0590763665be19b</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(VERSION_NAME)</string>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>weixinURLParamsAPI</string>
		<string>weixinULAPI</string>
		<string>weixin</string>
		<string>iosamap</string>
		<string>baidumap</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>此APP在链接蓝牙打印机时需调用您的蓝牙权限</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>访问蓝牙权限</string>
	<key>NSCameraUsageDescription</key>
	<string>此APP在扫描商品条码时需调用您的相机权限</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>此APP需要调用您的持续位置信息，用以更好的进行APP地图服务
	</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>AnotherUsageDescription</key>
		<string>This app needs accurate location so it can show you relevant results.</string>
		<key>ExampleUsageDescription</key>
		<string>This app needs accurate location so it can verify that you are in a supported region.</string>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>此APP在门店选址时需调用您的位置权限</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>此APP在拍摄视频上传时需要您的麦克风权限</string>
	<key>NSMotionUsageDescription</key>
	<string>此APP在导航时需要获取您的气压值，用以获得相对海拔高度变化</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>访问图库</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>此APP在上传门店地址时需调用您的相册权限</string>
	<key>RCTNewArchEnabled</key>
	<true/>
	<key>UIAppFonts</key>
	<array>
		<string>D-DIN-PRO-400-Regular.ttf</string>
		<string>D-DIN-PRO-500-Medium.ttf</string>
		<string>D-DIN-PRO-600-SemiBold.ttf</string>
		<string>D-DIN-PRO-700-Bold.ttf</string>
		<string>D-DIN-PRO-800-ExtraBold.ttf</string>
		<string>HarmonyOS_Sans_Black.ttf</string>
		<string>HarmonyOS_Sans_Bold.ttf</string>
		<string>HarmonyOS_Sans_Light.ttf</string>
		<string>HarmonyOS_Sans_Medium.ttf</string>
		<string>HarmonyOS_Sans_Regular.ttf</string>
		<string>HarmonyOS_Sans_Thin.ttf</string>
		<string>iconfont.ttf</string>
		<string>iconfontNew.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>fetch</string>
		<string>location</string>
		<string>nearby-interaction</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedExternalAccessoryProtocols</key>
	<array>
		<string>com.hxl.xlb</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
