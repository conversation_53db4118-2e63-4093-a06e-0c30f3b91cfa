import React, { useEffect } from 'react'
import styles from '../../newStoreSaleAnalysis/style'
import { XlbToggleChange } from '@xlb/common/src/components/features'
import { View } from 'react-native'
import useBossTableStore from '../model'
import XlbSelectText from '../../../components/xlbSelectText'
import useSelectCityState from '@xlb/common/src/components/BusinessAreaAndCitySelector/useSelectCityState'
import BusinessAreaAndCitySelector from '@xlb/common/src/components/BusinessAreaAndCitySelector'
import { useNavigation } from '@react-navigation/native'
import useXlbDatePicker from '@xlb/common/src/components/features/xlbDatePicker/model'

interface CommonAreaTimeProps {
  city: any
  setCity: (data: any) => void
}

const CommonAreaTime = (props: CommonAreaTimeProps) => {
  const { city, setCity } = props
  const { setIsCityShow } = useSelectCityState((state: any) => state)
  const { toggleList, setToggleListActive, bizday, setBizDay, date_range } = useBossTableStore((state: any) => state)
  const navigation = useNavigation<any>()
  const fontArr = useXlbDatePicker((state: any) => state.dateArr)

  useEffect(() => {
    if (toggleList.find((v: any) => v.active).value === 'CUSTOM' && JSON.stringify(fontArr) !== JSON.stringify(bizday)) {
      setBizDay(fontArr)
    }
  }, [fontArr, date_range])
  return (
    <View style={styles.storeTimeFilterView}>
      <XlbSelectText showText={city.data.length ? '部分区域' : '全部区域'} addRoute={() => setIsCityShow(true)} iconDropColor={'#6CA9FF'} />
      <XlbToggleChange
        toggleList={toggleList}
        setToggleListActive={(index: number) => {
          setToggleListActive(index)
          if (index === 3) {
            navigation.navigate('xlbDatePicker', { dateArr: bizday })
          }
        }}
      />
      <BusinessAreaAndCitySelector
        onChange={(val: { type: string; data: any[] }) => {
          setCity(val)
        }}
        postUrl={'/erp/hxl.erp.store.area.app.find'}
        selectedList={city.data}
      />
    </View>
  )
}

export default CommonAreaTime
