import React, { useState } from 'react'
import { Box, Toast } from 'native-base'
import { <PERSON>er, HeaderTitle, LazyLoad, Item, Flex, XIcon, ProText, ProButton, XText } from '@xlb/common/src/components'
import { colors } from '@xlb/common/src/config/theme'
import { XFBottomUser } from '@xlb/common/src/components/features'
import { isValidCode, isValidMobile } from '@xlb/common/src/utils/format'
import { Text, TouchableWithoutFeedback, View, StyleSheet, TouchableOpacity, StatusBar } from 'react-native'
import { FormProvider, useForm } from 'react-hook-form'
import { useProRoute } from '@xlb/common/src/hooks'
import { $alert } from '@xlb/common/src/utils/overlay'
import Api from '@xlb/business-base/src/api/mine.api'
import FromItemStyle from '@xlb/common/src/components/BaseForm/FromItemStyle'
import Field from '@xlb/common/src/components/BaseForm/Field'
import Show from '@xlb/common/src/components/Show/Show'
import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'
// import useRefresh from '@xlb/business-kms/src/screens/clientMange/hooks/useRefresh'
import { Routes } from '@xlb/common/src/config/route'
import { authModel } from '@xlb/business-base/src/models/auth'
import useRefreshStore from '@xlb/common/src/models/refresh'
/**
 * 设置 => 个人资料 => 修改密码
 */
const ChangePassword = (props: any) => {
  const { navigation, route } = useProRoute()
  const data = route.params || {}
  console.log(26, data)

  // const refresh = useRefresh()
  const setRefresh = useRefreshStore((state: any) => state.setRefresh)
  const baseForm = useForm({
    mode: 'onBlur',
    defaultValues: {
      ...data,
      account: data.account,
      pwd: '',
      confirm_pwd: '',
      code: '',
    },
  })
  const [loading, setLoading] = useState<any>(false) //是否在等待
  const [timeLeft, setTimeLeft] = useState<any>(60) //时间

  const formItems = [
    {
      name: 'pwd',
      label: '新的密码',
      placeholder: '请输入',
      // textColor: useHasAuth(edit) && (data.state === 'INIT' || data.state === 'FOLLOWING') ? undefined : '#86909C',
      require: true,
      // editable: useHasAuth(edit) && (data.state === 'INIT' || data.state === 'FOLLOWING'),
      rules: {
        required: true,
      },
    },
    {
      name: 'confirm_pwd',
      label: '再次输入',
      placeholder: '请输入',
      // textColor: useHasAuth(edit) && (data.state === 'INIT' || data.state === 'FOLLOWING') ? undefined : '#86909C',
      require: true,
      // editable: useHasAuth(edit) && (data.state === 'INIT' || data.state === 'FOLLOWING'),
      rules: {
        required: true,
      },
      // disabled: true,
    },
    {
      name: 'code',
      label: '验证码',
      placeholder: '请输入',
      require: true,
      rules: {
        required: true,
      },
      showButton: true,
      btnOnPress: btnOnPress,
    },
  ]

  // 发送验证码前验证手机号
  const _validate = () => {
    if (!baseForm.getValues().pwd) {
      Toast.show({ title: '请输入新密码' })
      return false
    }

    if (!baseForm.getValues().confirm_pwd) {
      Toast.show({ title: '请重复新密码' })
      return false
    }

    if (baseForm.getValues().pwd !== baseForm.getValues().confirm_pwd) {
      Toast.show({ title: '两次密码不一致' })
      return false
    }
    return true
  }

  const btnOnPress = async () => {
    const res = _validate()
    if (res) {
      //此处为发送验证码逻辑
      setLoading(true)
      let time = 59
      let interval = setInterval(() => {
        setTimeLeft(time)
        time--
        if (time < 0) {
          clearInterval(interval)
          setLoading(false)
          setTimeLeft(60)
        }
      }, 1000)
      const res = await ErpHttp.post<CommonResponse>('/erp/hxl.erp.user.resetcode.send', {
        account: data.account,
        tel: data.tel,
      })
      if (res?.code === 0) {
        Toast.show({ title: `验证码已发送到尾号为${data.tel.substring(data.tel.length - 4)}的手机上` })
      }
    }
  }

  // 保存
  const _save = async (needRefresh = true) => {
    const result = await baseForm.trigger()
    if (result) {
      $alert('提示', `请确认是否保存`, async () => {
        const res: any = await ErpHttp.post<CommonResponse>('/erp/hxl.erp.user.pwd.reset', {
          account: data.account,
          code: baseForm.getValues().code,
          pwd: baseForm.getValues().pwd,
          confirm_pwd: baseForm.getValues().confirm_pwd,
        })

        if (res?.code === 0) {
          Toast.show({ title: '保存成功' })
          if (needRefresh) {
            navigation.navigate('Profile')
          }
        }
      })
    }
  }

  const styles = StyleSheet.create({
    container: {
      marginTop: 5,
      paddingHorizontal: 4,
    },
    titleText: {
      color: '#333',
      fontWeight: 'bold',
    },
  })

  return (
    <LazyLoad>
      {/* <StatusBar  hidden={false} translucent={false} backgroundColor={'#fff'} barStyle={"dark-content"}/> */}
      <Header
        backgroundColor={'#fff'}
        centerComponent={
          <XText size16 semiBold>
            修改密码
          </XText>
        }
        leftComponent={
          <XIcon
            name={'back'}
            color="#000"
            onPress={() => {
              navigation.goBack()
            }}
          />
        }
      />
      {/* <XFBottomUser> */}
      <Flex>
        <FormProvider {...baseForm}>
          {/* <Item showIcon={false} containerStyle={{ paddingVertical: 0 }}>
                
                <FormItems data={formItems} />
              </Item> */}

          <Item showIcon={false} containerStyle={{ paddingVertical: 0 }}>
            <View style={StyleSheet.flatten([styles.container])}>
              {formItems.map((item, index) => {
                return (
                  <View key={index} style={StyleSheet.flatten([FromItemStyle.titleBox, { borderBottomWidth: index === data.length - 1 ? 0 : 0.5 }])}>
                    <ProText numberOfLines={1} ellipsizeMode="tail" style={StyleSheet.flatten([FromItemStyle.label])}>
                      {item.label}
                      {item.require && <Text style={{ color: 'red' }}>*</Text>}
                    </ProText>
                    <View style={FromItemStyle.compContainer}>
                      {<Field {...{ name: item.name, rules: item.rules, placeholder: item.placeholder, disabled: item.disabled }}></Field>}
                    </View>
                    <Show show={!!item.showButton}>
                      <TouchableOpacity
                        style={{
                          width: 92,
                          height: 24,
                          borderColor: colors.primary,
                          borderRadius: 50,
                          borderWidth: 1,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onPress={() => btnOnPress()}
                      >
                        <ProText style={{ color: '#1A6AFF' }}>{!loading ? '获取验证码' : timeLeft + '秒后重试'}</ProText>
                      </TouchableOpacity>
                    </Show>
                  </View>
                )
              })}
            </View>
          </Item>
        </FormProvider>
        {/* <View style={[cs.p10, cs.bgWhite]}>
          </View> */}
        <ProButton onPress={() => _save(true)}>保存</ProButton>
      </Flex>
    </LazyLoad>
  )
}

export default ChangePassword
