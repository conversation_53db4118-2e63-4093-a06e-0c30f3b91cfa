/**
 * 信息编辑
 */
import React, { useState } from 'react'
import { common<PERSON><PERSON><PERSON>, <PERSON>er, HeaderTitle, Row, XBottomFixed, XText, FormItems } from '@xlb/common/src/components'
import { useNavigation, useRoute } from '@react-navigation/native'
import FastImage from 'react-native-fast-image'
import { FormProvider, useForm } from 'react-hook-form'
import { View, Pressable, ScrollView } from 'react-native'
import { useModel } from 'foca'
import { authModel } from '../../models/auth'
import { colors } from '@xlb/common/src/config/theme'
import { ErpHttp } from '@xlb/common/src/services/lib/erphttpnew'
import Toast from 'react-native-root-toast'

const LoginInfo = () => {
  const navigation = useNavigation<any>()
  const [loading, setLoading] = useState<boolean>(false)
  const [timeLeft, setTimeLeft] = useState<any>(60) //时间
  const route: any = useRoute().params
  const sendCode = async () => {
    if (loading) return
    if (!form.getValues().phone) return Toast.show('手机号不能为空')
    const res: CommonResponse = await ErpHttp.post('/erp/hxl.erp.user.changepwdtelcode.send', {
      account: form.getValues().account,
      company_id: form.getValues().company_id,
      source: 'APP',
      tel: form.getValues().phone,
      AccessToken: route.token,
    })
    if (res?.code === 0) {
      setLoading(true)
      let time = 59
      let interval = setInterval(() => {
        setTimeLeft(time)
        time--
        if (time < 0) {
          clearInterval(interval)
          setLoading(false)
          setTimeLeft(60)
        }
      }, 1000)
      Toast.show(`验证码已发送到尾号为${form.getValues().phone.substring(form.getValues().phone.length - 4)}的手机上`)
    }
  }
  const auths = useModel(authModel)
  const form = useForm({
    defaultValues: {
      company_id: route?.company_id || auths.company_id,
      account: route?.account || auths.account,
      password: route?.password || auths.password,
      pwd: '',
      newPassword: '',
      phone: route?.tel,
      code: '',
    },
  })
  const data = [
    {
      name: 'company_id',
      label: '授权机构号',
      editable: false,
      placeholder: ' ',
    },
    {
      name: 'account',
      label: '账号',
      editable: false,
      placeholder: ' ',
    },
    {
      name: 'password',
      label: '原密码',
      require: true,
      editable: false,
      placeholder: ' ',
    },
    {
      name: 'pwd',
      label: '新密码',
      require: true,
      rules: {
        required: true,
      },
      placeholder: '请输入新密码',
    },
    {
      name: 'newPassword',
      label: '确认新密码',
      require: true,
      rules: {
        required: true,
      },
      unitComp: (
        <XText size12 danger>
          {form.watch('newPassword') ? (form.watch('newPassword') !== form.watch('pwd') ? '两次密码不一致' : '') : ''}
        </XText>
      ),
      placeholder: '请输入新密码',
    },
    {
      name: 'phone',
      label: '手机号',
      require: true,
      rules: {
        required: true,
      },
      unitComp: (
        <XText size12 danger>
          {form.watch('phone') ? (/^1[3456789]\d{9}$/.test(form.watch('phone')) ? '' : '请检查格式') : ''}
        </XText>
      ),
      placeholder: '请输入手机号',
    },
    {
      name: 'code',
      label: '验证码',
      require: true,
      rules: {
        required: true,
      },
      unitComp: (
        <XText onPress={sendCode} style={{ color: loading ? '#86909C' : '#1C6AFF' }} primary size15>
          {!loading ? '获取验证码' : timeLeft + '秒后重试'}
        </XText>
      ),
      placeholder: '请输入验证码',
    },
  ]
  const confirm = async () => {
    const result = await form.trigger()
    const postData = {
      account: form.getValues().account,
      code: form.getValues().code,
      company_id: form.getValues().company_id,
      confirm_pwd: form.getValues().newPassword,
      pwd: form.getValues().pwd,
      tel: form.getValues().phone,
      reset_tel: route.tel !== form.getValues().phone,
      AccessToken: route.token,
    }
    if (result) {
      const res: CommonResponse = await ErpHttp.post('/erp/hxl.erp.user.pwd.reset', postData)
      if (res?.code === 0) {
        authModel.setPassWord('')
        navigation.goBack()
      }
    } else {
      if (!postData.pwd) {
        Toast.show('请输入新密码')
        return
      }
      if (!postData.confirm_pwd) {
        Toast.show('请输入确认新密码')
        return
      }
      if (!postData.tel) {
        Toast.show('请输入手机号')
        return
      }
      if (!postData.code) {
        Toast.show('请输入验证码')
        return
      }
    }
  }
  return (
    <>
      <Header centerComponent={<HeaderTitle title="信息编辑" />} onBack={() => navigation.goBack()} />
      <XBottomFixed
        footer={
          <Row style={[{ paddingVertical: 14, paddingHorizontal: 16, backgroundColor: '#fff' }, commonStyles.centerBox]}>
            <Pressable
              onPress={() => navigation.goBack()}
              style={[
                {
                  borderTopLeftRadius: 100,
                  borderBottomLeftRadius: 100,
                  paddingVertical: 11,
                  backgroundColor: '#E5E6EA',
                  flex: 1,
                },
                commonStyles.centerBox,
              ]}
            >
              <XText>取消</XText>
            </Pressable>
            <Pressable
              onPress={confirm}
              style={[
                {
                  borderTopRightRadius: 100,
                  borderBottomRightRadius: 100,
                  backgroundColor: colors.primary,
                  paddingVertical: 11,
                  flex: 1,
                },
                commonStyles.centerBox,
              ]}
            >
              <XText white>确定</XText>
            </Pressable>
          </Row>
        }
      >
        <ScrollView>
          <Row style={[commonStyles.verCenBox, { paddingLeft: 10, paddingVertical: 10, backgroundColor: '#FEF7E7' }]}>
            <FastImage style={{ width: 16, height: 16 }} source={require('@xlb/common/src/assets/images/iconTips.png')} />
            <XText size13 style={{ color: '#FF7D01', marginLeft: 6 }}>
              为保障您的信息安全，请修改密码，并绑定手机号
            </XText>
            <XText size13 style={{ color: '#FF7D01', marginLeft: 6 }}>
              密码至少设置8位数，且必须包含大小写字母
            </XText>
          </Row>
          <View style={{ paddingTop: 12, paddingHorizontal: 12 }}>
            <View style={{ backgroundColor: '#fff', borderRadius: 12 }}>
              <FormProvider {...form}>
                <FormItems
                  data={data}
                  style={{
                    paddingHorizontal: 12,
                  }}
                />
              </FormProvider>
            </View>
          </View>
        </ScrollView>
      </XBottomFixed>
    </>
  )
}

export default LoginInfo
