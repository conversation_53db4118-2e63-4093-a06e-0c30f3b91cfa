import { NativeModules } from 'react-native';

interface NativeHotUpdateInterface {
  runDownload(version: string, downloadUrl: string): void;
  installApk(filePath: string): void;
  hasUpdate(version: string): Promise<boolean>;
  getCurrentVersion(): Promise<string>;
}

const {NativeHotUpdate} = NativeModules as {
    NativeHotUpdate: NativeHotUpdateInterface;
};

const nativeHotUpdate = {
  // 下载热更新
  runDownload(version: string, downloadUrl: string): void {
    NativeHotUpdate.runDownload(version, downloadUrl);
  },

  async installApk(filePath: string) {
    return NativeHotUpdate.installApk(filePath);
  },

  async hasUpdate(version: string): Promise<boolean> {
    return NativeHotUpdate.hasUpdate(version);
  },

  async getCurrentVersion(): Promise<string> {
    return NativeHotUpdate.getCurrentVersion();
  },
};

export default nativeHotUpdate;
