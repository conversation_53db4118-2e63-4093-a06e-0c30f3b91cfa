import React, {useState, useMemo, useRef, useEffect} from 'react';
import type {FC} from 'react';

import {
  View,
  ScrollView,
  Image,
  Pressable,
  StyleSheet,
  TextInput,
  Keyboard,
  Platform,
  Modal,
  hasAndroidPermission,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';

import Toast from 'react-native-root-toast';
import ImagePicker from 'react-native-image-crop-picker';
import Config from 'react-native-config';
import ImageViewer from 'react-native-image-zoom-viewer';

import cloneDeep from 'lodash/cloneDeep';
import last from 'lodash/last';

import {
  Space,
  Popup,
  Blank,
  Toast as ToastXS,
  Dialog,
  Row,
  Col,
  Button,
} from '@fruits-chain/react-native-xiaoshu';
import {TOKEN, XlbIconfont, XlbText, XlbIconfontNew} from '@xlb/components-rn';

import {XlbHeader} from '@xlb/common/src/xlb-components-new';

import {images} from '@xlb/common/src/config/images';
import Loading from '@xlb/common/src/components/RootView/Loading';
import {normalize} from '@xlb/components-rn/styles';

import {authModel} from '@xlb/business-base/src/models/auth';
import {ErpHttp} from '@xlb/common/src/services/lib/erphttp';
// import { savePicture } from '@xlb/common/src/utils/saveToLocal'
// import { savePicture } from 'src/utils/saveToLocal'

import {reg, businessDeptListType, depart} from './define';
import {ActionSheet} from './ActionSheet';

import {IndexStyle, ComponentStyle} from './style';

const UserInfoItem: FC<{
  title: string;
  value?: string;
  isLast: boolean;
  onPress?: (val?: string) => void;
  hideVal?: boolean;
}> = props => (
  <View
    style={StyleSheet.flatten([
      ComponentStyle.card_item,
      props.isLast ? {borderBottomWidth: 0} : {},
    ])}>
    <XlbText font_size_5>{props.title}</XlbText>
    <Pressable
      style={{flex: 1, alignItems: 'flex-end'}}
      onPress={() => {
        if (props.onPress) props.onPress(props.value);
      }}>
      <Space direction="horizontal" gap={0} align="center">
        {props.value && !props.hideVal ? (
          <XlbText grey_7 font_size_5>
            {props.value}
          </XlbText>
        ) : null}
        {props.onPress ? (
          <XlbIconfontNew
            name="youjiantou"
            size={TOKEN.space_3}
            color={TOKEN.grey_25}></XlbIconfontNew>
        ) : null}
      </Space>
    </Pressable>
  </View>
);

const Index: FC = () => {
  const route = useRoute();
  const navigation = useNavigation();

  const nameInputRef = useRef<TextInput>(null);
  const nameInputTempRef = useRef(authModel?.state?.userInfos?.name || '');

  const [visible, setVisible] = useState(false);
  const [data, setData] = useState(authModel?.state?.userInfos || {});
  const [imageList, setImageList] = useState<{url: string}[]>([]);
  const [imageVisible, setImageVisible] = useState(false);
  const [changePhoneDialogShow, setChangePhoneDialogShow] = useState(false);
  const [actionVisible, setActionVisible] = useState(false);

  const isUploaded = useRef(false);

  const is20250501 = useMemo(() => {
    const date = new Date('2025-05-01');
    const now = new Date();
    return now >= date;
  }, []);

  const userInfos = useMemo<any>(
    () => ({
      name: data.name,
      tel: route.params?.tel || data.tel,
      company_name: authModel?.state?.userInfos?.company_name,
      business_dept: authModel?.state?.userInfos?.business_dept,
    }),
    [data, route],
  );

  const userInfo = [
    {
      title: '姓名',
      key: 'name',
      onPress: is20250501
        ? undefined
        : () => {
            setVisible(true);
          },
    },
    {
      key: 'tel',
      title: '手机号码',
      setVal: (val: string) => (val ? val.replace(reg, ' ') : ''),
      onPress: (val?: string) => {
        // navigation.navigate('ModifyPhone', { tel: val.replace(/\s+/g, '') })
        setChangePhoneDialogShow(true);
      },
      isHide: is20250501,
    },
    {
      key: 'tel',
      title: '修改密码',
      setVal: (val: string) => (val ? val.replace(reg, ' ') : ''),
      hideVal: true,
      onPress: (val?: string) => {
        navigation.navigate('VerifyPhone', {tel: val});
      },
      isHide: is20250501,
    },
  ];

  const handleSave = async () => {
    Loading.show();
    const res: any = await ErpHttp.post('/erp/hxl.erp.user.self.update', {
      id: authModel?.state.userInfos.id,
      name: nameInputTempRef.current,
      store_id: authModel?.state.userInfos?.store_id,
      account: authModel?.state.userInfos?.account,
      tel: authModel?.state.userInfos?.tel,
      inner_user: authModel?.state.userInfos?.inner_user,
    });
    setVisible(false);
    if (res?.code == 0) {
      Toast.show('保存成功');
      setData(res.data);
      authModel.setUserInfos({
        ...cloneDeep(authModel?.state.userInfos || {}),
        name: res.data.name,
      });
    }
  };

  const uploadFunc = (param: any, isBig: boolean, oldUrl?: string) => {
    const {path, filename} = param;

    const userInfos = authModel?.state?.userInfos;

    const formData = new FormData();

    const fileReg = /^file:\/\//i;
    const filePath =
      Platform.OS === 'ios' && fileReg.test(path)
        ? path.replace('file://', '')
        : path;
    const imgType = last(path.split('.'));

    formData.append('file', {
      uri: filePath,
      name:
        filename ||
        `${authModel?.state?.userInfos?.tel || new Date().getTime()}.${imgType}`,
      type: 'multipart/form-data',
    } as any);

    if (isBig) {
      const arr = oldUrl ? oldUrl.split('/') : '';

      if (arr) {
        const a = last(arr)?.split('.') || [];
        arr[arr.length - 1] = `${a[0]}big.${last(a)}`;
      }

      const str = filename ? `${filename.split('.')[0]}big` : '';

      formData.append('old_url', arr ? arr.join('/') : '');
      formData.append('file_name', str);
    } else {
      formData.append('old_url', userInfos?.avatar_url || '');
      formData.append('file_name', '');
    }

    fetch(
      `https://erp.erp.ali-${Config.ENV.toLowerCase()}.xlbsoft.com/erp/hxl.erp.user.image.update`,
      {
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
          'Access-Token': `${userInfos?.access_token}`,
          'Api-Version': '1.5.0',
          'Company-Id':
            authModel?.state?.userInfos?.company_id?.toString() || '',
          'User-Id': authModel?.state?.userInfos?.id
            ? authModel?.state?.userInfos?.id?.toString()
            : '',
          'Org-Ids': authModel?.state.userInfos?.org_ids
            ? authModel?.state.userInfos?.org_ids.join(',')
            : '',
        },
        body: formData,
      },
    )
      .then(res => res.json())
      .then(res => {
        Loading.hide();
        if (res?.code === 0) {
          isUploaded.current = isBig;

          const a = cloneDeep(data);

          if (!isBig) {
            authModel.setUserInfos({
              ...cloneDeep(userInfos),
              avatar_url: res.data || '',
            });
            setData({...a, avatar_url: res.data || ''});
          }

          setImageList([{url: res.data}]);

          if (!isUploaded.current) {
            const name: string = last(res.data.split('/'))! || '';
            uploadFunc(
              {
                path: Platform.OS === 'ios' ? param.sourceURL : param.path,
                filename: name,
              },
              true,
              oldUrl,
            );
          }

          isUploaded.current = false;

          if (!isBig) ToastXS.success('头像上传成功');
        } else {
          ToastXS.fail('头像上传失败');
        }
      })
      .catch(() => {
        Loading.hide();
        ToastXS.fail('头像上传失败');
      });
  };

  const upload = async (action: string) => {
    const actionType = {相册: 'openPicker', 拍照: 'openCamera'};

    try {
      const res = await ImagePicker[actionType[action]]({
        cropping: true,
        cropperCircleOverlay: true,
        freeStyleCropEnabled: true,
        mediaType: 'photo',
        cropperChooseText: '确定',
        cropperCancelText: '取消',
      });
      Loading.show();
      uploadFunc(res, false, authModel?.state?.userInfos?.avatar_url);
    } catch (err) {
      Loading.hide();
      if (err?.code !== 'E_PICKER_CANCELLED')
        ToastXS.fail('图片选择失败，请重试');
    }
  };

  const uploadImage = async () => {
    setActionVisible(true);
  };

  useEffect(() => {
    const userInfos = authModel?.state?.userInfos;

    if (userInfos?.avatar_url) {
      const arr = userInfos.avatar_url.split('/');
      const filename: string[] = (last(arr) as string).split('.');

      arr[arr.length - 1] = `${filename[0]}big.${filename[1]}`;

      setImageList([{url: arr.join('/')}]);
    }
  }, []);

  return (
    <>
      <View style={IndexStyle.body}>
        <XlbHeader title="修改资料" hasInputFilter={false} />

        <View style={IndexStyle.body_content}>
          <ScrollView style={{flex: 1}} showsVerticalScrollIndicator={false}>
            <Space gap={TOKEN.space_3}>
              <View style={ComponentStyle.card}>
                <View style={IndexStyle.avatar_wrap}>
                  <Pressable style={IndexStyle.avatar} onPress={uploadImage}>
                    <Image
                      source={
                        data.avatar_url
                          ? {uri: data.avatar_url}
                          : images.newAvatar
                      }
                      style={IndexStyle.avatar_image}></Image>
                    <View style={IndexStyle.avatar_badge}>
                      <XlbIconfont
                        name="paizhao"
                        size={TOKEN.space_4}
                        color="#fff"></XlbIconfont>
                    </View>
                  </Pressable>
                </View>

                <View style={IndexStyle.userInfo}>
                  {userInfo
                    .filter(item => !item.isHide)
                    .map((item, index) => (
                      <UserInfoItem
                        key={item.title}
                        title={item.title}
                        value={
                          item.setVal
                            ? item.setVal(userInfos?.[item?.key])
                            : userInfos?.[item?.key]
                        }
                        isLast={index === userInfo.length - 1}
                        hideVal={item.hideVal}
                        onPress={item.onPress}
                      />
                    ))}
                </View>
              </View>

              <View style={ComponentStyle.card}>
                <View style={IndexStyle.userInfo}>
                  {depart.map((item, index) => (
                    <UserInfoItem
                      key={item.title}
                      title={item.title}
                      value={
                        item?.key === 'company_name'
                          ? userInfos?.[item?.key]
                          : businessDeptListType?.[userInfos?.[item?.key]]
                      }
                      isLast={index === depart.length - 1}
                    />
                  ))}
                </View>
              </View>
            </Space>
            {!is20250501 && (
              <XlbText
                style={{
                  marginTop: normalize(12),
                  textAlign: 'center',
                  color: TOKEN.red_10,
                }}>
                【姓名、手机号、修改密码】功能已迁移至【设置】中配置，当前页面修改功能将于2025年4月30日取消，请知悉！
              </XlbText>
            )}
          </ScrollView>
        </View>
      </View>

      <ActionSheet
        bottomUpType="portal"
        open={actionVisible}
        onClose={() => {
          setActionVisible(false);
        }}
        showCancel
        showSafeBottom
        containerStyle={{
          backgroundColor: '#F2F3F5',
          paddingHorizontal: 0,
          paddingVertical: 0,
          overflow: 'hidden',
        }}
        lists={[
          ...(imageList.length
            ? [
                {
                  label: '查看大图',
                  value: '查看大图',
                },
              ]
            : []),
          {
            label: '相册',
            value: '相册',
          },
          {
            label: '拍照',
            value: '拍照',
          },
        ]}
        hiddenHeader
        onChange={val => {
          if (val.value !== '查看大图') {
            upload(val.value);
          } else {
            if (imageList.length) setImageVisible(true);
          }
        }}
      />

      <Popup
        visible={visible}
        position="bottom"
        safeAreaInsetBottom
        round
        onClose={() => {
          Keyboard.dismiss();
        }}
        destroyOnClosed
        style={{paddingHorizontal: TOKEN.space_1}}
        onOpened={() => {
          if (nameInputRef.current) nameInputRef.current.focus();
        }}>
        <Popup.Header
          style={{height: normalize(56)}}
          showClose={false}
          rightExtra={
            <Pressable
              style={{
                height: '100%',
                width: normalize(50),
                justifyContent: 'center',
                alignItems: 'flex-end',
              }}
              onPress={() => {
                handleSave();
              }}>
              <XlbText font_size_4 primary_10>
                完成
              </XlbText>
            </Pressable>
          }
          leftExtra={
            <Pressable
              style={{
                height: '100%',
                width: normalize(50),
                justifyContent: 'center',
                alignItems: 'flex-start',
              }}
              onPress={() => {
                setVisible(false);
              }}>
              <XlbText font_size_4 grey_45>
                取消
              </XlbText>
            </Pressable>
          }
          titleTextStyle={{fontSize: TOKEN.space_4, fontWeight: '500'}}
          title="修改姓名"
          onClose={() => {
            setVisible(false);
          }}
        />
        <Blank top={TOKEN.space_1} bottom={TOKEN.space_2}>
          <Space gap={TOKEN.space_3}>
            <XlbText grey_45>
              {'请设置2-24个字符，不包括@<>/等无效字符'}
            </XlbText>
            <View style={IndexStyle.modifyUserName}>
              <TextInput
                ref={nameInputRef}
                maxLength={24}
                defaultValue={userInfos.name}
                placeholder="请填写姓名"
                placeholderTextColor={TOKEN.grey_45}
                onChangeText={text => {
                  nameInputTempRef.current = text;
                }}
                style={{padding: 0}}
              />
            </View>
            <View style={{height: normalize(326)}}></View>
          </Space>
        </Blank>
      </Popup>

      <Modal visible={imageVisible} transparent animationType="fade">
        <ImageViewer
          imageUrls={imageList}
          enableSwipeDown
          onClick={() => {
            setImageVisible(false);
          }}
          onSwipeDown={() => {
            setImageVisible(false);
          }}
          // onSave={savePicture}
          menuContext={{saveToLocal: '保存到相册', cancel: '取消'}}
        />
      </Modal>

      {/* 修改手机号确认弹框*/}
      <Dialog.Component
        title="确定修改手机号？"
        message={
          '当前手机号（+86 ' +
          authModel.state.userInfos?.tel +
          ')修改手机号需要短信验证'
        }
        visible={changePhoneDialogShow}
        showConfirmButton={false}
        showCancelButton={false}
        closeOnPressOverlay={false}
        showClose={false}
        width={300}>
        <View
          style={{
            backgroundColor: '#fff',
            padding: 20,
            paddingTop: 20,
            paddingBottom: 24,
            borderRadius: 12,
          }}>
          <Row gap={8}>
            <Col span={12}>
              <Button
                text="取消"
                color={'#1F2126'}
                type="outline"
                onPress={() => {
                  setChangePhoneDialogShow(false);
                }}
              />
            </Col>
            <Col span={12}>
              <Button
                text="确定"
                type="primary"
                onPress={async () => {
                  setChangePhoneDialogShow(false);
                  navigation.navigate('VerifyPhone', {
                    tel: userInfos?.tel,
                    routkey: 'ChangePhoneNumber',
                  });
                  // postAllRead()
                }}
              />
            </Col>
          </Row>
        </View>
      </Dialog.Component>
    </>
  );
};

export default Index;
