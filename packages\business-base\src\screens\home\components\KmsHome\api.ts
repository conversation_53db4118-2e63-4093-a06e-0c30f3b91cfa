import { ErpHttp } from '@xlb/common/src/services/lib/erphttp'
import {
  BuildTasktStatisticResDTO,
  ClientClueStatisticReqDTO,
  ClientClueStatisticResDTO,
  SignClientReqDTO,
  SignClientStatisticResDTO,
  TargetStatisticResDTO,
  UserNoticePageReqDTO,
  UserNoticeResDTO,
  WaitHandleDetailResDTO,
} from './type'

export default {
  //客户统计
  getCilentStatistic: (data: ClientClueStatisticReqDTO) => ErpHttp.post<Result<ClientClueStatisticResDTO>>('/kms/hxl.kms.report.intentclient.find', data),

  //客户折线图数据
  getChartList: (data: ClientClueStatisticReqDTO) => ErpHttp.post<Result<ClientClueStatisticResDTO>>('/kms/hxl.kms.report.intentlinechart.find', data),
  //客户折线图数据
  getTableList: (data: ClientClueStatisticReqDTO) => ErpHttp.post<Result<ClientClueStatisticResDTO>>('/kms/hxl.kms.report.intentclient.detail.find', data),

  //标地统计
  getTargetStatistic: (data: ClientClueStatisticReqDTO) => ErpHttp.post<Result<TargetStatisticResDTO>>('/kms/hxl.kms.app.target.statistic', data),

  //工程统计
  getBuildtaskStatistic: (data: ClientClueStatisticReqDTO) => ErpHttp.post<Result<BuildTasktStatisticResDTO>>('/kms/hxl.kms.app.buildtask.statistic', data),

  //用户公告
  getNotice: (data: UserNoticePageReqDTO) => ErpHttp.post<Result<TableList<UserNoticeResDTO>>>('/erp-mdm/hxl.erp.usernotice.page', data),

  //待办事项
  getWaitHandle: () => ErpHttp.post<Result<WaitHandleDetailResDTO[]>>('/kms/hxl.kms.join.waithandle.find'),

  // 签约top10 &  签约概况
  getSignClientStatistic: (data: SignClientReqDTO) => ErpHttp.post<Result<SignClientStatisticResDTO>>('/kms/hxl.kms.signclient.statistic', data),
}
