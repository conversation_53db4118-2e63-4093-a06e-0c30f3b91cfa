import React, { useMemo, useEffect } from 'react'
import { Pressable, View } from 'react-native'
import { commonStyles } from '../../index'
import { XText } from '../../index'
import XlbIcon from '../../../assets/xlbIconFont'
import useStore from './model'
// import { authModel } from '@xlb/business-base/src/models/auth'
import { useNavigation } from '@react-navigation/native'

type XlbStoreTextType = {
  textSize?: number
  textColor?: string
  iconStoreColor?: string
  iconDropColor?: string
  isH5?: boolean
}

const XlbStoreText = (props: XlbStoreTextType) => {
  const navigation = useNavigation<any>()
  const store = useStore((state: any) => state)
  const { textColor = '#fff', iconDropColor = '#fff', iconStoreColor = '#fff', textSize = 12, isH5 = false } = props

  useEffect(() => {
    // 初始化时默认全选所有门店
    if (!store.storeList.length) {
      const storeList = authModel?.state?.userInfos?.query_stores || []
      store.setStoreList([...storeList])
    }
  }, [])

  const showText = useMemo(() => {
    if (store.storeList.length) {
      return store.storeList.length == 1 ? store.storeList[0].store_name : '多家门店'
    } else {
      return '未选择门店'
    }
  }, [store.storeList])

  const addRoute = () => {
    if (isH5) {
      navigation.navigate('selectStoreH5', {
        isMultiple: true,
        params: {
          storeIds: store.storeList.map((v: any) => v.id),
        },
        storeIds: store.storeList.map((v: any) => v.id),
        onSelect: (data) => {
          // console.log('当前选择门店', data)
          store.setStoreList(data.list)
        },
      })
    } else {
      navigation.navigate('ErpSelectStore', {
        backRoute: 'goBack',
        model: store,
        listName: 'storeList',
        setListName: 'setStoreList',
        isMultiple: true,
        postUrl: '/erp/hxl.erp.store.short.page',
      })
    }
  }

  return (
    <Pressable onPress={() => addRoute()} style={{ flex: 1, paddingRight: 8, justifyContent: 'center' }}>
      <View style={{ ...commonStyles.horBox, ...commonStyles.verCenBox }}>
        <XlbIcon name={'iconStore'} size={14} color={iconStoreColor} />
        <XText numberOfLines={3} style={{ fontSize: textSize, color: textColor, marginLeft: 4 }}>
          {showText}
        </XText>
        {/*<XlbIcon name={'iconDropDown'} size={14} color={iconDropColor} style={{ marginLeft: 2 }} />*/}
      </View>
    </Pressable>
  )
}

export default XlbStoreText
