import axios, { AxiosError } from 'axios'
import { enhance } from 'foca-axios'
import dayjs from 'dayjs'
import { getToken, saveToken, signOut } from '../auth'
import XLBStorage from '../../utils/storage'
import { USER_ACCOUNT, USER_LICENSE, USER_PASSWORD } from '../../config/constant'
import XLBBaseApi from '@xlb/business-base/src/api'
// import { Toast } from 'native-base'
import { authModel } from '@xlb/business-base/src/models/auth'
import Config from 'react-native-config'
import { Cell, Toast } from '@fruits-chain/react-native-xiaoshu'
import { msgMap } from '../msg'
import { navigationRef } from '@xlb/common/src/navigation/NavigationService'
import Const from '@xlb/common/src/utils/const'

import { DeviceEventEmitter } from 'react-native'
import { cloneDeep } from 'lodash'
import * as Sentry from '@sentry/react-native'

const green = '\u001b[32m'
const yellow = '\u001b[33m'
const magenta = '\u001b[35m'
const cyan = '\u001b[36m'
const white = '\u001b[37m'
const reset = '\u001b[0m'

let isRefresh = false
let retryList: any[] = []

const instance = axios.create()

instance.interceptors.request.use(
  // 动态获取baseUrl 便于切换环境
  async (config) => {
    // const accessToken = config.url === '/erp-mdm/hxl.erp.user.login' ? null : await refreshToken();
    // console.info(cyan + '| ----------------- fetcher loging ----------------- ');
    // console.info(cyan + '| fetcher      | ' + magenta + 'requestFetcher');
    // console.info(cyan + '| endpoint     | ' + yellow + `${config.url}`);
    // // console.info(cyan + '| body         | ' + white, config.data);
    // console.info(cyan + '| Bearer token | ' + accessToken);
    // console.info(cyan + '| -------------------------------------------------- ' + reset);
    //循环遍历&替换参数
    let RequestBody: any = []
    for (let key in config.data) {
      let encodedKey = encodeURIComponent(key)
      let encodedValue = encodeURIComponent(config.data[key])
      //requestBody.append(encodedKey, encodedValue);
      RequestBody.push(encodedKey + '=' + encodedValue)
    }
    config.data = config.data || {}
    // 过滤为空的参数
    for (const i in config.data) {
      if (config.data[i] !== '' && config.data[i] !== null && config.data[i] !== undefined) {
        RequestBody[i] = config.data[i]
      }
    }
    // console.log('RequestBody', RequestBody);
    if (authModel?.state?.userInfo) {
      config.data.operator = authModel?.state?.userInfos?.id || ''
      config.data.operator_store_id = authModel?.state?.userInfos?.store.id || ''
      config.data.company_id = authModel?.state?.userInfos?.company_id || 1000
    }

    const urlSplit = config.url?.split('/')?.filter((item) => !!item) || []

    // config.data = RequestBody.join('&');
    // config.baseURL = await XLBGlobalStorage.getItem(BASE_URL);
    // config.baseURL = Config.ENV === 'DEVELOP' && urlSplit && urlSplit.length ? Const.NewApiService[urlSplit[0]] || Config.ERP_URL : Config.ERP_URL
    const urlEnvName = ['ENV', 'DEVELOP', 'TEST'].includes(Config.ENV || '') ? 'test' : Config.BRANCH_NAME;
    config.baseURL = `https://react-web.react-web.ali-${urlEnvName}.xlbsoft.com/`;
    console.log('transitHttp.js -> config.baseURL', config.baseURL);
    config.headers = {
      'Content-type': 'application/json',
      Authorization: `Bearer ${authModel?.state?.userInfos?.old_access_token}`,
      'Access-Token': `${authModel?.state?.userInfos?.access_token}`,
      'Api-Version': '1.5.0',
      'Company-Id': authModel?.state?.userInfos?.company_id || 1000,
      'User-Id': authModel?.state?.userInfos?.id,
      'Org-Ids': authModel?.state.userInfos?.org_ids ? authModel?.state.userInfos?.org_ids.join(',') : '',
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

instance.interceptors.response.use(
  (response) => {
    if (response.data.code !== 0) {
      // Toast.fail(response.data.msg)
      if (response.status == 401) {
        Toast.fail('用户暂无该接口权限\n请联系信息部处理')
      } else {
        if (response.data.msg === '登录已过期，请重新登录' && !response.config.url?.includes('usernotice.page')) {
          DeviceEventEmitter.emit('logOut')
          // if (!isRefresh) {
          //   isRefresh = true
          //   return instance
          //     .post('/erp-mdm/hxl.erp.user.token.refresh', {
          //       refresh_token: authModel.state.userInfos.refresh_token,
          //       company_id: authModel.state.userInfos.company_id,
          //     })
          //     .then((res) => {
          //       if (res?.code === 0) {
          //         const { access_token, refresh_token } = res.data
          //         authModel.setUserInfos({ ...cloneDeep(authModel.state.userInfos), access_token, token: access_token, refresh_token })
          //         XLBStorage.setItem('loginInfo', { loginTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), refresh_token })
          //         // token 刷新后将数组的方法重新执行
          //         retryList.forEach((cb) => {
          //           cb()
          //         })
          //         retryList = [] // 重新请求完清空
          //         return instance({ ...response.config, isHiddenMsg: true, data: JSON.parse(response.config.data) }) // 第一次401进入的接口重试
          //       } else {
          //         DeviceEventEmitter.emit('logOut')
          //         retryList = []
          //       }
          //     })
          //     .catch((err) => {
          //       console.log('抱歉，您的登录状态已失效，请重新登录！')
          //       return Promise.reject(err)
          //     })
          //     .finally(() => {
          //       isRefresh = false
          //     })
          // } else {
          //   // 返回未执行 resolve 的 Promise
          //   return new Promise((resolve) => {
          //     // 用函数形式将 resolve 存入，等待刷新后再执行
          //     retryList.push(() => {
          //       resolve(instance({ ...response.config, isHiddenMsg: true, data: JSON.parse(response.config.data) }))
          //     })
          //   })
          // }
        }

        if (response.data.msg && !response?.config?.isHiddenMsg) {
          Toast.fail(response.data.msg)
        }
      }
    }
    return response.config.url === '/erp-mdm/hxl.erp.user.login' ? response : response.data
  },
  (err: AxiosError) => {
    if (!err?.config?.isHiddenMsg) {
      if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
        Toast.fail('请求超时，请重试')
      } else if (err?.response?.status == 401) {
        Toast.fail('用户暂无该接口权限\n请联系信息部处理')
      } else if (err.message === 'Network Error') {
        const currentRoute = navigationRef?.current?.getCurrentRoute()
        if (currentRoute.name !== 'Home') {
          debugger
                    navigationRef.current.navigate('NoNetWork')
        }
      } else {
        if (err?.response?.status) {
          Sentry.captureException(`服务告警${err?.response?.status}---------${err?.config?.baseURL}${err?.config?.url};\n用户：${authModel?.state?.userInfos?.name} - ${authModel?.state?.userInfos?.tel}`)
          Toast.fail(msgMap[err?.response?.status])
        }
      }
    }
    return Promise.reject(err)
  }
)

export const XLBHttps = enhance(instance, {
  cache: {
    enable: false,
  },
  throttle: {
    enable: true,
  },
  retry: {
    enable: true,
  },
})

async function refreshToken() {
  const token = await getToken()
  let accessToken = token.accessToken
  if (!token || !token?.accessToken) {
    await signOut()
    return ''
  }
  // 判断当前日期是否晚于tokenExpireTime，如果是表示token已经过期，根据持久化的数据去调用登录接口返回token
  if (dayjs().isAfter(dayjs.unix(<number>token.time))) {
    const license = await XLBStorage.getItem(USER_LICENSE)
    const account = await XLBStorage.getItem(USER_ACCOUNT)
    const password = await XLBStorage.getItem(USER_PASSWORD)
    const response: VerifyPasswordSuccessPayload = await XLBHttps.post('/login/login', {
      license: license,
      account: account,
      pwd: password,
      from: 'app',
    })
    //登录成功，将token放置缓存中便于查询
    const tokenInfo: Token = {}
    tokenInfo.accessToken = response.access_token
    tokenInfo.userId = response.data._u_id
    tokenInfo.time = response.data._time
    tokenInfo.xlbToken = response.data._token
    tokenInfo.username = response.data.name
    saveToken(tokenInfo)
    return XLBBaseApi.Base.getUserInfo()
  }
  return accessToken
}
