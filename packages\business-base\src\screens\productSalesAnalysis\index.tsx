import React, { useEffect, useState } from 'react'
import { commonS<PERSON><PERSON>, Header, HeaderTitle, XIcon } from '@xlb/common/src/components'
import { useNavigation, useRoute } from '@react-navigation/native'
import { colors } from '@xlb/common/src/config/theme'
import { XlbStoreText, XlbToggle, XlbSelectDate } from '@xlb/common/src/components/features'
import useProductSalesAnalysis from './model'
import CardList from './components/ShowCard/CardList'
import dayjs from 'dayjs'
import Toast from 'react-native-root-toast'
import { View, StyleSheet } from 'react-native'
import useStore from './store'
import { useUnmount } from 'ahooks'
import useXlbDatePicker from '@xlb/common/src/components/features/xlbDatePicker/model'
import GoodsDialog from './components/goodsDialog'
// import AnalyticsUtil from '@xlb/common/src/utils/AnalyticsUtil'

// 门店+时间筛选组件
const StoreTimeFilter = React.memo(() => {
  const navigation = useNavigation<any>()
  const fontArr = useXlbDatePicker((state: any) => state.dateArr)
  const { toggleList, setDateType, setDateArr, setCompareDate, dateArr, dateType } = useProductSalesAnalysis((state: any) => state)

  const changeDateType = (item: any) => {
    switch (item.value) {
      case 'DAY':
        return [dayjs().startOf('day').format('YYYY-MM-DD'), dayjs().startOf('day').format('YYYY-MM-DD')]
      case 'WEEK':
        return [dayjs().add(0, 'week').startOf('week').add(0, 'day').format('YYYY-MM-DD'), dayjs().startOf('day').format('YYYY-MM-DD')]
      case 'MONTH':
        return [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().startOf('day').format('YYYY-MM-DD')]
      default:
        return null
    }
  }

  useEffect(() => {
    if (dateType === 'CUSTOM' && JSON.stringify(dateArr) !== JSON.stringify(fontArr)) {
      setDateArr([...fontArr])
      setCompareDate(fontArr)
    }
  }, [fontArr])

  useEffect(() => {
    const findDayItem = toggleList.find((v: any) => v.value === dateType)
    setDateArr(changeDateType(findDayItem) || dateArr)
  }, [])
  return (
    <View style={styles.storeTimeFilterView}>
      <XlbStoreText isH5 />
      <XlbToggle
        toggleList={toggleList}
        dateType={dateType}
        onChange={(item: any, isFont: boolean) => {
          setDateType(item.value)
          const res = changeDateType(item)
          if (res) {
            if (!isFont) {
              setDateArr(res)
              setCompareDate(res)
            }
          } else {
            navigation.navigate('xlbDatePicker', { dateArr })
          }
        }}
      />
    </View>
  )
})

// 快捷时间筛选组件
const FastTimeSelectRow = React.memo(() => {
  const { toggleList, dateArr, dateType, setDateArr } = useProductSalesAnalysis((state: any) => state)
  const date = toggleList.find((v: any) => v.active)?.value
  const dateClick = (type: string) => {
    const num = type === 'pre' ? -1 : 1
    let nextDate
    const nowDay = dayjs(new Date()).format('YYYY-MM-DD')
    if (dateArr[1] === dayjs(new Date()).format('YYYY-MM-DD') && num === 1) {
      return Toast.show('不能超过当前自然日')
    }
    switch (dateType) {
      case 'DAY':
        setDateArr([
          dayjs(dateArr[0]).startOf('day').add(num, 'day').format('YYYY-MM-DD'),
          dayjs(dateArr[1]).startOf('day').add(num, 'day').format('YYYY-MM-DD'),
        ])
        break
      case 'WEEK':
        nextDate = dayjs(dateArr[0]).add(num, 'week').endOf('week').format('YYYY-MM-DD')
        if (dayjs(nowDay).diff(nextDate, 'day') < 0) {
          nextDate = nowDay
        }
        setDateArr([dayjs(dateArr[0]).add(num, 'week').startOf('week').add(0, 'day').format('YYYY-MM-DD'), nextDate])
        break
      case 'MONTH':
        nextDate = dayjs(dateArr[0]).add(num, 'month').endOf('month').format('YYYY-MM-DD')
        if (dayjs(nowDay).diff(nextDate, 'day') < 0) {
          nextDate = nowDay
        }
        setDateArr([dayjs(dateArr[0]).add(num, 'month').startOf('month').format('YYYY-MM-DD'), nextDate])
        break
      default:
        const nums = num === -1 ? dayjs(dateArr[0]).diff(dateArr[1], 'day') - 1 : dayjs(dateArr[1]).diff(dateArr[0], 'day') + 1
        nextDate = dayjs(dateArr[1]).startOf('day').add(nums, 'day').format('YYYY-MM-DD')
        if (dayjs(nowDay).diff(nextDate, 'day') < 0) {
          nextDate = nowDay
        }
        setDateArr([dayjs(dateArr[0]).startOf('day').add(nums, 'day').format('YYYY-MM-DD'), nextDate])
        break
    }
  }

  return (
    <View style={styles.FastTimeSelectRow}>
      <XlbSelectDate
        dateArr={dateArr}
        dateClick={dateClick}
        boxStyle={{ backgroundColor: '#4787FF', borderRadius: 100 }}
        textStyle={{ color: '#fff', fontSize: 15 }}
        iconColor={colors.white}
      />
    </View>
  )
})

const ProductSalesAnalysis = () => {
  const navigation = useNavigation<any>()
  const route = useRoute<any>()
  const model = useStore((state: any) => state)
  const resetModel = useStore((state: any) => state.resetModel)
  const [goodsItem, setGoodsItem] = useState<any>(false)

  useUnmount(() => {
    resetModel()
  })
  return (
    <>
      <GoodsDialog goodsItem={goodsItem} visible={!!goodsItem} setVisible={setGoodsItem} />
      <Header
        centerComponent={<HeaderTitle title={'商品销售分析'} />}
        leftComponent={
          <XIcon
            name={'back'}
            onPress={() => {
              // AnalyticsUtil.onPageEnd(`${route?.params?.system || ''}-${route.name}-配送毛利分析`)
              navigation.goBack()
            }}
          />
        }
        rightComponent={
          <View>
            <XIcon
              name={'search'}
              size={20}
              onPress={() =>
                navigation.navigate('ErpSelectGood', {
                  isMultiple: true,
                  model: model,
                  listName: 'newGoodList',
                  changeItem: 'changeItem',
                  setListName: 'setDetails',
                  postUrl: '/erp/hxl.erp.item.short.page',
                  backRoute: 'productSalesAnalysis',
                  chooseMode: 'checkBox',
                  resetButton: true,
                  isAdd: true,
                  onChangeList: (list: any) => {
                    model.setNewGoodList(list)
                  },
                })
              }
              style={{ right: 15 }}
            />
          </View>
        }
      />
      <StoreTimeFilter />
      <FastTimeSelectRow />
      <CardList setGoodsItem={setGoodsItem} />
    </>
  )
}

const styles = StyleSheet.create({
  storeTimeFilterView: {
    ...commonStyles.horBox,
    ...commonStyles.centerBox,
    padding: 10,
    justifyContent: 'space-between',
    backgroundColor: colors.primary,
  },
  FastTimeSelectRow: {
    backgroundColor: colors.primary,
    padding: 10,
    paddingTop: 0,
  },
})

export default ProductSalesAnalysis
